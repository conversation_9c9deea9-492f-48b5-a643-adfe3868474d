package com.cnoocshell.base.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ValueAndOptionDTO extends ValueSetDTO {
    /**
     * 删除的ids
     */
    @ApiModelProperty("删除的ids")
    private List<String> deleteIds;

    /**
     * 值集项集合
     */
    @ApiModelProperty("值集项集合")
    List<ValueSetOptionDTO> list;

}
