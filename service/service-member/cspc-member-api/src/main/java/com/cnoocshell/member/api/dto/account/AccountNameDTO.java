package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccountNameDTO implements Serializable {

    @ApiModelProperty("账号id")
    private String accountId;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("手機號")
    private String mobile;

    @ApiModelProperty("郵箱")
    private String email;
}
