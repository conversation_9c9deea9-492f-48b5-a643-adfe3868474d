package com.cnoocshell.member.api.dto.report;


import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.common.excel.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class MemberReportResultDTO {
    @ApiModelProperty(value = "产品一级分类ID", notes = "ID")
    private String categoryIdLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "编码")
    private String categoryCodeLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "名称")
    @Excel(index = 1, title = "产品一级分类")
    private String categoryNameLevelOne;

    @ApiModelProperty(value = "产品二级分类ID", notes = "ID")
    private String categoryIdLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "名称")
    @Excel(index = 5, title = "产品二级分类")
    private String categoryNameLevelTwo;

    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty(value = "客户编码")
    @Excel(index = 10, title = "客户编码")
    private String crmCode;
    @ApiModelProperty(value = "会员编码")
    private String memberCode;
    @ApiModelProperty(value = "客户名称")
    @Excel(index = 15, title = "客户名称")
    private String memberName;

    @ApiModelProperty(value = "会员状态")
    private String status;

    @ApiModelProperty(value = "客户状态",notes = "1:禁用 0：启用")
    private Integer ableStatus;
    @ApiModelProperty(value = "客户状态显示名称",notes = "1:禁用 0：启用")
    @Excel(index = 20, title = "客户状态")
    private String ableStatusName;

    @ApiModelProperty(value = "保证金状态")
    private String depositStatus;
    @ApiModelProperty(value = "保证金状态显示名称")
    @Excel(index = 22, title = "保证金状态")
    private String depositStatusName;

    @ApiModelProperty(value = "销售渠道", notes = "销售渠道编码")
    private String saleChannel;
    @ApiModelProperty(value = "销售渠道", notes = "销售渠道名称")
    @Excel(index = 25, title = "销售渠道")
    private String saleChannelName;

    @ApiModelProperty(value = "意向类型", notes = "意向类型单选匹配 1：主要 2：次要")
    private Integer intentionType;
    @ApiModelProperty(value = "意向类型", notes = "意向类型单选匹配 1：主要 2：次要")
    @Excel(index = 30, title = "主次类别")
    private String intentionTypeName;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;

    @ApiModelProperty(value = "产品名称")
    @Excel(index = 35, title = "产品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码")
    @Excel(index = 40, title = "SAP物料编号")
    private String sapMaterialCode;

    @ApiModelProperty(value = "销售人员ID")
    private String saleUserId;

    @ApiModelProperty(value = "销售人员名称")
    @Excel(index = 45, title = "销售员姓名")
    private String saleUserName;

    @ApiModelProperty(value = "销售人员工号")
    @Excel(index = 50, title = "销售员工号")
    private String saleUserEmployeeId;

    @ApiModelProperty(value = "入驻审批通过时间")
    @Excel(index = 55, title = "入驻审批通过时间", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date approveTime;
}
