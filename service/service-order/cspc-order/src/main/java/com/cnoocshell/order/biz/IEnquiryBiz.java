package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.dao.vo.Enquiry;

import java.util.List;

public interface IEnquiryBiz extends IBaseBiz<Enquiry> {
    Enquiry getByNo(String no);


    /**
     * 是否存在对应状态的销售计划创建的询价数据
     * @param planNo 销售计划编号
     * @param enquiryStatus 询价状态
     * @return  true:存在
     */
    Boolean existByPlanAndStatus(String planNo, List<String> enquiryStatus);
}
