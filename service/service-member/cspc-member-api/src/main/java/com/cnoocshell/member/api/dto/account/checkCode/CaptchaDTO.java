package com.cnoocshell.member.api.dto.account.checkCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Base64;

/**
 * @DESCRIPTION:
 */
@Data
public class CaptchaDTO {

    /**
     * 验证码（二级制）
     */
    @ApiModelProperty("验证码（二级制）")
    private byte[] captcha;

    /**
     * 验证码code
     */
    @ApiModelProperty("验证码code")
    private String captchaCode;

    /**
     * 验证码 （base64）
     * @return
     */
    public String getCaptchaString(){
        return Base64.getEncoder().encodeToString(captcha);
    }
}
