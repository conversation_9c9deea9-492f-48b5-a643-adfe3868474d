package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.common.dto.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @DESCRIPTION:查询条件对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountIdDTO extends BasePageDTO {

    @ApiModelProperty("账户ID")
    private List<String> accountIds;
}
