package com.cnoocshell.member.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.biz.IMemberHistoryBiz;
import com.cnoocshell.member.dao.mapper.MemberHistoryMapper;
import com.cnoocshell.member.dao.vo.MemberHistory;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
@Transactional
@CacheConfig(cacheManager = "memberCacheManager")
@RequiredArgsConstructor
public class MemberHistoryBiz extends BaseBiz<MemberHistory> implements IMemberHistoryBiz {

    private final MemberHistoryMapper memberHistoryMapper;
    private final BizRedisService bizRedisService;

    @PostConstruct
    public void init() {
        Set<String> keys = bizRedisService.getRedisTemplate().keys(MemberRedisKeys.CACHE_MEMBER_HISTORY + "*");
        bizRedisService.getRedisTemplate().delete(keys);
    }

    @Cacheable(value = MemberRedisKeys.CACHE_MEMBER_HISTORY, key = "'" + MemberRedisKeys.CACHE_MEMBER_HISTORY + "'+#id", unless = "#result==null")
    @Override
    public MemberHistory get(String id) {
        return super.get(id);
    }

    @Override
    public boolean insertSelective(MemberHistory memberHistory) {
        memberHistory.setDelFlg(Boolean.FALSE);
        return memberHistoryMapper.insertSelective(memberHistory) > 0;
    }

    @Override
    public MemberHistory getByRequestId(String requestId) {
        if(CharSequenceUtil.isBlank(requestId))
            return null;
        Condition condition = new Condition(MemberHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.REQUEST_ID,requestId);
        return CollUtil.getFirst(this.findByCondition(condition));
    }

    @Override
    public List<MemberHistory> listByRequestIds(List<String> requestIds) {
        if (CollUtil.isEmpty(requestIds))
            return null;
        Condition condition = new Condition(MemberHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.REQUEST_ID, requestIds);
        return this.findByCondition(condition);
    }
}
