package com.cnoocshell.order.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "bidding_buyer_deal")
public class BiddingBuyerDeal extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "bidding_no")
    private String biddingNo;

    @Column(name = "bidding_buyer_detail_id")
    private String biddingBuyerDetailId;

    @Column(name = "member_code")
    private String memberCode;

    @Column(name = "member_name")
    private String memberName;

    @Column(name = "current_standard_price")
    private BigDecimal currentStandardPrice;

    @Column(name = "want_quantity")
    private BigDecimal wantQuantity;

    @Column(name = "delivery_mode")
    private String deliveryMode;

    @Column(name = "pack")
    private String pack;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "create_user_name")
    private String createUserName;

    /**
     *SAP合同号
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     *成交编号
     */
    @Column(name = "deal_no")
    private String dealNo;


    /**
     *创建SAP合同状态 0失败，1成功
     */
    @Column(name = "create_contract_status")
    private Boolean createContractStatus;


    /**
     *创建合同失败原因
     */
    @Column(name = "create_contract_fail_reason")
    private String createContractFailReason;

}