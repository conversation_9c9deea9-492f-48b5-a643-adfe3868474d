package com.cnoocshell.base.config;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsProperties {

    private String appId;

    private String secretId;
    private String secretKey;

    private String region;

    private String sign;

    private String countryCode;

    private String consumerHotline;

    private Boolean enable=true;
    /**
     * 默认不使用自定义签名
     */
    private Boolean customSignEnable=false;
    /**
     * 自定义签名替换，customSignReplaceStr: key-value,key-value...
     */
    private Map<String,String> customSign;
    private String customSignReplaceStr;

    public int getAppIdToInt(){
        return Integer.parseInt(appId);
    }

    public boolean getCustomSignEnableBoolean() {
        return customSignEnable == null ? false : customSignEnable;
    }

    public void setCustomSignReplaceStr(String customSignReplaceStr) {
        this.customSignReplaceStr = customSignReplaceStr;
        if(StringUtils.isNotBlank(customSignReplaceStr)) {
            this.customSign = Maps.newHashMap();
            String[] strings = customSignReplaceStr.split(",");
            for (String string : strings) {
                if (StringUtils.isNotBlank(string) && string.indexOf("-") != -1) {
                    String[] kv = string.split("-");
                    customSign.put(kv[0], kv[1]);
                }
            }
        }
    }
}
