package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 变更请求记录
 */
@Data
public class MemberApprovalRequestDTO implements Serializable {

    private static final long serialVersionUID = 8589713326320818314L;
    /**
     * 变更请求id
     */
    @ApiModelProperty("变更请求id")
    private String requestId;

    /**
     * 变更请求id（方便前端查询使用）
     */
    @ApiModelProperty("变更请求id（方便前端查询使用）")
    private String requestNum;

    /**
     * 会员id
     */
    @ApiModelProperty("会员ID")
    private String memberId;
	/**
     * 会员code
     */
    @ApiModelProperty("会员代码")
    private String memberCode;

    /**
     * 实名认证账号id
     */
    @ApiModelProperty("实名认证账号id")
    private String accountId;
    /**
     * 实名认证账号code
     */
    @ApiModelProperty("实名认证账号code")
    private String accountCode;

    /**
     * 修改基本信息、补录信息等，见原型 21-C-0101 企业变更
     */
    @ApiModelProperty("变更类型")
    private String requestType;

    /**
     * 变更类型文本
     */
    @ApiModelProperty("变更类型文本")
    private String requestTypeText;

    /**
     * 变更状态
     */
    @ApiModelProperty("变更状态")
    private String status;

    /**
     * 平台代理
     */
    @ApiModelProperty("平台代理 0-否，1-是 2 卖家代注册")
    private Integer proxyStatus;
    @ApiModelProperty("代注册会员id")
    private String proxyMemberId;
    @ApiModelProperty("代注册会员操作的账户id")
    private String proxyAccountId;

    /**
     * 变更状态文本
     */
    @ApiModelProperty("变更状态文本")
    private String statusText;

    /**
     * 变更摘要
     */
    @ApiModelProperty("变更信息")
    private String changeMessage;

    /**
     * 申请人Id
     */
    @ApiModelProperty("申请人id")
    private String requestMemberId;


    /**
     * 审批人Id
     */
    @ApiModelProperty("审批人ID")
    private String approveId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty("审批人")
    private String approveName;

    /**
     * 申请提交时间
     */
    @ApiModelProperty("申请提交时间")
    private Date requestTime;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approveTime;

    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String approveText;

    /**
     * 会员名
     */
    @ApiModelProperty("会员名")
    private String memberName;


    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactName;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contactPhone;

    /**
     * 会员类型
     */
    @ApiModelProperty("会员类型")
    private String memberType;

    /**
     * 主营产品
     */
    @ApiModelProperty("主营产品")
    private String mainProducts;

    /**
     * 买家类型
     */
    @ApiModelProperty("买家类型")
    private String buyerType;

    /**
     * 卖家类型
     */
    @ApiModelProperty("卖家类型")
    private String sellerType;
    /**
     * 承运商类型
     */
    @ApiModelProperty("承运商类型")
    private String carrierType;

    /**
     * 买家类型名称
     */
    @ApiModelProperty("承运商类型名称")
    private String carrierTypeName;

    /**
     * 供应商类型
     */
    @ApiModelProperty("供应商类型")
    private String supplierType;

    /**
     * 真实名称
     */
    @ApiModelProperty("实名认证-真实名称")
    private String realName;

    /**
     * 身份证号码
     */
    @ApiModelProperty("实名认证-身份证号码")
    private String realNameIdNumber;

    /**
     * 身份证有效时间
     */
    @ApiModelProperty("实名认证-身份证有效时间")
    private Date realNameEffectiveTime;

    /**
     * 所在城市集合
     */
    @ApiModelProperty("实名认证-所在城市集合")
    private List<String> realNameCity;

    /**
     * 身份证图片集合
     */
    @ApiModelProperty("实名认证-身份证图片集合")
    private List<String> realNameIdPic;

    /**
     * 省
     */
    @ApiModelProperty("省(仅查询显示时使用)")
    private String provinceCode;

    /**
     * 省
     */
    @ApiModelProperty("省")
    private String provinceName;

    /**
     * 市
     */
    @ApiModelProperty("市(仅查询显示时使用)")
    private String cityCode;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String cityName;

    /**
     * 区
     */
    @ApiModelProperty("区(仅查询显示时使用)")
    private String areaCode;

    /**
     * 区
     */
    @ApiModelProperty("区")
    private String areaName;


    /**
     * 是否卖家
     */
    @ApiModelProperty("是否卖家")
    private Integer sellerFlg;

    /**
     * 是否承运商
     */
    @ApiModelProperty("是否承运商")
    private Integer carrierFlg;

    /**
     * 是否供应商
     */
    @ApiModelProperty("是否供应商")
    private Integer supplierFlg;
    /**
     * 2020.11.2 新增承运商注册字段 业务范围（或者叫业务类型），
     */
    @ApiModelProperty("承运商业务范围")
    private String businessScope;


}
