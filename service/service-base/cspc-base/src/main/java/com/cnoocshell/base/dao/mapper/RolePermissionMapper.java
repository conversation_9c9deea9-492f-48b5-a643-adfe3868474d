package com.cnoocshell.base.dao.mapper;

import com.cnoocshell.base.dao.vo.Role;
import com.cnoocshell.base.dao.vo.RolePermission;
import com.cnoocshell.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface RolePermissionMapper extends IBaseMapper<RolePermission> {

    @Select("SELECT p.permission_code FROM sys_permission p INNER JOIN sys_role_permission rp ON p.permission_code = rp.permission_code WHERE rp.del_flg = 0 AND rp.role_code = #{roleCode} AND rp.is_choosed = 1 AND p.app_type = #{appType};")
    List<String> findPermissionByRoleCode(@Param("roleCode") String roleCode, @Param("appType") String appType);

    @Update("update sys_role_permission set del_flg = 1 where role_code =#{roleCode} and del_flg = 0")
    void deletePermissionByRoleCode(@Param("roleCode") String roleCode);

    Integer existPermission(@Param("roleCodes") List<String> roleCodes, @Param("permissionCodes") List<String> permissionCodes);
}