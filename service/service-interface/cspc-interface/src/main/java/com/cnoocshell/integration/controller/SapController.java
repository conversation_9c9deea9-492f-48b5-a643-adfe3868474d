package com.cnoocshell.integration.controller;

import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.integration.service.ISapService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sap")
public class SapController {

    private final ISapService sapService;

    @ApiOperation("获取token")
    @GetMapping(value="/getSapToken")
    public String getSapToken(){
        return sapService.getSapToken();
    }

    @ApiOperation("创建SAP合同")
    @PostMapping("/createSapContract")
    public BiddingDealContractDTO createSapContract(@RequestBody SapContractDTO param){
        return sapService.createSapContract(param,null,false);
    }
}
