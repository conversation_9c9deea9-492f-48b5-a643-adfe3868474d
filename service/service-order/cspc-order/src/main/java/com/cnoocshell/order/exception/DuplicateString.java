package com.cnoocshell.order.exception;

/**
 * 多次重复的字符串提取到该类
 */
public class DuplicateString {

    public static final String ADDRESS = "address";
    public static final String UTF_8 = "utf-8";

    public static final String RSA_SERVICE_NOT_AVAILABLE1 = "base模块还没配置RSA私钥，不能用该服务";
    public static final String RSA_SERVICE_NOT_AVAILABLE2 = "【base模块还没配置RSA私钥，不能用该服务】";
    public static final String IMG_URL = "imgUrl";
    public static final String CERTIFICATETYE = "certificateTye";
    public static final String PARAM_JSON = "paramJson";
    public static final String GOODS = "goods";
    public static final String ATTACHMENT_ID = "attachmentId";
    public static final String DEL_FLG = "delFlg";

    public static final String MENU_CODE = "menuCode";

    public static final String MEMBER_ID = "memberId";
    public static final String ACCOUNT_ID = "accountId";

    public static final String METHOD_DISCARD = "method discard";

    public static final String ADCODE_ASC = " `adcode` asc ";
    public static final String ADCODE_100000 = "100000";
    public static final String PROVINCE = "province";
    public static final String DISTRICT = "district";
    public static final String STREET = "street";
    public static final String LEVEL = "level";
    public static final String PROVINCE_CODE = "provinceCode";
    public static final String CITY_CODE = "cityCode";
    public static final String DISTRICT_CODE = "districtCode";
    public static final String STREET_CODE = "streetCode";
    public static final String TYPE = "type";


    public static final String PAGE_CODE = "pageCode";

    public static final String SERVICE_CODE = "serviceCode";
    public static final String CREATE_TIME = "createTime";

    public static final String WAREHOUSE_ID = "warehouseId";

    public static final String ID = "id";

    public static final String PLAN_NO = "planNo";

    public static final String PLAN_NAME = "planName";

    public static final String GOODS_NAME = "goodsName";

    public static final String STATUS = "status";

    public static final String IS_CREATED_INQUIRY = "isCreatedInquiry";

    public static final String IS_NEED_INQUIRY = "isNeedInquiry";

    public static final String DELIVERY_EFFECT_START_DATE = "deliveryEffectStartDate";

    public static final String DELIVERY_EFFECT_END_DATE = "deliveryEffectEndDate";

    public static final String CREATE_USER_NAME = "createUserName";

    public static final String CATEGORY_CODE = "categoryCode";

    public static final String GOODS_CODE = "goodsCode";

    public static final String SALES_PLAN_NAME = "销售计划";

    public static final String TO_APPROVE = "TO_APPROVE";

    public static final String APPROVED = "APPROVED";


    public static final String DRAFT = "DRAFT";

    public static final String REJECTED = "REJECTED";

    public static final String MP = "MP";
}
