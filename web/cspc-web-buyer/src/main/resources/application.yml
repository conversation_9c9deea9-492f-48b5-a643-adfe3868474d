logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
#先写死dev好跳转，各本地开发请自行屏蔽

spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: ${ENV:dev}
  application:
    name: cspc-web-buyer
  thymeleaf:
    prefix: classpath:/templates/
    encoding: UTF-8
    servlet:
      content-type: text/html
    mode: LEGACYHTML5
  kaptcha:
    properties:
      kaptcha:
        textproducer:
          char:
            length: "4"
            string: "0123456789"
server:
  port: 8085
  servlet:
    context-path: /buyerApi
