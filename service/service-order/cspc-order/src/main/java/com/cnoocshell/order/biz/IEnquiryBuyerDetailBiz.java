package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;

import java.util.List;

public interface IEnquiryBuyerDetailBiz extends IBaseBiz<EnquiryBuyerDetail> {


    /**
     * 根据询价对象获取买家询价详情列表。
     *
     * @param enquiry 询价对象，包含询价编号等信息
     * @return 买家询价详情列表
     */
    List<EnquiryBuyerDetail> listByNo(Enquiry enquiry);

    /**
     * 根据询价编号获取买家询价详情列表。
     *
     * @param enquiryNo 询价编号
     * @return 买家询价详情列表
     */
    List<EnquiryBuyerDetail> listByNo(String enquiryNo);
}
