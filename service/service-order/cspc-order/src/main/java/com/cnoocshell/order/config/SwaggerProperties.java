package com.cnoocshell.order.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "swagger")
@Data
@ToString
public class SwaggerProperties {

    /**
     * 作者
     */
    private String author;
    /**
     * 作者邮箱
     */
    private String email;
    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 版本
     */
    private String version;
    /**
     * 基础包
     */
    private String basePackage;
    /**
     * 接口地址
     */
    private String interfaceUrl;
}
