package com.cnoocshell.web.seller.enums;

import lombok.Getter;

/**
 * @Created: 14:33 04/12/2018
 * @Author: <EMAIL>
 * @Description: TODO
 */
@Getter
public enum ThirdPayAccountEnum {

    RECEIPTS(1, "收款账户"),

    PAYMENTS(2, "付款账户")
    ;

    private Integer code;

    private String message;

    ThirdPayAccountEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
