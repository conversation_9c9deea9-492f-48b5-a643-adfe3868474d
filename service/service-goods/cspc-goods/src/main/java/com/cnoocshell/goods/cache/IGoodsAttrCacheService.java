package com.cnoocshell.goods.cache;

import com.cnoocshell.goods.api.dto.GoodsAttributeDTO;
import com.google.common.collect.Multimap;
import com.google.common.collect.Table;

import java.util.concurrent.Future;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品属性缓存服务
 * @date   2018年8月21日 上午11:49:27
 */
public interface IGoodsAttrCacheService {
	
    public void setGoodsAttr(GoodsAttrCache cache);
    
    public GoodsAttrCache getGoodsAttr(String key);
    
    public void removeGoodsAttr(String key);
    
    /**
     * 
     * @return Table<CategoryId, CategoryType, Multimap<AttrName, AttrValue>>
     */
    public Table<String, String, Multimap<String, GoodsAttributeDTO>> getCateAttrTable();
    
    /**
     * 
     * @param table Table<CategoryId, CategoryType, Multimap<AttrName, AttrValue>>
     */
    public void setCateAttrTable(Table<String, String, Multimap<String, GoodsAttributeDTO>> table);
    
    public void clearCateAttrTable();
    
    /**
     * 异步初始化
     */
    public Future<String> initCateAttrCacheAsync();
}
