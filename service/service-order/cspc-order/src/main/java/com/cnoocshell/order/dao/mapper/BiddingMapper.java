package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisInfoDTO;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.dao.vo.Bidding;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface BiddingMapper extends IBaseMapper<Bidding> {

    List<BiddingSalesListViewDTO> salesBiddingList(@Param("dto") BiddingSalesListDTO dto);

    BiddingEditBiddingDTO queryEditQueryData(String biddingId);

    List<SimpleBiddingDTO> getSimpleBidding(@Param("status") String status,
                                            @Param("startDateTime") String startDateTime,
                                            @Param("endDateTime") String endDateTime);

    List<BiddingCustomerListDTO> queryCheckCustomerInfo(String biddingId);

    @Update("update bidding set status = #{status} where del_flg=0 and bidding_no=#{biddingNo}")
    int updateBiddingStatus(@Param("biddingNo") String biddingNo, @Param("status") String status);

    void updateStatus(
            @Param("biddingIdList") List<String> biddingIdList,
            @Param("biddingApproveStatus") String biddingApproveStatus,
            @Param("date") Date date,
            @Param("realName") String realName);

    void withdrawData(@Param("bidding") String bidding, @Param("status") String status);

    BiddingStrategyDetailDTO queryStrategyDetail(String biddingId);

    List<BiddingCustomerListDTO> queryCheckCustomerForCreate(String id);

    @Select("select * from bidding where del_flg = 0 and bidding_no=#{biddingNo}")
    Bidding getByNo(@Param("biddingNo") String biddingNo);

    List<BiddingEmailDTO> querySendEmailUserInfo(@Param("role") String role, @Param("idList") List<String> idList);

    List<String> queryCategoryCodeList(List<String> biddingIdList);

    List<BiddingCreateDataDTO> selectCreateBiddingList(List<String> enquiryIdList);

    String queryEnquiryIdBySalesId(String id);

    int updateBiddingQuantity(@Param("biddingNo") String biddingNo,
                              @Param("totalDealQuantity") BigDecimal totalDealQuantity);

    List<BiddingStrategyDetailExportExcelDTO> queryDownloadDataList(@Param("dto") BiddingExportDataDTO dto);

    List<Bidding> queryBiddingInfoByExcelBiddingNo(List<String> biddingNoList);

    List<ExportSellNumDataDTO> selectBiddingExportData(ExportSellNumDataDTO exportSellNumDataDTO);

    void updateImportData(@Param("item") Bidding item);

    Integer selectBiddingTimesByMemberCode(String memberCode, List<String> goodsCodes);


    Boolean judgeBool(@Param("memberCode") String memberCode, @Param("status") List<String> status, @Param("participationStatus") String participationStatus);

    int updateStatusByIds(@Param("biddingIds") List<String> biddingIds,@Param("status") String status);

    BiddingInfoDTO queryBiddingInfo(@Param("id") String id);

    @Update("UPDATE bidding SET over_deal_remark =#{remark} where id=#{id} AND del_flg=0")
    int updateOverDealRemark(@Param("remark") String remark, @Param("id") String id);

    @Update("UPDATE bidding SET del_flg=1 where id=#{id}")
    int removeBiddingById(@Param("id") String id);

    List<BiddingReportResultDTO> biddingReport(@Param("param") BiddingReportQueryDTO param);

    List<BiddingAnalysisInfoDTO> biddingAnalysisInfo(@Param("biddingNo") String biddingNo);


    /**
     * 客户活跃度报表基础分页查询
     */
    List<MemberActivityReportResultDTO> memberActivityReport(@Param("param") MemberActivityReportQueryDTO param);
}
