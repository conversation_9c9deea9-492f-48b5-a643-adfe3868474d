package com.cnoocshell.web.buyer.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.api.service.IAccountChangeHistoryService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:角色启用，禁用，授权历史
 */

@Api(tags = {"AccountChangeHistoryController"}, description = "账号变更历史")
@RestController
@RequiredArgsConstructor
@RequestMapping("/accountChangeHistory")
public class AccountChangeHistoryController {

    private final IAccountChangeHistoryService iAccountChangeHistoryService;

    @ApiOperation("根据DTO分页查询员工授权，启用，禁用历史")
    @PostMapping(value = "/pageHistoryInfoList")
    public ItemResult<PageInfo<AccountChangeHistoryDTO>> pageHistoryInfoList(@ApiParam(name = "pageAccountChangeHistoryDTO", value = "员工授权，启用，禁用历史DTO") @RequestBody PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO) throws Exception {
        return new ItemResult<>(iAccountChangeHistoryService.pageHistoryInfoList(pageAccountChangeHistoryDTO));
    }
}
