package com.cnoocshell.order.service;

import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.member.api.dto.account.AccountNameDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.enums.DepositStatusEnum;
import com.cnoocshell.member.api.enums.SubscribeWechatBusinessTypeEnum;
import com.cnoocshell.order.api.dto.AccountSubscribeWechatKeyDTO;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;

import java.util.List;
import java.util.Map;

public interface IBiddingAccountService {

    /**
     *根据memberCode 与goodsCode查询企业下拥有数据权限的产品人员
     * @param memberCodes 企业编码
     * @param goodsCodes 商品编码
     * @param depositStatus 保证金状态 若不为空则会过滤符合状态的企业数据
     */
    Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> queryAccountDataPermission(List<String> memberCodes,List<String> goodsCodes,String... depositStatus);
    Map<String,AccountSimpleDTO> getAccount(List<String> accountIds);
    Map<String,AccountSimpleDTO> getAccount(String... accountId);

    List<DataPermissionDTO> queryDataPermission(String accountId,String memberId,String goodsCode);


    /**
     * @return K:goodsCode
     */
    Map<String,List<AccountNameDTO>> findApprover(String roleCode, List<String> goodsCodes);

    Map<AccountSubscribeWechatKeyDTO,List<AccountSubscribeWechatDTO>> queryAccountSubscribeWechat(List<String> accountIds, List<String> businessNos, SubscribeWechatBusinessTypeEnum type);

    /**
     * 查询企业下拥有对应角色的账号
     *
     * @param memberCodes 企业编码 不能为空
     * @param roleCodes   角色编码
     * @param depositStatus 保证金状态 若为空则不过滤保证金状态
     */
    List<AccountSimpleDTO> queryMemberAccountByRoleCodes(List<String> memberCodes, List<String> roleCodes, DepositStatusEnum depositStatus);

    List<AccountNameDTO> queryAccountByRole(String roleCode);
}
