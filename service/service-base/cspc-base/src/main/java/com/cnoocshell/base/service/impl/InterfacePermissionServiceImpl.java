package com.cnoocshell.base.service.impl;

import com.cnoocshell.base.api.dto.permission.VerifyPermissionDTO;
import com.cnoocshell.base.biz.ISysInterfacePermissionBiz;
import com.cnoocshell.base.service.IInterfacePermissionService;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class InterfacePermissionServiceImpl implements IInterfacePermissionService {
    private final ISysInterfacePermissionBiz iSysInterfacePermissionBiz;

    @Override
    public Boolean verifyInterfacePermission(VerifyPermissionDTO param) {
        try {
            return iSysInterfacePermissionBiz.verifyPermission(param.getRequestUrl(), param.getRoleCodes());
        } catch (Exception e) {
            log.error("接口权限校验异常 error:", e);
            throw new BizException(BasicCode.CUSTOM_ERROR, "权限异常，请联系管理员");
        }
    }
}
