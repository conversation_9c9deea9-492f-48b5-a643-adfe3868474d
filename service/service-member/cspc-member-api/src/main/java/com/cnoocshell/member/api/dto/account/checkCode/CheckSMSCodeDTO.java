package com.cnoocshell.member.api.dto.account.checkCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * @DESCRIPTION:
 */
@Data
public class CheckSMSCodeDTO {

    /**
     * 短信验证码用途
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的用途,见GetSMSCodeDTO")
    private String use;

    /**
     * 手机号码
     */
    @ApiModelProperty("要验证的短信验证码的手机")
    private String mobile;

    /**
     * 会话id
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的前端sessionId")
    private String sessionId="sessionId";

    /**
     * 短信验证码
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码")
    private String smsCode;

    /**
     * 是否清除短信验证码
     */
    @ApiModelProperty("是否需要清除该短信验证码")
    private Boolean deleteSmsCode = Boolean.FALSE;

    /**
     * 校验短信验证码
     */
    public CheckSMSCodeDTO(){}

    /**
     * 校验短信验证码
     */
    public CheckSMSCodeDTO(String use,String mobile,String smsCode,String sessionId){
        this(use,mobile,smsCode,sessionId,false);
    }

    /**
     * 校验短信验证码
     */
    public CheckSMSCodeDTO(String use,String mobile,String smsCode,String sessionId,Boolean deleteSmsCode){
        this.use = use;
        this.mobile = mobile;
        this.sessionId = sessionId;
        this.smsCode = smsCode;
        this.deleteSmsCode = deleteSmsCode != null && deleteSmsCode;
    }

}
