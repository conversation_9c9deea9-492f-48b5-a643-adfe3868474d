package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@Data
public class PhoneCodeLoginDTO implements Serializable {

	private static final long serialVersionUID = -4972311274691611834L;

	/**
	 * 电话号码
	 */
	@ApiModelProperty("电话号码")
	@NotBlank(message = "请输入手机号")
	private String phone;

	/**
	 * 账户code
	 */
	@ApiModelProperty("账户code")
	private String accountCode;

	/**
	 * 验证code
	 */
	@ApiModelProperty("验证code")
	@NotBlank(message = "请输入验证码")
	private String verificationCode ;

	/**
	 * 平台类型
	 */
	@ApiModelProperty("平台类型")
	private String platformType;

	/**
	 * 手机唯一编码
	 */
	@ApiModelProperty("手机唯一编码")
	private String driverToken;

	/**
	 * 客户端ip
	 */
	@ApiModelProperty("客户端ip")
	private String ip;

	/**
	 * 会话id
	 */
	@ApiModelProperty("会话id")
	private String sessionId;

	/**
	 * app名称
	 */
	@ApiModelProperty("app名称")
	private String loginType;

	/**
	 * 客户端网卡物理地址
	 */
	@ApiModelProperty("客户端网卡物理地址")
	private String mac;

	/**
	 * 终端
	 */
	@ApiModelProperty("终端")
	private String terminal;
	/**
	 * 系统
	 */
	@ApiModelProperty("系统")
	private String os;

	/**
	 * 系统版本
	 */
	@ApiModelProperty("系统版本")
	private String osVersion;

	/**
	 * 机器品牌
	 */
	@ApiModelProperty("机器品牌")
	private String deviceBrand;

	/**
	 * 安卓手机唯一识别码
	 */
	@ApiModelProperty("安卓手机唯一识别码")
	private String imei;

	/**
	 * 浏览器信息
	 */
	@ApiModelProperty("浏览器信息")
	private String userAgent;

	/**
	 * 微信code
	 */
	@ApiModelProperty("微信code")
	private String wxCode;

}