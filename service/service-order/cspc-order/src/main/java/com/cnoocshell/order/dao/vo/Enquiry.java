package com.cnoocshell.order.dao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 询价类
 */
@Table(name = "enquiry")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Enquiry {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", nullable = false, length = 32)
    private String id;

    /**
     * 询价场次编号
     */
    @Column(name = "enquiry_no", nullable = false, length = 32)
    private String enquiryNo;

    /**
     * 询价场次名称
     */
    @Column(name = "enquiry_name", length = 32)
    private String enquiryName;

    /**
     * 付款条件
     */
    @Column(name = "pay_condition", length = 32)
    private String payCondition;

    /**
     * 价格贸易条款
     */
    @Column(name = "price_trade_term", length = 32)
    private String priceTradeTerm;

    /**
     * 交易币种
     */
    @Column(name = "trade_currency", length = 32)
    private String tradeCurrency;

    /**
     * 询价开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "enquiry_start_time")
    private Date enquiryStartTime;

    /**
     * 询价结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "enquiry_end_time")
    private Date enquiryEndTime;

    /**
     * 状态：DRAFT草稿、TO_START待开始、ENQUIRING询价中、END已结束、CANCELLED已作废
     */
    @Column(name = "status", nullable = false, length = 32)
    private String status;

    /**
     * 商品编码
     */
    @Column(name = "goods_code", length = 32)
    private String goodsCode;

    /**
     * 商品名称
     */
    @Column(name = "goods_name", length = 32)
    private String goodsName;

    /**
     * 商品分类编码
     */
    @Column(name = "category_code", length = 32)
    private String categoryCode;

    /**
     * 最高报价
     */
    @Column(name = "max_price")
    private BigDecimal maxPrice;

    /**
     * 最低报价
     */
    @Column(name = "min_price")
    private BigDecimal minPrice;

    /**
     * 询价总量
     */
    @Column(name = "total_quantity")
    private BigDecimal totalQuantity;

    /**
     *取消人ID
     */
    @Column(name = "cancel_user")
    private String cancelUser;

    /**
     *取消人姓名
     */
    @Column(name = "cancel_user_name")
    private String cancelUserName;

    /**
     *取消原因
     */
    @Column(name = "cancel_reason")
    private String cancelReason;

    /**
     *取消时间
     */
    @Column(name = "cancel_time")
    private Date cancelTime;

    /**
     *取消竞价通知标记 0:未通知 1：已通知
     */
    @Column(name = "cancel_bidding_notify_flag")
    private Boolean cancelBiddingNotifyFlag;

    /**
     * 删除标志
     */
    @Column(name = "del_flg", columnDefinition = "tinyint(1) default 0")
    private Boolean delFlg;

    /**
     * 创建人ID
     */
    @Column(name = "create_user", length = 32)
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name", length = 32)
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user", length = 32)
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name", length = 32)
    private String updateUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 销售计划编号
     */
    @Column(name = "sales_plan_no", length = 32)
    private String salesPlanNo;

    /**
     * 销售计划名称
     */
    @Column(name = "sales_plan_name", length = 32)
    private String salesPlanName;

    /**
     * 包装方式
     */
    @Column(name = "pack")
    private String pack;
}