package com.cnoocshell.web.emall.controller;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.CategoryTreeDTO;
import com.cnoocshell.goods.api.dto.HomeMenuCategoryTreeDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description Web端控制器，商品分类相关接口
 * @date 2019年1月28日 上午10:29:31
 */
@Slf4j
@RestController
@RequestMapping("/category")
@Api(tags = {"CategoryController"}, description = "商品分类相关接口")
public class CategoryController {

    @Autowired
    private IGoodsCategoryService goodsCategoryService;


    @ApiOperation("首页菜单分类")
    @PostMapping(value = "/anon/homeMenuCategoryTree")
    public ItemResult<HomeMenuCategoryTreeDTO> homeMenuCategoryTree(@ApiParam(name = "code", value = "分类编号") @Valid @RequestParam("code") String code) {
        log.info("/homeMenuCategoryTree,req:{}", code);
        return new ItemResult<>(goodsCategoryService.homeMenuCategoryTree(code));
    }

    @ApiOperation("商品分类")
    @PostMapping(value = "/anon/goodsCategoryTree")
    public ItemResult<List<CategoryTreeDTO>> goodsCategoryTree(@ApiParam(name = "code", value = "分类编号") @Valid @RequestParam("code") String code) {
        log.info("/goodsCategoryTree,req:{}", code);
        return new ItemResult<>(goodsCategoryService.goodsCategoryTree(code));
    }

    @ApiOperation("采购分类")
    @PostMapping(value = "/anon/purchaseCategoryTree")
    public ItemResult<List<CategoryTreeDTO>> purchaseCategoryTree(@ApiParam(name = "code", value = "分类编号") @Valid @RequestParam("code") String code) {
        log.info("/purchaseCategoryTree,req:{}", code);
        return new ItemResult<>(goodsCategoryService.purchaseCategoryTree(code));
    }


    @ApiOperation("商品分类")
    @GetMapping("tree")
    public ItemResult<List<CategoryTreeDTO>> categoryTree() {
        List<CategoryTreeDTO> tree = goodsCategoryService.findCategoryTree(null);
        return new ItemResult<>(tree);
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

}