package com.cnoocshell.base.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Data
@Table(name = "sys_interface_permission")
@EqualsAndHashCode(callSuper = true)
public class SysInterfacePermission extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "interface_url")
    private String interfaceUrl;

    @Column(name = "permission_code")
    private String permissionCode;

    @Column(name = "is_public")
    private Boolean isPublic;
}