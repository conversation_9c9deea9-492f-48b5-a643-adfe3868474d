package com.cnoocshell.web.seller.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.common.constant.HeaderConstant;
import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.excel.ExcelUtils;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BasicRuntimeException;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.order.api.dto.EnquiryDTO;
import com.cnoocshell.order.api.dto.GoodsSimpleDTO;
import com.cnoocshell.order.api.dto.enquiry.CancelEnquiryDTO;
import com.cnoocshell.order.api.dto.enquiry.NotifyBiddingDelayDTO;
import com.cnoocshell.order.api.dto.enquiry.RemoveEnquiryDTO;
import com.cnoocshell.order.api.service.IOrderService;
import com.cnoocshell.web.common.utils.LoginUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/enquiry")
@Slf4j
public class EnquiryController {

    @Autowired
    private IOrderService orderService;

    @PostMapping("/createEnquiry")
    public ItemResult<String> createEnquiry(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody EnquiryDTO enquiryDTO) {
        log.info("EnquiryController createEnquiry enquiryDTO:{}", enquiryDTO);
        checkLogin(loginInfo);
        try {
            enquiryDTO.setOperator(loginInfo.getAccountId());
            return orderService.createEnquiry(enquiryDTO);
        }catch (Exception e) {
            log.error("EnquiryController createEnquiry is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "创建询价失败！");
        }
    }

    @PostMapping("/queryEnquiryList")
    public ItemResult<PageInfo<EnquiryDTO>> queryEnquiryList(@ApiIgnore LoginInfo loginInfo,
                                                             @RequestBody EnquiryDTO enquiryDTO,
                                                             HttpServletRequest request) {
        checkLogin(loginInfo);
        try {
            String type = request.getHeader(HeaderConstant.APPLICATION_TYPE);
            enquiryDTO.setOperator(loginInfo.getAccountId());
            enquiryDTO.setClientType(type);
            ItemResult<PageInfo<EnquiryDTO>> result = orderService.queryEnquiryList(enquiryDTO);
            //仅只有销售人员角色不能看见询价列表申请可售量数据
            if(BooleanUtil.isTrue(isOnlyRole(loginInfo.getRoleList(),BaseRoleTypeEnum.SELLER_SALES_PERSON))){
                if(Objects.nonNull(result) && Objects.nonNull(result.getData()) && CollUtil.isNotEmpty(result.getData().getList())){
                    result.getData().getList().forEach(v->{
                        v.setApplySellableQuantity("");
                    });
                }
            }
            return result;
        }catch (Exception e) {
            log.error("EnquiryController queryEnquiryList is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价失败！");
        }
    }

    @PostMapping("/getEnquiryDetail")
    public ItemResult<EnquiryDTO> getEnquiryDetail(@ApiIgnore LoginInfo loginInfo,
                                                   @RequestBody EnquiryDTO enquiryDTO,
                                                   @ApiIgnore HttpServletRequest request) {
        checkLogin(loginInfo);
        try {
            enquiryDTO.setTerminal(request.getHeader(HeaderConstant.APPLICATION_TYPE));
            enquiryDTO.setOperator(loginInfo.getAccountId());
            return orderService.getEnquiryDetail(enquiryDTO);
        }catch (Exception e) {
            log.error("EnquiryController getEnquiryDetail is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价详情失败！");
        }
    }

    @PostMapping("/updateEnquiry")
    public ItemResult<EnquiryDTO> updateEnquiry(@ApiIgnore LoginInfo loginInfo, @RequestBody EnquiryDTO enquiryDTO) {
        checkLogin(loginInfo);
        try {
            enquiryDTO.setOperator(loginInfo.getAccountId());
            return orderService.updateEnquiry(enquiryDTO);
        }catch (Exception e) {
            log.error("EnquiryController updateEnquiry is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "修改询价失败！");
        }
    }

    @GetMapping("/getEnquiryGoods")
    public ItemResult<List<GoodsSimpleDTO>> getEnquiryGoods(@ApiIgnore LoginInfo loginInfo) {
        checkLogin(loginInfo);
        try {
            return orderService.getEnquiryGoods(loginInfo.getAccountId());
        }catch (Exception e) {
            log.error("EnquiryController getEnquiryGoods is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价商品失败！");
        }
    }

    @ApiOperation(value = "导出询价结果")
    @GetMapping("/exportEnquiryBuyerDetail")
    public void exportEnquiryBuyerDetail(@ApiIgnore LoginInfo loginInfo, @RequestParam("enquiryNo") String enquiryNo, HttpServletResponse response) {
        checkLogin(loginInfo);
        try {
            ItemResult<ExportExcelDTO> exportExcelDTOItemResult = orderService.exportEnquiryBuyerDetail(enquiryNo);
            ExcelUtils.exportXlsx(exportExcelDTOItemResult.getData(), response);
        } catch (Exception e) {
            log.error("导出询价结果错误", e);
            throw new BasicRuntimeException("导出询价结果失败");
        }
    }

    @ApiOperation("取消询价")
    @PostMapping("/cancelEnquiry")
    public ItemResult<Boolean> cancelEnquiry(@ApiIgnore LoginInfo loginInfo, @Validated @RequestBody CancelEnquiryDTO param) {
        LoginUtil.checkLogin(loginInfo);
        param.setOperatorBy(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        param.setOperationTime(new Date());
        return orderService.cancelEnquiry(param);
    }

    @ApiOperation("竞价场次推迟通知")
    @PostMapping("/notifyBiddingDelay")
    public ItemResult<Void> notifyBiddingDelay(@ApiIgnore LoginInfo loginInfo, @Validated @RequestBody NotifyBiddingDelayDTO param) {
        LoginUtil.checkLogin(loginInfo);
        param.setOperatorBy(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        param.setOperationTime(new Date());
        return orderService.notifyBiddingDelay(param);
    }

    @ApiOperation("删除草稿")
    @PostMapping("/removeDraft")
    public ItemResult<Void> removeDraft(@ApiIgnore LoginInfo loginInfo, @Validated @RequestBody RemoveEnquiryDTO param){
        LoginUtil.checkLogin(loginInfo);
        param.setOperatorBy(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        param.setOperationTime(new Date());

        return orderService.removeDraft(param);
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    private static Boolean isOnlyRole(List<String> roleCodes, BaseRoleTypeEnum baseRole){
        if(CollUtil.isEmpty(roleCodes) || Objects.isNull(baseRole))
            return false;

        return roleCodes.stream().allMatch(v-> CharSequenceUtil.equals(baseRole.getRoleCode(), v));
    }

}
