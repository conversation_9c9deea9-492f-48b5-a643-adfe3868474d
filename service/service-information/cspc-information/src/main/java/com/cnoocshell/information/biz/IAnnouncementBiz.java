package com.cnoocshell.information.biz;

import com.cnoocshell.information.api.dto.announcement.*;
import com.cnoocshell.information.dao.vo.Announcement;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IAnnouncementBiz {
        /**
         * 添加一个公告
         * @param announcementCreateDTO
         * @return
         */
        void create(AnnouncementCreateDTO announcementCreateDTO);
        /**
         * 删除一个公告
         * @param id    公共id
         * @return
         */
        void delete(String id,String operator);

        /**
         * 修改一个公告（如果修改审批 通过的公告，审批状态应该改为未审批，等待再次审批）
         * @param announcementUpdateDTO
         * @return
         */
        void update(AnnouncementUpdateDTO announcementUpdateDTO);

        /**
         * 公告审批
         * @param announcementApprovalDTO
         */
        void approval(AnnouncementApprovalDTO announcementApprovalDTO);

        /**
         * 禁止（暂停）公告
         * @param id    公共id
         * @param operator  操作人
         */
        void pause(String id,String operator);
        /**
         * 启用已暂停的公告(如果当前时间已经过期，当前公告也不会显示)
         * @param id    公共id
         * @param operator  操作人
         */
        void goon(String id,String operator);

        /**
         * 查询单条公告详情(不含审批记录)
         * @param id
         * @return
         */
        AnnouncementDTO findById(String id);
        /**
         * 查询单条公告详情(含审批记录)
         * @param id
         * @return
         */
        AnnouncementDetailDTO findDetailById(String id);

        /**
         * 按条件翻页查询公告
         * @param query
         * @return
         */
        PageInfo<Announcement> findAll(AnnouncementQueryDTO query);

        /**
         * 查询主页显示的公告，查询对象可以为空
         * @param query
         * @return
         */
        List<Announcement> findForIndex(AnnouncementIndexQueryDTO query);

        void cleanRedis();

        Announcement getById(String id);
}
