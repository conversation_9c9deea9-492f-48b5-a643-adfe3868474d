package com.cnoocshell.member.aop;

import cn.hutool.core.bean.BeanUtil;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.dao.vo.Member;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 20/12/2018 14:23
 * @DESCRIPTION:
 */
@Component
@Aspect
public class MemberCacheAspect extends BaseCacheAspect {
    public MemberCacheAspect() {
        setCacheName(MemberRedisKeys.CACHE_MEMBER);
    }
    private String memberIdKeyPrefix = "memberId-";
    private String memberCodePrefix = "memberCode-";

    @Value("${member.cache.member.enable:true}")
    private boolean memberCacheEnable=true;
    ////////////////////////////////////////////////////////////////////////////////
    ////切入点
    ////////////////////////////////////////////////////////////////////////////////

    @Pointcut("@annotation(com.cnoocshell.member.annotation.MemberCacheable)")
    public void cacheable() {
        //do nothing
    }

    @Pointcut("@annotation(com.cnoocshell.member.annotation.MemberCacheEvict)")
    public void cacheEvict() {
        //do nothing
    }

    @Pointcut("@annotation(com.cnoocshell.member.annotation.MemberCachePut)")
    public void cachePut() {
        //do nothing
    }

    @Pointcut("@annotation(com.cnoocshell.member.annotation.MemberCacheClear)")
    public void cacheClear() {
        //do nothing
    }

    ////////////////////////////////////////////////////////////////////////////////
    ////增强实现
    ////////////////////////////////////////////////////////////////////////////////

    @AfterReturning(value = "cachePut() ", returning = "member")
    public void cachePutAdvice(Object member) {
        if( memberCacheEnable ) {
            //put cache
            if (member instanceof Member) {
                put((Member) member);
            } else if (member instanceof List) {
                for (Object obj : (List) member) {
                    if (obj instanceof Member) {
                        put(BeanUtil.toBean(member,Member.class));
                    }
                }
            }
        }
    }

    @After(value = "cacheEvict() && args(arg)", argNames = "arg")
    public void cacheEvictAdvice(Object arg) {
        if( memberCacheEnable ) {
            if (arg == null) {
                return;
            }
            if (arg instanceof String) {
                //only evict id
                evictId(String.valueOf(arg));
            }
            if (arg instanceof String[]) {
                for (String id : (String[]) arg) {
                    //only evict id
                    evictId(id);
                }
            }

            if (arg instanceof Member) {
                //evict member
                evict((Member) arg);
            }
        }
    }

    @Around(value = "cacheable()")
    public Object cacheableAdvice(ProceedingJoinPoint pjp) throws Throwable {
        if( !memberCacheEnable ) {
            return pjp.proceed();
        }
        String methodName = pjp.getSignature().getName();
        Object arg = pjp.getArgs().length >= 1 ? pjp.getArgs()[0] : null;

        String key = "";
        boolean isIdKey = false;
        if ("get".equals(methodName) || "findById".equals(methodName)) {
            key = memberIdKey(String.valueOf(arg));
            isIdKey = true;
        } else if ("findByMemberCode".equals(methodName)) {
            key = memberCodeKey((String) arg);
        }

        Member member = null;
        if (isIdKey) {
            member = get(key);
        } else {
            String id = get(key);
            if (id != null) {
                key = memberCodeKey(String.valueOf(id));
                member = get(key);
            }
        }
        //cache hit
        if (member != null) {
            log.debug("cacheName:{}, hit key:{}", cacheName, key);
            return member;
        }
        log.debug("cacheName:{}, miss key:{}", cacheName, key);

        //cache miss
        member = (Member) pjp.proceed();

        //put cache
        put(member);
        return member;
    }

    @AfterReturning(value = "cacheClear()")
    public void cacheClearAdvice() {
        if( memberCacheEnable ) {
            clear();
        }
    }

    private String memberIdKey(String memberId) {
        return MemberRedisKeys.CACHE_MEMBER+memberIdKeyPrefix + memberId;
    }

    private String memberCodeKey(String memberCode) {
        return MemberRedisKeys.CACHE_MEMBER+memberCodePrefix + memberCode;
    }


    ////////////////////////////////////////////////////////////////////////////////
    ////cache 抽象实现
    ////////////////////////////////////////////////////////////////////////////////
    public void put(Member member) {
        if (member == null) {
            return;
        }
        String memberId = member.getMemberId();
        // memberId ---> member
        put(memberIdKey(memberId), member);
        //memberCode ---> memberId
        put(memberCodeKey(member.getMemberCode()), memberId);
    }


    public void evictId(String id) {
        evict(memberIdKey(id));
    }

    public void evict(Member member) {
        if (member == null) {
            return;
        }
        String memberId = member.getMemberId();
        evict(memberCodeKey(member.getMemberCode()));
        evict(memberIdKey(memberId));
    }
}
