package com.cnoocshell.web.base.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.base.api.dto.role.RoleDTO;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.redis.UserInfoKeys;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberSimpleDTO;
import com.cnoocshell.member.api.dto.member.enums.AccountTypeEnum;
import com.cnoocshell.member.api.service.IAccountLoginInfoService;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.base.util.IPUtils;
import com.cnoocshell.web.common.annotation.NoLogin;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Api(tags = {"AccountInfoController"}, description = "登录后，用户获取和维护账户信息，该接口不可匿名访问")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/account")
public class AccountController {
    private final IAccountService accountService;
    private final IAccountLoginInfoService accountLoginInfoService;
    private final BizRedisService bizRedisService;
    private final IMemberService memberService;
    private final IRoleService roleService;

    private static final String PHONE_NUMBER_REG = "^(1[0-9][0-9])\\d{8}$";
    private static final String ATTRIBUTE_NAME_ACCOUNT_NAME = "_accountMobile";

    @ApiOperation("登出(退出登录)")
    @GetMapping("/loginOut")
    public ItemResult<Boolean> loginOut(@ApiIgnore LoginInfo loginInfo, @ApiIgnore HttpServletRequest request) {
        checkLogin(loginInfo);
        String sessionId = request.getSession().getId();
        request.getSession().invalidate();
        accountLoginInfoService.offline(sessionId, "正常退出", loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @ApiOperation("获取登陆的当前用户信息(账号、绑定的账号、会员、组织机构、角色、权限、数据权限)")
    @GetMapping("/getCurrentUserInfo")
    public ItemResult<AccountDTO> getUserInfo(@ApiIgnore LoginInfo loginInfo, @ApiIgnore HttpServletRequest request, HttpServletResponse response) {
        checkLogin(loginInfo);

        AccountDTO accountDTO = bizRedisService.get(UserInfoKeys.USER_INFO_KEY + loginInfo.getAccountId());
        if (accountDTO != null && CollUtil.isNotEmpty(accountDTO.getRoleList())) {
            log.info("redis===>最先获取accountDTO: {}", accountDTO);
            return new ItemResult<>(accountDTO);
        }
        accountDTO = accountService.findById(loginInfo.getAccountId());
        log.info("getUserInfo===>最先获取accountDTO: {}", accountDTO);

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(accountDTO.getMemberId());
        if (memberSimpleDTO == null || memberSimpleDTO.getMemberId() == null) {
            log.error("对应会员信息没有找到，memberId: {}", accountDTO.getMemberId());
        } else {
            //会员信息
            accountDTO.setMemberName(memberSimpleDTO.getMemberName());
            accountDTO.setMemberShortName(memberSimpleDTO.getMemberShortName());
            accountDTO.setMemberSimpleDTO(memberSimpleDTO);
            accountDTO.setHasErp(loginInfo.getHasErp());
        }
        //角色 销售区域 数据权限
        try {
            List<RoleDTO> baseRoleList = roleService.getRoleByAccountId(accountDTO.getAccountId());
            List<com.cnoocshell.member.api.dto.base.RoleDTO> memberRoleList = baseRoleList.stream().map(item -> {
                com.cnoocshell.member.api.dto.base.RoleDTO roleDTO = new com.cnoocshell.member.api.dto.base.RoleDTO();
                BeanUtils.copyProperties(item, roleDTO);
                return roleDTO;
            }).collect(Collectors.toList());
            accountDTO.setRoleList(memberRoleList);
            accountDTO.setRoleTypeList(baseRoleList.stream().map(v->v.getRoleType()).distinct().collect(Collectors.toList()));
            accountDTO.setSaleRegionInfoIdList(loginInfo.getSaleRegionIdList());
            accountDTO.setRegionAdCodeList(loginInfo.getAccountRegionAdCodeList());
            accountDTO.setAccountStoreIdList(loginInfo.getAccountStoreIdList());
        } catch (Exception e) {
            log.error("获取权限、数据权限、销售区域信息出错：", e);
        }
        bizRedisService.setex(UserInfoKeys.USER_INFO_KEY + loginInfo.getAccountId(), UserInfoKeys.EXPIRATION_TIME, accountDTO);
        return new ItemResult<AccountDTO>(accountDTO);
    }

    @ApiOperation("修改自己的手机号key1:mobile,key2:smsCode")
    @PostMapping("/updateMobilePhone")
    public ItemResult<Boolean> updateMobilePhone(@ApiIgnore LoginInfo loginInfo, @RequestBody Map<String, String> map, @ApiIgnore HttpServletRequest request) {
        checkLogin(loginInfo);
        String mobile = map.get("mobile");
        String smsCode = map.get("smsCode");
        checkMobile(mobile);
        accountService.checkSMSCode(new CheckSMSCodeDTO(GetSMSCodeDTO.USE_MODIFY_MOBILE, mobile, smsCode, request.getSession().getId()));
        //如果手机号相同，则不修改
        if (!StringUtils.equals(loginInfo.getMobile(), mobile)) {
            accountService.updateMobilePhone(loginInfo.getAccountId(), mobile, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
            loginInfo.setMobile(mobile);
            updateLoginInfo(loginInfo, request);
        } else {
            return new ItemResult<>(BasicCode.CUSTOM_ERROR.getCode(), "手机号与原手机号一致");
        }
        return new ItemResult<>(true);
    }

    @ApiOperation("修改头像URL{\"headUrl\":\"http://xxxx\"}")
    @PostMapping("/updateHeadUrl")
    public ItemResult<Object> updateHeadUrl(@ApiIgnore LoginInfo loginInfo,
                                            @ApiParam(name = "map", value = "映射") @RequestBody Map<String, String> map) {
        checkLogin(loginInfo);
        String headUrl = map.getOrDefault("headUrl", null);
        isBlank("头像地址", headUrl);

        AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO = new AccountBaseInfoUpdateDTO();
        accountBaseInfoUpdateDTO.setHeadPic(headUrl);
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<>(new Object());
    }

    @ApiOperation("修改自己的账户名")
    @PostMapping("/updateAccountName")
    public ItemResult<Object> updateAccountName(@ApiIgnore LoginInfo loginInfo, String newAccountName, String smsCode, @ApiIgnore HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.updateAccountName(loginInfo.getAccountId(), newAccountName, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
        updateLoginInfo(loginInfo, request);
        return new ItemResult<Object>(new Object());
    }

    @ApiOperation("更新账户基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public ItemResult<Boolean> updateBaseInfo(@ApiIgnore LoginInfo loginInfo, @RequestBody AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO) throws Exception {
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountBaseInfoUpdateDTO.setOperator(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<Boolean>(true);
    }

    @ApiOperation("修改自己的邮箱地址")
    @PostMapping("/updateEmail")
    public ItemResult<Boolean> updateEmail(@ApiIgnore LoginInfo loginInfo, String email, String emailCode, @ApiIgnore HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountAnonV2Controller.checkEmail(email);
        ItemResult<Boolean> checkRs = accountService.checkEmailCode(new CheckEmailCodeDTO(EmailTemplateEnum.MODIFY_EMAIL_TEMPLATE.getCode(), email, emailCode, request.getSession().getId()));
        if (!BooleanUtil.isTrue(checkRs.getData())) {
            throw new BizException(MemberCode.VALIDATION_ERROR, "验证码错误");
        }

        AccountBaseInfoUpdateDTO update = new AccountBaseInfoUpdateDTO();
        update.setAccountId(loginInfo.getAccountId());
        update.setEmail(email);
        accountService.updateBaseInfo(update);
        return new ItemResult<>(true);
    }

    @ApiOperation("修改邮箱-获取邮件验证码")
    @GetMapping("/updateEmail/getEmailCode")
    public ItemResult<Boolean> updateEmailGetEmailCode(@ApiIgnore LoginInfo loginInfo, String email, @ApiIgnore HttpServletRequest request) {
        AccountAnonV2Controller.checkEmail(email);
        if(accountService.existsAccountByEmail(email,loginInfo.getAccountId())){
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"该邮箱地址不正确，请重新输入");
        }
        accountService.getEmailCode(new GetEmailCodeDTO(EmailTemplateEnum.MODIFY_EMAIL_TEMPLATE.getCode(), email, request.getSession().getId(), false, loginInfo.getAccountId()));
        return new ItemResult<>(true);
    }

    @ApiOperation("修改邮箱-校验邮件验证码")
    @PostMapping("/updateEmail/checkEmailCode")
    public ItemResult<Boolean> updateEmailCheckEmailCode(@ApiIgnore LoginInfo loginInfo, String email, String emailCode, @ApiIgnore HttpServletRequest request) {
        log.info("email: {},emailCode: {}", email, emailCode);
        AccountAnonV2Controller.checkEmail(email);
        return accountService.checkEmailCode(new CheckEmailCodeDTO(EmailTemplateEnum.MODIFY_EMAIL_TEMPLATE.getCode(), email, emailCode, request.getSession().getId()));
    }

    @ApiOperation("判断账户名是否存在，账户名唯一")
    @PostMapping("/checkAccountNameExists")
    public ItemResult<Boolean> checkAccountNameExists(@ApiIgnore LoginInfo loginInfo,String newAccountName) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.checkAccountNameExists(newAccountName));
    }

    @ApiOperation("根据角色编码查询用户名称")
    @PostMapping("/findAccountNameByRoleCode")
    @NoLogin
    public ItemResult<List<AccountNameDTO>> findAccountNameByRoleCode(@ApiIgnore LoginInfo loginInfo,@RequestParam("roleCode") String roleCode) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.findAccountNameByRoleCode(roleCode));
    }

    @ApiOperation("根据条件查询员工")
    @PostMapping("/list")
    public ItemResult<PageInfo<AccountDTO>> findAll(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody AccountSearchDTO accountSearchDTO) {
        checkLogin(loginInfo);
        accountSearchDTO.setAccountTypeList(Arrays.asList(AccountTypeEnum.PLATFORM_ACCOUNT.getAccountType()));
        return new ItemResult<PageInfo<AccountDTO>>(accountService.findAll(accountSearchDTO));
    }

    @ApiOperation("6企业主账号创建子账号")
    @PostMapping("/registerSubAccountByMaster")
    public ItemResult<AccountDTO> registerSubAccountByMaster(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody AccountRegisterByMasterDTO accountRegisterByMasterDTO,@ApiIgnore HttpServletRequest request) throws BizException {
        checkLogin(loginInfo);
        accountRegisterByMasterDTO.setOperatorId(loginInfo.getAccountId());
        accountRegisterByMasterDTO.setRegisterIP(IPUtils.getIpAddr(request));
        accountRegisterByMasterDTO.setIsPlatform(Boolean.TRUE);
        if(CharSequenceUtil.isNotBlank(accountRegisterByMasterDTO.getEmail())){
            if(accountService.existsAccountByEmail(accountRegisterByMasterDTO.getEmail(),null)){
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"邮箱已存在");
            }
        }
        return accountService.registerSubAccountByMaster(accountRegisterByMasterDTO);
    }

    @ApiOperation("根据id查询单个账户详细信息)")
    @PostMapping("/findDetailById")
    public ItemResult<AccountDTO> findDetailById(@ApiIgnore LoginInfo loginInfo,@RequestParam("accountId") String accountId) {
        checkLogin(loginInfo);
        return new ItemResult<AccountDTO>(accountService.findDetailById(accountId));
    }

    @ApiOperation("更新子账账户信息")
    @PostMapping(value="/updateSubAccountInfo")
    public ItemResult<Boolean> updateSubAccountInfo(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody SubAccountUpdateDTO subAccountUpdateDTO,@ApiIgnore HttpServletRequest request)throws Exception{
        checkLogin(loginInfo);
        subAccountUpdateDTO.setOperatorIP(IPUtils.getIpAddr(request));

        return accountService.updateSubAccountInfo(subAccountUpdateDTO);
    }

    @ApiOperation("禁用账号")
    @PostMapping(value="/disabled")
    public ItemResult<Boolean> disabled(@ApiIgnore LoginInfo loginInfo,@RequestParam("accountId") String accountId, @RequestParam("reason") String reason,@ApiIgnore HttpServletRequest request)throws Exception{
        checkLogin(loginInfo);
        accountService.disabled(accountId,reason,loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);

    }

    @ApiOperation("启用账号")
    @PostMapping(value="/enabled")
    public ItemResult<Boolean> enabled(@ApiIgnore LoginInfo loginInfo,@RequestParam("accountId") String accountId,@ApiIgnore HttpServletRequest request)throws Exception{
        checkLogin(loginInfo);
        accountService.enabled(accountId,loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);

    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    private void isBlank(String paramName, String paramValue) {
        if (StringUtils.isBlank(paramValue)) {
            throw new BizException(BasicCode.INVALID_PARAM, paramName + "不可为空");
        }
    }

    private void checkMobile(String mobile) {
        if (StringUtils.isBlank(mobile) || !Pattern.matches(PHONE_NUMBER_REG, mobile)) {
            throw new BizException(MemberCode.VALIDATION_ERROR, "手机号不正确");
        }
    }

    private void updateLoginInfo(LoginInfo loginInfo, HttpServletRequest request) {
        //保存session  Spring HttpSession 自动保存session到redis
        request.getSession().setAttribute(LoginInfo.SESSION_NAME, loginInfo);
        request.getSession().setAttribute(ATTRIBUTE_NAME_ACCOUNT_NAME, loginInfo.getMobile());
    }

}
