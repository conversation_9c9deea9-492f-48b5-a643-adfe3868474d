package com.cnoocshell.base.api.dto.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PermissionMenuTreeDTO {

    @ApiModelProperty(name = "权限id")
    private Long permissionId;

    /**
     * 权限CODE
     */
    @ApiModelProperty("权限CODE")
    private String permissionCode;

    /**
     * 权限名称
     */
    @ApiModelProperty("权限名称")
    private String permissionName;

    /**
     * 权限类型：菜单-MENU，元素-ELEMENT
     */
    @ApiModelProperty("权限类型")
    private String permissionType;

    /**
     * 上级功能CODE
     */
    @ApiModelProperty("上级功能CODE")
    private String parentPermissionCode;

    /**
     * 对应角色类型：运营平台-PLATFORM，买家-BUYER，卖家-SELLER
     */
    @ApiModelProperty("对应角色类型：运营平台-PLATFORM，买家-BUYER，卖家-SELLER")
    private String roleType;

    /**
     * 所属应用：电脑端PC，小程序端 MP
     */
    @ApiModelProperty("所属应用：电脑端PC，小程序端 MP")
    private String appType;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer orderNumber;

    /**
     * 权限描述
     */
    @ApiModelProperty("权限描述")
    private String description;

    /**
     * 前端路由
     */
    @ApiModelProperty("前端路由")
    private String path;

    @ApiModelProperty("子节点")
    private List<PermissionMenuTreeDTO> child;
}
