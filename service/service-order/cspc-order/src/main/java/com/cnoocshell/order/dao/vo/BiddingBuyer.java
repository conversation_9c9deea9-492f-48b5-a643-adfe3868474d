package com.cnoocshell.order.dao.vo;


import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "bidding_buyer")
public class BiddingBuyer extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "bidding_no")
    private String biddingNo;

    @Column(name = "member_code")
    private String memberCode;

    @Column(name = "member_name")
    private String memberName;

    @Column(name = "goods_code")
    private String goodsCode;

    @Column(name = "goods_name")
    private String goodsName;

    @Column(name = "participation_status")
    private String participationStatus;

    @Column(name = "buyer_source")
    private String buyerSource;

}