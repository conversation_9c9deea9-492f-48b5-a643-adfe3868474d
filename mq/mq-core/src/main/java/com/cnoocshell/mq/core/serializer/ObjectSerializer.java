

package com.cnoocshell.mq.core.serializer;


/**
 * ObjectSerializer.
 * @Author: <EMAIL>
 */
public interface ObjectSerializer {

    /**
     * 序列化对象.
     *
     * @param obj 需要序更列化的对象
     * @return byte []
     */
    byte[] serialize(Object obj) throws Exception;


    /**
     * 反序列化对象.
     *
     * @param param 需要反序列化的byte []
     * @param clazz java对象
     * @param <T>   泛型支持
     * @return 对象
     */
    <T> T deSerialize(byte[] param, Class<T> clazz) throws Exception;

}
