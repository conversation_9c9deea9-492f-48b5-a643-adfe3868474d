package com.cnoocshell.information.api.dto.announcement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author: <EMAIL>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Data
public class AnnouncementCreateDTO {


    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @NotNull
    @ApiModelProperty("公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType=0;

    /**
     * 标题
     */
    @NotBlank
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("用户名称")
    private String createUserName;

    /**
     * 生效开始时间
     */
    @ApiModelProperty("生效开始时间")
    private Date startTime;

    /**
     * 排序序号
     */
    @ApiModelProperty("排序序号")
    private Integer weight;

    /**
     * 生效结束时间
     */
    @ApiModelProperty("生效结束时间")
    private Date endTime;

    /**
     * 公告内容url
     */
    @ApiModelProperty("公告内容url")
    private String contentUrl;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    private String content;

    /**
     * 操作者
     */
    @ApiModelProperty("操作者")
    private String operator;

    /**
     * 附件id
     */
    @ApiModelProperty("附件id")
    private String attachmentIds;
}
