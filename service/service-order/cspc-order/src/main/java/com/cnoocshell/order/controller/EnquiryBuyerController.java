package com.cnoocshell.order.controller;


import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.EnquiryBuyerDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListQueryDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListViewDTO;
import com.cnoocshell.order.api.dto.EnquiryDTO;
import com.cnoocshell.order.service.EnquiryBuyerService;
import com.cnoocshell.order.service.EnquiryService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 买家询价
 */
@RestController
@RequestMapping("/enquiryBuyer")
@Slf4j
public class EnquiryBuyerController {

    @Autowired
    private EnquiryBuyerService enquiryBuyerService;

    @Autowired
    private EnquiryService enquiryService;


    @PostMapping("/createEnquiryBuyer")
    public ItemResult<String> createEnquiryBuyer(@RequestBody EnquiryBuyerDTO enquiryBuyerDTO) {
        log.info("EnquiryBuyerController createEnquiryBuyer request:{}", enquiryBuyerDTO);
        if (Objects.isNull(enquiryBuyerDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "询价参数为空！");
        }
        try {
            return enquiryBuyerService.createEnquiryBuyer(enquiryBuyerDTO);
        }catch (Exception e) {
            log.error("EnquiryBuyerController createEnquiryBuyer is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "买家询价失败！");
        }
    }

    @PostMapping("/getEnquiryBuyDetail")
    public ItemResult<EnquiryDTO> getEnquiryBuyDetail(@RequestBody EnquiryBuyerDTO enquiryBuyerDTO) {
        log.info("EnquiryBuyerController getEnquiryBuyDetail request:{}", enquiryBuyerDTO);
        if (Objects.isNull(enquiryBuyerDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "询价参数为空！");
        }

        EnquiryDTO enquiryDTO = EnquiryDTO.builder()
                .pageSize(1).pageNum(1)
                .id(enquiryBuyerDTO.getId())
                .clientType(enquiryBuyerDTO.getClientType())
                .operator(enquiryBuyerDTO.getOperator()).build();
        ItemResult<PageInfo<EnquiryDTO>> pageInfoItemResult = enquiryService.queryEnquiryList(enquiryDTO);
        if (Objects.isNull(pageInfoItemResult) || CollectionUtils.isEmpty(pageInfoItemResult.getData().getList())) {
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "无权限查看详情！");
        }

        try {
            return enquiryBuyerService.getEnquiryBuyDetail(enquiryBuyerDTO);
        }catch (Exception e) {
            log.error("EnquiryBuyerController getEnquiryBuyDetail is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价详情失败！");
        }
    }


    /**
     * 买家查询询价列表
     */
    @ApiOperation("查询询价列表")
    @PostMapping("/buyerQueryEnquiryList")
    public ItemResult<PageInfo<EnquiryBuyerListViewDTO>> buyerQueryEnquiryList(@RequestBody EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO) {
        PageInfo<EnquiryBuyerListViewDTO> pageInfo = enquiryBuyerService.buyerQueryEnquiryList(enquiryBuyerListQueryDTO);
        return ItemResult.success(pageInfo);
    }
}
