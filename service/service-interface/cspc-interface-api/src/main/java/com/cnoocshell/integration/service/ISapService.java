package com.cnoocshell.integration.service;

import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name ="cspc-service-interface")
public interface ISapService {
    @ApiOperation("创建SAP合同")
    @PostMapping("/sap/createSapContract")
    BiddingDealContractDTO createSapContract(@RequestBody SapContractDTO param);
}
