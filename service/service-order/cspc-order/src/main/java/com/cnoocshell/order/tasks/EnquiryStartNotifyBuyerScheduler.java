package com.cnoocshell.order.tasks;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.common.dto.MessageDTO;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.member.QueryMemberAccountDTO;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;
import com.cnoocshell.order.api.enums.BiddingMessageTemplateEnum;
import com.cnoocshell.order.biz.impl.EnquiryBiz;
import com.cnoocshell.order.biz.impl.EnquiryBuyerBiz;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;
import com.cnoocshell.order.service.IBiddingAccountService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * 询价开始前通知买家
 */
@Component
@Slf4j
public class EnquiryStartNotifyBuyerScheduler {

    @Autowired
    private EnquiryBiz enquiryBiz;

    @Autowired
    private EnquiryBuyerBiz enquiryBuyerBiz;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private ISmsSendService iSmsSendService;

    @Resource(name = "threadPoolExecutor")
    private Executor executor;

    @Autowired
    private IMessageSendService iMessageSendService;

    @Autowired
    private IXxlJobService iXxlJobService;

    @Autowired
    private IBiddingAccountService iBiddingAccountService;


    @XxlJob("notifyEnquiryStartJob")
    public void doJob() {
        XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler is start");
        Long jobId = XxlJobHelper.getJobId();
        String no = XxlJobHelper.getJobParam();
        try {
            XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler  enquiryNo:{}", no);
            if (StringUtils.isEmpty(no)) {
                return;
            }
            Condition condition1 = enquiryBuyerBiz.newCondition();
            condition1.createCriteria().andEqualTo("enquiryNo", no);
            List<EnquiryBuyer> enquiryBuyerList = enquiryBuyerBiz.findByCondition(condition1);
            if (CollectionUtils.isEmpty(enquiryBuyerList)) {
                XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler enquiryBuyerList is empty:");
                return;
            }

            Condition condition = enquiryBiz.newCondition();
            condition.createCriteria().andEqualTo("enquiryNo", enquiryBuyerList.get(0).getEnquiryNo());
            List<Enquiry> enquiryList = enquiryBiz.findByCondition(condition);
            if (CollectionUtils.isEmpty(enquiryList)) {
                XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler enquiryList is empty:");
                return;
            }

            Enquiry enquiry = enquiryList.get(0);
            String enquiryStartTime = DateUtil.formatDateTime(enquiry.getEnquiryStartTime());
            List<String> memberCodes = enquiryBuyerList.stream().map(EnquiryBuyer::getMemberCode).collect(Collectors.toList());
            QueryMemberAccountDTO query = new QueryMemberAccountDTO(memberCodes, Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()));
            List<AccountSimpleDTO> accountList = memberService.listAccountsByMemberCodes(query);
            List<String> contactPhones = accountList.stream().map(AccountSimpleDTO::getMobile).collect(Collectors.toList());
            XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler memberCodes:{}, contactPhones:{}", memberCodes, contactPhones);
            sendMessage(enquiry, contactPhones);

            //异步发送站内信
            executor.execute(()->{
                for (AccountSimpleDTO v : accountList) {
                    sendZnx(enquiry, v.getAccountId(), v.getMemberName(), enquiryStartTime);
                }
            });

            Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> accountPermission = iBiddingAccountService.queryAccountDataPermission(memberCodes, Arrays.asList(enquiry.getGoodsCode()));
            //通知客户产品相关人员
            List<QueryAccountDataPermissionDTO> queryAccountDataPermissionDTOList = memberCodes.stream()
                    .distinct()
                    .map(v -> new QueryAccountDataPermissionDTO(v, enquiry.getGoodsCode()))
                    .collect(Collectors.toList());

            queryAccountDataPermissionDTOList.forEach(v -> {
                List<AccountSimpleDTO> accounts = CommonUtils.getByKey(accountPermission, v);
                XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler 发送站内信 accounts:{}", accounts);
                if (CollUtil.isNotEmpty(accounts)) {
                    //短信通知
                    List<String> moblies = accounts.stream().map(AccountSimpleDTO::getMobile).collect(Collectors.toList());
                    sendMessage(enquiry, moblies);
                    //站内信通知
                    for (AccountSimpleDTO accountSimpleDTO : accounts) {
                        XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler 发送站内信:{}", accountSimpleDTO);
                        sendZnx(enquiry, accountSimpleDTO.getAccountId(), accountSimpleDTO.getMemberName(), enquiryStartTime);
                    }
                }
            });
        }catch (Exception e) {
            log.error("EnquiryStartNotifyBuyerScheduler no:{} error:",no,e);
            XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler is error:"+CharSequenceUtil.sub(ExceptionUtil.stacktraceToString(e),0,1024));
        }
        XxlJobHelper.log("EnquiryStartNotifyBuyerScheduler is end");
        //屏蔽销毁逻辑
//        destroyJob(jobId, no);
    }

    public void sendMessage(Enquiry enquiry,  List<String> contactPhones) {
        List<String> param = Arrays.asList(enquiry.getEnquiryNo(), DateUtil.formatDateTime(enquiry.getEnquiryStartTime()), enquiry.getEnquiryNo(), enquiry.getEnquiryName());
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.PRICE_INQUIRY_START_CODE.getCode(), contactPhones, param);
        iSmsSendService.sendSms(sms);
    }

    public void sendZnx(Enquiry enquiry, String accountId, String memberName, String enquiryStartTime) {
        MessageDTO message = new MessageDTO();
        message.setTitle(BiddingMessageTemplateEnum.NOTIFY_ENQUIRY_START.getTitle());
        message.setReceiveAccountIds(Arrays.asList(accountId));
        message.setContent(BiddingMessageTemplateEnum.NOTIFY_ENQUIRY_START.getMsg(memberName, enquiry.getEnquiryNo(), enquiryStartTime,enquiry.getGoodsName()));
        iMessageSendService.sendMessage(message);
        XxlJobHelper.log("znx send success");
    }

    private void destroyJob(Long jobId, String no) {
        XxlJobHelper.log(CharSequenceUtil.format("询价自动任务销毁开始 jobId:{} enquiryNo：{}", jobId, no));
        iXxlJobService.destroyXxlJob(jobId);
    }

}
