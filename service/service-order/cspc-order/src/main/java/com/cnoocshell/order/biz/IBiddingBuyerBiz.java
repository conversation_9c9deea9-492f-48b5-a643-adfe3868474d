package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerGoodsDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerListViewDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerQueryDTO;
import com.cnoocshell.order.api.enums.ParticipationStatusEnum;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyer;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IBiddingBuyerBiz extends IBaseBiz<BiddingBuyer> {

    List<BiddingBuyer> listByBiddingBuyer(String biddingNo);

    BiddingBuyer queryBiddingBuyer(String id);

    List<BiddingBuyer> listByNo(String biddingNo);

    PageInfo<BiddingBuyerListViewDTO> buyerBiddingList(BiddingBuyerQueryDTO biddingBuyerListDTO);

    List<String> buyerGoodsBiddingList(BiddingBuyerGoodsDTO dto);

    Integer countBiddingBuyer(String biddingNo);

    List<BiddingBuyer> queryCustomerList(String biddingNo);

    Integer updateStatus(ParticipationStatusEnum status, String memberCode, String biddingNo, String operatorId);

    List<BiddingBuyer> listByBidding(List<Bidding> biddingList);

    List<String> queryMemberByBiddingId(String id);

    BiddingBuyer getBuyer(String biddingNo, String memberCode);

    void resetBuyerStatus(String biddingNo);


    /**
     *获取需要通知竞价将要开始的客户
     */
    List<BiddingBuyer> getNotifyBiddingStartBuyer(List<Bidding> biddingList);
}
