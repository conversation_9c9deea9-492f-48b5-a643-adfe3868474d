package com.cnoocshell.base.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.base.api.common.RedisKey;
import com.cnoocshell.base.api.dto.*;
import com.cnoocshell.base.biz.IValueSetBiz;
import com.cnoocshell.base.cache.ValueSetCache;
import com.cnoocshell.base.cache.ValueSetCacheService;
import com.cnoocshell.base.dao.mapper.ValueSetMapper;
import com.cnoocshell.base.dao.mapper.ValueSetOptionMapper;
import com.cnoocshell.base.dao.vo.ValueSet;
import com.cnoocshell.base.dao.vo.ValueSetOption;
import com.cnoocshell.base.exception.DuplicateString;
import com.cnoocshell.base.exception.ValueCode;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.RedisService;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.CommonUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 值集
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ValueSetBiz extends BaseBiz<ValueSet> implements IValueSetBiz {
    private final UUIDGenerator uuidGenerator;
    private final ValueSetOptionMapper valueSetOptionMapper;
    private final ValueSetMapper valueSetMapper;
    private final ValueSetCacheService valueSetCacheService;
    private final RedisService redisService;


    @Override
    public void createValueSet(ValueSet valueSet, String operator) {
        if (getValueSetByCode(valueSet.getReferenceCode()) != null) {
            throw new BizException(ValueCode.CODE_EXIST, valueSet.getReferenceCode());
        }
        save(valueSet, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createValueSetOption(ValueSetOption valueSetOption, String operator) {
        //查询是否存在相同key
        Condition condition = new Condition(ValueSetOption.class);
        condition.createCriteria().andEqualTo(DuplicateString.DEL_FLG, 0).andEqualTo("optionKey", valueSetOption.getOptionKey());
        List<ValueSetOption> valueSetOptions = valueSetOptionMapper.selectByCondition(condition);
        if (StringUtils.isBlank(valueSetOption.getOptionKey())) {
            return;
        }
        if (valueSetOptions != null && valueSetOptions.size() > 0) {
            throw new BizException(ValueCode.VALUEKEY_EXIST, valueSetOption.getOptionKey());
        }
        if (StringUtils.isBlank(valueSetOption.getId())) {
            //throw new BizException("ddd")
        }
        if (StringUtils.isBlank(valueSetOption.getParentId())) {
            valueSetOption.setParentId(valueSetOption.getId());
        }
        valueSetOption.setOptionId(uuidGenerator.gain());
        setOperInfo(valueSetOption, operator, true);
        log.info("用户：{}添加数据库值集项=================》{}", operator, valueSetOption);
        valueSetOptionMapper.insert(valueSetOption);
        log.info("用户：{}添加缓存值集项=================》{}", operator, valueSetOption);
        valueSetCacheService.saveOrUpdate(valueSetOption);
    }

    @Override
    public ValueSetOption getValueSetOption(String valueSetOptionId) {
        return valueSetOptionMapper.selectByPrimaryKey(valueSetOptionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteValueSetOption(String valueSetOptionId, String operator) {
        ValueSetOption valueSetOption = valueSetOptionMapper.selectByPrimaryKey(valueSetOptionId);
        valueSetOption.setDelFlg(Boolean.TRUE);
        setOperInfo(valueSetOption, operator, false);

        log.info("用户：{}删除数据库值集项=================》id:{}", operator, valueSetOptionId);
        valueSetOptionMapper.updateByPrimaryKeySelective(valueSetOption);
        log.info("用户：{}删除缓存值集项=================》id:{}", operator, valueSetOptionId);
        valueSetCacheService.remove(valueSetOption.getParentId(), valueSetOption.getOptionId(), valueSetOption.getOptionKey());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteValueSet(String valueSetId, String operator) {
        ValueSet valueSet = this.get(valueSetId);
        valueSet.setDelFlg(Boolean.TRUE);
        setOperInfo(valueSet, operator, false);

        log.info("用户：{}删除数据库值集=================》id:{}", operator, valueSetId);
        valueSetMapper.updateByPrimaryKeySelective(valueSet);
        log.info("用户：{}删除缓存值集=================》id:{}", operator, valueSetId);
        valueSetCacheService.removeTree(valueSetId);

        Condition condition = new Condition(ValueSetOption.class);
        condition.createCriteria().andEqualTo(DuplicateString.DEL_FLG, 0)
                .andEqualTo("id", valueSetId);
        List<ValueSetOption> options = valueSetOptionMapper.selectByCondition(condition);
        if (options != null && !options.isEmpty()) {
            for (ValueSetOption option : options) {
                deleteValueSetOption(option.getOptionId(), operator);
            }
        }

    }

    @Override
    public Boolean canValueSetEdit(String valueSetId) {
        ValueSet valueSet = valueSetMapper.selectByPrimaryKey(valueSetId);
        return valueSet.getCanEdit() == 1 ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOption(List<ValueSetOption> valueSetOptions, String operator) {
        for (ValueSetOption valueSetOption : valueSetOptions) {
            setOperInfo(valueSetOption, operator, false);
            valueSetOptionMapper.updateByPrimaryKey(valueSetOption);
            valueSetCacheService.saveOrUpdate(valueSetOption);
        }
    }

    @Override
    public List<ValueSetOption> listValueSetOption(String valueSetId) {
        ValueSetOption valueSetOption = new ValueSetOption();
        valueSetOption.setId(valueSetId);
        valueSetOption.setDelFlg(Boolean.FALSE);
        List<ValueSetCache> caches = valueSetCacheService.getValueSetChilds(valueSetId);
        if (caches != null && caches.size() > 0) {
            return cache2Vo(caches);
        }
        return valueSetOptionMapper.select(valueSetOption);
    }

    @Override
    public ValueSetTreeDTO getChildValueSetOption(String parentOptionId) {
        ValueSetOption root = valueSetOptionMapper.selectByPrimaryKey(parentOptionId);
        ValueSetTreeDTO valueSetTreeDTO = new ValueSetTreeDTO();
        if (root != null) {
            BeanUtils.copyProperties(root, valueSetTreeDTO);
        }
        log.info("<===================从缓存中获取值集===================>");
        ValueSetTreeDTO treeDTO = getChilds(parentOptionId, valueSetTreeDTO, true);
        if (treeDTO.getTreeList() != null && treeDTO.getTreeList().size() > 0) {
            return treeDTO;
        }
        log.info("<===================缓存获取值集失败，从数据库中获取值集===================>");
        return findChilds(parentOptionId, valueSetTreeDTO, true);
    }

    /**
     * 缓存中取值集树
     *
     * @param parentOptionId
     * @param valueSetTreeDTO
     * @return
     */
    private ValueSetTreeDTO getChilds(String parentOptionId, ValueSetTreeDTO valueSetTreeDTO, Boolean flag) {
        List<ValueSetCache> caches = valueSetCacheService.getValueSetChilds(parentOptionId);
        if (caches != null && caches.size() > 0) {
            for (ValueSetCache cache : caches) {
                ValueSetTreeDTO tree = new ValueSetTreeDTO();
                BeanUtils.copyProperties(cache, tree);
                valueSetTreeDTO.getTreeList().add(tree);
                if (flag) {
                    getChilds(cache.getOptionId(), tree, true);
                }
            }
        }
        return valueSetTreeDTO;

    }

    /**
     * 从数据库取值集树
     *
     * @param parentOptionId
     * @param valueSetTreeDTO
     * @param flag
     * @return
     */
    private ValueSetTreeDTO findChilds(String parentOptionId, ValueSetTreeDTO valueSetTreeDTO, Boolean flag) {
        List<ValueSetOption> options = findOptionByParentId(parentOptionId);
        if (options != null && options.size() > 0) {
            for (ValueSetOption option : options) {
                ValueSetTreeDTO tree = new ValueSetTreeDTO();
                BeanUtils.copyProperties(option, tree);
                if (flag) {
                    getChilds(option.getOptionId(), tree, true);
                }
                valueSetTreeDTO.getTreeList().add(tree);
            }
        }
        return valueSetTreeDTO;

    }

    private List<ValueSetOption> findOptionByParentId(String parentOptionId) {
        Condition condition = new Condition(ValueSetOption.class);
        condition.createCriteria().andEqualTo(DuplicateString.DEL_FLG, 0).andEqualTo("parentId", parentOptionId);
        List<ValueSetOption> options = valueSetOptionMapper.selectByCondition(condition);
        return options;
    }

    private ValueSetTreeDTO getChilds(String parentOptionId, Boolean flag) {
        return getChilds(parentOptionId, new ValueSetTreeDTO(), flag);
    }


    @Override
    public ValueSetTreeDTO getByRefrenceCode(String refrenceCode) {
        List<ValueSetDTO> list = this.getValueTreeByCodes(Arrays.asList(refrenceCode));
        if(CollUtil.isEmpty(list))
            return null;
        ValueSetTreeDTO result = new ValueSetTreeDTO();
        result.setReferenceCode(refrenceCode);
        result.setTreeList(CollUtil.getFirst(list).getOptions());
        return result;
    }

    private List<ValueSet> getValueSetByCode(String code) {
        ValueSet valueSet = new ValueSet();
        valueSet.setReferenceCode(code);
        valueSet.setDelFlg(Boolean.FALSE);
        List<ValueSet> valueSets = valueSetMapper.select(valueSet);
        if (valueSets != null && valueSets.size() > 0) {
            return valueSets;
        }
        return null;
    }

    @Override
    public PageInfo<ValueSetDTO> getByPage(ValueQueryDTO valueQueryDTO) {
        Page<ValueSet> page = PageMethod.startPage(valueQueryDTO.getPageNum(), valueQueryDTO.getPageSize());
        List<ValueSetDTO> list = valueSetMapper.findPage(valueQueryDTO);
        return new PageInfo<ValueSetDTO>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createValueAndOption(ValueAndOptionDTO valueAndOptionDTO, String operator) {
        List<ValueSetOptionDTO> optionDTOS = valueAndOptionDTO.getList();
        if (optionDTOS != null && optionDTOS.size() > 1) {
            for (ValueSetOptionDTO option : optionDTOS) {
                if (StringUtils.isBlank(option.getOptionKey()) || StringUtils.isBlank(option.getOptionValue())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "key或value不能为空格");
                }
            }
        } else if (optionDTOS == null) {
            valueAndOptionDTO.setList(Lists.newArrayList());
        } else if (optionDTOS.size() == 1 && (StringUtils.isBlank(optionDTOS.get(0).getOptionKey()) || StringUtils.isBlank(optionDTOS.get(0).getOptionValue()))) {
            valueAndOptionDTO.setList(Lists.newArrayList());
        }
        log.info("用户：{}添加值集=================》{}", operator, valueAndOptionDTO.toString());
        if (getValueSetByCode(valueAndOptionDTO.getReferenceCode()) != null) {
            throw new BizException(ValueCode.CODE_EXIST, valueAndOptionDTO.getReferenceCode());
        }
        ValueSet valueSet = new ValueSet();
        String id = uuidGenerator.gain();
        BeanUtils.copyProperties(valueAndOptionDTO, valueSet);
        valueSet.setDelFlg(Boolean.FALSE);
        valueSet.setId(id);
        setOperInfo(valueSet, operator, true);
        valueSetMapper.insert(valueSet);
        for (ValueSetOptionDTO valueSetOptionDTO : valueAndOptionDTO.getList()) {
            ValueSetOption valueSetOption = new ValueSetOption();
            BeanUtils.copyProperties(valueSetOptionDTO, valueSetOption);
//            valueSetOption.setDelFlg(0);
//            valueSetOption.setOptionId(uuidGenerator.gain());
//            setOperInfo(valueSetOption,operator,true);
            valueSetOption.setId(id);
            valueSetOption.setParentId(id);
            createValueSetOption(valueSetOption, operator);
//            log.info("用户：{}删除缓存值集=================》id:{}",operator,valueSetId);
//            valueSetOptionMapper.insert(valueSetOption);
//            log.info("用户：{}删除缓存值集=================》id:{}",operator,valueSetId);
//            valueSetCacheService.saveOrUpdate(valueSetOption);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateValueAndOption(ValueAndOptionDTO valueAndOptionDTO, String operator) {
        log.info("用户：{}修改值集=================》{}", operator, valueAndOptionDTO.toString());
        List<ValueSet> valueSets = getValueSetByCode(valueAndOptionDTO.getReferenceCode());
        if (valueSets != null && valueSets.size() > 1) {
            throw new BizException(ValueCode.CODE_EXIST, valueAndOptionDTO.getReferenceCode());
        }
        ValueSet valueSet = new ValueSet();
        BeanUtils.copyProperties(valueAndOptionDTO, valueSet);
        setOperInfo(valueSet, operator, false);
        valueSetMapper.updateByPrimaryKeySelective(valueSet);
        for (ValueSetOptionDTO valueSetOptionDTO : valueAndOptionDTO.getList()) {
            ValueSetOption valueSetOption = new ValueSetOption();
            BeanUtils.copyProperties(valueSetOptionDTO, valueSetOption);

            //新增key-value
            if (StringUtils.isBlank(valueSetOptionDTO.getOptionId())) {
                valueSetOption.setId(valueAndOptionDTO.getId());
                createValueSetOption(valueSetOption, operator);
            }
            //更新key-value
            else {
                setOperInfo(valueSetOption, operator, false);
                log.info("用户：{}修改值集项=================》{}", operator, valueSetOption.toString());
                valueSetOptionMapper.updateByPrimaryKeySelective(valueSetOption);
                valueSetCacheService.saveOrUpdate(valueSetOption);
            }
        }
        if (valueAndOptionDTO.getDeleteIds() != null && valueAndOptionDTO.getDeleteIds().size() > 0) {
            for (String optionId : valueAndOptionDTO.getDeleteIds()) {
                deleteValueSetOption(optionId, operator);
            }
        }

    }

    @Override
    public List<ValueSetOptionDTO> findOptionByKey(String key) {
        List<ValueSetOptionDTO> optionDTOS = new LinkedList<>();
        return findChildsByKey(key, optionDTOS);
    }

    @Override
    public void reloadRedisCache() {
        try {
            valueSetCacheService.run(null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public List<ValueSetDTO> getValueTreeByCodes(List<String> referenceCodes) {
        if (CollUtil.isEmpty(referenceCodes))
            return Collections.emptyList();
        referenceCodes = referenceCodes.stream().filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<ValueSetDTO> result = new ArrayList<>();
        List<String> needQuery = new ArrayList<>();
        for (String referenceCode : referenceCodes) {
            String key = RedisKey.VALUE_TREE_CACHE + referenceCode;
            if (redisService.hasKey(key)) {
                ValueSetDTO value = redisService.get(key, ValueSetDTO.class);
                if (Objects.nonNull(value)) {
                    result.add(value);
                    continue;
                }
            }
            needQuery.add(referenceCode);
        }
        if (CollUtil.isEmpty(needQuery))
            return result;
        List<ValueSetDTO> otherList = this.getValueSet(needQuery);
        if (CollUtil.isEmpty(otherList))
            return result;
        //K：id
        Map<String, List<ValueSetTreeDTO>> otherOptions = CommonUtils.group(this.getValueOptions(otherList.stream().map(ValueSetDTO::getId).collect(Collectors.toList())), ValueSetTreeDTO::getId);
        for (ValueSetDTO valueSetDTO : otherList) {
            String key = RedisKey.VALUE_TREE_CACHE + valueSetDTO.getReferenceCode();
            valueSetDTO.setOptions(handChildren(CommonUtils.getByKey(otherOptions, valueSetDTO.getId()), valueSetDTO.getReferenceCode(), null, true));
            redisService.set(key,valueSetDTO);
        }
        result.addAll(otherList);
        return result;
    }

    private static List<ValueSetTreeDTO> handChildren(List<ValueSetTreeDTO> nodes, String code, String parentId, boolean isFirst) {
        if (CollUtil.isEmpty(nodes))
            return Collections.emptyList();
        List<ValueSetTreeDTO> child = nodes.stream()
                .map(v -> {
                    v.setReferenceCode(code);
                    return v;
                }).filter(v -> {
                    if (isFirst)
                        return CharSequenceUtil.isBlank(v.getParentId());
                    return CharSequenceUtil.equals(parentId, v.getParentId());
                }).collect(Collectors.toList());
        if (isFirst && CollUtil.isEmpty(child))
            return nodes;
        for (ValueSetTreeDTO node : child) {
            node.setTreeList(handChildren(nodes, code, node.getOptionId(), false));
        }
        return child;
    }

    private List<ValueSetDTO> getValueSet(List<String> codes) {
        if (CollUtil.isEmpty(codes))
            return Collections.emptyList();
        Condition condition1 = new Condition(ValueSet.class);
        condition1.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.REFERENCE_CODE, codes);
        return BeanUtil.copyToList(this.findByCondition(condition1), ValueSetDTO.class);
    }

    private List<ValueSetTreeDTO> getValueOptions(List<String> valueSetIds) {
        if (CollUtil.isEmpty(valueSetIds))
            return Collections.emptyList();
        Condition condition1 = new Condition(ValueSetOption.class);
        condition1.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.ID, valueSetIds);
        condition1.orderBy(DuplicateString.OPTION_ORDER).asc();

        return BeanUtil.copyToList(valueSetOptionMapper.selectByCondition(condition1), ValueSetTreeDTO.class);
    }

    private List<ValueSetOptionDTO> findChildsByKey(String key, List<ValueSetOptionDTO> valueSetOptionDTOS) {
        ValueSetCache cache = valueSetCacheService.findOptionByKey(key);
        if (cache == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不存在key: " + key);
        }

        valueSetOptionDTOS.add(cache2Dto(cache));
        ValueSetTreeDTO valueSetTreeDTO = getChilds(cache.getOptionId(), false);
        if (valueSetTreeDTO.getTreeList() != null && valueSetTreeDTO.getTreeList().size() > 0) {
            for (ValueSetTreeDTO valueSetTreeDTO1 : valueSetTreeDTO.getTreeList()) {
                findChildsByKey(valueSetTreeDTO1.getOptionKey(), valueSetOptionDTOS);
            }
        }
        return valueSetOptionDTOS;
    }

    private List<ValueSetOption> cache2Vo(List<ValueSetCache> caches) {
        if (caches == null || caches.size() == 0) {
            return null;
        }
        List<ValueSetOption> options = new LinkedList<ValueSetOption>();
        for (ValueSetCache cache : caches) {
            ValueSetOption option = new ValueSetOption();
            BeanUtils.copyProperties(cache, option);
            options.add(option);
        }
        return options;
    }

    private ValueSetOptionDTO cache2Dto(ValueSetCache valueSetCache) {
        ValueSetOptionDTO valueSetOptionDTO = new ValueSetOptionDTO();
        BeanUtils.copyProperties(valueSetCache, valueSetOptionDTO);
        return valueSetOptionDTO;
    }
}

