package com.cnoocshell.common.enums;

import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 20:09 27/08/2018
 */
@Getter
public enum BusinessCode {
    PRICE("price","10"),
    ORDER("order","D"),//订单 11
    GOODS("goods","12"),
    RESOURCE("resource","13"),
    TAKE("take","F"),//发货单
    PICK("pick","T"),//提货单
    DISPATCHER("dispatcher","DD"),//调度单
    TRANSPORT("transport","Y"),//运单
    REPERTORY("repertory","18"),//仓库
    UNLOADING("unloading","19"),//卸货点
    PAY("pay","20"),//支付
    CHANNEL("channel","21"),//渠道

    REFUND("refund","22"),//退款
    WITHDRAWAL("withdrawal","23"),//提现
    RECHARGE("recharge","24"),//充值
    PAY_SPLIT("pay_split","25");//分账

    private String appName;
    private String code;
    BusinessCode(String appName,String code){
        this.appName=appName;
        this.code=code;
    }
}
