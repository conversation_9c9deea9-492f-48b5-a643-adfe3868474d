
package com.cnoocshell.web.common.interceptor;

import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.base.api.dto.permission.VerifyPermissionDTO;
import com.cnoocshell.base.api.service.IPermissionService;
import com.cnoocshell.common.exception.ForcedLogoutException;
import com.cnoocshell.common.exception.NoLoginException;
import com.cnoocshell.common.exception.NoPermissionException;
import com.cnoocshell.common.filter.LogParameterFilter;
import com.cnoocshell.common.service.IAuthorizeService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.common.annotation.NoLogin;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @Description 拦截url并鉴权
 * <AUTHOR>
 * @Date Create in 10:24 06/07/2018
 **/
@Slf4j
@RefreshScope
@Component
@RequiredArgsConstructor
public class AuthInterceptor extends HandlerInterceptorAdapter {
    private final IPermissionService permissionService;
    private final IAuthorizeService iAuthorizeService;
    @Value("${web.auth.switch:true}")
    private Boolean authSwitch;
    @Value("${spring.profiles.active}")
    private String profile;

    private static final Set<String> HEALH_SET = new HashSet<>();
    private static final Set<String> SOLT_SET = new HashSet<>();

    static {
        HEALH_SET.add("anon");
        HEALH_SET.add("refresh");
        SOLT_SET.add("swagger");
        SOLT_SET.add("api-docs");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            String url = request.getRequestURI();
            boolean refresh = HEALH_SET.stream().anyMatch(url::contains);
            if (refresh) {
                return Boolean.TRUE;
            }
            boolean swagger = SOLT_SET.stream().anyMatch(url::contains);
            if (!"prod".equals(profile) && (swagger)) {
                return Boolean.TRUE;
            }

            /// 不需要登录
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Object object = handlerMethod.getBean();
            Method method = handlerMethod.getMethod();
            // 验证该方法上是否有不需要登录的注解
            NoLogin cnoLogin = method.getAnnotation(NoLogin.class);
            // 验证该类上是否有不需要登录的注解
            NoLogin fnoLogin = object.getClass().getAnnotation(NoLogin.class);
            if (cnoLogin != null || fnoLogin != null) {
                return Boolean.TRUE;
            }

            HttpSession session = request.getSession();
            LoginInfo loginInfo = (LoginInfo) session.getAttribute(LoginInfo.SESSION_NAME);
            //登录验证
            checkLogin(loginInfo, session);

            registerUserInLog(request);

            Boolean pass = BooleanUtil.isTrue(authSwitch) ? this.authenticate(url, loginInfo.getRoleList()) : true;
            log.info("AuthInterceptor接口权限校验 authorize {},authSwitch:{},url:{},roleIds:{},accountId:{} sessionId:{}",
                    pass,authSwitch, url, loginInfo.getRoleList(), loginInfo.getAccountId(),request.getSession().getId());
            if (pass) {
                return Boolean.TRUE;
            } else {
                throw new NoPermissionException("");
            }
        }
        return super.preHandle(request, response, handler);
    }

    private static void checkLogin(LoginInfo loginInfo, HttpSession session) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getMobile())) {
            log.info("NoLoginException session {} is timeout", session.getId());
            throw new NoLoginException("未登陆，请重新登陆");
        }
        //是否已经被强制登出
        String msg = (String) session.getAttribute("forceOffLine");
        if (StringUtils.isNotBlank(msg)) {
            session.invalidate();
            log.info("进入 ForcedLogoutException============>{}", msg);
            throw new ForcedLogoutException(msg);
        }
    }

    /**
     * 从LoginInfo中获取用户ID和用户名，存入MDC（填充到日志中）及RequestContextHolder（通过Feign调用API时从中取出放入到request）中
     *
     * @param request
     */
    private void registerUserInLog(HttpServletRequest request) {
        HttpSession session = request.getSession();
        LoginInfo loginInfo = (LoginInfo) session.getAttribute(LoginInfo.SESSION_NAME);
        if (loginInfo != null) {
            String userId = loginInfo.getAccountId();
            String username = loginInfo.getAccountName();

            MDC.put(LogParameterFilter.USER_ID, userId);
            MDC.put(LogParameterFilter.USER_NAME, username);

            ServletRequestAttributes requestAttributes = new ServletRequestAttributes(request);
            requestAttributes.setAttribute(LogParameterFilter.USER_ID, userId, 1);
            requestAttributes.setAttribute(LogParameterFilter.USER_NAME, username, 1);
            RequestContextHolder.setRequestAttributes(requestAttributes);
        }
    }


    /**
     * 校验接口权限
     */
    private Boolean authenticate(String requestUrl, List<String> roleCodes) {
        //先校验公共权限 若为空则需校验受控制权限
        Boolean rs = iAuthorizeService.authorize(requestUrl, roleCodes);
        if (Objects.nonNull(rs))
            return rs;

        return permissionService.verifyInterfacePermission(new VerifyPermissionDTO(requestUrl, roleCodes));
    }
}
