package com.cnoocshell.member.api.dto.account.checkCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @DESCRIPTION:
 */
@Data
public class CheckCaptchaDTO {
    /**
     * 用途
     */
    @ApiModelProperty("用途")
    private String use;

    /**
     * 要验证的图形证码的前端sessionId
     */
    @ApiModelProperty("要验证的图形证码的前端sessionId")
    private String sessionId="sessionId";

    /**
     * 要验证的图形验证码
     */
    @ApiModelProperty("要验证的图形验证码")
    private String captcha;

    /**
     * 是否需要清除该图形验证码
     */
    @ApiModelProperty("是否需要清除该图形验证码")
    private Boolean deleteCaptcha;
}
