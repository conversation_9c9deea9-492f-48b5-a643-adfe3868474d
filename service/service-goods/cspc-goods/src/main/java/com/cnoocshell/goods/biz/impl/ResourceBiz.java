package com.cnoocshell.goods.biz.impl;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.annotation.PrintArgs;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.CsStringUtils;
import com.cnoocshell.common.utils.DateUtil;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.enums.*;
import com.cnoocshell.goods.biz.IResourceBiz;
import com.cnoocshell.goods.dao.mapper.*;
import com.cnoocshell.goods.dao.vo.Resource;
import com.cnoocshell.goods.dao.vo.ResourceHistory;
import com.cnoocshell.goods.dao.vo.ResourceRegion;
import com.cnoocshell.goods.dao.vo.ResourceUneffective;
import com.cnoocshell.goods.exception.ResourceCode;
import com.cnoocshell.goods.service.IGoodsService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@EnableAsync
public class ResourceBiz extends BaseBiz<Resource> implements IResourceBiz {

    private static final Double HOUR = 3600d;
    private static final Double DAY = 86400d;
    private static final String CENTER_STORE_ID = "1234567890";
    private static final String NO_BRAND = "无";

    private static final String GOODS_TYPE = "goodsType";
    private static final String SALE_AREA = "saleArea";
    private static final String SALE_AREA_CODE = "saleAreaCode";
    private static final String SALE_AREA_CODE2 = "saleAreaCode2";
    private static final String SALE_AREA_CODE3 = "saleAreaCode3";
    private static final String SALE_AREA_CODE4 = "saleAreaCode4";
    private static final String SALE_AREA_CODE5 = "saleAreaCode5";
    private static final String RESOURCE_CODE = "resourceCode";
    private static final String IF_PROTOCOL_PRICE = "ifProtocolPrice";
    private static final String UP_TIME = "upTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String GOODS_ID = "goodsId";
    private static final String STORE_ID = "storeId";
    private static final String SALE_AREA_REAL_CODE = "saleAreaRealCode";
    private static final String ADMIN = "admin";
    private static final String GOODS_RESOURCE_ID = "goodsResourceId";
    private static final String RESOURCE_ATTRIBUTES = "resourceAttributes";
    private static final String CATEGORY_CODE = "categoryCode";
    private static final String SELLER_ID = "sellerId";
    private static final String DEL_FLG = "delFlg";
    private static final String RESOURCE_ID = "resourceId";
    private static final String STATUS = "status";
    private static final String RESOURCE_REGIONS = "resourceRegions";
    private static final String COUNTRY_NAME = "countryName";
    private static final String CITY_NAME = "cityName";
    private static final String PROVINCE_NAME = "provinceName";
    private static final String AREA_NAME = "areaName";
    private static final String STREET_NAME = "streetName";
    private static final String COUNTRY_CODE = "countryCode";
    private static final String PROVINCE_CODE = "provinceCode";
    private static final String CITY_CODE = "cityCode";
    private static final String AREA_CODE = "areaCode";
    private static final String STREET_CODE = "streetCode";
    private static final String GOODS_DESCRIBE = "goodsDescribe";
    private static final String SPU_ID = "spuId";
    private static final String PAGE_NUM_BEGIN_FROM_1 = "页码从1开始";
    private static final String PAGE_SIZE_MUST_MORE_THAN_0 = "页面大小必须大于0";
    private static final String RESOURCE_CODE_LIST = "资源编号集合";
    private static final String CAN_NOT_CHANGE_THIS_STATUS_RESOURCE = "不能修改该状态的资源";

    @javax.annotation.Resource
    private ResourceMapper resourceMapper;
    @javax.annotation.Resource
    private ResourceHistoryMapper resourceHistoryMapper;
    @javax.annotation.Resource
    private ResourceUneffectiveMapper resourceUneffectiveMapper;
    @javax.annotation.Resource
    private ResourceRegionMapper resourceRegionMapper;
    @javax.annotation.Resource
    private GoodsMapper goodsMapper;
    @javax.annotation.Resource
    private GoodsAttributeValueMapper goodsAttributeValueMapper;

    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private UUIDGenerator uuidGenerator;
//    @Autowired
//    private ThreadPoolExecutor threadPoolExecutor;

    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqDto) {
        // 参数检测
        checkParams(reqDto);
        // 初始化结果
        PageInfo<ResourceSellerDTO> pageData = new PageInfo<>();
        try {
            // 入参封装
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(SELLER_ID, reqDto.getSellerId());
            criteriaAddParams(reqDto, criteria);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            example.orderBy(UPDATE_TIME).desc();
            // 分页查询数据库
            Page<Resource> page = PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true)
                    .doSelectPage(() -> resourceMapper.selectByExample(example));
            // 结果封装
            if (page.size() < 1) {
                pageData.setPages(0);
                pageData.setTotal(0);
                return pageData;
            }
            pageData.setPages(page.getPages());
            pageData.setTotal(page.getTotal());
            List<ResourceSellerDTO> list = new ArrayList<>();
            List<Resource> resources = page.getResult();
            listAddValues(resources, list);
            pageData.setList(list);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("pageResourceSeller Exception Message:", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    private void listAddValues(List<Resource> resources, List<ResourceSellerDTO> list) {
        if (resources != null && resources.size() > 0) {
            for (Resource resource : resources) {
                ResourceSellerDTO dto = new ResourceSellerDTO();
                BeanUtils.copyProperties(resource, dto);
                GoodsDTO goodsInfo = getGoodsInfo(resource.getGoodsId());
                if (goodsInfo != null) {
                    dto.setImgs(goodsInfo.getImgs());
                }
                // 设置特殊值
                // 交易状态
                boolean tradeStatus = checkResourceTradeStatus(resource);
                // 查询是否有资源历史记录
                list.add(dto);
            }
        }
    }

    @NotNull
    private List<String> getDeliveryWays(Resource resource, ResourceSellerDTO dto) {
        if (resource.getUpdateTime() == null) {
            dto.setUpdateTime(resource.getCreateTime());
        }
        List<String> deliveryWays = Lists.newArrayList();
        return deliveryWays;
    }

    private static void criteriaAddParams(ReqResourceSellerDTO reqDto, Example.Criteria criteria) {
        if (!StringUtils.isBlank(reqDto.getGoodsType())) {
            criteria.andEqualTo(GOODS_TYPE, reqDto.getGoodsType());
        }
        if (!StringUtils.isBlank(reqDto.getGoodsDescribe())) {
            criteria.andLike(GOODS_DESCRIBE, "%" + reqDto.getGoodsDescribe() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getCategoryCode())) {
            criteria.andLike(CATEGORY_CODE, reqDto.getCategoryCode() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getSaleArea())) {
            criteria.andLike(SALE_AREA, "%" + reqDto.getSaleArea() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode())) {
            criteria.andEqualTo(SALE_AREA_CODE, reqDto.getSaleAreaCode());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode2())) {
            criteria.andEqualTo(SALE_AREA_CODE2, reqDto.getSaleAreaCode2());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode3())) {
            criteria.andEqualTo(SALE_AREA_CODE3, reqDto.getSaleAreaCode3());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode4())) {
            criteria.andEqualTo(SALE_AREA_CODE4, reqDto.getSaleAreaCode4());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode5())) {
            criteria.andEqualTo(SALE_AREA_CODE5, reqDto.getSaleAreaCode5());
        }
        if (!StringUtils.isBlank(reqDto.getResourceCode())) {
            criteria.andLike(RESOURCE_CODE, "%" + reqDto.getResourceCode() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getStoreName())) {
            criteria.andLike("storeName", "%" + reqDto.getStoreName() + "%");
        }
        if (reqDto.getIfProtocolPrice() != null) {
            criteria.andEqualTo(IF_PROTOCOL_PRICE, reqDto.getIfProtocolPrice());
        }
        if (reqDto.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(UP_TIME, reqDto.getStartTime());
        }
        if (reqDto.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(UP_TIME, reqDto.getEndTime());
        }
        if (!StringUtils.isBlank(reqDto.getStatus())) {
            criteria.andEqualTo(STATUS, reqDto.getStatus());
        }
    }

    private static void checkParams(ReqResourceSellerDTO reqDto) {
        if (reqDto == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (StringUtils.isBlank(reqDto.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (reqDto.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (reqDto.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
    }

    /**
     * @param reqDto
     * @return
     */
    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSellerWithDataPerm(ReqResourceSellerDTO reqDto) {
        log.info("访问数据权限查挂牌列表");
        return PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize())
                .doSelectPageInfo(() -> resourceMapper.pageQueryWithDataPerm(reqDto).stream()
                        .collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public void createResource(ReqCreateResourceDTO reqCreateResourceDTO) {
        // 参数检查
        createResourceCheck(reqCreateResourceDTO);
        // 入参
        String sellerId = reqCreateResourceDTO.getSellerId();
        String sellerName = reqCreateResourceDTO.getSellerName();
        String operator = reqCreateResourceDTO.getOperator();
        String unit = reqCreateResourceDTO.getUnit();
        String spuId = reqCreateResourceDTO.getSpuId();
        List<GoodsBaseDTO> goodsBaseDTOs = reqCreateResourceDTO.getGoodsBaseDTOs();
        GoodsPriceDTO goodsPriceDTO = reqCreateResourceDTO.getGoodsPriceDTO();
        GoodsQuantityDTO goodsQuantityDTO = reqCreateResourceDTO.getGoodsQuantityDTO();
        GoodsConsignDTO goodsConsignDTO = reqCreateResourceDTO.getGoodsConsignDTO();
        GoodsOtherDTO goodsOtherDTO = reqCreateResourceDTO.getGoodsOtherDTO();
        log.info("===============>reqCreateResourceDTOgetGoodsConsignDTO{}", reqCreateResourceDTO.getGoodsConsignDTO());
        String machineRuleId = "";
        String emptyLoadRuleId = "";
        if (goodsConsignDTO != null) {
            machineRuleId = goodsConsignDTO.getMachineRuleId() == null ? "" : goodsConsignDTO.getMachineRuleId();
            emptyLoadRuleId = goodsConsignDTO.getEmptyLoadRuleId() == null ? "" : goodsConsignDTO.getEmptyLoadRuleId();
        }
        try {
            for (GoodsBaseDTO goodsBaseDTO : goodsBaseDTOs) {
                //检查商品基本信息
                checkGoodsBaseDTO(goodsBaseDTO);
                // 查询商品信息
                String goodsId = goodsBaseDTO.getGoodsId();
                GoodsDTO goodsDTO = getGoodsInfo(goodsId);
                Integer goodsStatus = goodsDTO.getGoodsStatus();
                if (GoodsStatusEnum.ENABLE.getCode() != goodsStatus) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, goodsDTO.getGoodsName() + "的状态异常,不能挂牌");
                }
                // 新增资源
                List<GoodsResourceStoreDTO> goodsResourceStoreDTOS = goodsPriceDTO.getGoodsResourceStoreDTOS();
                resourceAddParams(reqCreateResourceDTO, goodsBaseDTO, goodsResourceStoreDTOS, spuId, goodsId, goodsPriceDTO, goodsQuantityDTO, goodsConsignDTO, goodsOtherDTO, machineRuleId, emptyLoadRuleId, goodsDTO, sellerId, sellerName, operator);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("createResource Exception Message:", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void resourceAddParams(ReqCreateResourceDTO reqCreateResourceDTO, GoodsBaseDTO goodsBaseDTO, List<GoodsResourceStoreDTO> goodsResourceStoreDTOS, String spuId, String goodsId, GoodsPriceDTO goodsPriceDTO, GoodsQuantityDTO goodsQuantityDTO, GoodsConsignDTO goodsConsignDTO, GoodsOtherDTO goodsOtherDTO, String machineRuleId, String emptyLoadRuleId, GoodsDTO goodsDTO, String sellerId, String sellerName, String operator) {
        if (goodsResourceStoreDTOS == null || goodsResourceStoreDTOS.size() == 0) {
            return;
        }
        for (GoodsResourceStoreDTO dto : goodsResourceStoreDTOS) {
            log.info(" >>> priceWay: {}", dto.getPriceWay());
            // 查询是否挂牌了该资源
            String saleAreaRealCode = "";
            saleAreaRealCode = getSaleAreaRealCode(dto);
            boolean isCanCreateResource = checkIsCanCreateResource(spuId, goodsId, dto.getStoreId(), saleAreaRealCode);
            // 只有启用的资源才存库
            if (EnableStatusEnum.ENABLE_STATUS100.code().equals(dto.getOperateStatus()) && isCanCreateResource) {
                //检查商品其他信息
                checkGoodsOtherInfo(goodsPriceDTO, goodsQuantityDTO, goodsConsignDTO, goodsOtherDTO, dto);
                //初始化
                Resource resource = new Resource();
                // 商品基本信息
                resource.setResourceId(uuidGenerator.gain());
                //设置空载费和台班费规则
                resource.setSpuId(spuId);
                resource.setGoodsId(goodsId);
                resource.setGoodsName(goodsBaseDTO.getGoodsName());
                resource.setGoodsDescribe(goodsDTO.getSearchKeywords());
                resource.setResourceName(goodsDTO.getGoodsName());
                resource.setResourceCode(generatorResourceCode()); //生成资源编号

                // 商品定价信息
                resource.setPriceUnit(goodsDTO.getMeasureUnit());
                resource.setSaleUnit(goodsDTO.getUnit());
                //设置是否可议价

                // 商品数量信息

                // 物流价格
                resourceAddParams2(goodsBaseDTO, goodsConsignDTO, goodsDTO, dto, resource);
                // 商品交付信息
                StringBuilder payWayStr = new StringBuilder();
                List<String> payWays = goodsConsignDTO.getPayWay();
                setPayWayStr(payWays, payWayStr, resource);

                // 商品其他信息
                resource.setSellerId(sellerId); // 设置卖家信息
                resource.setSellerName(sellerName);
                resource.setSellerNickName(reqCreateResourceDTO.getSellerNickName());

                // 设置上下架时间
                resource.setIfup(goodsOtherDTO.getIfup());
                // 交易时间、(支付有效期、提货/发货有效期,存到数据库采用毫秒形式)

                // 设置商品属性
                String resourceAttributes = "";
                List<String> attributesList = new ArrayList<>();
                // 查询商品属性
                List<GoodsCategoryAttrDTO> attrVals = goodsService.getCategoryAttrByGoodsId(goodsId);
                attributesListAddItems(attrVals, attributesList);
                Collections.sort(attributesList);
                resourceAttributes = getResourceAttributes(attributesList, resourceAttributes);
                resource.setResourceAttributes(resourceAttributes);

                // 区域聚合
                List<ResourceRegion> resourceRegions = new ArrayList<>();
                resourceRegionsAddValues(dto, resourceRegions);
                resourceSetResourceRegions(resourceRegions, resource);

                // 资源存库
                resource.setCreateUser(operator);
                resource.setCreateTime(new Date());
                resource.setUpdateUser(operator);
                resource.setUpdateTime(new Date());
                resource.setDelFlg(false);
                log.info(" >>> resourceMapper-priceWay: {}", dto.getPriceWay());
                resourceMapper.insert(resource);

                // 历史资源存库
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);

                // 行政区域存库
                int regionCount = 0;
                List<AdministrativeRegionDTO> administrativeRegions = dto.getAdministrativeRegions();
                checkCount(administrativeRegions, regionCount, resource);
                resourceRegionMapperInsert(operator, administrativeRegions, resource);
                // 聚合商品资源
                //ES异步创建资源索引
                Resource createResource = resourceMapper.selectByPrimaryKey(resource.getResourceId());
            }
        }
    }

    private static String getResourceAttributes(List<String> attributesList, String resourceAttributes) {
        if (attributesList != null && attributesList.size() > 0) {
            for (String attributeStr : attributesList) {
                resourceAttributes = resourceAttributes.concat(attributeStr)
                        .concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
            }
        }
        return resourceAttributes;
    }


    private void resourceRegionMapperInsert(String operator, List<AdministrativeRegionDTO> administrativeRegions, Resource resource) {
        if (CollectionUtils.isEmpty(administrativeRegions)) {
            return;
        }
        for (AdministrativeRegionDTO region : administrativeRegions) {
            if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                String streetNameStr = region.getStreetName();
                String[] streetNames = streetNameStr.split("、");
                if (streetNames.length > 0) {
                    for (String streetName : streetNames) {
                        ResourceRegion regionRecord = new ResourceRegion();
                        BeanUtils.copyProperties(region, regionRecord);
                        regionRecord.setStreetName(streetName);
                        regionRecord.setResourceRegionId(uuidGenerator.gain());
                        regionRecord.setResourceId(resource.getResourceId());
                        regionRecord.setCreateUser(operator);
                        regionRecord.setCreateTime(new Date());
                        regionRecord.setUpdateUser(operator);
                        regionRecord.setUpdateTime(new Date());
                        regionRecord.setDelFlg(false);
                        resourceRegionMapper.insert(regionRecord);
                    }
                }
            }
        }
    }

    private static void checkCount(List<AdministrativeRegionDTO> administrativeRegions, int regionCount, Resource resource) {
        if (administrativeRegions != null && administrativeRegions.size() > 0) {
            for (AdministrativeRegionDTO region : administrativeRegions) {
                if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                    regionCount = regionCount + 1;
                }
            }
        }
        if (regionCount <= 0) {
            throw new BizException(ResourceCode.CAN_NOT_CREATE,
                    "该商品在{"  + "}区域{" + "}仓库不可配送，请不要启用！");
        }
    }

    private void resourceSetResourceRegions(List<ResourceRegion> resourceRegions, Resource resource) {
        if (resourceRegions != null && resourceRegions.size() > 0) {
            List<String> regionStrs = mergeGoodsRegion(resourceRegions);
            if (regionStrs != null && regionStrs.size() > 0) {
                String resourceRegionsStr = regionStrs.get(0);
            }
        }
    }

    private static void resourceRegionsAddValues(GoodsResourceStoreDTO dto, List<ResourceRegion> resourceRegions) {
        if (dto.getAdministrativeRegions() == null && dto.getAdministrativeRegions().size() == 0) {
            return;
        }
        for (AdministrativeRegionDTO region : dto.getAdministrativeRegions()) {
            if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                String streetNameStr = region.getStreetName();
                String[] streetNames = streetNameStr.split("、");
                if (streetNames.length > 0) {
                    for (String streetName : streetNames) {
                        ResourceRegion regionRecord = new ResourceRegion();
                        BeanUtils.copyProperties(region, regionRecord);
                        regionRecord.setStreetName(streetName);
                        resourceRegions.add(regionRecord);
                    }
                }
            }
        }
    }

    private static void attributesListAddItems(List<GoodsCategoryAttrDTO> attrVals, List<String> attributesList) {
        if (attrVals != null && attrVals.size() > 0) {
            for (GoodsCategoryAttrDTO goodsCategoryAttrDTO : attrVals) {
                String attriValueCode = goodsCategoryAttrDTO.getValueCode();
                String goodsAttriId = goodsCategoryAttrDTO.getGoodsAttriId();
                if (StringUtils.isNotBlank(attriValueCode)
                        && StringUtils.isNotBlank(goodsAttriId)) {
                    String attributeStr = goodsAttriId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                            .concat(attriValueCode);
                    if (!attributesList.contains(attributeStr)) {
                        attributesList.add(attributeStr);
                    }
                }

            }
        }
    }


    private static void setPayWayStr(List<String> payWays, StringBuilder payWayStr, Resource resource) {
        if (payWays != null && payWays.size() > 0) {
            for (String payWay : payWays) {
                if (payWayStr.length() == 0) {
                    payWayStr.append(payWay);
                } else {
                    payWayStr.append(",").append(payWay);
                }
            }
        }
    }

    private static void resourceAddParams2(GoodsBaseDTO goodsBaseDTO, GoodsConsignDTO goodsConsignDTO, GoodsDTO goodsDTO, GoodsResourceStoreDTO dto, Resource resource) {
        if (CsStringUtils.isNullOrBlank(goodsDTO.getLogistics())
                && goodsConsignDTO.getIfPlatformDelivery()) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR,
                    "该商品{" + goodsBaseDTO.getGoodsName() + "}不可平台配送！");
        }
    }

    private static String getSaleAreaRealCode(GoodsResourceStoreDTO dto) {
        String saleAreaRealCode;
        if (StringUtils.isNotBlank(dto.getSaleAreaCode5())) {
            saleAreaRealCode = dto.getSaleAreaCode5();
        } else if (StringUtils.isNotBlank(dto.getSaleAreaCode4())) {
            saleAreaRealCode = dto.getSaleAreaCode4();
        } else if (StringUtils.isNotBlank(dto.getSaleAreaCode3())) {
            saleAreaRealCode = dto.getSaleAreaCode3();
        } else if (StringUtils.isNotBlank(dto.getSaleAreaCode2())) {
            saleAreaRealCode = dto.getSaleAreaCode2();
        } else {
            saleAreaRealCode = dto.getSaleAreaCode();
        }
        return saleAreaRealCode;
    }

    private void checkGoodsOtherInfo(GoodsPriceDTO goodsPriceDTO, GoodsQuantityDTO goodsQuantityDTO, GoodsConsignDTO goodsConsignDTO, GoodsOtherDTO goodsOtherDTO, GoodsResourceStoreDTO dto) {
        if (goodsPriceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品定价信息");
        }
        if (goodsConsignDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品配送信息");
        }
        if (goodsOtherDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品其他信息");
        }
        if (dto == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品仓库信息");
        }

        if (goodsPriceDTO.getAreaLevel() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "定价层级");
        }

        if (dto.getSaleNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "上架数量");
        }
        if (dto.getSaleNum().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, "上架数量");
        }
        if (StringUtils.isBlank(dto.getStoreId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库编号");
        }
        if (StringUtils.isBlank(dto.getStoreType())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库类型");
        }
        if (StringUtils.isBlank(dto.getStoreName())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库名称");
        }
    }

    /**
     * 检查商品基本信息
     *
     * @param goodsBaseDTO
     */
    private void checkGoodsBaseDTO(GoodsBaseDTO goodsBaseDTO) {
        if (goodsBaseDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品基本信息");
        }
        if (StringUtils.isBlank(goodsBaseDTO.getGoodsId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (StringUtils.isBlank(goodsBaseDTO.getGoodsName())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品名称");
        }
    }

    /**
     * 查询是否挂牌了该资源
     *
     * @param spuId
     * @param goodsId
     * @param storeId
     * @return
     */
    private boolean checkIsCanCreateResource(String spuId, String goodsId, String storeId, String saleAreaRealCode) {
        boolean isCanCreateResource = true;
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(SPU_ID, spuId);
        criteria.andEqualTo(GOODS_ID, goodsId);
        criteria.andEqualTo(STORE_ID, storeId);
        criteria.andEqualTo(SALE_AREA_REAL_CODE, saleAreaRealCode);
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        List<Resource> resources = resourceMapper.selectByExample(example);
        if (resources != null && resources.size() > 0) {
            isCanCreateResource = false;
        }
        return isCanCreateResource;
    }

    /**
     * 创建资源参数检查
     *
     * @param reqCreateResourceDTO 入参
     */
    private void createResourceCheck(ReqCreateResourceDTO reqCreateResourceDTO) {
        if (reqCreateResourceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (StringUtils.isBlank(reqCreateResourceDTO.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (StringUtils.isBlank(reqCreateResourceDTO.getSpuId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "SPU编号");
        }
        if (StringUtils.isBlank(reqCreateResourceDTO.getOperator())) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (StringUtils.isBlank(reqCreateResourceDTO.getUnit())) {
            throw new BizException(ResourceCode.PARAM_NULL, "单位");
        }
        if (reqCreateResourceDTO.getGoodsBaseDTOs() == null || reqCreateResourceDTO.getGoodsBaseDTOs().size() == 0) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "未选择商品");
        }
    }

    @PrintArgs
    @Transactional
    @Override
    public void updateResource(ReqUpdateResourceDTO req) {
        // 参数检查
        checkParams(req);
        try {
            // 查询老数据
            String operator = req.getOperator();
            String resourceId = req.getResourceId();
            Boolean ifEffectNow = req.getIfEffectNow();
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (ifEffectNow) { // 立即生效
                if (Objects.isNull(resource)) {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改已下架的资源！");
                }
                if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code()))) {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改已上架、下架处理中状态的资源！");
                }
                // 修改资源

                // 设置上下架时间
                resourceSetTime(req, resource);

                //交易时间

                //上架数量


                // 商品支付信息
                StringBuilder payWayStr = new StringBuilder();
                List<String> payWays = req.getPayWay();
                payWaysAddPayWay(payWays, payWayStr);

                // 设置商品属性
                String resourceAttributes = "";
                List<String> attributesList = new ArrayList<>();
                // 查询商品属性
                List<GoodsCategoryAttrDTO> attrVals = goodsService.getCategoryAttrByGoodsId(resource.getGoodsId());
                attributesListAddItem(attrVals, attributesList);
                Collections.sort(attributesList);
                if (attributesList != null && attributesList.size() > 0) {
                    for (String attributeStr : attributesList) {
                        resourceAttributes = resourceAttributes.concat(attributeStr)
                                .concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
                    }
                }
                resource.setResourceAttributes(resourceAttributes);

                //价格属性

                //搬运费规则

                resourceMapper.updateByPrimaryKey(resource);

                // 添加历史资源记录
                ResourceHistory historyResource = new ResourceHistory();
                BeanUtils.copyProperties(resource, historyResource);
                historyResource.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(historyResource);
                // 聚合商品资源
                //异步更新资源ES
                resource = resourceMapper.selectByPrimaryKey(resourceId);
            } else if (!ifEffectNow) { // 定时生效
                BeanUtils.copyProperties(req, resource);

                // 插入未生效资源检查
                checkUneffectiveResource(resourceId);
                // 插入未生效资源表
                ResourceUneffective uneffective = new ResourceUneffective();
                BeanUtils.copyProperties(resource, uneffective);
                uneffective.setEffectiveId(uuidGenerator.gain());
                uneffective.setDelFlg(false);
                uneffective.setCreateUser(operator);
                uneffective.setCreateTime(new Date());
                uneffective.setUpdateTime(new Date());
                uneffective.setUpdateUser(operator);
                resourceUneffectiveMapper.insert(uneffective);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("updateResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private static void attributesListAddItem(List<GoodsCategoryAttrDTO> attrVals, List<String> attributesList) {
        if (attrVals != null && attrVals.size() > 0) {
            for (GoodsCategoryAttrDTO goodsCategoryAttrDTO : attrVals) {
                String goodsAttriId = goodsCategoryAttrDTO.getGoodsAttriId();
                String attriValueCode = goodsCategoryAttrDTO.getValueCode();
                if (StringUtils.isNotBlank(attriValueCode)
                        && StringUtils.isNotBlank(goodsAttriId)) {
                    String attributeStr = goodsAttriId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                            .concat(attriValueCode);
                    if (!attributesList.contains(attributeStr)) {
                        attributesList.add(attributeStr);
                    }
                }

            }
        }
    }

    private static void payWaysAddPayWay(List<String> payWays, StringBuilder payWayStr) {
        if (payWays != null && payWays.size() > 0) {
            for (String payWay : payWays) {
                if (payWayStr.length() == 0) {
                    payWayStr.append(payWay);
                } else {
                    payWayStr.append(",").append(payWay);
                }
            }
        }
    }

    private static void resourceSetTime(ReqUpdateResourceDTO req, Resource resource) {
        resource.setIfup(req.getIfup());
        if (req.getIfup()) {
            resource.setUpTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
        } else {
            if (req.getFixUptime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时上架需要选择定时上架时间！");
            }
            resource.setStatus(ResourceStatusEnum.RES_STATUS400.code());
        }
        resource.setIfdown(req.getIfdown());
        if (!req.getIfdown()) {
            if (req.getFixDowntime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时下架需要选择定时下架时间！");
            }
        }
    }

    private static void checkParams(ReqUpdateResourceDTO req) {
        if (StringUtils.isBlank(req.getOperator())) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (StringUtils.isBlank(req.getResourceId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (req.getIfEffectNow() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "是否生效");
        }
    }


    @Transactional
    @Override
    public void deleteResource(List<String> resourceIds, String operatorId) {
        // 参数检查
        checkParams2(resourceIds, operatorId);
        try {
            if (resourceIds != null && resourceIds.size() > 0) {
                for (String resourceId : resourceIds) {
                    Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                    checkParams3(resource);
                    resourceElasticsearchBizAsyncEsDeleteResource(operatorId, resourceId, resource);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("deleteResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void resourceElasticsearchBizAsyncEsDeleteResource(String operatorId, String resourceId, Resource resource) {
        if (resource != null) {
            if (!(resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                    || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code()))) {
                // 逻辑删除
                resource.setDelFlg(true);
                resource.setUpdateTime(new Date());
                resource.setUpdateUser(operatorId);
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 删除资源销售区域
                Example regionExample = new Example(ResourceRegion.class);
                Example.Criteria regionExampleCriteria = regionExample.createCriteria();
                regionExampleCriteria.andEqualTo(RESOURCE_ID, resourceId);
                regionExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
                List<ResourceRegion> regions = resourceRegionMapper.selectByExample(regionExample);
                if (regions != null && regions.size() > 0) {
                    for (ResourceRegion region : regions) {
                        region.setUpdateUser(operatorId);
                        region.setUpdateTime(new Date());
                        region.setDelFlg(true);
                        resourceRegionMapper.updateByPrimaryKey(region);
                    }
                }
                // 添加历史资源记录
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
//                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operatorId, resource.getBrand());
                //删除资源ES
//                resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
            }
        }
    }

    private static void checkParams3(Resource resource) {
        if (Objects.isNull(resource)) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "已上架商品为空");
        }
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "已上架商品【" + resource.getResourceCode() + "】不能删除");
        }
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code())) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "下架处理中商品【" + resource.getResourceCode() + "】不能删除");
        }
    }

    private static void checkParams2(List<String> resourceIds, String operatorId) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (StringUtils.isBlank(operatorId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public void offSaleResourceBatch(List<String> resourceIds, String operator) {
        // 参数检查
        checkOffsale(resourceIds, operator);
        if (resourceIds.isEmpty()) {
            return;
        }
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                if (resource != null) {
                    if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                        throw new BizException(ResourceCode.UNKNOWN_ERROR, "商品【" + resource.getResourceCode() + "】不能下架，只能下架已上架状态商品");
                    }
                    asyncEsDeleteResource(operator, resourceId, resource);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResourceBatch Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void asyncEsDeleteResource(String operator, String resourceId, Resource resource) {
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                && operator.equals(resource.getSellerId())) {
            resource.setUpdateTime(new Date());
            resource.setUpdateUser(operator);
            resource.setDownTime(new Date());
            resourceMapper.updateByPrimaryKeySelective(resource);
            // 记录历史
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
//            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
        }
    }

    private static void checkOffsale(List<String> resourceIds, String operator) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public void onSaleResourceBatch(List<String> resourceIds, String operator) {
        // 参数检查
        checkOnsaleRe(resourceIds, operator);
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                if (Objects.isNull(resource)) {
                    throw new BizException(BasicCode.DATA_NOT_EXIST, resourceId);
                }
                GoodsDTO goodsDTO = getGoodsInfo(resource.getGoodsId());
                Integer goodsStatus = goodsDTO.getGoodsStatus();
                if (GoodsStatusEnum.ENABLE.getCode() != goodsStatus) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, resource.getResourceName() + "商品状态异常,不能挂牌");
                }
                if (!(resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                    throw new BizException(ResourceCode.UNKNOWN_ERROR,
                            "商品【" + resource.getResourceCode() + "】不能上架，只能上架已下架状态、未上架商品");
                }
                asyncEsUpdateResource2(operator, resourceId, resource);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResourceBatch Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void asyncEsUpdateResource2(String operator, String resourceId, Resource resource) {
        if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))
                && operator.equals(resource.getSellerId())) {
            resource.setUpdateUser(operator);
            resource.setUpTime(new Date());
            resource.setUpdateTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
            resourceMapper.updateByPrimaryKeySelective(resource);
            // 记录历史
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
//            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
            resource = resourceMapper.selectByPrimaryKey(resourceId);
//            resourceElasticsearchBiz.asyncEsUpdateResource(resource);
        }
    }

    private static void checkOnsaleRe(List<String> resourceIds, String operator) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源ID集合");
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }


    @Override
    public void offSaleResource(String resourceId, String operator) {
        // 参数检查
        checkOffSale3(resourceId, operator);
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource != null) {
                if (!operator.equals(resource.getSellerId())) {
                    if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                        resource.setUpdateTime(new Date());
                        resource.setDownTime(new Date());
                        resource.setUpdateUser(operator);
                        resourceMapper.updateByPrimaryKeySelective(resource);
                        // 记录历史
                        ResourceHistory history = new ResourceHistory();
                        BeanUtils.copyProperties(resource, history);
                        history.setResourceHistoryId(uuidGenerator.gain());
                        resourceHistoryMapper.insert(history);
                        // 聚合商品资源
//                        this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                        //异步修改资源ES
//                        resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
                    } else {
                        throw new BizException(ResourceCode.CAN_NOT_UPDATE, CAN_NOT_CHANGE_THIS_STATUS_RESOURCE);
                    }
                } else {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "只能下架卖家自己的资源");
                }
            } else {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "资源" + resourceId);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private static void checkOffSale3(String resourceId, String operator) {
        if (StringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public void onSaleResource(String resourceId, String operator) {
        // 参数检查
        if (StringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
//            GoodsDTO goodsDTO = getGoodsInfo(resource.getGoodsId());
//            resource.setGoodsName(goodsDTO.getGoodsName());
//            resource.setGoodsType(goodsDTO.getCategoryType().toString());
//            resource.setGoodsDescribe(goodsDTO.getSearchKeywords());
//            if(CsStringUtils.isNullOrBlank(goodsDTO.getBrand())){
//                resource.setBrand(NO_BRAND);
//            }else {
//                resource.setBrand(goodsDTO.getBrand());
//            }
//            resource.setResourceName(goodsDTO.getGoodsName());

            if (!operator.equals(resource.getSellerId())) {
                if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                    resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
                    resource.setUpdateUser(operator);
                    resource.setUpdateTime(new Date());
                    resource.setUpTime(new Date());
                    resourceMapper.updateByPrimaryKeySelective(resource);
                    // 记录历史
                    ResourceHistory history = new ResourceHistory();
                    BeanUtils.copyProperties(resource, history);
                    history.setResourceHistoryId(uuidGenerator.gain());
                    resourceHistoryMapper.insert(history);
                    // 聚合商品资源
//                    this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                    //异步修改资源ES
//                    resource = resourceMapper.selectByPrimaryKey(resourceId);
//                    resourceElasticsearchBiz.asyncEsUpdateResource(resource);
                    resource = resourceMapper.selectByPrimaryKey(resourceId);
                } else {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能上架该状态的资源");
                }
            } else {
                throw new BizException(ResourceCode.CAN_NOT_UPDATE, "只能上架卖家自己的资源");
            }

        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    /**
     * 查询商品基本属性
     *
     * @return
     */
    private GoodsDTO getGoodsInfo(String goodsId) {
        try {
            GoodsDTO dto;
            if (StringUtils.isBlank(goodsId)) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "商品ID不能为空");
            }
            dto = goodsService.findGoodsById(goodsId);
            if (dto == null) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "商品信息不存在");
            }
            return dto;
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getGoodsInfo Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    /**
     * 区域聚合
     *
     * @param regions
     * @return
     */
    public List<String> mergeGoodsRegion(List<ResourceRegion> regions) {
        log.info(" 开始聚合商品行政区域:" + JSON.toJSONString(regions));
        List<String> result = new ArrayList<>();

        Map<String, String> tempResult = new HashMap<>();
        String tmpResult = null;
        for (ResourceRegion resourceRegion : regions) {
            String country = resourceRegion.getCountryName();
            String province = resourceRegion.getProvinceCode();
            String city = resourceRegion.getCityCode();
            String area = resourceRegion.getAreaCode();
            String street = resourceRegion.getStreetCode();

            if (StringUtils.isNotEmpty(street)) {
                tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + street + MergeRegionPrefixEnum.ENDSUB.getCode();
                if (!tempResult.containsKey(tmpResult)) {
                    tempResult.put(tmpResult, tmpResult);
                }
            }

            dealTempResult(area, tempResult, street);

            dealTempResultCity(city, tempResult, area);

            dealTempResultProvince(province, tempResult, city);

            dealTempResultCountry(country, tempResult, province);

        }

        StringBuilder builder = new StringBuilder();
        for (Iterator<Map.Entry<String, String>> iterator = tempResult.entrySet().iterator(); iterator
                .hasNext(); ) {
            builder.append(iterator.next().getValue());
            if (builder.length() > 2000) {
                result.add(builder.toString());
                builder = new StringBuilder();
            }
        }
        if (result.isEmpty()) {
            result.add(builder.toString());
        }
        log.info("  商品行政区域聚合结果 {}", result);
        return result;
    }

    private static void dealTempResultCountry(String country, Map<String, String> tempResult, String province) {
        String tmpResult;
        if (StringUtils.isNotEmpty(country)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (StringUtils.isEmpty(province)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResultProvince(String province, Map<String, String> tempResult, String city) {
        String tmpResult;
        if (StringUtils.isNotEmpty(province)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (StringUtils.isEmpty(city)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResultCity(String city, Map<String, String> tempResult, String area) {
        String tmpResult;
        if (StringUtils.isNotEmpty(city)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (StringUtils.isEmpty(area)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResult(String area, Map<String, String> tempResult, String street) {
        String tmpResult;
        if (StringUtils.isNotEmpty(area)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (StringUtils.isEmpty(street)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    /**
     * 生成资源CODE
     *
     * @return
     */
    private String generatorResourceCode() {
        String defaultDateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        StringBuilder result = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < 6; i++) {
            result.append(random.nextInt(10));
        }
        return "SP" + defaultDateTimeStr + result;
    }
    /**
     * 检查
     */
    private void checkUneffectiveResource(String resourceId) {
        // 插入未生效资源检查
        Example effectExample = new Example(ResourceUneffective.class);
        Example.Criteria effectExampleCriteria = effectExample.createCriteria();
        effectExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        effectExampleCriteria.andEqualTo(RESOURCE_ID, resourceId);
        List<ResourceUneffective> uneffectives = resourceUneffectiveMapper.selectByExample(effectExample);
        if (uneffectives != null && uneffectives.size() > 0) {
            throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改该资源，当前挂牌资源有一条变更信息未生效！");
        }
    }

    /**
     * 判断资源是否可以交易
     */
    @Override
    public boolean checkResourceTradeStatus(Resource resource) {
        return false;
    }

    @Override
    public void processResourceRegionsExample(String name, Example example, String countryName, String provinceName,
                                              String cityName, String areaName, String streetName) {
        List<String> regions = new ArrayList<>();
        if (StringUtils.isNotBlank(countryName)) {
            regions.add(countryName);
        }
        if (StringUtils.isNotBlank(provinceName)) {
            regions.add(provinceName);
        }
        if (StringUtils.isNotBlank(cityName)) {
            regions.add(cityName);
        }
        if (StringUtils.isNotBlank(areaName)) {
            regions.add(areaName);
        }
        if (StringUtils.isNotBlank(streetName)) {
            regions.add(streetName);
        }
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(regions)) {
            for (int i = 0; i < regions.size(); i++) {
                if (i == regions.size() - 1) {
                    criteria.orLike(name, "%" + regions.get(i) + "\\" + MergeRegionPrefixEnum.ENDSUB.getCode() + "%");
                } else {
                    criteria.orLike(name, "%" + MergeRegionPrefixEnum.NOSUB.getCode() + regions.get(i)
                            + "\\" + MergeRegionPrefixEnum.ENDSUB.getCode() + "%");
                }
            }
            example.and(criteria);
        }
    }


    @Override
    public PageInfo<Resource> pageResource(ReqResourceSellerDTO reqDto) {
        // 入参封装
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteriaAddParams2(reqDto, criteria);
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode2())) {
            criteria.andEqualTo(SALE_AREA_CODE2, reqDto.getSaleAreaCode2());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode3())) {
            criteria.andEqualTo(SALE_AREA_CODE3, reqDto.getSaleAreaCode3());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode4())) {
            criteria.andEqualTo(SALE_AREA_CODE4, reqDto.getSaleAreaCode4());
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode5())) {
            criteria.andEqualTo(SALE_AREA_CODE5, reqDto.getSaleAreaCode5());
        }
        if (!StringUtils.isBlank(reqDto.getResourceCode())) {
            criteria.andLike(RESOURCE_CODE, "%" + reqDto.getResourceCode() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getStoreName())) {
            criteria.andLike("storeName", "%" + reqDto.getStoreName() + "%");
        }
        if (reqDto.getIfProtocolPrice() != null) {
            criteria.andEqualTo(IF_PROTOCOL_PRICE, reqDto.getIfProtocolPrice());
        }
        if (reqDto.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(UP_TIME, reqDto.getStartTime());
        }
        if (reqDto.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(UP_TIME, reqDto.getEndTime());
        }
        if (!StringUtils.isBlank(reqDto.getStatus())) {
            criteria.andEqualTo(STATUS, reqDto.getStatus());
        }
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        example.orderBy(UPDATE_TIME).desc();
        // 分页查询数据库
        PageInfo<Resource> pageInfo = PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true).doSelectPageInfo(() -> resourceMapper.selectByExample(example));
        return pageInfo;
    }


    private static void criteriaAddParams2(ReqResourceSellerDTO reqDto, Example.Criteria criteria) {
        if (!StringUtils.isBlank(reqDto.getSellerId())) {
            criteria.andEqualTo(SELLER_ID, reqDto.getSellerId());
        }
        if (!StringUtils.isBlank(reqDto.getGoodsType())) {
            criteria.andEqualTo(GOODS_TYPE, reqDto.getGoodsType());
        }
        if (!StringUtils.isBlank(reqDto.getGoodsDescribe())) {
            criteria.andLike("goodsName", "%" + reqDto.getGoodsDescribe() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getSaleArea())) {
            criteria.andLike(SALE_AREA, "%" + reqDto.getSaleArea() + "%");
        }
        if (!StringUtils.isBlank(reqDto.getSaleAreaCode())) {
            criteria.andEqualTo(SALE_AREA_CODE, reqDto.getSaleAreaCode());
        }
    }


    @Override
    public PageInfo<Resource> pageResourceWithDataPerm(ReqResourceSellerDTO reqDto) {
        // 入参封装
//        List<Resource> resourceList = resourceMapper.pageQueryWithDataPerm(reqDto);
//        Page<Resource> page=new Page<>();
//        page.addAll(resourceList);
//        int total = resourceMapper.countQueryWithDataPerm(reqDto);
//        int pages = 0;
//        if(total % reqDto.getPageSize() > 0){
//            pages = total /reqDto.getPageSize() +1;
//        }else {
//            pages = total /reqDto.getPageSize();
//        }
//        page.setPages(pages);
//        page.setTotal(total);
        reqDto.setPageNum(reqDto.getPageNum() != null ? reqDto.getPageNum() : 1);
        reqDto.setPageSize(reqDto.getPageSize() != null ? reqDto.getPageSize() : 10 );

        return PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize()).doSelectPageInfo(
                () -> resourceMapper.pageQueryWithDataPerm(reqDto));
    }


    @Override
    public void batchUpdateByPrimaryKeySelective(List<Resource> resources) {
        if (!CollectionUtils.isEmpty(resources)) {
            resources.forEach(resource -> resourceMapper.updateByPrimaryKeySelective(resource));
        }
    }


    @Override
    public void batchUpdateResources(List<Resource> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        resources.forEach(resource -> resourceMapper.updateByPrimaryKey(resource));
    }


    @Override
    public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(ReqGoodsResourceDTO req) {
        if (req.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
        if (req.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        // 初始化结果
        List<GoodsResourceListDTO> goodsList = Lists.newArrayList();
        try {
            PageMethod.startPage(req.getPageNum(), req.getPageSize());
            List<SellerGoodsDTO> sellerGoodsDTOS = goodsMapper.selectGoodsByCategoryIds(req.getCategoryTypeStr());
            for (SellerGoodsDTO sellerGoodsDTO : sellerGoodsDTOS) {
                GoodsResourceListDTO goodsResourceListDTO = GoodsResourceListDTO.builder().build();
                BeanUtils.copyProperties(sellerGoodsDTO, goodsResourceListDTO);
                goodsList.add(goodsResourceListDTO);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsResourceEmall Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return new PageInfo<>(goodsList);
    }



}
