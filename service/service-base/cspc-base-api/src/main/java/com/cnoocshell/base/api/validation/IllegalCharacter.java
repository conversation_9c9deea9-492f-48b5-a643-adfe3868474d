package com.cnoocshell.base.api.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CharacterValidator.class)
public @interface IllegalCharacter {
    String message() default "该字段只能是汉字、数字、字母及下划线";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
