package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.member.api.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @DESCRIPTION:个人账号注册对象
 */
@ApiModel("个人账号PC注册DTO")
@Data
public class AccountPCRegisterDTO implements Serializable {

    /**
     * 用户名格式
     */
    @ApiModelProperty("用户名格式")
    public static final String USERNAME_PATTERN = "^$|^[a-zA-Z]\\w{5,19}$";
    /**
     * 电话号码格式
     */
    @ApiModelProperty("电话号码格式")
    public static final String MOBILE_PHONE_NUMBER_PATTERN = "^0?(1\\d{2})\\d{8}$";

    private static final long serialVersionUID = -8320418069151359001L;

    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称(6到20个字母、数字或下划线组成，且必须以字母开头)")
    private String accountName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = MOBILE_PHONE_NUMBER_PATTERN, message = "手机号错误")
    private String mobile;

    /**
     * 短信验证码
     */
    @ApiModelProperty("短信验证码")
    @NotBlank
    private String smsCode;

    /**
     * 注册时使用的应用名称
     */
    @ApiModelProperty("注册时使用的应用名称")
    private Integer registerType = RegisterTypeEnum.PC.getType();

    /**
     * 注册时使用的应用名称
     */
    @ApiModelProperty("注册时使用的应用名称")
    private String registerApp;

    /**
     * 短信签名
     */
    @ApiModelProperty("短信签名")
    private String registerSign;

    /**
     * 注册客户端ip
     */
    @ApiModelProperty("注册客户端ip")
    private String registerIP;

    @ApiModelProperty("用户真实姓名")
    private String realName;
}
