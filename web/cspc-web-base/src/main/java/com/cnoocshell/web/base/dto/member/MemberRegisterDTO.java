package com.cnoocshell.web.base.dto.member;

import com.cnoocshell.member.api.dto.KeyValueDTO;
import com.cnoocshell.member.api.dto.member.MemberCertQueryDTO;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.validation.RegisterBuyerGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @description TODO
 * @date 23/08/2018 11:19
 */
@Data
public class MemberRegisterDTO {

    private String memberId;

    /**
     * 会员名
     */
    @NotBlank(message = "企业名称不能为空",groups = RegisterBuyerGroup.class)
    private String memberName;
    @ApiModelProperty("会员简称")
    private String memberShortName;
    @ApiModelProperty("会员code")
    private String memberCode;
    @ApiModelProperty("商标URL")
    private String trademarkUrl;


    private String contactName;

    private String contactPhone;
    /**
     * 草稿使用
     */
    private String requestId;


    /**
     * 所在国家编码
     */
    private String countryCode;

    /**
     * 所在省编码
     */
    private String provinceCode;

    /**
     * 所在城市编码
     */
    private String cityCode;

    /**
     * 所在地区编码
     */
    private String areaCode;

    /**
     * 所在街道编码
     */
    private String streetCode;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 注册国家编码
     */
    private String rigistCountryCode;

    /**
     * 注册省编码
     */
    private String rigistProvinceCode;

    /**
     * 注册城市编码
     */
    private String rigistCityCode;

    /**
     * 注册地区编码
     */
    private String rigistAreaCode;

    /**
     * 注册街道编码
     */
    private String rigistStreetCode;

    /**
     * 注册详细地址
     */
    @NotNull
    private String rigistAddressDetail;

    /**
     * 法人姓名
     */
    private String legalName;

    /**
     * 法人证件类型
     */
    private Integer legalCertificateType;

    /**
     * 法人证件号码
     */
    private String legalCertificateCode;

    /**
     * 买家类型
     */
    private String buyerType;

    /**
     * 卖家类型
     */
    private String sellerType;

    /**
     * 承运商类型
     */
    private String carrierType;

    /**
     * 供应商类型
     */
    private String supplierType;

    /**
     * 是否三证合一
     */
    @NotNull
    private Integer isSyncretic;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 工商执照号码
     */
    private String businessLicenseCode;

    /**
     * 税务登记号码
     */
    private String taxCode;

    /**
     * 企业信用号
     */
    private String creditCode;

    /**
     * 企业注册时间
     */
    private Date registerTime;

    /**
     * 注册资本
     */
    private BigDecimal registerFund;

    /**
     * 主营产品
     */
    @NotNull(message = "营业范围不能为空",groups = RegisterBuyerGroup.class)
    private String mainProducts;

    private MemberCertQueryDTO businessQueryCert;
    private MemberCertQueryDTO organizationQueryCert;
    private MemberCertQueryDTO taxQueryCert;
    @ApiModelProperty("承运商业务范围")
    private String businessScope;

    @ApiModelProperty("省份(销售或购买范围)")
    private List<KeyValueDTO> provinceList;


    @ApiModelProperty("商品类别(销售或购买的)")
    private List<KeyValueDTO> goodsCategoryList;

    @ApiModelProperty("租户(要购买或者销售的商品对应的商家的总公司id)")
    private List<KeyValueDTO> tenantList;


    /**
     * 一般资质
     */
    private List<MemberRegisterCertDTO> memberRegisterCertDTOList;

    @ApiModelProperty("是否中海壳牌股东关联方")
    private Boolean shareholderRelationParty;

    @ApiModelProperty("购买商品意向")
    @Valid
    @NotNull(message = "意向购买商品不能为空",groups = RegisterBuyerGroup.class)
    private List<MemberPurchaseGoodsIntentionDTO> intentionList;

}
