package com.cnoocshell.web.base.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.enums.*;
import com.cnoocshell.member.api.dto.member.validation.RegisterBuyerGroup;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.base.dto.member.MemberApprovalRequestPageDTO;
import com.cnoocshell.web.base.dto.member.MemberBaseInfoQueryDTO;
import com.cnoocshell.web.base.dto.member.MemberBusinessInfoQueryDTO;
import com.cnoocshell.web.base.dto.member.MemberRegisterDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 会员服务
 */
@Slf4j
@Api(tags = {"MemberController"}, description = "会员服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/member")
public class MemberController {
    private final IMemberService iMemberService;

    @ApiOperation("根据ID查找用户详情")
    @PostMapping(value = "/findMemberDetailById")
    public ItemResult<MemberDTO> findMemberDetailById(@ApiIgnore LoginInfo loginInfo, String memberId) {
        checkLogin(loginInfo);
        if (StringUtils.isEmpty(memberId)) {
            memberId = loginInfo.getMemberId();
        }
        //非平台账号只能访问自己的会员信息
        if (!AccountTypeEnum.PLATFORM_ACCOUNT.getAccountType().equals(loginInfo.getAccountType())) {
            memberId = loginInfo.getMemberId();
        }
        //如果个人会员，则查询是否有企业申请审批被拒绝的信息
        if (loginInfo.getAccountType() != null && Objects.equals(loginInfo.getAccountType(), AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType())) {
            MemberApprovalRequestQueryDTO query = new MemberApprovalRequestQueryDTO();
            query.setMemberId(loginInfo.getMemberId());
            PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageRegisterMemberApprovalRequests(query, 1, 10);
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                MemberApprovalRequestDTO approvalRequestDTO = pageInfo.getList().get(0);
                //如果最近一条记录是审批拒绝
                if (approvalRequestDTO != null && AdvertStatusEnum.REJECT.getCode().equals(approvalRequestDTO.getStatus()) &&
                        (ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode().equals(approvalRequestDTO.getRequestType()) ||
                                ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode().equals(approvalRequestDTO.getRequestType()))) {
                    return new ItemResult<>(iMemberService.getMemberApprovalDetails(pageInfo.getList().get(0).getRequestId()));
                }
            }
        }
        if (!Objects.equals(loginInfo.getAccountType(), AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType())) {
            return new ItemResult<>(iMemberService.findRealMemberById(memberId));
        }
        return new ItemResult<>(iMemberService.findMemberDetailById(memberId));
    }

    @ApiOperation("根据ID查找用户详情（没有资质信息）")
    @PostMapping(value = "/findMemberSimpleDetailById")
    public ItemResult<MemberDTO> findMemberSimpleDetailById(@ApiIgnore LoginInfo loginInfo,
                                                            @RequestParam("memberId") String memberId) {
        checkLogin(loginInfo);
        MemberDTO memberDetailById = iMemberService.findMemberDetailById(memberId);
        return new ItemResult<>(memberDetailById);
    }

    @ApiOperation("提交企业注册（买家） requestType - 申请类型 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。")
    @PostMapping(value = "/registerBuyer")
    public ItemResult<String> registerBuyer(@ApiIgnore LoginInfo loginInfo,
                                            @Validated(value = RegisterBuyerGroup.class)
                                            @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        return iMemberService.registerBuyer(dto);
    }

    @ApiOperation("分页获取会员请求")
    @PostMapping(value = "/pageMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageMemberApprovalRequests(@ApiIgnore LoginInfo loginInfo, @RequestBody MemberApprovalRequestPageDTO query) {
        query.defaultPageIfNull();

        if (!AccountTypeEnum.PLATFORM_ACCOUNT.getAccountType().equals(loginInfo.getAccountType())) {
            query.getMemberApprovalRequestQueryDTO().setMemberId(loginInfo.getMemberId());
        }
        PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @ApiOperation("更新会员基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public ItemResult updateBaseInfo(@ApiIgnore LoginInfo loginInfo,
                                     @ApiParam(name = "dto", value = "会员基本信息查询DTO") @Valid @RequestBody MemberBaseInfoQueryDTO dto) {
        MemberBaseInfoDTO infoDTO = BeanUtil.toBean(dto, MemberBaseInfoDTO.class);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        iMemberService.updateBaseInfo(infoDTO);
        return new ItemResult<>(new Object());
    }

    @ApiOperation("变更企业经营信息")
    @PostMapping(value = "/updateBusinessInfo")
    public ItemResult updateBusinessInfo(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody MemberBusinessInfoQueryDTO dto) {
        MemberBusinessInfoDTO infoDTO = businessQueryInfo2BusinessInfo(dto);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateBusinessInfo(infoDTO);
        return new ItemResult<>(requestId);
    }

    @ApiOperation("一般资质变更,必须写明资质类型 certType、CertId")
    @PostMapping(value = "/updateCert")
    public ItemResult<String> updateCert(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody UpdateCertDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        dto.setMemberId(loginInfo.getMemberId());
        if (CollUtil.isNotEmpty(dto.getMemberCertDTOList())) {
            List<MemberCertDTO> filterResult = Lists.newArrayList();
            dto.getMemberCertDTOList().stream().filter(item -> StringUtils.isNotBlank(item.getCertType())).forEach(item -> {
                item.setMemberId(loginInfo.getMemberId());
                filterResult.add(item);
            });
            if (CollUtil.isEmpty(filterResult)) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "剔除证件类型未空的证件后没有可更新的证件了");
            }
            dto.setMemberCertDTOList(filterResult);
        }
        return new ItemResult<>(iMemberService.updateCert(dto));
    }

    @ApiOperation("提交会员商品意向数据变更请求")
    @PostMapping(value = "/submitMemberIntentionChange")
    ItemResult<Boolean> submitMemberIntentionChange(@ApiIgnore LoginInfo loginInfo,@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param){
        checkLogin(loginInfo);
        verifyIntention(param.getIntentions());
        param.setMemberId(loginInfo.getMemberId());
        param.setMemberCode(loginInfo.getMemberCode());
        param.setOperatorId(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        return iMemberService.submitMemberIntentionChange(param);
    }

    @ApiOperation("会员是否存在企业注册记录")
    @GetMapping(value = "/existEnterpriseRegistrationRequest")
    ItemResult<Boolean> existEnterpriseRegistrationRequest(@ApiIgnore LoginInfo loginInfo,
                                                           @RequestParam(value = "status",required = false) String status){
        return ItemResult.success(iMemberService.existEnterpriseRegistrationRequest(loginInfo.getMemberId(),status));
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    private MemberRequestDTO registerDTO2RequestDTO(MemberRegisterDTO memberRegisterDTO) {
        MemberRequestDTO memberRequestDTO = new MemberRequestDTO();
        BeanUtils.copyProperties(memberRegisterDTO, memberRequestDTO);

        if (memberRegisterDTO.getBusinessQueryCert() != null) {
            memberRequestDTO.setBusinessCert(memberCertQuery2MemberCert(memberRegisterDTO.getBusinessQueryCert()));
        }
        if (memberRegisterDTO.getOrganizationQueryCert() != null) {
            memberRequestDTO.setOrganizationCert(memberCertQuery2MemberCert(memberRegisterDTO.getOrganizationQueryCert()));
        }
        if (memberRegisterDTO.getTaxQueryCert() != null) {
            memberRequestDTO.setTaxCert(memberCertQuery2MemberCert(memberRegisterDTO.getTaxQueryCert()));
        }

        if (memberRegisterDTO.getMemberRegisterCertDTOList() != null && !memberRegisterDTO.getMemberRegisterCertDTOList().isEmpty()) {
            List<MemberCertDTO> collect = memberRegisterDTO.getMemberRegisterCertDTOList().stream()
                    .map(item -> {
                        MemberCertDTO memberCertDTO = new MemberCertDTO();
                        BeanUtils.copyProperties(item, memberCertDTO);
                        return memberCertDTO;
                    }).collect(Collectors.toList());
            memberRequestDTO.setMemberCertDTOList(collect);
        }
        return memberRequestDTO;
    }

    private MemberBusinessInfoDTO businessQueryInfo2BusinessInfo(MemberBusinessInfoQueryDTO dto) {
        MemberBusinessInfoDTO infoDTO = new MemberBusinessInfoDTO();
        if (dto.getBusinessCert() != null) {
            infoDTO.setBusinessCert(memberCertQuery2MemberCert(dto.getBusinessCert()));
        }
        BeanUtils.copyProperties(dto, infoDTO);
        return infoDTO;
    }

    private MemberCertDTO memberCertQuery2MemberCert(MemberCertQueryDTO dto) {
        MemberCertDTO memberCertDTO = new MemberCertDTO();
        BeanUtils.copyProperties(dto, memberCertDTO);
        return memberCertDTO;
    }

    private static void verifyIntention(List<MemberPurchaseGoodsIntentionDTO> list) {
        if (CollUtil.isEmpty(list))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "意向购买信息不能为空");
        if (list.stream()
                .collect(Collectors
                        .groupingBy(v ->
                                        CharSequenceUtil.format("{}_{}", v.getGoodsCategoryId(), v.getGoodsId()),
                                Collectors.counting()))
                .values().stream()
                .anyMatch(v -> v > 1L)) {
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "意向购买信息中存在相同的商品类目和商品");
        }

    }


}