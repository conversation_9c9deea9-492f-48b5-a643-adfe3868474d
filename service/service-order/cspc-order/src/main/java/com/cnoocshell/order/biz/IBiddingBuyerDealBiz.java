package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingDealQuantityDTO;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyerDeal;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;

import java.util.List;

public interface IBiddingBuyerDealBiz extends IBaseBiz<BiddingBuyerDeal> {

    /**
     *保存成交结果
     */
    void saveDealResult(List<BiddingBuyerDetail> buyerBiddingDetail, Bidding bidding,
                        String operatorId, String operatorName);


    /**
     *重置成交结果
     */
    void resetBuyerDealByBiddingNos(List<String> biddingNos);

    /**
     *重置成交结果
     */
    void resetBuyerDealByBiddingList(List<Bidding> biddingList);

    List<BiddingBuyerDeal> listByBiddingNos(List<String> biddingNos);

    List<BiddingBuyerDeal> listByBiddingList(List<Bidding> biddingList);

    List<BiddingBuyerDeal> listByBiddingNo(String biddingNo,String memberCode);


    /**
     *获取竞价成交总量
     */
    List<BiddingDealQuantityDTO> getBiddingDealTotalQuantity(List<String> biddingList);

    void updateBiddingDealContractInfo(BiddingDealContractDTO param);

    BiddingBuyerDeal getByDealNo(String dealNo);

    List<BiddingBuyerDeal> listByBiddingAndDealNos(List<Bidding> biddingList,List<String> dealNos);

    /**
     * 成交通知
     */
    void notifyByDeal(Bidding bidding,BiddingBuyerDetail detail,String contractNo);
}
