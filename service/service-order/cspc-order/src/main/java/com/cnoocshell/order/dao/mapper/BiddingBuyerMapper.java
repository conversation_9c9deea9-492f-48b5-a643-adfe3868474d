package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerListViewDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerQueryDTO;
import com.cnoocshell.order.dao.vo.BiddingBuyer;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface BiddingBuyerMapper extends IBaseMapper<BiddingBuyer> {

    List<BiddingBuyerDTO> selectBuyerList(List<String> idList);

    BiddingBuyer queryBiddingBuyer(String id);

    List<BiddingBuyerListViewDTO> buyerBiddingList(@Param("page") Page<BiddingBuyerListViewDTO> page, @Param("dto") BiddingBuyerQueryDTO dto);

    List<BiddingBuyerDTO> selectMemberBiddingDataByMemberCodes(List<String> memberCodes, String startDate, String endDate);

    List<String> buyerGoodsBiddingList(String memberCode);

    List<BiddingBuyer> queryCustomerList(@Param("biddingNo") String biddingNo);

    int updateStatus(@Param("status") String status,
                     @Param("memberCode") String memberCode,
                     @Param("biddingNo") String biddingNo,
                     @Param("operatorId") String operatorId);

    List<String> queryMemberByBiddingId(@Param("id") String id);

    @Update("UPDATE bidding_buyer SET participation_status=#{status} where bidding_no=#{biddingNo} and del_flg=0")
    int updateStatusByBiddingNo(@Param("biddingNo") String biddingNo, @Param("status") String status);


    /**
     *查询当前竞价场次前一轮参与报价的客户
     */
    List<BiddingBuyer> queryLastBiddingBuyers(@Param("biddingNos") List<String> biddingNos);
}
