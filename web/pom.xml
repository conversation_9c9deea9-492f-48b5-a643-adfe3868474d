<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cnoocshell-root</artifactId>
        <groupId>com.cnoocshell</groupId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>pom</packaging>

    <artifactId>web</artifactId>
    <version>2.1.4-RELEASE</version>

    <modules>
        <module>cspc-web-common</module>
        <module>cspc-web-seller</module>
        <module>cspc-web-base</module>
        <module>cspc-web-buyer</module>
        <module>cspc-web-emall</module>
        <module>cspc-web-platform</module>
    </modules>

</project>