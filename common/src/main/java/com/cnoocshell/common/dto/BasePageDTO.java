package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
public class BasePageDTO {
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页数量")
    private Integer pageSize = 10;

    public void defaultPageIfNull() {
        if (Objects.isNull(this.pageNum)) this.pageNum = 1;
        if (Objects.isNull(this.pageSize)) this.pageSize = 10;
    }

    public void defaultPageIfNull(Integer pageNum, Integer pageSize) {
        if (Objects.isNull(this.pageNum)) this.pageNum = pageNum;
        if (Objects.isNull(this.pageSize)) this.pageSize = pageSize;
    }
}
