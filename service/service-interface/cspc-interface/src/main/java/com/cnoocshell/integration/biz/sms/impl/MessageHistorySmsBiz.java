package com.cnoocshell.integration.biz.sms.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.integration.biz.sms.IMessageHistorySmsBiz;
import com.cnoocshell.integration.dao.mapper.MessageHistorySmsMapper;
import com.cnoocshell.integration.dao.vo.MessageHistorySms;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 03/01/2020 10:50
 * @DESCRIPTION:
 */
@Service
@RequiredArgsConstructor
public class MessageHistorySmsBiz extends BaseBiz<MessageHistorySms> implements IMessageHistorySmsBiz {

    private final MessageHistorySmsMapper messageHistorySmsMapper;


    @Override
    public void saveHistory(MessageHistorySms history, List<String> mobiles) {
        if(CollUtil.isEmpty(mobiles))
            return;
        List<MessageHistorySms> list = mobiles.stream().map(v->{
            MessageHistorySms his = new MessageHistorySms();
            BeanUtil.copyProperties(history,his);
            his.setReceiverMobile(v);
            his.setMessageHistoryMqSms(getUuidGeneratorGain());
            his.setMessageHistoryMqId(ObjectUtil.defaultIfBlank(his.getMessageHistoryMqId(),""));
            return his;
        }).collect(Collectors.toList());

        messageHistorySmsMapper.insertList(list);
    }
}
