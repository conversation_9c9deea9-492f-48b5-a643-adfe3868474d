package com.cnoocshell.information.api.enums;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午3:46 21/6/28
 */
public enum QuotaOptionTypeEnum {

    PARENT("parent", "父级指标"),

    OPTION("option", "选项指标");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    QuotaOptionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QuotaOptionTypeEnum valueOfCode(String code) {
        QuotaOptionTypeEnum[] enums = values();
        for (QuotaOptionTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
