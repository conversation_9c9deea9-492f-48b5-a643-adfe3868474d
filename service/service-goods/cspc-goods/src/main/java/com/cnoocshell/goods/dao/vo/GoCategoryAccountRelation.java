package com.cnoocshell.goods.dao.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Table(name = "go_category_account_relation")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoCategoryAccountRelation {

    @Id
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(name = "category_id", length = 32)
    private String categoryId;

    @Column(name = "category_code", length = 32)
    private String categoryCode;

    @Column(name = "category_name", length = 32)
    private String categoryName;

    @Column(name = "account_id", length = 32)
    private String accountId;

    @Column(name = "account_real_name", length = 32)
    private String accountRealName;

    @Column(name = "account_role", length = 32)
    private String accountRole;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user", length = 32)
    private String updateUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user", length = 32)
    private String createUser;

    @Column(name = "del_flg")
    private Boolean delFlg;

    // Getters and Setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountRealName() {
        return accountRealName;
    }

    public void setAccountRealName(String accountRealName) {
        this.accountRealName = accountRealName;
    }

    public String getAccountRole() {
        return accountRole;
    }

    public void setAccountRole(String accountRole) {
        this.accountRole = accountRole;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Boolean getDelFlg() {
        return delFlg;
    }

    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }
}