package com.cnoocshell.base.api.dto;

import com.cnoocshell.common.dto.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ValueQueryDTO extends BasePageDTO {
    /**
     * 属性
     */
    @ApiModelProperty("属性")
    private String attribute;
    /**
     * name
     */
    @ApiModelProperty("name")
    private String name;
    /**
     * 引用代码
     */
    @ApiModelProperty("引用代码")
    private String referenceCode;
    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private String memberId;



}
