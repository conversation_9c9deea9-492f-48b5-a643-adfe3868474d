package com.cnoocshell.base.biz.impl;

import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.biz.IAttachmentInfoBiz;
import com.cnoocshell.base.dao.mapper.AttachmentInfoMapper;
import com.cnoocshell.base.dao.vo.AttachmentInfo;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentInfoBiz extends BaseBiz<AttachmentInfo> implements IAttachmentInfoBiz {

    private final AttachmentInfoMapper attachmentInfoMapper;
    private final UUIDGenerator uuidGenerator;


    @Override
    public String insertFileInfo(List<AttachmentinfoDTO> list, String operatorId) {
        List<AttachmentInfo> saveList = list.stream().map(v->{
            AttachmentInfo attachmentInfo = new AttachmentInfo();
            attachmentInfo.setAttachmentId(uuidGenerator.gain());
            attachmentInfo.setAttachmentName(v.getAttcName());
            attachmentInfo.setAttachmentPath(v.getAttcPath());
            attachmentInfo.setAttachmentSize(v.getAttcSize());
            attachmentInfo.setAttachmentType(v.getAttcType());

            setOperatorInfo(attachmentInfo,operatorId,true);
            return attachmentInfo;
        }).collect(Collectors.toList());

        if(this.insertList(saveList) > 0){
            return saveList.stream().map(AttachmentInfo::getAttachmentId).collect(Collectors.joining(","));
        }
        return null;
    }
}