package com.cnoocshell.common.excel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.dto.ExportSheetDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ExcelUtils {

    private static final Integer ZERO = 0;

    private ExcelUtils() {
        throw new IllegalStateException("ExcelUtils class");
    }

    public static final String NO_CACHE = "no-cache";
    public static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    public static final String HEADER_CONTENT_DISPOSITION = "Content-disposition";
    public static final String HEADER_CACHE_CONTROL = "Cache-Control";
    public static final String HEADER_PRAGMA = "Pragma";
    public static final String FILE_NAME = "fileName";


    @SneakyThrows
    public static <T> void exportXlsx(ExportExcelDTO excelData, HttpServletResponse response) {
        log.info("导出Excel开始 {}", excelData.getFileName());
        ExcelWriter writer = ExcelUtil.getWriter(excelData.getFileName());
        int index = 0;
        for (ExportSheetDTO sheet : excelData.getSheetList()) {
            String sheetName = CharSequenceUtil.isNotBlank(sheet.getSheetName()) ? sheet.getSheetName() : CharSequenceUtil.format("Sheet{}", index);
            Sheet workSheet = writer.setSheet(index).getSheet();
            ExcelWriter sheetWriter = null;
            if (Objects.isNull(workSheet)) {
                sheetWriter = writer.setSheet(sheetName);
            } else {
                sheetWriter = writer.renameSheet(sheetName);
            }
            handleExcelWriter(sheet.getClazz(), sheetWriter);

            List<Field> fields = getExcelFields(ReflectUtil.getFields(sheet.getClazz()));
            List<Map<String, Object>> dataMapList = new ArrayList<>();
            if (CollUtil.isNotEmpty(sheet.getDataList())) {
                for (Object data : sheet.getDataList()) {
                    dataMapList.add(getMap(data,sheet.getClazz(), fields));
                }
            }
            sheetWriter.write(dataMapList, false);
            index++;
        }

        response.setHeader(HEADER_CONTENT_DISPOSITION, CONTENT_DISPOSITION + URLEncodeUtil.encode(excelData.getFileName()));
        response.setHeader(HEADER_CACHE_CONTROL, NO_CACHE);
        response.setHeader(HEADER_PRAGMA, NO_CACHE);
        response.setHeader(FILE_NAME, Base64.encode(excelData.getFileName()));
        response.flushBuffer();

        log.info("导出Excel结束 {}", excelData.getFileName());
        writer.flush(response.getOutputStream(), true);
    }


    /**
     * @param bookStream    excel输入流
     * @param sheetIndex    sheet页索引位置 从0开始 默认0
     * @param headIndex     首行位置 从0开始 默认0
     * @param startRowIndex 读取数据首行开始索引位置 从0开始
     * @param clazz         返回结果类型
     * @return 解析workbook sheet页数据返回
     */
    @SneakyThrows
    public static <T> List<T> readXlsx(InputStream bookStream, Integer sheetIndex, Integer headIndex, Integer startRowIndex, Class<T> clazz) {
        if (Objects.isNull(bookStream) || Objects.isNull(clazz))
            return Collections.emptyList();
        if (Objects.isNull(sheetIndex)) sheetIndex = ZERO;
        if (Objects.isNull(headIndex)) headIndex = ZERO;
        if (Objects.isNull(startRowIndex)) startRowIndex = ZERO;
        ExcelReader reader = ExcelUtil.getReader(bookStream, sheetIndex);
        reader.setHeaderAlias(getHeadAlias(clazz, false));
        return reader.read(headIndex, startRowIndex, clazz);
    }

    private static <T> void handleExcelWriter(Class<T> clazz, ExcelWriter writer) {
        List<Field> excelColumns = getExcelFields(ReflectUtil.getFields(clazz));

        Map<String, String> headerMap = new LinkedHashMap<>(CollUtil.size(excelColumns));
        List<String> headerRow = new ArrayList<>();
        for (Field field : excelColumns) {
            Excel excel = field.getAnnotation(Excel.class);
            headerMap.put(field.getName(), excel.title());
            headerRow.add(excel.title());
        }
        writer.setHeaderAlias(headerMap);
        writer.writeHeadRow(headerRow);

    }

    public static Map<String, String> getHeadAlias(Class<?> clazz, boolean isWrite) {
        List<Field> excelColumns = getExcelFields(ReflectUtil.getFields(clazz));
        Map<String, String> headerMap = new LinkedHashMap<>(CollUtil.size(excelColumns));
        for (Field field : excelColumns) {
            Excel excel = field.getAnnotation(Excel.class);
            if (isWrite) {
                headerMap.put(field.getName(), excel.title());
            } else {
                headerMap.put(excel.title(), field.getName());
            }
        }
        return headerMap;
    }

    private static <T> List<Field> getExcelFields(Field[] fields) {
        List<Field> excelColumns = Arrays.stream(fields)
                .filter(v -> Objects.nonNull(v.getAnnotation(Excel.class)))
                .sorted(Comparator.comparing(v -> {
                    Excel excel = v.getAnnotation(Excel.class);
                    int index = excel.index();
                    if (Objects.isNull(index))
                        index = -1;
                    return index;
                })).collect(Collectors.toList());
        return excelColumns;
    }

    private static Map<String, Object> getMap(Object data,Class<?> clazz, List<Field> fields) {
        Map<String, Object> map = new HashMap<>();
        if (CollUtil.isEmpty(fields))
            return map;
        if(!ClassUtil.equals(clazz,data.getClass().getName(),true))
            data = BeanUtil.toBean(data,clazz);

        for (Field field : fields) {
            Object value = ReflectUtil.getFieldValue(data, field);
            Excel excel = field.getAnnotation(Excel.class);
            if (Objects.nonNull(value) && value instanceof Date && CharSequenceUtil.isNotBlank(excel.dateFormat()))
                value = DateUtil.format((Date) value, excel.dateFormat());
            map.put(field.getName(), value);
        }
        return map;
    }
}
