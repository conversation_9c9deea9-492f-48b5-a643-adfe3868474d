package com.cnoocshell.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.service.IResourceListingService;
import com.cnoocshell.goods.api.service.IResourceQueryService;
import com.cnoocshell.goods.api.service.IResourceService;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description 挂牌管理-Web端控制器，挂牌管理相关功能对外服务接口
 * @Date 30/08/2018 13:47
 */
@Slf4j
@RestController
@RequestMapping("/resource")
@Api(tags={"ResourceController"},description = "web端控制器，挂牌管理相关功能对外服务接口")
public class ResourceController {


    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IResourceQueryService resourceQueryService;
    @Autowired
    private IResourceListingService resourceListingService;

    @ApiOperation("卖家挂牌管理列表")
    @PostMapping(value="/pageResourceSeller")
    public ItemResult<PageInfo<ResourceSellerDTO>> pageResourceSeller(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody ReqResourceSellerDTO reqResourceSellerDTO){
        reqResourceSellerDTO.setSellerId(loginInfo.getMemberId());
        //非主账号，才添加数据权限过滤 bug fixed 10497 2021.7.29 
        if( loginInfo.getAccountType() == null ) {
            reqResourceSellerDTO.setRegionList(loginInfo.getSaleRegionIdList());
        }
        log.info("reqResourceSellerDTO,req:{}", reqResourceSellerDTO.toString());
        PageInfo<ResourceSellerDTO> pageInfo = resourceQueryService.pageResourceSeller(reqResourceSellerDTO);
        
        return  new ItemResult<PageInfo<ResourceSellerDTO>>(pageInfo);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @ApiOperation("资源是否可选择该区域定价")
    @GetMapping(value="/ifCanSelectAreaPrice")
    public ItemResult<Boolean> ifCanSelectAreaPrice(@ApiParam(name = "sellerId", value = "卖家Id") @Valid @RequestParam("sellerId") String sellerId,
                                                    @ApiParam(name = "goodsId", value = "商品Id") @Valid @RequestParam("goodsId") String goodsId,
                                                    @ApiParam(name = "level", value = "层级") @Valid @RequestParam("level") Integer level){
        return  new ItemResult<Boolean>(resourceService.ifCanSelectAreaPrice(sellerId,goodsId,level));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @ApiOperation("查询卖家定价模式")
    @GetMapping(value="/getPriceMode")
    public ItemResult<List<PriceModeDTO>> getPriceMode(@ApiIgnore LoginInfo loginInfo,
                                                       @ApiParam(name = "sellerId", value = "卖家Id") @Valid @RequestParam("sellerId") String sellerId){
        List<PriceModeDTO> result = resourceService.getPriceMode(loginInfo.getMemberId());
        return new ItemResult<List<PriceModeDTO>>(result);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @ApiOperation("查询商品的资源仓库详情")
    @GetMapping(value="/getGoodsResourceStore")
    public ItemResult<GoodsPriceDTO> getGoodsResourceStore(@ApiIgnore LoginInfo loginInfo,
                                                           @ApiParam(name = "level", value = "层级") @Valid @RequestParam("level") Integer level,
                                                           @ApiParam(name = "goodsId", value = "商品Id") @Valid @RequestParam("goodsId") String goodsId,
                                                           @ApiParam(name = "sellerId", value = "卖家Id") @Valid @RequestParam("sellerId") String sellerId){
        checkLogin(loginInfo);
        GetGoodsResourceStoreDTO getGoodsResourceStoreDTO = new GetGoodsResourceStoreDTO();
        getGoodsResourceStoreDTO.setLevel(level);
        getGoodsResourceStoreDTO.setGoodsId(goodsId);
        getGoodsResourceStoreDTO.setSellerId(loginInfo.getMemberId());
        getGoodsResourceStoreDTO.setAccountId(loginInfo.getAccountId());
        getGoodsResourceStoreDTO.setFilterIdList(loginInfo.getSaleRegionIdList());
        log.info("===getGoodsResourceStore=req={}", JSON.toJSONString(getGoodsResourceStoreDTO));
        GoodsPriceDTO result = resourceService.getGoodsResourceStore(getGoodsResourceStoreDTO);
        return new ItemResult<GoodsPriceDTO>(result);
    }

    @ApiOperation("创建资源(立即挂牌)")
    @PostMapping(value="/createResource")
    public ItemResult<Boolean> createResource(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody CreateResourceDTO reqCreateResourceDTO){
        reqCreateResourceDTO.setSellerId(loginInfo.getMemberId());
        reqCreateResourceDTO.setSalesId(loginInfo.getAccountId());
        reqCreateResourceDTO.setContactPhone(loginInfo.getMobile());
        if( reqCreateResourceDTO.getIfUp() != null && reqCreateResourceDTO.getIfUp() && reqCreateResourceDTO.getFixUptime() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"定时上架时间不可为空");
        }
        resourceListingService.createResource(reqCreateResourceDTO, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @ApiOperation("创建资源(预约挂牌)")
    @PostMapping(value="/createResourceTask")
    public ItemResult<Boolean> createResourceTask(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody CreateResourceDTO reqCreateResourceDTO){
        reqCreateResourceDTO.setSellerId(loginInfo.getMemberId());
        reqCreateResourceDTO.setSalesId(loginInfo.getAccountId());
        reqCreateResourceDTO.setContactPhone(loginInfo.getMobile());
        reqCreateResourceDTO.setIfUp(true);
        if( reqCreateResourceDTO.getFixUptime() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"定时上架时间不可为空");
        }
        resourceListingService.createResource(reqCreateResourceDTO, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @ApiOperation("批量更新资源")
    @PostMapping(value="/batchUpdateResource")
    public ItemResult<Boolean> batchUpdateResource(@ApiIgnore LoginInfo loginInfo,
                                                   @RequestBody BatchUpdateResourceDTO batchUpdateResourceDTO){
        batchUpdateResourceDTO.setSellerId(loginInfo.getMemberId());
        batchUpdateResourceDTO.setOperator(loginInfo.getAccountId());
        resourceListingService.batchUpdateResource(batchUpdateResourceDTO);
        return new ItemResult<>(true);
    }

    @ApiOperation("修改资源")
    @PostMapping(value="/updateResource")
    public ItemResult<Boolean> updateResource(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody UpdateResourceDTO resourceDTO){
        checkLogin(loginInfo);
        resourceListingService.updateResource(resourceDTO,loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);
    }

    @ApiOperation("删除资源")
    @PostMapping(value="/deleteResource")
    public ItemResult<Boolean> deleteResource(@ApiIgnore LoginInfo loginInfo,
                                              @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                              @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator)throws Exception{
        checkLogin(loginInfo);
        if (StringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceListingService.deleteResource(resourceIds,operator);
        return new ItemResult<Boolean>(true);
    }

    @ApiOperation("获取资源详情，订单不可使用此接口来获取资源详情")
    @GetMapping(value="/getResourceDetail")
    public  ItemResult<ResourceDTO> getResourceDetail(@ApiParam(name = "resourceId", value = "资源Id") @Valid @RequestParam("resourceId") String resourceId){
        return  new ItemResult<ResourceDTO>(resourceQueryService.getComplexResourceDetail(resourceId));
    }

    @ApiOperation("获取改量改价列表")
    @GetMapping(value="/getChangePriceAndNumList")
    public  ItemResult<List<ChangePriceAndNumDTO>> getChangePriceAndNumList(@ApiIgnore LoginInfo loginInfo,
                                                                            @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                                                            @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        if (StringUtils.isEmpty(operator)) {
            operator = loginInfo.getMemberId();
        }
        return  new ItemResult<List<ChangePriceAndNumDTO>>(resourceService.getChangePriceAndNumList(resourceIds,operator));
    }


    @ApiOperation("撤销未生效资源修改")
    @PostMapping(value="/repealResourceUpdate")
    public ItemResult<Boolean> repealResourceUpdate(@ApiIgnore LoginInfo loginInfo,
                                                    @ApiParam(name = "resourceHistoryId", value = "资源历史Id") @Valid @RequestParam("resourceHistoryId") String resourceHistoryId,
                                                    @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        if (StringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceService.repealResourceUpdate(resourceHistoryId,operator);
        return new ItemResult<Boolean>(true);
    }

    @ApiOperation("批量平台强制上架")
    @PostMapping(value="/onSaleResourceBatchPlatform")
    public ItemResult<Boolean> onSaleResourceBatchPlatform(@ApiIgnore LoginInfo loginInfo,
                                                           @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                                           @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        ManualOnSaleDTO manualOnSaleDTO = new ManualOnSaleDTO();
        manualOnSaleDTO.setResourceIds(resourceIds);
        manualOnSaleDTO.setOperator(loginInfo.getAccountId());
        manualOnSaleDTO.setSellerId(loginInfo.getMemberId());
        resourceListingService.mandatoryOnSale(manualOnSaleDTO);
        return  new ItemResult<Boolean>(true);
    }

    @ApiOperation("批量平台强制下架")
    @PostMapping(value="/offSaleResourceBatchPlatform")
    public ItemResult<Boolean> offSaleResourceBatchPlatform(@ApiIgnore LoginInfo loginInfo,
                                                            @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                                            @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        if (StringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceListingService.mandatoryOffSale(resourceIds,operator);
        return  new ItemResult<Boolean>(true);
    }

    @ApiOperation("批量上架资源")
    @PostMapping(value="/onSaleResourceBatch")
    public ItemResult<Boolean> onSaleResourceBatch(@ApiIgnore LoginInfo loginInfo,
                                                   @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                                   @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        ManualOnSaleDTO manualOnSaleDTO = new ManualOnSaleDTO();
        manualOnSaleDTO.setResourceIds(resourceIds);
        manualOnSaleDTO.setOperator(loginInfo.getAccountId());
        manualOnSaleDTO.setSellerId(loginInfo.getMemberId());
        resourceListingService.manualOnSale(manualOnSaleDTO);
        return  new ItemResult<Boolean>(true);
    }

    @ApiOperation("批量下架/撤牌资源")
    @PostMapping(value="/offSaleResourceBatch")
    public ItemResult<String> offSaleResourceBatch(@ApiIgnore LoginInfo loginInfo,
                                                    @ApiParam(name = "resourceIds", value = "资源Id集合") @Valid @RequestParam("resourceIds") List<String> resourceIds,
                                                    @ApiParam(name = "operator", value = "操作人") @RequestParam("operator") String operator){
        checkLogin(loginInfo);
        if(loginInfo.getMemberId() == null){
            log.error("not find memberId");
            throw new BizException(BasicCode.TOKEN_ERROR);
        }
        if (StringUtils.isEmpty(operator)) {
            operator = loginInfo.getMemberId();
        }
        return resourceListingService.manualOffSale(resourceIds,operator);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @ApiOperation("提示已挂牌资源")
    @PostMapping(value="/promptOnsaleResource")
    public ItemResult<List<PromptOnsaleResourceDTO>> promptOnsaleResource(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody ReqPromptOnsaleResourceDTO resourceDTO){
        checkLogin(loginInfo);
        resourceDTO.setRegionList(loginInfo.getSaleRegionIdList());
        log.info("promptOnsaleResource,req:{}", resourceDTO.toString());
        return  new ItemResult<List<PromptOnsaleResourceDTO>>(resourceService.promptOnsaleResource(resourceDTO));
    }

    private void checkLogin(LoginInfo loginInfo){
        if ( loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId()) ){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
        if (StringUtils.isBlank(loginInfo.getMemberId())){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }

}
