package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@Data
public class AccountLoginDTO implements Serializable {

	private static final long serialVersionUID = -4344323892105787518L;

	/**
	 * 登录名
	 */
	@ApiModelProperty("登录名")
	@NotBlank
	private String loginName;

	/**
	 * 密码
	 */
	@ApiModelProperty("密码")
	private String password;

	/**
	 * 微信的openId
	 */
	@ApiModelProperty("微信的openId")
	private String openId;

	/**
	 * 手机唯一编码
	 */
	@ApiModelProperty("手机唯一编码")
	private String driverToken;

	/**
	 * 微信code
	 */
	@ApiModelProperty("微信code")
	private String code;

	/**
	 * 平台类型： windows ios android
	 */
	@ApiModelProperty("平台类型 windows ios android")
	private String platformType;

	/**
	 * 客户端ip
	 */
	@ApiModelProperty("客户端ip")
	private String ip;

	/**
	 * 会话id
	 */
	@ApiModelProperty("会话id")
	private String sessionId;

	/**
	 * app名称
	 */
	@ApiModelProperty("app名称")
	private String loginType;

	/**
	 * 客户端网卡物理地址
	 */
	@ApiModelProperty("客户端网卡物理地址")
	private String mac;

	/**
	 * 终端
	 */
	@ApiModelProperty("终端")
	private String terminal;

	/**
	 * 系统
	 */
	@ApiModelProperty("系统")
	private String os;

	/**
	 * 系统版本
	 */
	@ApiModelProperty("系统版本")
	private String osVersion;

	/**
	 * 机器品牌
	 */
	@ApiModelProperty("机器品牌")
	private String deviceBrand;

	/**
	 * 安卓手机唯一识别码
	 */
	@ApiModelProperty("安卓手机唯一识别码")
	private String imei;

	/**
	 * 浏览器信息
	 */
	@ApiModelProperty("浏览器信息")
	private String userAgent;


}