<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.XxlJobInfoMapper">
    <update id="stopJob">
        UPDATE xxl_job.xxl_job_info tb1
        left join xxl_job.xxl_job_group tb2 on tb1.job_group = tb2.id
        set tb1.trigger_status =0,
        tb1.trigger_last_time =0,
        tb1.trigger_next_time =0
        where tb1.executor_handler IN
        <foreach collection="param.executorHandlers" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        and tb1.executor_param = #{param.executorParam}
        and tb2.app_name = #{param.appName}
    </update>
</mapper>