package com.cnoocshell.order.dao.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 买家询价
 */
@Table(name = "enquiry_buyer")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBuyer {

    @Id
    @Column(name = "id")
    private String id; // 主键ID

    @Column(name = "enquiry_no")
    private String enquiryNo; // 询价场次编号

    @Column(name = "member_code")
    private String memberCode; // 买家编码

    @Column(name = "member_name")
    private String memberName; // 买家名称

    @Column(name = "goods_code")
    private String goodsCode; // 商品编码

    @Column(name = "goods_name")
    private String goodsName; // 商品名称

    @Column(name = "participation_status")
    private String participationStatus; // 参与状态：TODO未参与、DONE已参与

    @Column(name = "del_flg")
    private boolean delFlg; // 删除标志

    @Column(name = "create_user")
    private String createUser; // 创建人

    @Column(name = "create_time")
    private Date createTime; // 创建时间

    @Column(name = "update_user")
    private String updateUser; // 修改人

    @Column(name = "update_time")
    private Date updateTime; // 修改时间

}