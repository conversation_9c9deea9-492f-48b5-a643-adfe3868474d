package com.cnoocshell.base.service;

import com.cnoocshell.base.api.dto.*;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 *值集
 */
public interface IValueSetService {
	/**
	 * 创建值集
	 * @param valueSet 值集DTO
	 * @param operator 操作人id
	 * @return void
	 */
	void createValueSet(ValueSetDTO valueSet,String operator);

	/**
	 * 创建值集项
	 * @param valueSet 值集DTO
	 * @param operator 操作人id
	 * @return void
	 */
	void createValueSetOption(ValueSetOptionDTO valueSet,String operator);

	/**
	 * 创建值集和值集项
	 * @param valueAndOptionDTO 值集和值集项DTO
	 * @param operator 操作人id
	 */
	void createValueAndOption(ValueAndOptionDTO valueAndOptionDTO, String operator);

	/**
	 * 更新值集和值集项
	 * @param valueAndOptionDTO 值集和值集项DTO
	 * @param operator 操作人id
	 */
	void updateValueAndOption(ValueAndOptionDTO valueAndOptionDTO,String operator);
	/**
	 * 该值集是否可修改
	 * @param valueSetId 值集id
	 * @return 值集是否可修改
	 */
	Boolean canValueSetEdit(String valueSetId);

	/**
	 * 通过值集id删除值集
	 * @param valueSetId 值集id
	 * @param operator 操作人id
	 */
	void deleteValueSet(String valueSetId,String operator);

	/**
	 * 通过值集项id删除值集项
	 * @param valueSetId 值集id
	 * @param operator 操作人id
	 */
	void deleteValueSetOption(String valueSetId,String operator);

	/**
	 * 修改值集
	 * @param valueSet 值集id
	 * @param operator 操作人id
	 */
	void updateValueSet(ValueSetDTO valueSet,String operator);

	/**
	 * 修改值集项
	 * @param valueSetOptions 修改值集项DTO的集合
	 * @param operator 操作人id
	 */
	void updateValueSetOptions(List<ValueSetOptionDTO> valueSetOptions,String operator);

	/**
	 * 值集分页查询
	 * @param valueQueryDTO 值集项查询DTO
	 * @return 值集项DTO
	 */
	PageInfo<ValueSetDTO> pageValueSet(ValueQueryDTO valueQueryDTO);

	/**
	 * 根据值集id查询值集项
	 * @param valueSetId 值集id
	 * @return 值集项DTO的集合
	 */
	List<ValueSetOptionDTO> listValueSetOption(String valueSetId);

	/**
	 * 根据值集id得到值集
	 * @param valueSetId 值集项id
	 * @return 值集项DTO
	 */
	ValueSetDTO getValueSet(String valueSetId);

	/**
	 * 根据值集项id得到值集项
	 * @param valueSetOptionId 值集项id
	 * @return 值集项DTO
	 */
	ValueSetOptionDTO getValueSetOption(String valueSetOptionId);

	/**
	 * 根据父节点id获取子节点树
	 * @param parentOptionId 父节点id
	 * @return 值集树DTO
	 */
	ValueSetTreeDTO getChildValueSetOption(String parentOptionId);

	/**
	 * 根据code获取值集子节点树
	 * @param referenceCode 引用代码
	 * @return 值集树DTO
	 */
	ValueSetTreeDTO getValueSetByReferenceCode(String referenceCode);
	/**
	 * 根据code获取值集子节点树,并通过关键字过滤，最多返回20条记录
	 * @param referenceCode 引用代码
	 * @param keyWord 关键字
	 * @return 值集项DTO的集合
	 */
	List<ValueSetOptionDTO> getValueSetByReferenceCodeAndKeyWord(String referenceCode,String keyWord);

//	/**
//	 * 根据父节点id获取子节点树（键值对）
//	 * @param parentOptionId
//	 * @return
//	 */
//	List<Map<String,String>> getChildSimpleValueSetOptions(String parentOptionId);

	/**
	 * 通过key查询值集合
	 * @param key 关键字
	 * @return 值集项DTO的集合
	 */
	List<ValueSetOptionDTO> findOptionByKey(String key);

	/**
	 * 通过key的集合查询值集合
	 * @param keys 关键字集合
	 * @return 值集项DTO的集合
	 */
	List<ValueSetOptionDTO> batchFindOption(List<String> keys);

	/**
	 * 根据角色类型查找关联的平台（JZ0006）
	 * @param roleType
	 * @return
	 */
	List<String[]> findPlatform(String roleType);

	List<String[]> findPlatformByRoleType(List<String> roleTypeList);
	Set<String> findPlatformByRole(Set<String> roleTypeList,ValueSetTreeDTO  jz1,ValueSetTreeDTO  jz6,boolean underLineToHump);
	List<String[]> findPlatformByRole(List<Integer> roleIds);

	/**
	 * 根据平台查询角色类型
	 * @param platformList
	 * @return
	 */
	Set<String> findRoleTypeByPlatform(Collection<String> platformList);
	/**
	 * 刷新缓存
	 */
	void reloadRedisCache();

	List<ValueSetDTO> getValueTreeByCodes(List<String> referenceCodes);

}