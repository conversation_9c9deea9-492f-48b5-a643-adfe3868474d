package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@Data
public class ChangeAccountTypeDTO implements Serializable {

	private static final long serialVersionUID = -4047841629067432182L;

	/**
	 * 会员id
	 */
	@ApiModelProperty("会员id")
	private String memberId;

	/**
	 * 账户id
	 */
	@ApiModelProperty("账户id")
	@NotBlank
    private String accountId;

	/**
	 * 操作人AccountId
	 */
	@ApiModelProperty("操作人AccountId")
	private String operatorId;

	/**
	 * 操作人客户端ip
	 */
	@ApiModelProperty("操作人客户端ip")
	private String operatorIP;

	@ApiModelProperty(value = "是否卖家",hidden = true)
	private Boolean isSeller = false;

}