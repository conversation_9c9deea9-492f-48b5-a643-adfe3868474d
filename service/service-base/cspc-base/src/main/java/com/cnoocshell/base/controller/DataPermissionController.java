package com.cnoocshell.base.controller;

import com.cnoocshell.base.api.dto.report.AccountReportQueryDTO;
import com.cnoocshell.base.api.dto.report.AccountReportResultDTO;
import com.cnoocshell.base.biz.IDataPermissionBiz;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = {"DataPermission"}, description = "数据权限")
@RestController
@RequestMapping("/dataPermission")
@RequiredArgsConstructor
public class DataPermissionController {
    private final IDataPermissionBiz iDataPermissionBiz;

    @ApiOperation("客户账号报表 根据负责产品查询")
    @PostMapping("/accountReport")
    PageInfo<AccountReportResultDTO> accountReport(@RequestBody AccountReportQueryDTO param) {
        return iDataPermissionBiz.accountReport(param);
    }
}
