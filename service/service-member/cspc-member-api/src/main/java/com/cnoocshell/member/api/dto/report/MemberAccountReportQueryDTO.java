package com.cnoocshell.member.api.dto.report;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MemberAccountReportQueryDTO extends OperatorDTO {
    @ApiModelProperty(value = "分页参数 是否分页",notes = "控制分页列表和导出查询是否分页处理",hidden = true)
    private Boolean needPage = Boolean.TRUE;
    @ApiModelProperty(value = "分页参数 页码")
    private Integer pageNum = 1;
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "客户编码", notes = "模糊查询 前端传值")
    private String crmCode;

    @ApiModelProperty(value = "会员名称", notes = "模糊查询")
    private String memberName;

    /**
     * MemberStatusEnum
     * 会员启用禁用状态 在查询时会将该数据做条件转换查询
     */
    @ApiModelProperty(value = "启用禁用状态", notes = "启用禁用状态 201：禁用状态 其它状态为正常  1：禁用 0：启用 单选")
    private Integer ableStatus;

    /**
     *对于 客户账号列表只会用到1和2
     */
    @ApiModelProperty(value = "账号类别",notes = "0个人/1企业主账号/2企业子账号/运维账号 单选")
    private Integer accountType;

    @ApiModelProperty(value = "账号状态",notes = "账号状态（ 0 可用 1 禁用 ）")
    private Integer accountStatus;

    @ApiModelProperty(value = "姓名",notes = "模糊查询")
    private String realName;

    @ApiModelProperty(value = "手机号",notes = "模糊查询")
    private String mobile;

    @ApiModelProperty(value = "角色",notes = "模糊查询")
    private String role;

    @ApiModelProperty(value = "产品分类", notes = "多选匹配")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "产品名称", notes = "模糊匹配")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码", notes = "模糊查询 前端传值")
    private String sapMaterialCode;
    @ApiModelProperty(value = "SAP物料编码模糊匹配到的产品编码", hidden = true)
    private List<String> goodsCodeBySapMaterialCode;

    /**
     * 字典编码：VS_SALES_CHANNEL
     */
    @ApiModelProperty(value = "销售渠道", notes = "字典数据 多选匹配")
    private List<String> saleChannel;

    @ApiModelProperty(value = "数据权限 商品编码",hidden = true)
    private List<String> dataPermissionGoodsCodes;
}
