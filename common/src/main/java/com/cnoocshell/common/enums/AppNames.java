package com.cnoocshell.common.enums;

public enum AppNames {

    MICRO_SERVICE_BASE(AppName.SERVICE_BASE, "microServiceBase", "", "基础数据中心"),
    MICRO_SERVICE_MEMBER(AppName.SERVICE_MEMBER, "member", "", "会员中心"),
    MICRO_SERVICE_GOODS(AppName.SERVICE_GOODS, "goods", "", "商品中心"),
    MICRO_SERVICE_ORDER(AppName.SERVICE_ORDER, "order", "", "订单中心"),
    MICRO_SERVICE_INFORMATION(AppName.SERVICE_INFORMATION, "information", "", "资讯中心"),
    MICRO_SERVICE_LOGISTICS(AppName.SERVICE_LOGISTICS, "logistics", "", "物流中心"),
    //物流监控、路线规划、报警、电子围栏
    MICRO_SERVICE_TRACE(AppName.SERVICE_TRACE, "trace", "", "轨迹中心"),
    MICRO_SERVICE_INTERFACE_OPEN(AppName.SERVICE_OPEN, "open", "", "接口中心"),
    MICRO_SERVICE_INTERFACE(AppName.SERVICE_INTERFACE, "interface", "", "接口中心"),
    MICRO_SERVICE_PAY(AppName.SERVICE_PAY, "pay", "", "支付中心"),
    MICRO_SERVICE_PRICE(AppName.SERVICE_PRICE, "price", "", "价格中心"),
    MICRO_SERVICE_REPORT(AppName.SERVICE_REPORT, "report", "", "报表中心"),

    MICRO_SERVICE_STORAGE("service-storage", "storage", "", "仓储中心"),

    WEB_SERVICE_BASE(AppName.WEB_BASE, "base", "/baseApi", "授权认证"),
    WEB_SERVICE_BUYER(AppName.WEB_BUYER, "buyer", "/buyerApi", "买家中心"),
    WEB_SERVICE_SELLER(AppName.WEB_SELLER, "seller", "/sellerApi", "卖家中心"),
    WEB_SERVICE_CARRIER(AppName.WEB_CARRIER, "carrier", "/carrierApi", "承运商中心"),
    WEB_SERVICE_CARRIERAPP(AppName.APP_CARRIER, "carrier_app", "/carrierAppApi", "承运商app"),
    WEB_SERVICE_OPENAPI(AppName.WEB_OPEN_API, "open_api", "/openApi", "开放服务"),
    WEB_SERVICE_DRIVER(AppName.APP_DRIVER, "driver_app", "/driverAppApi", "司机app"),
    WEB_SERVICE_BUYERAPP(AppName.APP_BUYER, "buyer_app", "/buyerAppApi", "买家app"),
    WEB_SERVICE_SELLERAPP(AppName.APP_SELLER, "seller_app", "/sellerAppApi", "卖家app"),
    //接收gps信息
    WEB_SERVICE_TRACE_COLLECT(AppName.TRACE_COLLECT, "tractCollect", "", "轨迹中心"),
    WEB_SERVICE_EMALL(AppName.E_MALL, "emall", "/emallApi", "交易大厅"),
    WEB_SERVICE_PLATFORM(AppName.WEB_PLATFORM, "platform", "/platformApi", "平台中心");

    private final String code;
    private final String platform;
    private final String apiRoutePrefix;
    private final String appName;

    AppNames(String code, String platform, String apiRoutePrefix, String appName) {
        this.code = code;
        this.platform = platform;
        this.apiRoutePrefix = apiRoutePrefix;
        this.appName = appName;
    }

    public String getCode() {
        return code;
    }

    public String getAppName() {
        return appName;
    }

    public String getPlatform() {
        return platform;
    }

    public String getApiRoutePrefix() {
        return apiRoutePrefix;
    }

    public static AppNames getByCode(String code) {
        for (AppNames value : AppNames.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static AppNames getByAlias(String alias) {
        for (AppNames value : AppNames.values()) {
            if (value.getPlatform().equals(alias)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 是否中心层服务
     */
    public static boolean isMicroService(String code) {
        for (AppNames value : AppNames.values()) {
            if (value.getCode().equals(code)) {
                return value.name().startsWith("MICRO_SERVICE_");
            }
        }
        return false;
    }

    public static boolean isMicroService(AppNames appNames) {
        return appNames != null && appNames.name().startsWith("MICRO_SERVICE_");
    }

    /**
     * 是否WEB层服务
     */
    public static boolean isWebService(String code) {
        for (AppNames value : AppNames.values()) {
            if (value.getCode().equals(code)) {
                return value.name().startsWith("WEB_SERVICE_");
            }
        }
        return false;
    }

    public static boolean isWebService(AppNames appNames) {
        return appNames != null && appNames.name().startsWith("WEB_SERVICE_");
    }
}
