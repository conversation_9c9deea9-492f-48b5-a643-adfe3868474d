package com.cnoocshell.goods.dao.vo;

import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;


@Table(name = "go_goods_category_relation")
public class GoodCategoryRelation {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /**
     * 标准商品id
     */
    @Column(name = "goods_id")
    private String goodId;
    /**
     * 分类id
     */
    @Column(name = "category_id")
    private String categoryId;
    /**
     * 分类级联id example 1级,2级,3级,4级
     */
    @Column(name = "category_string")
    private String categoryString;
    /**
     * 分类名称
     */
    @Column(name = "category_name")
    private String categoryName;
    /**
     * 分类全名称
     */
    @Column(name = "category_full_name")
    private String categoryFullName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGoodId() {
        return goodId;
    }

    public void setGoodId(String goodId) {
        this.goodId = goodId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryString() {
        return categoryString;
    }

    public void setCategoryString(String categoryString) {
        this.categoryString = categoryString;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryFullName() {
        return categoryFullName;
    }

    public void setCategoryFullName(String categoryFullName) {
        this.categoryFullName = categoryFullName;
    }

    public static GoodCategoryRelation from(GoodCategoryRelationDto dto) {
        GoodCategoryRelation relation = new GoodCategoryRelation();
        relation.setGoodId(dto.getGoodId());
        relation.setCategoryId(dto.getCategoryId());
        relation.setCategoryName(dto.getCategoryName());
        relation.setCategoryString(dto.getCategoryString());
        relation.setCategoryFullName(dto.getCategoryFullName());
        return relation;
    }

    public static List<GoodCategoryRelation> from(List<GoodCategoryRelationDto> dtos) {
        return dtos.stream().map(GoodCategoryRelation::from).collect(Collectors.toList());
    }
}
