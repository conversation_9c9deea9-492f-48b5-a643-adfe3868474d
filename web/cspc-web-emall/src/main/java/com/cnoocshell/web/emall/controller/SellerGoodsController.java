package com.cnoocshell.web.emall.controller;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: <EMAIL>
 * @Description  PlatformGoodsController
 * @date   2018年8月20日 上午10:29:31
 */
@Slf4j
@RestController
@RequestMapping("/sellerGoods")
@Api(tags={"SellerGoodsController"},description = "商品")
public class SellerGoodsController {

    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IGoodsCategoryService goodsCategoryService;

    private static final String LOG_INFO_FORMAT = "/findBaseGoodsLikeCategoryCode,req:{}";

    @ApiOperation("分页查看卖家商品列表")
    @PostMapping(value="/pageSellerGoods")
    public ItemResult<PageInfo<SellerGoodsDTO>> pageSellerGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody PageSellerGoodsDTO pageSellerGoodsDTO){
        checkLogin(loginInfo);
        log.info("/pageSellerGoods,req:{}", pageSellerGoodsDTO);
        pageSellerGoodsDTO.setSellerId(loginInfo.getMemberId());
        return goodsService.pageSellerGoods(pageSellerGoodsDTO);
    }

    @ApiOperation(value="取得商品详情")
    @GetMapping(value = "/getGoodsInfo")
    public ItemResult<GoodsDTO> getGoodsInfo(@ApiParam(name = "goodsId", value = "商品Id") String goodsId) {
    	log.info("/getGoodsInfo-goodsId_{}", goodsId);
    	return goodsService.getGoodsInfo(goodsId);
    }

    @ApiOperation(value="搜索商品")
    @GetMapping(value = "/findGoods")
    public ItemResult<PageInfo<GoodsDTO>> findGoods(@ApiIgnore LoginInfo loginInfo,
                                                    @ApiParam(name = "name", value = "商品名称") String name,
                                                    @ApiParam(name = "goodsStatus", value = "商品状态") Integer goodsStatus,
                                                    @ApiParam(name = "categoryType", value = "商品品类类型") Integer categoryType,
                                                    @ApiParam(name = "goodsType", value = "商品类型") Integer goodsType,
                                                    @ApiParam(name = "category", value = "商品品类") String category,
                                                    @ApiParam(name = "sellernm", value = "卖家名称") String sellernm,
                                                    @ApiParam(name = "keywords", value = "关键字") String keywords,
                                                    @ApiParam(name = "pageSize", value = "每页条数") Integer pageSize,
                                                    @ApiParam(name = "pageNum", value = "当前页码") Integer pageNum) {
    	log.info("/findGoods-{},{},{},{},{},{},{},{},{}",
    			name, goodsStatus, categoryType, goodsType, category, sellernm, keywords, pageSize, pageNum);
    	//sellernm -> sellerId
    	String sellerId = "";
    	return goodsService.findGoods(name, goodsStatus, categoryType, goodsType, category, loginInfo.getMemberId(), keywords, pageSize, pageNum);
    }

    @ApiOperation(value="选择SPU（平台商品）")
    @GetMapping(value = "/findBaseGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findBaseGoodsLikeName(@ApiIgnore LoginInfo loginInfo,
                                                            @ApiParam(name = "name", value = "商品名称") String name,
                                                            @ApiParam(name = "categoryType", value = "商品品类") Integer categoryType) {
    	log.info("/findBaseGoodsLikeName-name_{} categoryType_{}", name, categoryType);
    	return goodsService.findBaseGoodsLikeName(name, categoryType,loginInfo.getMemberId());
    }

    @ApiOperation(value="选择SPU（平台商品）和卖家商品")
    @GetMapping(value = "/findUserAndBaseGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findUserAndBaseGoodsLikeName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "name", value = "商品名称") String name,@ApiParam(name = "categoryType", value = "商品品类") Integer categoryType ) {
    	log.info("/findBaseGoodsLikeName-name_{} categoryType_{}", name, categoryType);
    	return goodsService.findUserAndBaseGoodsLikeName(name, loginInfo.getMemberId());
    }

    @ApiOperation(value = "商品通用查询")
    @PostMapping(value = "/goodsCommonQuery")
    public ItemResult<PageData<GoodsDTO>> goodsCommonQuery(@ApiIgnore LoginInfo loginInfo, @RequestBody PageQuery<GoodsQueryCondDTO> pageQuery) throws Exception {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        GoodsQueryCondDTO goodsQueryCondDTO = pageQuery.getQueryDTO();
        if (goodsQueryCondDTO == null) {
            goodsQueryCondDTO = new GoodsQueryCondDTO();
            pageQuery.setQueryDTO(goodsQueryCondDTO);
        }
        goodsQueryCondDTO.setSellerId(loginInfo.getMemberId());
        if (StringUtils.isNotEmpty(goodsQueryCondDTO.getGoodsNameLike())) {
            goodsQueryCondDTO.setGoodsNameLike(goodsQueryCondDTO.getGoodsNameLike().trim());
        }
        return goodsService.goodsCommonQuery(pageQuery);
    }


    @ApiOperation(value="通过spu获取sku")
    @PostMapping(value = "/findSkuBySpuAndAttr")
    public ItemResult<List<GoodsDTO>> findSkuBySpuAndAttr(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsQueryDTO goodsQueryDTO) {
    	log.info("/findSkuBySpuAndAttr-{}", JSON.toJSONString(goodsQueryDTO));
    	goodsQueryDTO.setSellerId(loginInfo.getMemberId());
    	return goodsService.findSkuBySpuAndAttr(goodsQueryDTO);
    }

    @ApiOperation(value="获取spu属性")
    @GetMapping(value = "/findSpuAttrRange")
    public ItemResult<List<GoodsCategoryAttrDTO>> findSpuAttrRange(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "spuId", value = "spu属性id") String spuId) {
    	log.info("/getSpuAttrRange-spuId_{}", spuId);
    	long start = System.currentTimeMillis();
        ItemResult<List<GoodsCategoryAttrDTO>> categoryAttrByGoodsId = goodsService.getCategoryAttrByGoodsId(spuId);
        long end = System.currentTimeMillis();
        log.info("--findSpuAttrRange--spend time {}ms",(end-start));
        return categoryAttrByGoodsId;
    }

    @ApiOperation(value="查询有效的卖家商品")
    @GetMapping(value = "/findUserGoods")
    public ItemResult<List<GoodsDTO>> findUserGoods(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "name", value = "商品名称") String name,@ApiParam(name = "categoryType", value = "商品品类") Integer categoryType) {
    	log.info("/findUserGoods-name_{} categoryType_{}", name, categoryType);
    	return goodsService.findUserGoods(name, null, loginInfo.getMemberId(), categoryType);
    }

    @ApiOperation(value="模糊查询有效的卖家商品")
    @GetMapping(value = "/findUserGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findUserGoodsLikeName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "name", value = "商品名称") String name,@ApiParam(name = "categoryType", value = "商品品类") Integer categoryType) {
    	log.info("/findUserGoodsLikeName-name_{} categoryType_{}", name, categoryType);
    	return goodsService.findUserGoodsLikeName(name, loginInfo.getMemberId(), categoryType);
    }

    @ApiOperation(value="创建商品")
    @PostMapping(value = "/createGoods")
    public ItemResult<GoodsDTO> createGoods(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsDTO goodsDTO) {
    	log.info("/createGoods-goodsDTO_{} operator_{}", JSON.toJSONString(goodsDTO), loginInfo.getAccountId());
    	goodsDTO.setSellerId(loginInfo.getMemberId());
    	if(goodsDTO.getGoodsType() == null){
    		goodsDTO.setGoodsType(2);
    	}
    	return goodsService.create(goodsDTO, loginInfo.getAccountId());
    }

    @ApiOperation(value="创建商品List")
    @PostMapping(value = "/createGoodsList")
    public ItemResult<List<GoodsDTO>> createGoodsList(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsDTO goodsDTO) {
    	log.info("/createGoodsList-goodsDTO_{} operator_{}", JSON.toJSONString(goodsDTO), loginInfo.getAccountId());
    	goodsDTO.setSellerId(loginInfo.getMemberId());
    	if(goodsDTO.getGoodsType() == null){
    		goodsDTO.setGoodsType(2);
    	}
    	return goodsService.createList(goodsDTO, loginInfo.getAccountId());
    }

    @ApiOperation(value="更新商品")
    @PostMapping(value = "/updateGoods")
    public ItemResult<GoodsDTO> updateGoods(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsDTO goodsDTO) {
    	log.info("/updateGoods-goodsDTO_{} operator_{}", JSON.toJSONString(goodsDTO), loginInfo.getAccountId());
    	return goodsService.update(goodsDTO, loginInfo.getAccountId());
    }

    @ApiOperation(value="更新商品状态")
    @PostMapping(value = "/updateGoodsStatus")
    public ItemResult<String> updateStatus(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsId", value = "商品Id") String goodsId,@ApiParam(name = "goodsStatus", value = "商品状态") Integer goodsStatus) {
    	log.info("/updateGoodsStatus-goodsId_{} goodsStatus_{} operator_{}", goodsId, goodsStatus, loginInfo.getAccountId());
    	return goodsService.updateStatus(goodsId, goodsStatus, loginInfo.getAccountId());
    }

    @ApiOperation(value="删除商品（逻辑删除，支持批量删除）")
    @PostMapping(value = "/deleteGoods")
    public ItemResult<Boolean> deleteGoods(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsIds", value = "商品Id集合") String goodsIds) {
    	log.info("/deleteGoods-goodsIds_{} operator_{}", goodsIds, loginInfo.getAccountId());
    	return goodsService.delete(goodsIds, loginInfo.getAccountId());
    }

    @ApiOperation(value="查询全部商品分类List")
    @GetMapping(value = "/getAllCategoryList")
    public ItemResult<List<GoodsCategoryDTO>> getAllCategoryList() {
    	return goodsCategoryService.getAllList();
    }

    @ApiOperation(value="查询全部商品分类Tree")
    @GetMapping(value = "/getAllCategoryTree")
    public ItemResult<List<GoodsCategoryDTO>> getAllCategoryTree() {
    	return goodsCategoryService.getAllTree();
    }

    @ApiOperation(value="模糊查询商品分类ByName")
    @GetMapping(value = "/getCategoryByLikeName")
    public ItemResult<List<GoodsCategoryDTO>> getCategoryByLikeName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "name", value = "商品分类名称") String name) {

    	log.info("/getCategoryByLikeName-name_{} goodsStatus{} operator_{}", name);
    	ItemResult<List<GoodsCategoryDTO>> result =  goodsCategoryService.getByLikeName(name);
        return result;
    }

    @ApiOperation(value="模糊查询商品分类ByName(限制)")
    @GetMapping(value = "/queryCategoryByLikeName")
    public ItemResult<List<GoodsCategoryDTO>> queryCategoryByLikeName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "name", value = "商品分类名称") String name) {

    	log.info("/queryCategoryByLikeName-name_{}", name);
    	ItemResult<List<GoodsCategoryDTO>> result =  goodsCategoryService.getByLikeName(name);
    	List<GoodsCategoryDTO> goodsCategoryDTOs = result.getData();
    	if (!CollectionUtils.isEmpty(goodsCategoryDTOs) && goodsCategoryDTOs.size() > 50) {
    		List<GoodsCategoryDTO> dtos = goodsCategoryDTOs.subList(0, 50);
    		result.setData(dtos);
		}
        return result;
    }

    @ApiOperation(value="创建商品分类")
    @PostMapping(value = "/createCategory")
    public ItemResult<GoodsCategoryDTO> createCategory(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "categoryName", value = "商品分类名称") String categoryName,@ApiParam(name = "parentId", value = "父类id")String parentId) {
    	log.info("/createCategory-categoryName_{} parentId_{} operator_{}", categoryName, parentId);
    	ItemResult<GoodsCategoryDTO> result = goodsCategoryService.create(null, categoryName, parentId, loginInfo.getAccountId());
        return result;
    }

    @ApiOperation(value="更新商品分类")
    @PostMapping(value = "/updateCategory")
    public ItemResult<GoodsCategoryDTO> updateCategory(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "categoryId", value = "商品分类Id") String categoryId,@ApiParam(name = "categoryName", value = "商品分类名称") String categoryName) {
    	log.info("/updateCategory-categoryId_{} categoryName_{} operator_{}", categoryId, categoryName, loginInfo.getAccountId());
    	ItemResult<GoodsCategoryDTO> result = goodsCategoryService.update(categoryId, null, categoryName, loginInfo.getAccountId());
        return result;
    }

    @ApiOperation(value="删除商品分类")
    @PostMapping(value = "/deleteCategory")
    public ItemResult<Boolean> deleteCategory(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "categoryId", value = "商品分类Id") String categoryId) {
    	log.info("/deleteCategory-categoryId_{} operator_{}", categoryId, loginInfo.getAccountId());
    	ItemResult<Boolean> result = goodsCategoryService.delete(categoryId, loginInfo.getAccountId());
    	return result;
    }

    @ApiOperation(value="获取商品属性")
    @GetMapping(value = "/getAttrValByGoodsId")
    public ItemResult<List<GoodsAttributeDTO>> getAttrValByGoodsId(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsId", value = "商品Id") String goodsId,@ApiParam(name = "attriType", value = "属性类型") Integer attriType) {
    	log.info("/getAttrValByGoodsId-goodsId_{} attriType_{} operator_{}", goodsId, attriType);
    	return goodsService.getAttrValByGoodsId(goodsId, attriType);
    }

    @ApiOperation(value="根据属性名称查询属性值")
    @GetMapping(value = "/getAttrValByName")
    public ItemResult<List<GoodsAttributeDTO>> getAttrValByName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "attrName", value = "属性名称") String attrName,@ApiParam(name = "categoryType", value = "分类类型") Integer categoryType) {
    	log.info("/getAttrValByName-attrName_{} categoryType_{}", attrName, categoryType);
    	return goodsService.getAttrValByName(attrName, categoryType);
    }

    @ApiOperation(value="查询商品单位换算信息")
    @GetMapping(value = "/getUnitConverInfo")
    public ItemResult<List<UnitConverDTO>> getUnitConverInfo(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsId", value = "商品Id") String goodsId) {
    	log.info("/getUnitConverInfo-goodsId_{}", goodsId);
    	ItemResult<List<UnitConverDTO>> result = goodsService.getUnitConverInfo(goodsId);
    	return result;
    }

    @ApiOperation(value="获取分类关联属性信息")
    @GetMapping(value = "/getCategoryAttrVals")
    public ItemResult<Map<String,List<GoodsAttributeDTO>>> getCategoryAttrVals(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "categoryId", value = "分类属性Id") String categoryId,@ApiParam(name = "categoryType", value = "分类属性类型") Integer categoryType) {
    	log.info("/getCategoryAttrVals-categoryId_{} categoryType_{}", categoryId, categoryType);
    	ItemResult<Map<String,List<GoodsAttributeDTO>>> result = goodsService.getCategoryAttrVals(categoryId, categoryType);
    	return result;
    }

    @ApiOperation("创建卖家商品")
    @PostMapping(value="/createSellerGoods")
    public ItemResult<Boolean> createSellerGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody SellerGoodsDTO sellerGoodsDTO){
        checkLogin(loginInfo);
        log.info("/createSellerGoods,req:{}", sellerGoodsDTO);
        sellerGoodsDTO.setSellerId(loginInfo.getMemberId());
//        goodsService.createSellerGoods(sellerGoodsDTO,loginInfo.getAccountId());
        return goodsService.createSellerGoods(sellerGoodsDTO,loginInfo.getAccountId());
    }

    @ApiOperation("修改卖家商品")
    @PostMapping(value="/updateSellerGoods")
    public ItemResult<Boolean> updateSellerGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody SellerGoodsDTO sellerGoodsDTO){
        checkLogin(loginInfo);
        log.info("/updateSellerGoods,req:{}", sellerGoodsDTO);
        sellerGoodsDTO.setSellerId(loginInfo.getMemberId());
//        goodsService.updateSellerGoods(sellerGoodsDTO,loginInfo.getAccountId());
        return goodsService.updateSellerGoods(sellerGoodsDTO,loginInfo.getAccountId());
    }

    @ApiOperation("查看卖家商品详情")
    @PostMapping(value="/getSellerGoodsDetail")
    public ItemResult<SellerGoodsDTO> getSellerGoodsDetail(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsId", value = "商品Id")  @Valid @RequestParam("goodsId") String goodsId){
        checkLogin(loginInfo);
        log.info("/getSellerGoodsDetail,req:{}", goodsId);
        return  goodsService.getSellerGoodsDetail(goodsId);
    }

    @ApiOperation("删除卖家商品")
    @PostMapping(value="/deleteSellerGoods")
    public ItemResult<List<String>> deleteSellerGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> goodsIds){
        checkLogin(loginInfo);
        log.info("/deleteSellerGoods,req:{}", goodsIds);
        return goodsService.deleteSellerGoods(goodsIds,loginInfo.getAccountId());
    }

    @ApiOperation("查询标准商品属性")
    @PostMapping(value="/findBaseGoodsAttribute")
    public ItemResult<List<CategoryAttributeDTO>> findBaseGoodsAttribute(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsId", value = "商品Id")  @Valid @RequestParam("goodsId") String goodsId){
        checkLogin(loginInfo);
        log.info("/findBaseGoodsAttribute,req:{}", goodsId);
        return goodsService.findBaseGoodsAttribute(goodsId);
    }

    private void checkLogin(LoginInfo loginInfo){
        if ( loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId()) ){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }

    @ApiOperation(value = "根据分类Code查询标准商品")
    @PostMapping(value = "/findBaseGoodsLikeCategoryCode")
    public ItemResult<List<GoodsDTO>> findBaseGoodsLikeCategoryCode(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody GoodsQueryByCateCodeDTO goodsQueryByCateCodeDTO){
        checkLogin(loginInfo);
        log.info(LOG_INFO_FORMAT,goodsQueryByCateCodeDTO);
        return goodsService.findBaseGoodsLikeCategoryCode(goodsQueryByCateCodeDTO);
    }

    @ApiOperation(value = "根据分类Id查询卖家商品")
    @PostMapping(value = "/findSellerGoodsLikeCategoryCode")
    public ItemResult<List<GoodsDTO>> findSellerGoodsLikeCategoryCode(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody GoodsQueryByCateCodeDTO goodsQueryByCateCodeDTO){
        checkLogin(loginInfo);
        log.info(LOG_INFO_FORMAT,goodsQueryByCateCodeDTO);
        goodsQueryByCateCodeDTO.setSellerId(loginInfo.getMemberId());
        return goodsService.findSellerGoodsLikeCategoryCode(goodsQueryByCateCodeDTO);
    }

    @ApiOperation(value = "根据分类Id查询供应商商品")
    @PostMapping(value = "/findPurchaseGoodsLikeCategoryCode")
    public ItemResult<List<GoodsDTO>> findPurchaseGoodsLikeCategoryCode(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody GoodsQueryByCateCodeDTO goodsQueryByCateCodeDTO){
        checkLogin(loginInfo);
        log.info(LOG_INFO_FORMAT,goodsQueryByCateCodeDTO);
        goodsQueryByCateCodeDTO.setSellerId(loginInfo.getMemberId());
        return goodsService.findPurchaseGoodsLikeCategoryCode(goodsQueryByCateCodeDTO);
    }

    @ApiOperation(value = "查询有效的支持门店优惠商品")
    @GetMapping(value = "/findRefferGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findRefferGoodsLikeName(@ApiIgnore LoginInfo loginInfo,@ApiParam(name = "goodsName", value = "商品名称") @RequestParam("goodsName") String goodsName){
        checkLogin(loginInfo);
        log.info("/findRefferGoodsLikeName,req:{}",goodsName);
        return goodsService.findRefferGoodsLikeName(goodsName,loginInfo.getMemberId());
    }

}