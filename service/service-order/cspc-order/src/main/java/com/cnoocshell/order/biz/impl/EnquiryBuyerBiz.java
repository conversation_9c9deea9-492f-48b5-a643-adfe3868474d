package com.cnoocshell.order.biz.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.biz.IEnquiryBuyerBiz;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.Objects;


/**
 * @Author: wangshunmin
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnquiryBuyerBiz extends BaseBiz<EnquiryBuyer> implements IEnquiryBuyerBiz {

    @Override
    public List<EnquiryBuyer> listByNo(Enquiry enquiry) {
        if(Objects.isNull(enquiry))
            return null;

        return this.listByNo(enquiry.getEnquiryNo());
    }

    @Override
    public List<EnquiryBuyer> listByNo(String enquiryNo) {
        if(CharSequenceUtil.isBlank(enquiryNo))
            return null;
        Condition condition = new Condition(EnquiryBuyer.class);
        condition.createCriteria()
                .andEqualTo(ColumnConstant.ENQUIRY_NO,enquiryNo)
                .andEqualTo(ColumnConstant.DEL_FLG,Boolean.FALSE);

        return this.findByCondition(condition);
    }
}
