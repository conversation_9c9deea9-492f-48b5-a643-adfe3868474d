

package com.cnoocshell.base.api.dto.region;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@ApiModel("行政区域SampleDTO")
@Data
public class RegionSampleDTO implements Serializable {

    private static final long serialVersionUID = -3174138670542114511L;

    /**
     * 上级区域代码
     */
    @ApiModelProperty("上级区域代码")
    private String parentAdcode;
    /**
     * 区域代码
     */
    @ApiModelProperty("区域代码")
    private String adcode;

    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String citycode;

    /**
     * 行政区名称
     */
    @ApiModelProperty("行政区名称")
    private String name;

    /**
     * 中心位置
     */
    @ApiModelProperty("中心位置")
    private String center;

    @ApiModelProperty("行政区划级别")
    private String level;

    @ApiModelProperty("标记删除(如果停用则标记为true)")
    private Boolean delFlg;
}
