package com.cnoocshell.base.consumer;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.dto.StopJobDTO;
import com.cnoocshell.common.dto.XxlJobDTO;
import com.cnoocshell.base.biz.IXxlJobBiz;
import com.cnoocshell.common.constant.MqTopicConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class XxlJobConsumer {
    private final IXxlJobBiz iXxlJobBiz;

    /**
     * 创建自动任务消费
     */
    @KafkaListener(topics = MqTopicConstant.CREATE_XXL_JOB_TOPIC)
    public void createXxlJobConsumer(ConsumerRecord<String, String> data) {
        log.info("创建Xxl job自动任务消费开始");
        if (data == null) {
            log.info("CREATE_XXL_JOB_TOPIC message is null");
            return;
        }

        String value = data.value();
        log.info("CREATE_XXL_JOB_TOPIC value:{}", value);
        if (StringUtils.isBlank(value)) {
            log.info("createXxlJobConsumer value is null");
            return;
        }
        try {
            XxlJobDTO param = JSONUtil.toBean(value, XxlJobDTO.class);
            if (Objects.isNull(param)) {
                log.info("创建Xxl job自动任务入参数据为空");
                return;
            }
            Long id = iXxlJobBiz.createJob(param);
            if (Objects.nonNull(id))
                log.info("创建Xxl job自动任务success");
        } catch (Exception e) {
            log.error("创建Xxl Job自动任务异常 ", e);
        }

        log.info("创建Xxl Job自动任务消费结束");
    }

    /**
     * 停止自动任务消费
     */
    @KafkaListener(topics = MqTopicConstant.STOP_XXL_JOB_TOPIC)
    public void stopXxlJobConsumer(ConsumerRecord<String, String> data) {
        log.info("停止Xxl job自动任务消费开始");
        if (data == null) {
            log.info("STOP_XXL_JOB_TOPIC message is null");
            return;
        }

        String value = data.value();
        log.info("STOP_XXL_JOB_TOPIC value:{}", value);
        if (StringUtils.isBlank(value)) {
            log.info("stopXxlJobConsumer value is null");
            return;
        }
        try {
            Long id = NumberUtil.parseLong(value);
            if (Objects.isNull(id)) {
                log.info("停止Xxl job自动任务入参数据为空");
                return;
            }
            iXxlJobBiz.stopJob(id);
            log.info("停止Xxl job自动任务success");
        } catch (Exception e) {
            log.error("停止Xxl Job自动任务异常 ", e);
        }

        log.info("停止Xxl Job自动任务消费结束");
    }

    /**
     * 销毁自动任务消费
     */
    @KafkaListener(topics = MqTopicConstant.DESTROY_XXL_JOB_TOPIC)
    public void destroyXxlJobConsumer(ConsumerRecord<String, String> data) {
        log.info("销毁Xxl job自动任务消费开始");
        if (data == null) {
            log.info("DESTROY_XXL_JOB_TOPIC message is null");
            return;
        }

        String value = data.value();
        log.info("DESTROY_XXL_JOB_TOPIC value:{}", value);
        if (StringUtils.isBlank(value)) {
            log.info("destroyXxlJobConsumer value is null");
            return;
        }
        try {
            Long id = NumberUtil.parseLong(value);
            if (Objects.isNull(id)) {
                log.info("销毁Xxl job自动任务入参数据为空");
                return;
            }
            iXxlJobBiz.destroyJob(id);
            log.info("销毁Xxl job自动任务success");
        } catch (Exception e) {
            log.error("销毁Xxl Job自动任务异常 ", e);
        }

        log.info("销毁Xxl Job自动任务消费结束");
    }

    /**
     * 根据jobName与执行参数 停止对应自动任务消费
     */
    @KafkaListener(topics = MqTopicConstant.STOP_XXL_JOB_BY_PARAM_TOPIC)
    public void stopXxlJobByParamConsumer(ConsumerRecord<String, String> data) {
        log.info("根据参数停止Xxl job自动任务消费开始");
        if (data == null) {
            log.info("STOP_XXL_JOB_BY_PARAM_TOPIC message is null");
            return;
        }

        String value = data.value();
        log.info("STOP_XXL_JOB_BY_PARAM_TOPIC value:{}", value);
        if (StringUtils.isBlank(value)) {
            log.info("stopXxlJobByParamConsumer value is null");
            return;
        }
        try {
            StopJobDTO param = JSONUtil.toBean(value,StopJobDTO.class);
            if (Objects.isNull(param)) {
                log.info("根据参数停止Xxl job自动任务入参数据为空");
                return;
            }
            iXxlJobBiz.stopJob(param);
            log.info("根据参数停止Xxl job自动任务success");
        } catch (Exception e) {
            log.error("根据参数停止Xxl Job自动任务异常 ", e);
        }

        log.info("根据参数停止Xxl Job自动任务消费结束");
    }
}
