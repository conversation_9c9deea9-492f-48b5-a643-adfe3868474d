package com.cnoocshell.member.api.dto.member;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.member.api.dto.member.validation.RegisterBuyerGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberPurchaseGoodsIntentionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("memberId")
    private String memberId;

    @ApiModelProperty("memberCode")
    private String memberCode;

    @ApiModelProperty("意向类型 1:主要意向 2:次要意向")
    @NotNull(message = "意向类型不能为空",groups = RegisterBuyerGroup.class)
    private Integer intentionType;

    @ApiModelProperty("商品类别ID")
    @NotBlank(message = "商品类别ID不能为空",groups = RegisterBuyerGroup.class)
    private String goodsCategoryId;

    @ApiModelProperty("商品类别名称")
    private String goodsCategoryName;

    @ApiModelProperty("商品ID")
    @NotBlank(message = "商品ID不能为空",groups = RegisterBuyerGroup.class)
    private String goodsId;

    @ApiModelProperty("商品编码")
    @NotBlank(message = "商品编码不能为空",groups = RegisterBuyerGroup.class)
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("销售人员ID")
    private String saleUserId;

    @ApiModelProperty("销售人员真实姓名")
    private String saleUserName;

    @ApiModelProperty("销售人员 员工编号")
    private String saleUserEmployeeId;

    @ApiModelProperty("销售渠道(字典)")
    private String saleChannel;

    @ApiModelProperty("销售渠道名称(字典)")
    private String saleChannelName;

    @ApiModelProperty("维护销售信息的操作人ID")
    private String maintainUserId;

    @ApiModelProperty("维护销售信息的操作人姓名")
    private String maintainUserName;

    @ApiModelProperty("维护销售信息的操作时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date maintainTime;

    public void defaultNull(){
        this.saleUserId = null;
        this.saleUserName = null;
        this.maintainUserId = null;
        this.maintainUserName = null;
        this.maintainTime = null;
    }
}
