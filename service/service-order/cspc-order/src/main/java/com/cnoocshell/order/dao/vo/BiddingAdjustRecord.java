package com.cnoocshell.order.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "bidding_adjust_record")
public class BiddingAdjustRecord extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "bidding_no")
    private String biddingNo;

    @Column(name = "old_current_round")
    private Integer oldCurrentRound;

    @Column(name = "old_standard_price")
    private BigDecimal oldStandardPrice;

    @Column(name = "new_standard_price")
    private BigDecimal newStandardPrice;

    @Column(name = "bidding_start_time")
    private Date biddingStartTime;

    @Column(name = "bidding_end_time")
    private Date biddingEndTime;

    @Column(name = "create_user_name")
    private String createUserName;

}