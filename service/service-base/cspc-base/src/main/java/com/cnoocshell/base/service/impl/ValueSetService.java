package com.cnoocshell.base.service.impl;

import com.cnoocshell.base.api.dto.*;
import com.cnoocshell.base.biz.IRoleBiz;
import com.cnoocshell.base.biz.IValueSetBiz;
import com.cnoocshell.base.dao.vo.Role;
import com.cnoocshell.base.dao.vo.ValueSet;
import com.cnoocshell.base.dao.vo.ValueSetOption;
import com.cnoocshell.base.service.IValueSetService;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.service.common.BaseService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ValueSetService extends BaseService<ValueSet> implements IValueSetService {

    public static final String JZ_0006 = "JZ0006";
    public static final String JZ_0001 = "JZ0001";

    private final IValueSetBiz valueSetBiz;
    private final IRoleBiz roleBiz;

    @AddLog(operatorIndex = 1)
    @Override
    public void createValueSet(ValueSetDTO valueSet,String operator) {
        valueSetBiz.createValueSet(covertVo(valueSet),operator);
    }
    @AddLog(operatorIndex = 1)
    @Override
    public void createValueSetOption(ValueSetOptionDTO valueSetOptionDTO,String operator) {
        valueSetBiz.createValueSetOption(covertOptionVo(valueSetOptionDTO),operator);
    }
    @AddLog(operatorIndex = 1)
    @Override
    public void createValueAndOption(ValueAndOptionDTO valueAndOptionDTO,String operator) {
        valueSetBiz.createValueAndOption(valueAndOptionDTO,operator);
    }
    @AddLog(operatorIndex = 1)
    @Override
    public void updateValueAndOption(ValueAndOptionDTO valueAndOptionDTO,String operator) {
        valueSetBiz.updateValueAndOption(valueAndOptionDTO,operator);
    }

    @Override
    public Boolean canValueSetEdit(String valueSetId) {
        return valueSetBiz.canValueSetEdit(valueSetId);

    }
    @AddLog(operatorIndex = 1)
    @Override
    public void deleteValueSet(String valueSetId,String operator) {
        valueSetBiz.deleteValueSet(valueSetId,operator);


    }
    @AddLog(operatorIndex = 1)
    @Override
    public void deleteValueSetOption(String valueSetOptionId,String operator) {
        valueSetBiz.deleteValueSetOption(valueSetOptionId,operator);
    }
    @AddLog(operatorIndex = 1)
    @Override
    public void updateValueSet(ValueSetDTO valueSet,String operator) {
        ValueSet valueSet1= covertVo(valueSet);
        if( valueSet1 != null ) {
            valueSet1.setUpdateUser(operator);
            valueSet1.setUpdateTime(new Date());
            valueSetBiz.updateSelective(valueSet1);
        }
    }
    @AddLog(operatorIndex = 1)
    @Override
    public void updateValueSetOptions(List<ValueSetOptionDTO> valueSetOptions,String operator) {
        valueSetBiz.updateOption(covertOptionListVo(valueSetOptions),operator);

    }

    @Override
    public PageInfo<ValueSetDTO> pageValueSet(ValueQueryDTO valueQueryDTO) {
        return valueSetBiz.getByPage(valueQueryDTO);
    }

    @Override
    public List<ValueSetOptionDTO> listValueSetOption(String valueSetId) {
        return covertOptionListDTO(valueSetBiz.listValueSetOption(valueSetId));
    }

    @Override
    public ValueSetDTO getValueSet(String valueSetId) {
        return covertDTO(valueSetBiz.get(valueSetId));
    }

    @Override
    public ValueSetOptionDTO getValueSetOption(String valueSetOptionId) {
        return covertOptionDTO(valueSetBiz.getValueSetOption(valueSetOptionId));
    }

    @Override
    public ValueSetTreeDTO getChildValueSetOption(String parentOptionId) {
        return valueSetBiz.getChildValueSetOption(parentOptionId);
    }

    @Override
    public ValueSetTreeDTO getValueSetByReferenceCode(String referenceCode) {
        return valueSetBiz.getByRefrenceCode(referenceCode);
    }

    @Override
    public List<ValueSetOptionDTO> getValueSetByReferenceCodeAndKeyWord(String referenceCode, String keyWord) {
        ValueSetTreeDTO valueSetTreeDTO = valueSetBiz.getByRefrenceCode(referenceCode);
        List<ValueSetOptionDTO> result = Lists.newArrayList();
        filter(result,valueSetTreeDTO.getTreeList(),referenceCode,keyWord);
        return result;
    }

    private void filter(List<ValueSetOptionDTO> result,List<ValueSetTreeDTO> source,String referenceCode,String keyWord){
        if( CollectionUtils.isEmpty(source)){
            return;
        }
        for (ValueSetTreeDTO treeDTO : source) {
            if( checkContain(treeDTO.getOptionInfo(),keyWord) ||
                    checkContain(treeDTO.getOptionKey(),keyWord) ||
                    checkContain(treeDTO.getOptionValue(),keyWord)
                    ){
                ValueSetOptionDTO valueSetOptionDTO = new ValueSetOptionDTO();
                valueSetOptionDTO.setId(treeDTO.getId());
                valueSetOptionDTO.setOptionId(treeDTO.getOptionId());
                valueSetOptionDTO.setOptionInfo(treeDTO.getOptionInfo());
                valueSetOptionDTO.setOptionKey(treeDTO.getOptionKey());
                valueSetOptionDTO.setOptionOrder(treeDTO.getOptionOrder());
                valueSetOptionDTO.setOptionValue(treeDTO.getOptionValue());
                valueSetOptionDTO.setParentId(treeDTO.getParentId());
                valueSetOptionDTO.setReferenceCode(referenceCode);
                result.add(valueSetOptionDTO);
                if(result.size() >=20){
                    return;
                }
            }
            if( CollectionUtils.isNotEmpty(treeDTO.getTreeList()) ){
                filter(result,source,referenceCode,keyWord);
            }
        }
    }

    private boolean checkContain(String value,String keyWord){
        return value != null && value.contains(keyWord);
    }

    @Override
    public List<ValueSetOptionDTO> findOptionByKey(String key) {
        return valueSetBiz.findOptionByKey(key);
    }

    @Override
    public List<ValueSetOptionDTO> batchFindOption(List<String> keys) {
        List<ValueSetOptionDTO> valueSetOptionDTOS = new LinkedList<>();
        for (String key:keys){
            valueSetOptionDTOS.addAll(valueSetBiz.findOptionByKey(key));
        }
        return valueSetOptionDTOS;
    }

    /**
     * ag: seller -> [["seller","卖家"],["seller_app","卖家app"]]
     * @param roleType
     * @return
     */
    @Override
    public List<String[]> findPlatform(String roleType) {
        //踢掉最后的数字
        String rt = roleType.endsWith("3") ? roleType.substring(0,roleType.length()-1) : roleType;
        //JZ0001，JZ0003，JZ0006
        ValueSetTreeDTO  jz6 = valueSetBiz.getByRefrenceCode(JZ_0006);
        ValueSetTreeDTO  jz1 = valueSetBiz.getByRefrenceCode(JZ_0001);
        List<String[]> result = Lists.newArrayList();
        if( jz6 == null || jz6.getTreeList() == null || jz6.getTreeList().isEmpty() ){
            jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->{
                result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
            });
            return result;
        }
        ValueSetTreeDTO valueSetTreeDTO = jz6.getTreeList()
                .stream()
                .filter(item->StringUtils.equals(item.getOptionKey(),rt+6))
                .findAny().orElse(null);
        if( valueSetTreeDTO == null ){
            jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->{
                result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
            });
            return result;
        }
        Set<String> keys = Sets.newHashSet(valueSetTreeDTO.getOptionValue().split(","));
        jz1.getTreeList().stream().filter(item->keys.contains(item.getOptionKey())).forEach(item->{
            result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
        });
        return result;
    }

    @Override
    public List<String[]> findPlatformByRoleType(List<String> roleTypeList) {
        if(CollectionUtils.isEmpty(roleTypeList) ){
            return Lists.newArrayList();
        }
        List<String[]> result = Lists.newArrayList();
        //JZ0001，JZ0003，JZ0006
        ValueSetTreeDTO  jz6 = valueSetBiz.getByRefrenceCode(JZ_0006);
        ValueSetTreeDTO  jz1 = valueSetBiz.getByRefrenceCode(JZ_0001);
        for (String roleType : Sets.newHashSet(roleTypeList)) {
            //踢掉最后的数字
            String rt = roleType.endsWith("3") ? roleType.substring(0,roleType.length()-1) : roleType;
            if( jz6 == null || jz6.getTreeList() == null || jz6.getTreeList().isEmpty() ){
                jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->{
                    result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
                });
                continue;
            }
            ValueSetTreeDTO valueSetTreeDTO = jz6.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt+6)).findAny().orElse(null);
            if( valueSetTreeDTO == null ){
                jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->{
                    result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
                });
                continue;
            }
            Set<String> keys = Sets.newHashSet(valueSetTreeDTO.getOptionValue().split(","));
            jz1.getTreeList().stream().filter(item->keys.contains(item.getOptionKey())).forEach(item->{
                result.add(new String[]{item.getOptionKey(),item.getOptionValue()});
            });
        }
        return result;
    }


    @Override
    public Set<String> findPlatformByRole(Set<String> roleTypeList, ValueSetTreeDTO jz1, ValueSetTreeDTO jz6,boolean underlineToHump) {
        Set<String> result = Sets.newHashSet();
        if(CollectionUtils.isEmpty(roleTypeList) ){
            return result;
        }
        //JZ0001，JZ0003，JZ0006
        for (String roleType : roleTypeList) {
            //踢掉最后的数字
            String rt = roleType.endsWith("3") ? roleType.substring(0,roleType.length()-1) : roleType;
            if( jz6 == null || jz6.getTreeList() == null || jz6.getTreeList().isEmpty() ){
                jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->result.add(underlineToHump(item.getOptionKey(),underlineToHump)));
                continue;
            }
            ValueSetTreeDTO valueSetTreeDTO = jz6.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt+6)).findAny().orElse(null);
            if( valueSetTreeDTO == null ){
                jz1.getTreeList().stream().filter(item->StringUtils.equals(item.getOptionKey(),rt)).forEach(item->result.add(underlineToHump(item.getOptionKey(),underlineToHump)));
                continue;
            }
            Set<String> keys = Sets.newHashSet(valueSetTreeDTO.getOptionValue().split(","));
            jz1.getTreeList().stream().filter(item->keys.contains(item.getOptionKey())).forEach(item->result.add(underlineToHump(item.getOptionKey(),underlineToHump)));
        }
        return result;
    }

    private final static String UNDERLINE = "_";
    /***
     * 下划线命名转为驼峰命名
     *
     * @param para
     *        下划线命名的字符串
     */
    public static String underlineToHump(String para,boolean change) {
        if( !change ){
            return para;
        }
        StringBuilder result = new StringBuilder();
        String[] a = para.split(UNDERLINE);
        for (String s : a) {
            if (!para.contains(UNDERLINE)) {
                result.append(s);
                continue;
            }
            if (result.length() == 0) {
                result.append(s.toLowerCase());
            } else {
                result.append(s.substring(0, 1).toUpperCase());
                result.append(s.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    @Override
    public List<String[]> findPlatformByRole(List<Integer> roleIds) {
        if( CollectionUtils.isEmpty(roleIds) ){
            return Lists.newArrayList();
        }
        List<Role> roleList = roleBiz.findByIds(roleIds);
        if( CollectionUtils.isEmpty(roleList) ){
            return Lists.newArrayList();
        }
        return findPlatformByRoleType(roleList.stream().filter(item->StringUtils.isNotBlank(item.getRoleType())).map(item->item.getRoleType()).distinct().collect(Collectors.toList()));
    }

    @Override
    public Set<String> findRoleTypeByPlatform(Collection<String> platformList) {
        if(CollectionUtils.isEmpty(platformList) ){
            return Sets.newHashSet();
        }
        Set<String> result = Sets.newHashSet();
        //JZ0001，JZ0003，JZ0006
        ValueSetTreeDTO  jz1 = valueSetBiz.getByRefrenceCode(JZ_0001);
        ValueSetTreeDTO  jz6 = valueSetBiz.getByRefrenceCode(JZ_0006);
        jz1.getTreeList().stream().filter(item->platformList.contains(item.getOptionKey())).forEach(item->{
            result.add(item.getOptionKey()+3);
        });
        jz6.getTreeList().stream().filter(item->StringUtils.isNotBlank(item.getOptionValue())).forEach(item->{
            for (String s : item.getOptionValue().split(",")) {
                if( platformList.contains(s)){
                    result.add(item.getOptionKey().endsWith("6") ? item.getOptionKey().substring(0,item.getOptionKey().length()-1)+3 : item.getOptionKey()+3);
                    break;
                }
            }
        });
        return result;
    }

    @Override
    public void reloadRedisCache(){
        valueSetBiz.reloadRedisCache();
    }

    @Override
    public List<ValueSetDTO> getValueTreeByCodes(List<String> referenceCodes) {
        return valueSetBiz.getValueTreeByCodes(referenceCodes);
    }


    private ValueSet covertVo(ValueSetDTO valueSetDTO){
        if(valueSetDTO == null){
            return null;
        }
        ValueSet valueSet = new ValueSet();
        BeanUtils.copyProperties(valueSetDTO,valueSet);
        valueSet.setDelFlg(Boolean.FALSE);
        return valueSet;

    }
    private ValueSetDTO covertDTO(ValueSet valueSet){
        if(valueSet == null){
            return null;
        }
        ValueSetDTO valueSetDTO = new ValueSetDTO();
        BeanUtils.copyProperties(valueSet,valueSetDTO);
        return valueSetDTO;

    }

    private List<Map<String,String>> covertTreeMap(List<ValueSetOption> valueSetOptions){
        if(valueSetOptions == null || valueSetOptions.size()==0){
            return null;
        }
        List<Map<String,String>> list = new LinkedList<Map<String,String>>();
        Map<String,String> map = new HashMap<String,String>();
        for(ValueSetOption valueSetOption:valueSetOptions){
            map.put(valueSetOption.getOptionKey(),valueSetOption.getOptionValue());
            list.add(map);
        }
        return list;
    }

    private ValueSetOption covertOptionVo(ValueSetOptionDTO valueSetOptionDTO){
        if(valueSetOptionDTO == null){
            return null;
        }
        ValueSetOption valueSetOption = new ValueSetOption();
        BeanUtils.copyProperties(valueSetOptionDTO,valueSetOption);
        return valueSetOption;
    }
    private List<ValueSetOption> covertOptionListVo(List<ValueSetOptionDTO> valueSetOptionDTOs){
        if(valueSetOptionDTOs == null || valueSetOptionDTOs.size()==0){
            return null;
        }
        List<ValueSetOption> list = new LinkedList<ValueSetOption>();
        for(ValueSetOptionDTO valueSetOptionDTO:valueSetOptionDTOs){
            ValueSetOption valueSetOption = new ValueSetOption();
            BeanUtils.copyProperties(valueSetOptionDTO,valueSetOption);
            list.add(valueSetOption);
        }
        return list;

    }
    private List<ValueSetOptionDTO> covertOptionListDTO(List<ValueSetOption> valueSetOptions){
        if(valueSetOptions == null || valueSetOptions.size()==0){
            return null;
        }
        List<ValueSetOptionDTO> list = new LinkedList<ValueSetOptionDTO>();
        for(ValueSetOption valueSetOption:valueSetOptions){
            ValueSetOptionDTO valueSetOptionDTO = new ValueSetOptionDTO();
            BeanUtils.copyProperties(valueSetOption,valueSetOptionDTO);
            list.add(valueSetOptionDTO);
        }
        return list;

    }
    private List<ValueSetDTO> covertListDTO(List<ValueSet> valueSets){
        if(valueSets == null || valueSets.size()==0){
            return null;
        }
        List<ValueSetDTO> list = new LinkedList<ValueSetDTO>();
        for(ValueSet valueSet:valueSets){
            ValueSetDTO valueSetDTO = new ValueSetDTO();
            BeanUtils.copyProperties(valueSet,valueSetDTO);
            list.add(valueSetDTO);
        }
        return list;

    }
    private ValueSetOptionDTO covertOptionDTO(ValueSetOption valueSetOption){

        if(valueSetOption == null){
            return null;
        }
        ValueSetOptionDTO valueSetOptionDTO = new ValueSetOptionDTO();
        BeanUtils.copyProperties(valueSetOption,valueSetOptionDTO);
        return valueSetOptionDTO;
    }



}
