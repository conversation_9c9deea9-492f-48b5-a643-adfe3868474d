package com.cnoocshell.member.api.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RoleCertDTO implements Serializable {
	private static final long serialVersionUID = -6733301341243166793L;
	/**
	 * 角色id
	 */
	@ApiModelProperty("角色id")
	private Integer roleId;

	/**
	 * 资质类型
	 */
	@ApiModelProperty("资质类型")
	private String certType;

	/**
	 * 是否必须
	 */
	@ApiModelProperty("是否必须")
	private Integer required;
}
