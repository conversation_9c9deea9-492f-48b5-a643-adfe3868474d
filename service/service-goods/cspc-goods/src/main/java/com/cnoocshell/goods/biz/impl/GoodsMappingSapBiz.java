package com.cnoocshell.goods.biz.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.biz.IGoodsMappingSapBiz;
import com.cnoocshell.goods.dao.mapper.GoGoodsMappingSapMapper;
import com.cnoocshell.goods.dao.vo.GoGoodsMappingSap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsMappingSapBiz extends BaseBiz<GoGoodsMappingSap> implements IGoodsMappingSapBiz {
    @Resource
    private GoGoodsMappingSapMapper goGoodsMappingSapMapper;

    @Override
    public List<GoGoodsMappingSap> queryGoodsMapping(List<String> goodsCodes, List<String> packList) {
        if(CollUtil.isEmpty(goodsCodes))
            return Collections.emptyList();
        Condition condition = new Condition(GoGoodsMappingSap.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo("delFlg",Boolean.FALSE)
                .andIn("goodsCode",goodsCodes);
        CommonUtils.andInIfNotEmpty(criteria,"pack",packList);

        return this.findByCondition(condition);
    }
}
