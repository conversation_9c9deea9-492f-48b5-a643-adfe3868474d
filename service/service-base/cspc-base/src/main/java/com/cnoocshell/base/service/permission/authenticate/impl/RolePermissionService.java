package com.cnoocshell.base.service.permission.authenticate.impl;

import com.cnoocshell.base.api.dto.AccountPermissionDTO;
import com.cnoocshell.base.api.dto.authRes.VUEPageCrumbsDTO;
import com.cnoocshell.base.service.permission.authenticate.IRoleCashService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class RolePermissionService implements IRoleCashService {
    public static final String RIGHT_ACCESS_CACHE_NAME = "right_role_cache";
    //二级缓存
    public static final String RIGHT_ACCESS_CACHE_NAME_2 = "right_role_cache2";
    public static final String RIGHT_CRUMBS_CACHE_NAME = "right_crumbs_cache";
    private final BizRedisService redisService;

    @Override
    public List<AccountPermissionDTO> getRolePermissionList(List<String> roleIds, String projectType) {
        List<AccountPermissionDTO> list = new ArrayList<>();
        for (String roleId : roleIds) {
            try {
                AccountPermissionDTO accountPermissionDTO = this.redisService.hget(RIGHT_ACCESS_CACHE_NAME, projectType + "_" + roleId, AccountPermissionDTO.class);
                if (accountPermissionDTO != null) {
                    list.add(accountPermissionDTO);
                }
            } catch (Exception e) {
                log.error("getRolePermissionList projectType: {},roleId: {}", projectType, roleId);
                log.error(e.getMessage(), e);
            }
        }

        return list;
    }

    @Override
    public Map<String, List<VUEPageCrumbsDTO>> getPlatformCrumbs(String projectType) {
        try {
            return this.redisService.hget(RIGHT_CRUMBS_CACHE_NAME, projectType);
        } catch (Exception e) {
            log.error("getPlatformCrumbs: {}", projectType);
            log.error(e.getMessage(), e);
            return Maps.newHashMap();
        }
    }
}
