package com.cnoocshell.common.exception;

import com.cnoocshell.common.utils.CsStringUtils;

public class BasicRuntimeException extends RuntimeException {
    public BasicRuntimeException(){
        super();
    }
    public BasicRuntimeException(String message, String... args){
        super(CsStringUtils.replaceParams(message, args));
    }
    public BasicRuntimeException(Throwable e){
        super(e);
    }
    public BasicRuntimeException(String message, Throwable e){
        super(message, e);
    }
    public BasicRuntimeException(Throwable e, String message, String... args){
        super(CsStringUtils.replaceParams(message, args), e);
    }
}
