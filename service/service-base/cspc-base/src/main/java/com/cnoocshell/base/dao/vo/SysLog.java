package com.cnoocshell.base.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "ba_sys_log")
@EqualsAndHashCode(callSuper = true)
public class SysLog extends BaseEntity {
    /**
     * id
     */
    @Id
    private String id;

    /**
     * 请求回话id
     */
    @Column(name = "session_id")
    private String sessionId;

    /**
     * 中心名称
     */
    @Column(name = "center_name")
    private String centerName;

    /**
     * 中心节点ip
     */
    @Column(name = "certer_ip")
    private String certerIp;

    /**
     * 操作模块(服务模块名称)
     */
    @Column(name = "module_name")
    private String moduleName;

    /**
     * 操作动作(服务方法)
     */
    @Column(name = "oper_method")
    private String operMethod;

    /**
     * 方法名称(服务方法内部方法名称)
     */
    @Column(name = "oper_sub_method")
    private String operSubMethod;

    /**
     * 操作结果(成功/失败/异常)
     */
    @Column(name = "oper_result")
    private String operResult;

    /**
     * 业务编号
     */
    @Column(name = "business_umber")
    private String businessUmber;

    /**
     * 操作入参
     */
    @Column(name = "oper_input_params")
    private String operInputParams;

    /**
     * 操作出参
     */
    @Column(name = "oper_return_value")
    private String operReturnValue;

    /**
     * 错误消息
     */
    @Column(name = "error_message")
    private String errorMessage;
    /**
     * 操作开始时间
     */
    @Column(name = "oper_start_time")
    private Date operStartTime;

    /**
     * 操作结束时间
     */
    @Column(name = "oper_end_time")
    private Date operEndTime;

    /**
     * 操作耗时
     */
    @Column(name = "oper_cost_time")
    private Integer operCostTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人所在会员id
     */
    @Column(name = "oper_member_id")
    private String operMemberId;

    /**
     * 操作人所在会员code
     */
    @Column(name = "oper_member_code")
    private String operMemberCode;

    /**
     * 客户端IP
     */
    @Column(name = "oper_ip")
    private String operIp;

    /**
     * 操作人名称
     */
    @Column(name = "create_user_name")
    private String createUserName;
}