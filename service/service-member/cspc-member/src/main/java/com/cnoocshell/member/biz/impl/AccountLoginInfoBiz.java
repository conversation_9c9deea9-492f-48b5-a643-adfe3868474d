package com.cnoocshell.member.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;
import com.cnoocshell.member.biz.IAccountLoginInfoBiz;
import com.cnoocshell.member.dao.mapper.AccountLoginInfoMapper;
import com.cnoocshell.member.dao.vo.AccountLoginInfo;
import com.cnoocshell.member.exception.DuplicateString;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountLoginInfoBiz extends BaseBiz<AccountLoginInfo> implements IAccountLoginInfoBiz {

    public static final String SESSION_ID = "sessionId";
    private final AccountLoginInfoMapper accountLoginInfoMapper;

    @Override
    public int offline(String oldSessionId, String newSessionId, String reason, String operator) {
        if (StringUtils.isBlank(oldSessionId)) {
            log.info("sessionId is null ");
            return 0;
        }
        Condition condition = new Condition(AccountLoginInfo.class);
        condition.createCriteria()
                .andEqualTo(SESSION_ID, oldSessionId)
                .andIsNull("logoutTime")
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        Date now = new Date();
        AccountLoginInfo item = new AccountLoginInfo();
        item.setLogoutComment(reason);
        item.setLogoutTime(now);
        item.setUpdateUser(operator);
        item.setUpdateTime(now);
        int result = accountLoginInfoMapper.updateByConditionSelective(item, condition);
        log.info("offline oldSessionId: {},newSessionId: {},update result : {}", oldSessionId, newSessionId, result);
        return result;
    }

    @Override
    public List<String> findLastSessionId(String accountId, String loginType) {
        if (StringUtils.isBlank(accountId)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isBlank(loginType)) {
            return accountLoginInfoMapper.findLastSessionIdByAccountId(accountId, 20);
        }
        return accountLoginInfoMapper.findLastSessionId(accountId, loginType);
    }

    @Override
    public AccountLoginInfo findByAccountId(String accountId) {
        Condition condition = new Condition(AccountLoginInfo.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.ACCOUNT_ID, accountId)
                .andIsNull("logoutTime");
        condition.orderBy(DuplicateString.CREATE_TIME).desc();

        PageInfo<AccountLoginInfo> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(1);
        PageInfo<AccountLoginInfo> page = super.pageInfo(condition, pageInfo);
        if (null != page)
            return CollUtil.getFirst(page.getList());
        return null;
    }

    @Override
    public List<String> findLoginSessionId(Integer pageNum, Integer pageSize) {
        pageNum = ObjectUtil.defaultIfNull(pageNum,0);
        pageSize = ObjectUtil.defaultIfNull(pageSize,10);
        return accountLoginInfoMapper.findLoginSessionId(pageNum * pageSize, pageSize);
    }

    @Override
    public List<String> findLoginSessionId(Set<String> accountIdSet, Integer pageNum, Integer pageSize) {
        if (CollUtil.isEmpty(accountIdSet)) {
            return Lists.newArrayList();
        }
        pageNum = ObjectUtil.defaultIfNull(pageNum,0);
        pageSize = ObjectUtil.defaultIfNull(pageSize,10);
        return accountLoginInfoMapper.findLoginSessionId2(accountIdSet,
                DateUtil.format(LocalDateTime.now().minusDays(1), DatePattern.NORM_DATETIME_PATTERN), pageNum * pageSize, pageSize);
    }

    @Override
    public List<String> findLastSessionIdByTerminal(String accountId, String terminal) {
        if (StringUtils.isBlank(accountId)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isBlank(terminal)) {
            return accountLoginInfoMapper.findLastSessionIdByAccountId(accountId, 20);
        }
        return accountLoginInfoMapper.findLastSessionIdByTerminal(accountId, terminal);
    }

    @Override
    public void insertLoginInfo(AccountLoginInfoDTO param) {
        AccountLoginInfo loginInfo = BeanUtil.toBean(param,AccountLoginInfo.class);
        loginInfo.setId(this.getUuidGeneratorGain());
        loginInfo.handleUser(param.getAccountId(),true);
        this.insert(loginInfo);
    }
}
