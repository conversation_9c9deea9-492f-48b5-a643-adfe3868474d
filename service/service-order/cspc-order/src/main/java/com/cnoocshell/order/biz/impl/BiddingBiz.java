package com.cnoocshell.order.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.*;
import cn.hutool.cron.pattern.CronPatternBuilder;
import cn.hutool.cron.pattern.Part;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.base.api.dto.*;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.enums.RoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.*;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.*;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.common.utils.GeneratorCodeUtil;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.goods.GoodCodesListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;
import com.cnoocshell.goods.api.service.IGoodCategoryRelationService;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsMappingSapService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.integration.enums.WechatTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountInfoReturnDTO;
import com.cnoocshell.member.api.dto.account.AccountNameDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.enums.DepositStatusEnum;
import com.cnoocshell.member.api.enums.SubscribeWechatBusinessTypeEnum;
import com.cnoocshell.member.api.service.IAccountRelationService;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.api.constant.OrderNumberConstant;
import com.cnoocshell.order.api.constant.StringConstant;
import com.cnoocshell.order.api.constant.XxlJobConstant;
import com.cnoocshell.order.api.dto.AccountSubscribeWechatKeyDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerDTO;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;
import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisInfoDTO;
import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisResultDTO;
import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisSaleChannelDTO;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingEnquiryDataDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.enums.*;
import com.cnoocshell.order.biz.*;
import com.cnoocshell.order.config.XxlJobConfig;
import com.cnoocshell.order.dao.mapper.BiddingBuyerDealMapper;
import com.cnoocshell.order.dao.mapper.BiddingBuyerMapper;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerMapper;
import com.cnoocshell.order.dao.vo.*;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.EnquiryBuyerService;
import com.cnoocshell.order.service.IBiddingAccountService;
import com.cnoocshell.order.service.SalesPlanService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingBiz extends BaseBiz<Bidding> implements IBiddingBiz {


    @Resource
    private BiddingMapper biddingMapper;

    @Resource
    private SalesPlanService salesPlanService;

    @Resource
    private EnquiryBuyerService enquiryBuyerService;

    @Resource
    private IBiddingBuyerBiz biddingBuyerService;

    @Resource
    private IMemberService memberService;

    @Resource
    private IGoodsService goodsService;

    @Resource
    private IGoodsCategoryService goodsCategoryService;

    @Resource
    private EnquiryBuyerMapper enquiryBuyerMapper;

    @Resource
    private BiddingBuyerMapper biddingBuyerMapper;

    @Resource
    private BiddingBuyerDealMapper biddingBuyerDealMapper;

    @Resource
    private IBiddingApproveRecordBiz biddingApproveRecordService;

    @Resource
    private BiddingBiz self;

    private final GeneratorCodeUtil generatorCodeUtil;
    private final IXxlJobService iXxlJobService;
    private final XxlJobConfig xxlJobConfig;
    private final IBiddingApproveRecordBiz iBiddingApproveRecordBiz;
    private final IBiddingBuyerDealBiz iBiddingBuyerDealBiz;
    private final IMemberService iMemberService;
    private final IMessageSendService iMessageSendService;
    private final IEmailSendService iEmailSendService;
    private final ISmsSendService iSmsSendService;
    private final IAccountRelationService iAccountRelationService;
    private final IAccountService iAccountService;
    private final IBiddingBuyerDetailBiz iBiddingBuyerDetailBiz;
    private final ISalesPlanBiz iSalesPlanBiz;
    private final IValueSetService iValueSetService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final IGoodsMappingSapService iGoodsMappingSapService;
    private final IBiddingAccountService iBiddingAccountService;
    private final IWechatSendService iWechatSendService;
    private final BizRedisService bizRedisService;
    private final IBiddingBuyerBiz iBiddingBuyerBiz;
    private final IEnquiryBiz iEnquiryBiz;
    private final IGoodCategoryRelationService iGoodCategoryRelationService;

    @Resource
    private IEmailSendService emailSendService;

    @Resource
    private IMessageSendService messageSendService;

    @Resource(name = "threadPoolExecutor")
    private Executor executor;

    @Resource
    private IRoleService roleService;


    /**
     * 二级审批竞价策略通过判断竞价开始、结束时间是否在当前时间之后的容错时间（分） 当前+faultToleranceTime
     * 2025.4.23 调整审批容错时间为一分钟
     */
    @Value("${bidding.approveJob.faultToleranceTime:1}")
    private Integer faultToleranceTime;


    /**
     *逆向流程 （策略撤回、竞价取消）审批拒绝还原竞价状态若错时间（秒）
     */
    @Value("${bidding.approveJob.reverseRejectFaultToleranceTime:10}")
    private Integer reverseRejectFaultToleranceTime;

    //销售渠道
    private static final String VS_SALES_CHANNEL = "VS_SALES_CHANNEL";
    //销售组
    private static final String VS_SALES_GROUP = "VS_SALES_GRUOP";
    //销售单位
    private static final String VS_SALES_UNIT = "VS_SALES_UNIT";
    //运输方式
    private static final String VS_DELIVERY_METHOD = "VS_DELIVERY_METHOD";
    //工厂
    private static final String VS_FACTORY = "VS_FACTORY";

    private static final String BIDDING_NO = "biddingNo";
    private static final String BIDDING_NAME = "biddingName";
    private static final String REAL_NAME = "realName";
    private static final String TEMPLATE = "{}_{}";
    private static final String MESSAGE_LAST = "，请及时处理，谢谢。";
    private static final String APPROVAL_NAME = "approvalName";
    private static final String REJECT_REASON = "rejectReason";


    /**
     * 自提
     */
    private static final String VO_DELIVERY_METHOD_BP = "VO_DELIVERY_METHOD_BP";

    /**
     * 配送
     */
    private static final String VO_DELIVERY_METHOD_SD = "VO_DELIVERY_METHOD_SD";

    /**
     *order:approval:bidding:竞价单号
     */
    private static final String BIDDING_APPROVAL_LOCK = "order:approval:bidding:{}";


    @Override
    public List<SimpleBiddingDTO> getWaitStartBidding(Date startDateTime, Date endDateTime) {

        return biddingMapper.getSimpleBidding(BiddingStatusEnum.APPROVED_STRATEGY.getStatus(),
                DateUtil.formatDateTime(startDateTime), DateUtil.formatDateTime(endDateTime));
    }

    /**
     * 批量创建或者单个创建竞价
     *
     * @param biddingCreateBiddingDTO 传参信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingCreateReturnDTO batchCreateBidding(BiddingCreateBiddingDTO biddingCreateBiddingDTO, int dataFrom) {
        List<String> idList = biddingCreateBiddingDTO.getIdList();
        String status = biddingCreateBiddingDTO.getStatus();
        //竞价开始时间
        Date biddingStartTime = new DateTime(biddingCreateBiddingDTO.getBiddingStartTime());
        //竞价结束时间
        Date biddingEndTime = new DateTime(biddingCreateBiddingDTO.getBiddingEndTime());
        BiddingCreateReturnDTO biddingCreateReturnDTO = new BiddingCreateReturnDTO();
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        List<BiddingCreateDataDTO> biddingDataList = biddingMapper.selectCreateBiddingList(idList);
        int dataSource = biddingCreateBiddingDTO.getDataSource();
        List<String> buyerList = biddingCreateBiddingDTO.getBuyerList();
        String id = biddingCreateBiddingDTO.getId();
        List<BiddingBuyerDTO> buyerDTOS = getUserInfo(dataFrom, idList, dataSource, buyerList, id, biddingDataList, biddingCreateBiddingDTO);
        //剩余量不足
        List<String> quantityGoodsNameList = new ArrayList<>();
        List<String> quantitySalesNoList = new ArrayList<>();
        //状态不是已完成或者已作废
        List<String> statusGoodsNameList = new ArrayList<>();
        List<String> statusSalesNoList = new ArrayList<>();
        //竞价开始时间销售计划提货结束时间不满足校验的询价单号
        List<String> verifyFailDeliveryEffectEndDateEnquiryNoList = new ArrayList<>();
        //bidding插入数据list
        List<Bidding> insertList = new ArrayList<>();
        //biddingBuyer插入数据list
        List<BiddingBuyer> insertBuyerList = new ArrayList<>();
        //成功数量
        int successNo = 0;
        //失败数量
        int errorNo = 0;
        for (BiddingCreateDataDTO createDataDTO : biddingDataList) {
            //单个创建判断
            if(Objects.equals(dataFrom,OrderNumberConstant.TWO)){
                this.verifyBiddingStartTime(biddingStartTime,createDataDTO.getDeliveryEffectEndDate(),true);
            }else{
                if(BooleanUtil.isTrue(this.verifyBiddingStartTime(biddingStartTime,createDataDTO.getDeliveryEffectEndDate(),false))){
                    errorNo++;
                    setNoNullValue(verifyFailDeliveryEffectEndDateEnquiryNoList, createDataDTO.getEnquiryNo());
                    continue;
                }
            }
            BigDecimal remainSellableQuantity = createDataDTO.getRemainSellableQuantity();
            //剩余量是否大于0
            Boolean quantityCorrect = getQuantityCorrect(remainSellableQuantity);
            String currentStatus = createDataDTO.getCurrentStatus();
            //状态是否正确
            boolean statusCorrect = checkCurrentStatus(currentStatus);
            String enquiryNo = createDataDTO.getEnquiryNo();
            String salesPlanNo = createDataDTO.getSalesPlanNo();
            String goodsCode = createDataDTO.getGoodsCode();
            String formatData = DateUtil.format(new Date(), "yyyy年MM月dd日");
            if (Boolean.TRUE.equals(quantityCorrect)) {
                if (Boolean.TRUE.equals(statusCorrect)) {
                    Bidding bidding = BeanUtil.copyProperties(createDataDTO, Bidding.class);
                    bidding.setBiddingStartTime(biddingStartTime);
                    bidding.setBiddingEndTime(biddingEndTime);
                    bidding.setLastBiddingStartTime(biddingStartTime);
                    bidding.setLastBiddingEndTime(biddingEndTime);
                    //CR 2025.4.28 提货时间处理
                    bidding.setDeliveryEffectStartDate(this.getNewDeliveryEffectStartDate(bidding.getLastBiddingStartTime(),bidding));
                    bidding.setId(IdUtil.simpleUUID());
                    String biddingNo = generatorCodeUtil.generate("A", 3);
                    bidding.setBiddingNo(biddingNo);
                    String biddingName = formatData + bidding.getGoodsName() + "产品竞价";
                    bidding.setBiddingName(biddingName);
                    bidding.setCreateUserName(biddingCreateBiddingDTO.getRealName());
                    bidding.setCreateUser(biddingCreateBiddingDTO.getAccountId());
                    bidding.setCreateTime(new Date());
                    bidding.setCurrentRound(1);
                    bidding.setDelFlg(false);
                    bidding.setStatus(status);
                    insertList.add(bidding);
                    List<BiddingBuyerDTO> biddingBuyerList = buyerDTOS.stream().filter(f -> f.getGoodsCode().equals(goodsCode)).collect(Collectors.toList());
                    List<BiddingBuyer> biddingBuyers = BeanUtil.copyToList(biddingBuyerList, BiddingBuyer.class);
                    biddingBuyers.forEach(data -> {
                        data.setId(IdUtil.simpleUUID());
                        data.setBiddingNo(biddingNo);
                        data.setParticipationStatus(BiddingBuyerStatusEnum.TODO.getStatus());
                        data.handleUser(biddingCreateBiddingDTO.getAccountId(),true);
                    });
                    insertBuyerList.addAll(biddingBuyers);
                } else {
                    errorNo++;
                    setNoNullValue(statusGoodsNameList, enquiryNo);
                    setNoNullValue(statusSalesNoList, salesPlanNo);
                }
            } else {
                errorNo++;
                setNoNullValue(quantityGoodsNameList, enquiryNo);
                setNoNullValue(quantitySalesNoList, salesPlanNo);
            }
        }
        biddingCreateReturnDTO.setStatusGoodsNameList(statusGoodsNameList);
        biddingCreateReturnDTO.setQuantityGoodsNameList(quantityGoodsNameList);
        biddingCreateReturnDTO.setQuantitySalesNoList(quantitySalesNoList);
        biddingCreateReturnDTO.setStatusSalesNoList(statusSalesNoList);
        biddingCreateReturnDTO.setVerifyFailDeliveryEffectEndDateEnquiryNoList(verifyFailDeliveryEffectEndDateEnquiryNoList);
        //设置包装方式和销售组
        setGoodsData(insertList);
        successNo = this.insertList(insertList);
        biddingBuyerService.insertList(insertBuyerList);
        biddingCreateReturnDTO.setErrorNo(errorNo);
        biddingCreateReturnDTO.setSuccessNo(successNo);
        return biddingCreateReturnDTO;
    }

    private void setNoNullValue(List<String> list, String value) {
        if (StringUtils.isNotEmpty(value)) {
            list.add(value);
        }
    }


    /**
     * 判断询价下的所有竞价状态是否满足创建新的竞价
     * @param currentStatus 询价下的所有竞价状态 多个用逗号隔开
     * @return true:允许创建 false:状态不符合不允许创建
     */
    public Boolean checkCurrentStatus(String currentStatus) {
        if (StringUtils.isBlank(currentStatus)) {
            return true;
        }
        List<String> statusList = new ArrayList<>(Arrays.asList(currentStatus.split(",")));
        //允许的状态 已作废 已完成 CR 2025.5.26 新增逆向流程状态已取消
        List<String> allowStatus = List.of(BiddingStatusEnum.CANCELLED.getStatus(),
                BiddingStatusEnum.COMPLETED.getStatus(),
                BiddingStatusEnum.BIDDING_CANCELLED.getStatus());

        return statusList.stream().allMatch(v -> CollUtil.contains(allowStatus,v));
    }

    private void setGoodsData(List<Bidding> insertList) {
        if (CollUtil.isNotEmpty(insertList)) {
            List<String> goodsCodeList = insertList.stream().map(Bidding::getGoodsCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            GoodCodesListDTO dto = new GoodCodesListDTO();
            dto.setGoodsCodeList(goodsCodeList);
            ItemResult<List<GoodsDataListDTO>> packSalesGroupByGoodsCode = goodsService.getPackSalesGroupByGoodsCode(dto);
            List<GoodsDataListDTO> goodsCodeData = packSalesGroupByGoodsCode.getData();
            if (CollUtil.isNotEmpty(goodsCodeData)) {
                insertList.forEach(data -> {
                    GoodsDataListDTO goodsDataListDTO = goodsCodeData.stream().filter(f -> f.getGoodsCode().equals(data.getGoodsCode())).findFirst().orElse(null);
                    if (Objects.nonNull(goodsDataListDTO)) {
                        data.setPack(goodsDataListDTO.getPack());
                        data.setSalesGroup(goodsDataListDTO.getSalesGroup());
                    }
                });
            }
        }
    }

    /**
     * 剩余量是否大于0
     */
    public Boolean getQuantityCorrect(BigDecimal remainSellableQuantity) {
        boolean quantityCorrect;
        if (Objects.isNull(remainSellableQuantity)) {
            quantityCorrect = false;
        } else {
            quantityCorrect = remainSellableQuantity.compareTo(BigDecimal.ZERO) > 0;
        }
        return quantityCorrect;
    }

    /**
     * 查询买家信息
     */
    public List<BiddingBuyerDTO> getUserInfo(int dataFrom, List<String> idList, int dataSource, List<String> buyerList, String id, List<BiddingCreateDataDTO> biddingDataList, BiddingCreateBiddingDTO biddingCreateBiddingDTO) {
        //参与竞价的买家
        List<BiddingBuyerDTO> buyerDTOS = new ArrayList<>();
        if (dataFrom == 1) {
            String enquiryStatus = BiddingBuyerStatusEnum.DONE.getStatus();
            buyerDTOS = enquiryBuyerService.selectBuyerList(idList, enquiryStatus);
        }
        if (dataFrom == 2) {
            String payCondition = biddingCreateBiddingDTO.getPayCondition();
            String priceTradeTerm = biddingCreateBiddingDTO.getPriceTradeTerm();
            String tradeCurrency = biddingCreateBiddingDTO.getTradeCurrency();
            biddingDataList.forEach(bidding -> {
                bidding.setPayCondition(payCondition);
                bidding.setPriceTradeTerm(priceTradeTerm);
                bidding.setTradeCurrency(tradeCurrency);
            });
            if (dataSource == 1) {
                String enquiryStatus = BiddingBuyerStatusEnum.DONE.getStatus();
                buyerDTOS = enquiryBuyerService.selectBuyerList(idList, enquiryStatus);
                if (CollUtil.isNotEmpty(buyerList)) {
                    BiddingCreateDataDTO first = CollUtil.getFirst(biddingDataList);
                    String goodsCode = first.getGoodsCode();
                    String goodsName = first.getGoodsName();
                    BiddingCustomerInfoDTO queryCustomer = new BiddingCustomerInfoDTO();
                    queryCustomer.setEnquiryId(CollUtil.getFirst(idList));
                    queryCustomer.setGoodsCode(goodsCode);
                    queryCustomer.setInMemberCodes(buyerList);
                    List<BiddingCustomerListDTO> allowCustomer = this.queryCustomerInfoList(queryCustomer);
                    if(CollUtil.isNotEmpty(allowCustomer)) {
                        List<BiddingBuyerDTO> biddingBuyers = allowCustomer.stream().map(v->{
                            BiddingBuyerDTO target = new BiddingBuyerDTO();
                            target.setGoodsCode(goodsCode);
                            target.setGoodsName(goodsName);
                            target.setBuyerSource(BiddingBuyerStatusEnum.INVITED.getStatus());
                            target.setMemberCode(v.getMemberCode());
                            target.setMemberName(v.getMemberName());
                            return target;
                        }).collect(Collectors.toList());
                        buyerDTOS.addAll(biddingBuyers);
                    }

                }
            } else if (dataSource == 2) {
                BiddingBuyer biddingBuyer = biddingBuyerService.queryBiddingBuyer(id);
                if (BeanUtil.isNotEmpty(biddingBuyer)) {
                    List<MemberDataInfoDTO> memberDTOS;
                    try {
                        memberDTOS = memberService.queryMemberInfo(biddingBuyer.getGoodsCode());
                    } catch (Exception e) {
                        log.error("memberService query Member info error:{}", biddingBuyer.getGoodsCode(), e);
                        return Collections.emptyList();
                    }
                    buyerDTOS = BeanUtil.copyToList(memberDTOS, BiddingBuyerDTO.class);
                    buyerDTOS.forEach(data -> {
                        data.setGoodsName(biddingBuyer.getGoodsName());
                        data.setGoodsCode(biddingBuyer.getGoodsCode());
                    });
                }
            }
        }
        return buyerDTOS;
    }

    /**
     * 根据销售ID创建竞价信息
     *
     * @param biddingCreateBiddingDTO 接收参数
     * @return BiddingCreateReturnDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingCreateReturnDTO singleCreateBidding(BiddingCreateBiddingDTO biddingCreateBiddingDTO, int dataFrom) {
        //调用批量创建的方式创建单个竞价
        if (biddingCreateBiddingDTO.getDataSource() == 2) {
            biddingCreateBiddingDTO.setIdList(Collections.singletonList(biddingCreateBiddingDTO.getId()));
        }
        return self.batchCreateBidding(biddingCreateBiddingDTO, dataFrom);
    }

    /**
     * 创建竞价场次信息查询
     *
     * @param id 销售ID
     * @return BiddingQueryCreateDataDTO
     */
    @Override
    public BiddingQueryCreateDataDTO queryCreateBiddingData(String id) {
        BiddingQueryCreateDataDTO biddingQueryCreateDataDTO = new BiddingQueryCreateDataDTO();
        biddingQueryCreateDataDTO.setSalesId(id);
        String enquiryId = biddingMapper.queryEnquiryIdBySalesId(id);
        biddingQueryCreateDataDTO.setEnquiryId(enquiryId);
        return biddingQueryCreateDataDTO;
    }

    /**
     * 客户列表信息查询
     *
     * @param dto 入参
     * @return BiddingCustomerListDTO
     */
    @Override
    public List<BiddingCustomerListDTO> queryCustomerInfoList(BiddingCustomerInfoDTO dto) {
        //查询已参与询价客户
        List<EnquiryBuyer> joinEnquiryBuyers = enquiryBuyerMapper.queryEnquiryBuyer(dto.getEnquiryId(),ParticipationStatusEnum.DONE.getCode());
        List<String> notInMemberCodes = CommonUtils.getListValueByDistinctAndFilterBank(joinEnquiryBuyers,EnquiryBuyer::getMemberCode);
        //查询已添加到可参与竞价客户中的数据
        if(CharSequenceUtil.isNotBlank(dto.getBiddingNo())) {
            List<BiddingBuyer> list = iBiddingBuyerBiz.listByNo(dto.getBiddingNo());
            if(CollUtil.isNotEmpty(list))
                notInMemberCodes = CommonUtils.unionDistinct(notInMemberCodes,CommonUtils.getListValueByDistinctAndFilterBank(list,BiddingBuyer::getMemberCode));
        }
        QueryBiddingMemberDTO query = new QueryBiddingMemberDTO();
        query.setGoodsCode(dto.getGoodsCode());
        query.setCrmCode(dto.getCrmCode());
        query.setMemberName(dto.getMemberName());
        query.setNotInMemberCodes(notInMemberCodes);
        query.setMainGoods(dto.getMainGoods());
        query.setMinorGoods(dto.getSecondoryGoods());
        query.setInMemberCodes(dto.getInMemberCodes());

        List<BiddingMemberDTO> result = memberService.queryBiddingMemberList(query);

        return BeanUtil.copyToList(result,BiddingCustomerListDTO.class);
    }

    /**
     * 卖家竞价场次列表
     *
     * @param biddingSalesListDTO DTO
     * @param dataFrom            1列表查看 2审批列表
     * @return BiddingSalesListViewDTO
     */
    @Override
    public PageInfo<BiddingSalesListViewDTO> salesBiddingList(BiddingSalesListDTO biddingSalesListDTO, int dataFrom) {
        if (dataFrom == 1) {
            AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
            accountRoleDTO.setAccountId(biddingSalesListDTO.getAccountId());
            List<DataPermissionDTO> goodsByCategoryAccountId;
            try {
                goodsByCategoryAccountId = roleService.getDataPermissionList(accountRoleDTO);
            } catch (Exception e) {
                log.error("Failed to get data permission list for account ID: {}", accountRoleDTO.getAccountId(), e);
                return new PageInfo<>();
            }
            if (CollUtil.isEmpty(goodsByCategoryAccountId)) {
                return null;
            }
            List<String> goodsCodeList = goodsByCategoryAccountId.stream().map(DataPermissionDTO::getGoodsCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            if (CollUtil.isEmpty(goodsCodeList)) {
                return null;
            }
            List<String> roleList = biddingSalesListDTO.getRoleList();
            biddingSalesListDTO.setGoodsCodeList(goodsCodeList);
        }
        String status = biddingSalesListDTO.getStatus();
        if (StringUtils.isNotEmpty(status)) {
            biddingSalesListDTO.setStatusParams(List.of(status));
        }
        Integer pageSize = biddingSalesListDTO.getPageSize();
        if (pageSize.compareTo(-1) != 0) {
            PageMethod.startPage(biddingSalesListDTO.getPageNum(), pageSize);
        }
        List<BiddingSalesListViewDTO> list = biddingMapper.salesBiddingList(biddingSalesListDTO);
        //处理轮次
        handRound(list);

        return new PageInfo<>(list);
    }

    /**
     * 草稿-编辑竞价场次查询
     *
     * @param biddingNo 竞价ID
     * @return 竞价信息
     */
    @Override
    public List<BiddingCustomerListDTO> queryEditQueryData(String biddingNo) {
        List<BiddingBuyer> biddingBuyers = biddingBuyerService.queryCustomerList(biddingNo);
        List<String> memberCodeList = biddingBuyers.stream().map(BiddingBuyer::getMemberCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberCodeList)) {
            List<MemberGoodsInfoDTO> infoDTOS;
            try {
                infoDTOS = memberService.queryGoodsByMemberCode(memberCodeList);
            } catch (Exception e) {
                log.error("memberService query goods by member code error:{}", memberCodeList, e);
                return Collections.emptyList();
            }
            List<BiddingCustomerListDTO> list = BeanUtil.copyToList(biddingBuyers, BiddingCustomerListDTO.class);
            List<String> goodsIdList = infoDTOS.stream().map(MemberGoodsInfoDTO::getGoodsId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(goodsIdList)) {
                GoodsCodeInfoDTO goodsCodeInfoDTO = new GoodsCodeInfoDTO();
                goodsCodeInfoDTO.setGoodsIds(goodsIdList);
                List<GoodsNameInfoDTO> goodsNameInfoDTOS;
                try {
                    goodsNameInfoDTOS = goodsService.queryGoodsNameByGoodsCode(goodsCodeInfoDTO);
                } catch (Exception e) {
                    log.error("goodsService query goods name by goods code error:{}", goodsCodeInfoDTO, e);
                    return Collections.emptyList();
                }
                setGoodsInfo(list, infoDTOS, goodsNameInfoDTOS);
                return list;
            }
        }
        return Collections.emptyList();
    }

    private void setGoodsInfo(List<BiddingCustomerListDTO> list, List<MemberGoodsInfoDTO> infoDTOS, List<GoodsNameInfoDTO> goodsNameInfoDTOS) {
        list.forEach(data -> {
            List<MemberGoodsInfoDTO> collect = infoDTOS.stream().filter(f -> BeanUtil.isNotEmpty(data.getMemberCode()) && data.getMemberCode().equals(f.getMemberCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                //主要
                setMainGoods(collect, goodsNameInfoDTOS, data);
                //次要
                setMinorGoods(collect, goodsNameInfoDTOS, data);
            }
        });
    }

    private void setMinorGoods(List<MemberGoodsInfoDTO> collect, List<GoodsNameInfoDTO> goodsNameInfoDTOS, BiddingCustomerListDTO data) {
        List<MemberGoodsInfoDTO> minGoodsList = collect.stream().filter(f -> f.getIntentionType() == 2).collect(Collectors.toList());
        List<String> nameList = new ArrayList<>();
        minGoodsList.forEach(goods -> {
            GoodsNameInfoDTO min = goodsNameInfoDTOS.stream().filter(f -> f.getGoodsCode().equals(goods.getGoodsCode())).findFirst().orElse(null);
            data.setCrmCode(goods.getCrmCode());
            if (Objects.nonNull(min)) {
                nameList.add(min.getGoodsName());
            }
        });
        String names = String.join(",", nameList);
        data.setMinorGoods(names);
    }

    private void setMainGoods(List<MemberGoodsInfoDTO> collect, List<GoodsNameInfoDTO> goodsNameInfoDTOS, BiddingCustomerListDTO data) {
        MemberGoodsInfoDTO mainGoods = collect.stream().filter(f -> f.getIntentionType() == 1).findFirst().orElse(null);
        if (Objects.nonNull(mainGoods)) {
            GoodsNameInfoDTO goodsNameMain = goodsNameInfoDTOS.stream().filter(f -> f.getGoodsCode().equals(mainGoods.getGoodsCode())).findFirst().orElse(null);
            data.setCrmCode(mainGoods.getCrmCode());
            if (Objects.nonNull(goodsNameMain)) {
                data.setMainGoods(goodsNameMain.getGoodsName());
            }
        }
    }

    @Override
    public Bidding getByNo(String biddingNo) {
        Condition condition = new Condition(Bidding.class);
        condition.createCriteria()
                .andEqualTo(ColumnConstant.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);
        List<Bidding> list = this.findByCondition(condition);
        if (CollUtil.size(list) > OrderNumberConstant.ONE)
            throw new BizException(OrderErrorCode.CUSTOM, "存在多条竞价数据");
        return CollUtil.getFirst(list);
    }

    /**
     * 草稿-提交/保存竞价场次
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitSingleData(BiddingCreateBiddingSaveDTO dto) {
        Bidding byPrimaryKey = biddingMapper.selectByPrimaryKey(dto.getId());
        if(Objects.isNull(byPrimaryKey))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不存在");

        Bidding bidding = BeanUtil.copyProperties(dto, Bidding.class);
        //竞价开始时间
        Date biddingStartTime = new DateTime(dto.getBiddingStartTime());
        //竞价结束时间
        Date biddingEndTime = new DateTime(dto.getBiddingEndTime());
        bidding.setBiddingStartTime(biddingStartTime);
        bidding.setBiddingEndTime(biddingEndTime);
        bidding.setLastBiddingStartTime(biddingStartTime);
        bidding.setLastBiddingEndTime(biddingEndTime);
        //CR 2025-04-28 提货时间调整
        bidding.setDeliveryEffectStartDate(this.getNewDeliveryEffectStartDate(bidding.getLastBiddingStartTime(), byPrimaryKey));
        //校验竞价开始时间
        this.verifyBiddingStartTime(bidding.getLastBiddingStartTime(),byPrimaryKey.getSalesPlanNo(),true);

        bidding.setUpdateTime(new Date());
        bidding.setUpdateUser(dto.getRealName());
        if (Boolean.TRUE.equals(BiddingStatusEnum.hasStatus(dto.getStatus()))) {
            //更新竞价主表
            biddingMapper.updateByPrimaryKeySelective(bidding);

            if (CollUtil.isEmpty(dto.getBuyerList()))
                return true;

            //新增竞价买家
            List<String> buyerList = dto.getBuyerList();

            Enquiry enquiry = iEnquiryBiz.getByNo(byPrimaryKey.getEnquiryNo());
            //原有竞价买家
            List<String> memberCodeList = biddingBuyerService.queryMemberByBiddingId(dto.getId());
            BiddingCustomerInfoDTO biddingCustomerInfoDTO = new BiddingCustomerInfoDTO();
            if(Objects.nonNull(enquiry))
                biddingCustomerInfoDTO.setEnquiryId(enquiry.getId());
            biddingCustomerInfoDTO.setBiddingNo(byPrimaryKey.getBiddingNo());
            biddingCustomerInfoDTO.setGoodsCode(byPrimaryKey.getGoodsCode());

            //处理新添加的参与客户 需要在添加客户查询 范围内 且不能与已经添加的客户重复
            List<BiddingCustomerListDTO> allowCustomerList = this.queryCustomerInfoList(biddingCustomerInfoDTO);
            if (CollUtil.isEmpty(allowCustomerList))
                return true;

            List<BiddingCustomerListDTO> newCustomerList = allowCustomerList.stream()
                    .filter(f -> CollUtil.contains(buyerList, f.getMemberCode()) &&
                            !CollUtil.contains(memberCodeList, f.getMemberCode()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(newCustomerList))
                return true;

            List<BiddingBuyer> newBuyers = newCustomerList.stream()
                    .map(v -> {
                        BiddingBuyer data = new BiddingBuyer();
                        data.setId(IdUtil.simpleUUID());
                        data.setMemberCode(v.getMemberCode());
                        data.setMemberName(v.getMemberName());
                        data.setBiddingNo(byPrimaryKey.getBiddingNo());
                        data.setBuyerSource(BiddingBuyerStatusEnum.INVITED.getStatus());
                        data.setGoodsCode(byPrimaryKey.getGoodsCode());
                        data.setGoodsName(byPrimaryKey.getGoodsName());
                        data.setParticipationStatus(BiddingBuyerStatusEnum.TODO.getStatus());
                        data.handleUser(dto.getAccountId(),true);
                        return data;
                    }).collect(Collectors.toList());

            biddingBuyerService.insertList(newBuyers);
        }
        return true;
    }

    /**
     * 提交竞价策略
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitStrategyData(BiddingSubmitStrategySaveDTO dto) {

        Bidding sourceBidding = this.get(dto.getBiddingId());
        if(Objects.isNull(sourceBidding))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次信息不存在");

        Bidding bidding = BeanUtil.copyProperties(dto, Bidding.class);
        String realName = dto.getRealName();
        bidding.setId(dto.getBiddingId());
        bidding.setUpdateUser(dto.getRealName());
        bidding.setUpdateTime(new Date());
        bidding.setSubmitUser(dto.getAccountId());
        bidding.setSubmitTime(new Date());
        bidding.setSubmitUserName(realName);
        bidding.setSubmitUpdateUser(dto.getAccountId());
        bidding.setSubmitUpdateTime(new Date());
        bidding.setSubmitUpdateUserName(realName);
        bidding.setLastStandardPrice(dto.getStandardPrice());
        bidding.setStatus(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus());
        //CR 2025.4.28 提货时间处理
        bidding.setDeliveryEffectStartDate(this.getNewDeliveryEffectStartDate(sourceBidding.getLastBiddingStartTime(), sourceBidding));
        biddingMapper.updateByPrimaryKeySelective(bidding);
        List<String> biddingIdList = Arrays.asList(dto.getBiddingId());
        submitBiddingForSendEmail(biddingIdList, realName);
        return true;
    }

    public void submitBiddingForSendEmail(List<String> biddingIdList, String realName) {
        EmailDTO emailDTO = new EmailDTO();
        EmailTemplateEnum biddingSubmitTemplate = EmailTemplateEnum.BIDDING_SUBMIT_TEMPLATE;
        emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());
        //bidding数据
        List<BiddingEmailDTO> biddingInfo = querySendEmailUserInfo(BaseRoleTypeEnum.SELLER_CMMS, biddingIdList);
        //CMMS
        List<AccountNameDTO> accountNameByRoleCode = iAccountService.findAccountNameByRoleCode(BaseRoleTypeEnum.SELLER_CMMS.getRoleCode());
        //商务经理
        List<String> goodsCodeList = biddingMapper.queryCategoryCodeList(biddingIdList);
        DataPermissionGoodsCodeDTO dataPermissionGoodsCodeDTO = new DataPermissionGoodsCodeDTO();
        dataPermissionGoodsCodeDTO.setGoodsCodeList(goodsCodeList);
        dataPermissionGoodsCodeDTO.setRoleCode(BaseRoleTypeEnum.SELLER_BUSINESS_MANAGER.getRoleCode());
        List<DataPermissionAccountInfoDTO> accountInfoDTOS = roleService.findAccountByGoodsCode(dataPermissionGoodsCodeDTO);

        biddingInfo.forEach(data -> {
            //Tos
            List<String> cEmail = accountNameByRoleCode.stream().map(AccountNameDTO::getEmail).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<String> cName = accountNameByRoleCode.stream().map(AccountNameDTO::getRealName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            //ToCC
            List<String> ccEmail = new ArrayList<>();
            DataPermissionAccountInfoDTO dataPermissionAccountInfoDTO = accountInfoDTOS.stream().filter(f -> f.getGoodsCode().equals(data.getGoodsCode())).findFirst().orElse(null);
            if (BeanUtil.isNotEmpty(dataPermissionAccountInfoDTO)) {
                List<AccountInfoDTO> accountList = dataPermissionAccountInfoDTO.getAccountList();
                ccEmail = accountList.stream().map(AccountInfoDTO::getEmail).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                emailDTO.setToCcs(ccEmail);
            }
            if (CollUtil.isNotEmpty(cEmail)) {
                emailDTO.setTos(cEmail);
                String joinWithUserName = String.join(",", cName);
                //邮件模板参数
                Map<String, Object> templateParam = new HashMap<>();
                templateParam.put(REAL_NAME, joinWithUserName);
                templateParam.put(BIDDING_NO, data.getBiddingNo());
                templateParam.put(BIDDING_NAME, data.getBiddingName());
                templateParam.put("submitUser", realName);
                emailDTO.setTemplateParam(templateParam);
                //发送邮件
                emailSendService.sendEmail(emailDTO);
                //站内信
                accountNameByRoleCode.forEach(message -> {
                    MessageDTO messageDTO = new MessageDTO();
                    messageDTO.setReceiveAccountIds(Arrays.asList(message.getAccountId()));
                    messageDTO.setTitle("竞价策略待审批");
                    messageDTO.setContent(" 尊敬的" + message.getRealName() + "，您有一条新的竞价策略需要审批，竞价场次编号为" + data.getBiddingNo() + MESSAGE_LAST);
                    messageSendService.sendMessage(messageDTO);
                });
            }
        });
    }

    public List<BiddingEmailDTO> querySendEmailUserInfo(BaseRoleTypeEnum baseRoleTypeEnum, List<String> idList) {
        return biddingMapper.querySendEmailUserInfo(baseRoleTypeEnum.getRoleCode(), idList);
    }

    /**
     * 竞价场次审批列表
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<BiddingSalesListViewDTO> queryApproveList(BiddingSalesListDTO dto) {
        int dataFrom;
        List<String> roleList = dto.getRoleList();
        List<String> statusList;
        String CMMS = BaseRoleTypeEnum.SELLER_CMMS.getRoleCode();
        String CM = BaseRoleTypeEnum.SELLER_CM.getRoleCode();
        String SYSTEM_ADMIN = BaseRoleTypeEnum.SYSTEM_ADMIN.getRoleCode();

        //策略审批状态
        List<String> strategyStatusList = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_STRATEGY.getStatus());
        //成交结果审批状态
        List<String> resultStatusList = Arrays.asList(BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(),
                BiddingStatusEnum.APPROVING_RESULT.getStatus());
        //撤回策略审批状态
        List<String> withdrawStatusList = Arrays.asList(BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus());
        //取消竞价审批状态
        List<String> cancelStatusList = Arrays.asList(BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.CANCEL_APPROVING.getStatus());
        //待审批状态
        List<String> toApprovalStatusList = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(),
                BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus());
        //审批中状态
        List<String> approvingStatusList = Arrays.asList(BiddingStatusEnum.APPROVING_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_RESULT.getStatus(),
                BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus(),
                BiddingStatusEnum.CANCEL_APPROVING.getStatus());

        if (CollUtil.isNotEmpty(roleList)) {
            if (roleList.contains(SYSTEM_ADMIN)) {
                dataFrom = 2;
                statusList = CommonUtils.addAll(null, strategyStatusList, resultStatusList,withdrawStatusList,cancelStatusList);
            }else if(CollUtil.containsAll(roleList,Arrays.asList(BaseRoleTypeEnum.SELLER_CM.getRoleCode(),
                    BaseRoleTypeEnum.SELLER_CMMS.getRoleCode()))){
                dataFrom = 2;
                statusList = CommonUtils.addAll(null, strategyStatusList, resultStatusList,withdrawStatusList,cancelStatusList);
            } else if (roleList.contains(CMMS)) {
                dataFrom = 2;
                statusList = toApprovalStatusList;
            } else if (roleList.contains(CM)) {
                dataFrom = 1;
                statusList = approvingStatusList;
            } else {
                //没有符合的角色信息，直接返回
                return null;
            }
            dto.setStatusList(statusList);
            String status = dto.getStatus();
            if (StringUtils.isNotEmpty(status)) {
                dto.setStatusParams(List.of(status));
            }
            return this.salesBiddingList(dto, dataFrom);
        } else {
            return null;
        }
    }

    /**
     * 竞价场次、成交结果批量审批通过驳回
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Boolean> batchApprovalOrRejected(BiddingApprovalDTO dto) {
        List<Integer> allowCheckStatus = Arrays.asList(OrderNumberConstant.ONE, OrderNumberConstant.TWO);
        Integer checkStatus = dto.getCheckStatus();
        if (!CollUtil.contains(allowCheckStatus, checkStatus))
            return fail("审批状态不合法");

        List<Bidding> biddingList = this.listByIds(dto.getBiddingIdList());
        //校验竞价状态是否符合审批操作
        ItemResult verifyRs = verifyBidding(dto, biddingList);
        if (Objects.nonNull(verifyRs))
            return verifyRs;

        try {
            //批量审批 对审批的单据加锁
            this.lock(biddingList,dto.getAccountId());

            return this.approvalOrRejectBiddingByBatch(dto, biddingList, checkStatus);
        }catch (BizException e){
            log.error("批量审批竞价数据 业务报错 error:",e);
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            log.error("批量审批竞价数据 异常 error:",e);
            return fail("竞价场次审批异常，请联系管理员");
        }finally {
            //审批操作结束 释放当前操作人的锁
            this.unlock(biddingList,dto.getAccountId());
        }
    }

    private static ItemResult verifyBidding(BiddingApprovalDTO dto, List<Bidding> biddingList) {
        if (CollUtil.isEmpty(biddingList))
            return fail("竞价场次数据不存在");

        List<String> roleCodes = dto.getRoleList();
        if (CollUtil.isEmpty(roleCodes))
            return fail("您没有审批权限");
        String errMsg = "当前场次状态已发生变更，请刷新页面";
        //所有允许审批的前置状态
        List<String> allAllowStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(), BiddingStatusEnum.APPROVING_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_RESULT.getStatus(),BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus(),BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.CANCEL_APPROVING.getStatus());
        if (!BooleanUtil.isTrue(biddingList.stream().allMatch(v -> CollUtil.contains(allAllowStatus, v.getStatus()))))
            return fail(errMsg);

        //如果有卖家管理员角色 操作放行
        if (CollUtil.contains(roleCodes, BaseRoleTypeEnum.SYSTEM_ADMIN.getRoleCode()))
            return null;
        //如果有cm 与cmms角色 操作放行
        if (CollUtil.contains(roleCodes, BaseRoleTypeEnum.SELLER_CMMS.getRoleCode())
                && CollUtil.contains(roleCodes, BaseRoleTypeEnum.SELLER_CM.getRoleCode()))
            return null;
        //拥有cmms角色 判断竞价是否为 策略待审批 结果待审批  2025.5.14 CR 新增逆向流程 撤回、取消 待审批状态
        if (CollUtil.contains(roleCodes, BaseRoleTypeEnum.SELLER_CMMS.getRoleCode())) {
            List<String> allowStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                    BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(),BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                    BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus());
            if (!BooleanUtil.isTrue(biddingList.stream()
                    .allMatch(v -> CollUtil.contains(allowStatus, v.getStatus()))))
                return fail(errMsg);
        } else if (CollUtil.contains(roleCodes, BaseRoleTypeEnum.SELLER_CM.getRoleCode())) {
            //拥有cm角色 判断竞价是否为 策略审批中 结果审批中 2025.5.14 CR 新增逆向流程 撤回、取消 审批中状态
            List<String> allowStatus = Arrays.asList(BiddingStatusEnum.APPROVING_STRATEGY.getStatus(),
                    BiddingStatusEnum.APPROVING_RESULT.getStatus(),BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus(),
                    BiddingStatusEnum.CANCEL_APPROVING.getStatus());
            if (!BooleanUtil.isTrue(biddingList.stream()
                    .allMatch(v -> CollUtil.contains(allowStatus, v.getStatus()))))
                return fail(errMsg);
        } else {
            return fail("您没有审批权限");
        }

        return null;
    }

    private ItemResult approvalOrRejectBiddingByBatch(BiddingApprovalDTO dto, List<Bidding> biddingList, Integer checkStatus) {
        Date now = new Date();
        //更新竞价状态，保存审批记录 并对应通知相关人员
        switch (checkStatus) {
            //审批通过
            case 1:
                this.biddingApprovalPass(dto, now, biddingList);
                break;
            //审批拒绝
            case 2:
                this.biddingApprovalReject(dto, now, biddingList);
                break;
            default:
                return fail("审批状态不合法");
        }

        return ItemResult.success(true);
    }

    /**
     * 竞价场次批量撤回
     *
     * @param bidding 主键ID
     * @return
     */
    @Override
    public Boolean singleWithdrawData(String bidding) {
        Bidding biddingData = this.getById(bidding);
        if (Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM, "竞价场次不存在");
        //只允许 竞价策略待审批 竞价策略审批中进行撤回
        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_STRATEGY.getStatus());
        if (!CollUtil.contains(allowStatus, biddingData.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不允许撤回竞价策略");

        biddingMapper.withdrawData(bidding, BiddingStatusEnum.TO_SUBMIT_STRATEGY.getStatus());
        return true;
    }

    /**
     * 竞价策略查看
     *
     * @param biddingId
     * @return
     */
    @Override
    public BiddingStrategyViewDTO queryStrategy(String biddingId) {
        BiddingStrategyViewDTO biddingStrategyViewDTO = new BiddingStrategyViewDTO();
        //竞价基础信息
//        BiddingEditBiddingDTO editBiddingDTO = queryEditQueryData(biddingId);
        //竞价策略
        BiddingStrategyDetailDTO biddingStrategyDetailDTO = biddingMapper.queryStrategyDetail(biddingId);
        //审批记录
        List<BiddingApproveListDTO> biddingApproveListDTOList = biddingApproveRecordService.queryApproveList(biddingId, ApproveTypeEnum.STRATEGY.getStatus());

//        biddingStrategyViewDTO.setBiddingEditBiddingDTO(editBiddingDTO);
        biddingStrategyViewDTO.setBiddingStrategyDetailDTO(biddingStrategyDetailDTO);
        biddingStrategyViewDTO.setBiddingApproveListDTOList(biddingApproveListDTOList);
        return biddingStrategyViewDTO;
    }

    @Override
    public int updateBiddingStatus(String biddingNo, BiddingStatusEnum status) {
        return biddingMapper.updateBiddingStatus(biddingNo, status.getStatus());
    }

    @Override
    public void createJob(Bidding bidding) {
        String notifyJobDesc = CharSequenceUtil.format("竞价场次编号为：{}（{}） 的通知客户竞价即将开始自动任务", bidding.getBiddingNo(), bidding.getBiddingName());
        String startJobDesc = CharSequenceUtil.format("竞价场次编号为：{}（{}） 的自动开始竞价任务", bidding.getBiddingNo(), bidding.getBiddingName());

        //通知客户自动任务
        CronPatternBuilder notifyCron = CronPatternBuilder.of();
        //竞价开始前10分钟
        Date notifyTime = DateUtil.offsetMinute(bidding.getLastBiddingStartTime(), -10);
        notifyCron.set(Part.SECOND, String.valueOf(DateUtil.second(notifyTime)));
        notifyCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(notifyTime)));
        notifyCron.set(Part.HOUR, String.valueOf(DateUtil.hour(notifyTime, true)));
        notifyCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(notifyTime)));
        notifyCron.set(Part.MONTH, String.valueOf(DateUtil.month(notifyTime) + 1));
        notifyCron.set(Part.DAY_OF_WEEK, "?");
        Integer year = DateUtil.year(notifyTime);
        notifyCron.setRange(Part.YEAR, year, year);
        XxlJobDTO notifyJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                notifyJobDesc,
                notifyCron.build(),
                XxlJobConstant.NOTIFY_BIDDING_START_JOB,
                bidding.getBiddingNo());
        iXxlJobService.createXxlJob(notifyJob);

        //竞价开始户自动任务
        CronPatternBuilder startCron = CronPatternBuilder.of();
        startCron.set(Part.SECOND, String.valueOf(DateUtil.second(bidding.getLastBiddingStartTime())));
        startCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(bidding.getLastBiddingStartTime())));
        startCron.set(Part.HOUR, String.valueOf(DateUtil.hour(bidding.getLastBiddingStartTime(), true)));
        startCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(bidding.getLastBiddingStartTime())));
        startCron.set(Part.MONTH, String.valueOf(DateUtil.month(bidding.getLastBiddingStartTime()) + 1));
        startCron.set(Part.DAY_OF_WEEK, "?");
        Integer startYear = DateUtil.year(bidding.getLastBiddingStartTime());
        startCron.setRange(Part.YEAR, startYear, startYear);
        XxlJobDTO startJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                startJobDesc,
                startCron.build(),
                XxlJobConstant.BIDDING_START_JOB,
                bidding.getBiddingNo());

        iXxlJobService.createXxlJob(startJob);

        this.createEndJob(bidding);
    }

    @Override
    public List<Bidding> listByIds(Collection<String> ids) {
        if (CollUtil.isEmpty(ids))
            return Collections.emptyList();
        Condition condition = new Condition(Bidding.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(ColumnConstant.ID, ids);

        return this.findByCondition(condition);
    }

    @Override
    public void updateBiddingStatusByIds(BiddingStatusEnum status, String operatorId, List<Bidding> biddingList, boolean isDeal) {
        if (CollUtil.isEmpty(biddingList))
            return;

        List<String> ids = biddingList.stream().map(v -> v.getId()).collect(Collectors.toList());

        log.info("更新竞价状态updateBiddingStatusByIds status：{} operatorId：{} biddingIds:{} isDeal:{}",status.getStatus(),operatorId,ids,isDeal);

        Condition condition = new Condition(Bidding.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.ID, ids);

        Bidding modify = new Bidding();
        modify.setStatus(status.getStatus());

        //成交确认
        transactionConfirmation(status, operatorId, isDeal, modify, biddingList);

        biddingMapper.updateByConditionSelective(modify, condition);

        //若为二级审批，则状态更新为：已完成，且对接SAP，创建合同，并记录SAP合同编号。
        if (BiddingStatusEnum.COMPLETED.equals(status)) {
            //对接SAP合同创建
            this.createSapContract(ids, null, true);
        }

        //策略审批通过 创建竞价自动任务
        if (BiddingStatusEnum.APPROVED_STRATEGY.equals(status)) {
            //创建竞价定时任务或直接开始竞价
            this.biddingStartOrJob(biddingList);
        }
    }

    public void transactionConfirmation(BiddingStatusEnum status, String operatorId, boolean isDeal, Bidding modify, List<Bidding> biddingList) {
        //成交确认
        if (BiddingStatusEnum.COMPLETED.equals(status) || BiddingStatusEnum.TO_APPROVE_RESULT.equals(status)) {
            if (CharSequenceUtil.isNotBlank(operatorId) && BooleanUtil.isTrue(isDeal)) {
                modify.setDealOperator(operatorId);
                modify.setSubmitDealTime(new Date());
            }

            if (BiddingStatusEnum.COMPLETED.equals(status)) {
                modify.setDealTime(new Date());
                //更新销售计划剩余销售量
                List<String> biddingNos = biddingList.stream().map(v -> v.getBiddingNo()).collect(Collectors.toList());
                Map<String, BiddingDealQuantityDTO> biddingDealQuantity = CommonUtils.getMap(iBiddingBuyerDealBiz.getBiddingDealTotalQuantity(biddingNos), BiddingDealQuantityDTO::getBiddingNo);
                for (Bidding bidding : biddingList) {
                    BiddingDealQuantityDTO updateSalePlan = CommonUtils.getByKey(biddingDealQuantity, bidding.getBiddingNo());
                    if (Objects.nonNull(updateSalePlan)) {
                        //更新销售计划剩余可售量
                        iSalesPlanBiz.updateRemainSellAbleQuantity(bidding.getSalesPlanNo(), updateSalePlan.getTotalQuantity());
                        //更新竞价总成交量、本场剩余可售量
                        biddingMapper.updateBiddingQuantity(bidding.getBiddingNo(), updateSalePlan.getTotalQuantity());
                    }
                }
            }
        }
    }


    /**
     * 竞价开始 或者创建自动任务开始
     */
    private void biddingStartOrJob(List<Bidding> biddingList) {
        if (CollUtil.isEmpty(biddingList))
            return;

        Date nowTime = new Date();
        Date now;
        if (Objects.nonNull(faultToleranceTime))
            now = DateUtil.offsetMinute(nowTime, faultToleranceTime);
        else {
            now = nowTime;
        }
        //可以通过定时任务自动开始的
        List<Bidding> biddingByJob = biddingList.stream()
                .filter(v -> v.getLastBiddingStartTime().after(now))
                .collect(Collectors.toList());
        //不可通过定时任务开始的
        List<Bidding> biddingByNotJob = biddingList.stream()
                .filter(v -> !v.getLastBiddingStartTime().after(now))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(biddingByJob)) {
            executor.execute(() -> {
                for (Bidding bidding : biddingByJob) {
                    //定时任务执行 自动开始，通知，结束
                    this.createJob(bidding);
                }
            });
        }

        if (CollUtil.isNotEmpty(biddingByNotJob)) {
            List<BiddingBuyer> buyers = biddingBuyerService.getNotifyBiddingStartBuyer(biddingByNotJob);
            //更新状态到竞价中
            List<String> ids = biddingByNotJob.stream().map(v -> v.getId()).collect(Collectors.toList());
            biddingMapper.updateStatusByIds(ids, BiddingStatusEnum.BIDDING.getStatus());

            //通知相关人员
            for (Bidding bidding : biddingByNotJob) {
                //创建自动结束任务
                this.createEndJob(bidding);
            }
            //不通过定时任务 直接通知竞价开始
            executor.execute(() -> {
                this.notifyBuyerStartBidding(biddingByNotJob, buyers);
            });
        }

    }

    @Override
    public List<Bidding> listBySalePlanNo(List<String> salePlanNos, List<BiddingStatusEnum> status) {
        if (CollUtil.isEmpty(salePlanNos))
            return Collections.emptyList();

        Condition condition = new Condition(Bidding.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        criteria.andIn(ColumnConstant.SALES_PLAN_NO, salePlanNos);
        if (CollUtil.isNotEmpty(status))
            criteria.andIn(ColumnConstant.STATUS, status.stream().map(BiddingStatusEnum::getStatus).collect(Collectors.toList()));

        return this.findByCondition(condition);
    }


    @Override
    public void notifyByNotDeal(Bidding bidding, List<BiddingBuyerDetail> buyerDetails) {
        if (Objects.isNull(bidding) || CollUtil.isEmpty(buyerDetails))
            return;
        List<String> buyerCodes = buyerDetails.stream().map(v -> v.getMemberCode()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(buyerCodes))
            return;
        QueryMemberAccountDTO query = new QueryMemberAccountDTO(buyerCodes, Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()));
        List<AccountSimpleDTO> buyerAccounts = iMemberService.listAccountsByMemberCodes(query);
        //微信订阅记录
        Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMapByAdmin = iBiddingAccountService
                .queryAccountSubscribeWechat(getAccountIds(buyerAccounts), Arrays.asList(bidding.getBiddingNo()), SubscribeWechatBusinessTypeEnum.BIDDING);
        //通知客户企业管理员
        log.info("竞价不成交通知企业管理员 账户：{}", buyerAccounts);
        this.notifyBiddingNotDeal(bidding, buyerAccounts, wechatMapByAdmin, true);
        //通知客户产品人员
        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> permissionAccountMap = iBiddingAccountService.queryAccountDataPermission(buyerCodes, Arrays.asList(bidding.getGoodsCode()));
        List<QueryAccountDataPermissionDTO> keys = buyerCodes.stream()
                .map(v1 -> new QueryAccountDataPermissionDTO(v1, bidding.getGoodsCode()))
                .collect(Collectors.toList());
        //微信订阅记录
        Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMapByGoods = iBiddingAccountService
                .queryAccountSubscribeWechat(getAccountIds(Objects.nonNull(permissionAccountMap) ? permissionAccountMap.values() : null),
                        Arrays.asList(bidding.getBiddingNo()), SubscribeWechatBusinessTypeEnum.BIDDING);
        keys.forEach(v -> {
            List<AccountSimpleDTO> goodsAccount = CommonUtils.getByKey(permissionAccountMap, v);
            log.info("竞价不成交通知企业产品人员 账户：{}", goodsAccount);
            this.notifyBiddingNotDeal(bidding, goodsAccount, wechatMapByGoods, false);
        });
    }

    private void notifyBiddingNotDeal(Bidding bidding, List<AccountSimpleDTO> buyerAccounts,
                                      Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMap,
                                      boolean isAdmin) {
        log.info("竞价不成交通知开始 biddingNo:{} isAdmin:{} accounts:{}", bidding.getBiddingNo(), isAdmin, buyerAccounts);
        if (CollUtil.isEmpty(buyerAccounts))
            return;
        List<String> mobiles = buyerAccounts.stream().map(v -> v.getMobile()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        //短信通知
        SmsDTO sms = new SmsDTO();
        sms.setTemplateCode(SmsTemplateEnum.TRANSACTION_FAILURE_CODE.getCode());
        sms.setMobiles(mobiles);
        sms.setTemplateParams(Arrays.asList(bidding.getBiddingNo(), bidding.getBiddingName()));
        iSmsSendService.sendSms(sms);

        //站内信通知
        MessageDTO message = new MessageDTO();
        message.setTitle(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_NOT_DEAL.getTitle());
        buyerAccounts.forEach(v -> {
            message.setReceiveAccountIds(Arrays.asList(v.getAccountId()));
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_NOT_DEAL.getMsg(v.getMemberName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(message);

            List<AccountSubscribeWechatDTO> wechatAccounts = CommonUtils.getByKey(wechatMap,
                    new AccountSubscribeWechatKeyDTO(v.getAccountId(), bidding.getBiddingNo(), SubscribeWechatBusinessTypeEnum.BIDDING.getType()));
            this.notifyWechatByNotDeal(bidding, wechatAccounts);
        });
    }

    @Override
    public PageInfo<BiddingBuyerListViewDTO> buyerBiddingList(BiddingBuyerQueryDTO biddingBuyerListDTO) {
        return biddingBuyerService.buyerBiddingList(biddingBuyerListDTO);
    }

    @Override
    public ItemResult<PageInfo<ExportBiddingEnquiryDataDTO>> queryBiddingEnquiryDataList(ExportBiddingEnquiryDataDTO queryCondition) {
        MemberSimpleDataDTO condition = MemberSimpleDataDTO.builder().build();
        BeanUtil.copyProperties(queryCondition, condition);
        PageInfo<MemberSimpleDataDTO> memberSimpleDataDTOS = memberService.queryMemberByMemberNameAndCrmCode(condition);
        List<MemberSimpleDataDTO> list = memberSimpleDataDTOS.getList();
        if (CollUtil.isEmpty(list)) {
            return new ItemResult<>(new PageInfo<>());
        }
        if(CharSequenceUtil.isNotBlank(queryCondition.getEndDate())){
            //如果前端 传入日期 yyyy-MM-dd 则转为该日期当天结束时间
            queryCondition.setEndDate(getEndTime(queryCondition.getEndDate()));
        }
        if (StringUtils.isEmpty(queryCondition.getStartDate()) && StringUtils.isEmpty(queryCondition.getEndDate())) {
            queryCondition.setStartDate(getLastYear());
            //若前端不传时间数据 结束时间取当前时间 bug3656修复
            queryCondition.setEndDate(DateUtil.formatDateTime(new Date()));
        }
        List<ExportBiddingEnquiryDataDTO> exportBiddingEnquiryDataList = getExportBiddingEnquiryDataDTOS(list, queryCondition.getStartDate(), queryCondition.getEndDate());
        PageInfo<ExportBiddingEnquiryDataDTO> exportBiddingEnquiryDataDTOPageInfo = new PageInfo<>(exportBiddingEnquiryDataList);
        exportBiddingEnquiryDataDTOPageInfo.setTotal(memberSimpleDataDTOS.getTotal());
        return new ItemResult<>(exportBiddingEnquiryDataDTOPageInfo);
    }

    private List<ExportBiddingEnquiryDataDTO> getExportBiddingEnquiryDataDTOS(List<MemberSimpleDataDTO> memberSimpleDataDTOS, String startDate, String endDate) {
        List<String> memberCodes = memberSimpleDataDTOS.stream().map(MemberSimpleDataDTO::getMemberCode).collect(Collectors.toList());
        List<EnquiryBuyerDTO> enquiryBuyerList = enquiryBuyerMapper.selectMemberEnquiryDataByMemberCodes(memberCodes, startDate, endDate);
        Map<String, List<EnquiryBuyerDTO>> enquiryBuyerMap = enquiryBuyerList.stream().collect(Collectors.groupingBy(EnquiryBuyerDTO::getMemberCode));

        List<BiddingBuyerDTO> biddingBuyerDTOS = biddingBuyerMapper.selectMemberBiddingDataByMemberCodes(memberCodes, startDate, endDate);
        Map<String, List<BiddingBuyerDTO>> biddingBuyerMap = biddingBuyerDTOS.stream().collect(Collectors.groupingBy(BiddingBuyerDTO::getMemberCode));

        List<BiddingBuyerDealDTO> biddingBuyerDeals = biddingBuyerDealMapper.selectMemberBiddingDealDataByMemberCodes(memberCodes, startDate, endDate);
        Map<String, List<BiddingBuyerDealDTO>> biddingDealMap = biddingBuyerDeals.stream().collect(Collectors.groupingBy(BiddingBuyerDealDTO::getMemberCode));

        List<ExportBiddingEnquiryDataDTO> exportBiddingEnquiryDataList = BeanUtil.copyToList(memberSimpleDataDTOS, ExportBiddingEnquiryDataDTO.class);
        exportBiddingEnquiryDataList.forEach(v -> {
            v.setEnquiryCount(Optional.ofNullable(enquiryBuyerMap.get(v.getMemberCode())).map(list -> list.get(0)).map(EnquiryBuyerDTO::getEnquiryCount).orElse(0));
            v.setBiddingCount(Optional.ofNullable(biddingBuyerMap.get(v.getMemberCode())).map(list -> list.get(0)).map(BiddingBuyerDTO::getBiddingCount).orElse(0));
            v.setDealCount(Optional.ofNullable(biddingDealMap.get(v.getMemberCode())).map(list -> list.get(0)).map(BiddingBuyerDealDTO::getDealCount).orElse(0));
        });
        return exportBiddingEnquiryDataList;
    }

    @Override
    public ItemResult<ExportExcelDTO> exportBiddingEnquiryDataList(ExportBiddingEnquiryDataDTO queryCondition) {
        String sheet1Name = "客户询价结果列表";
        ExportSheetDTO sheet1 = new ExportSheetDTO(sheet1Name, ExportBiddingEnquiryDataDTO.class, Collections.emptyList());

        MemberSimpleDataDTO condition = MemberSimpleDataDTO.builder().build();
        BeanUtil.copyProperties(queryCondition, condition);
        if(CharSequenceUtil.isNotBlank(queryCondition.getEndDate())){
            //如果前端 传入日期 yyyy-MM-dd 则转为该日期当天结束时间
            queryCondition.setEndDate(getEndTime(queryCondition.getEndDate()));
        }
        if (StringUtils.isEmpty(queryCondition.getStartDate()) && StringUtils.isEmpty(queryCondition.getEndDate())) {
            queryCondition.setStartDate(getLastYear());
            //若前端不传时间数据 结束时间取当前时间 bug3656修复
            queryCondition.setEndDate(DateUtil.formatDateTime(new Date()));
        }
        condition.setPageNum(null);
        condition.setPageSize(null);
        PageInfo<MemberSimpleDataDTO> memberSimpleDataDTOS = memberService.queryMemberByMemberNameAndCrmCode(condition);
        List<MemberSimpleDataDTO> list = memberSimpleDataDTOS.getList();
        if (CollUtil.isEmpty(list)) {
            return new ItemResult<>(BasicCode.DATA_EXIST.getCode(), BasicCode.DATA_EXIST.getMsg());
        }
        List<ExportBiddingEnquiryDataDTO> exportBiddingEnquiryDataList = getExportBiddingEnquiryDataDTOS(list, queryCondition.getStartDate(), queryCondition.getEndDate());

        sheet1.setDataList(exportBiddingEnquiryDataList);

        return new ItemResult<>(new ExportExcelDTO("客户询价/竞价活跃度报表.xlsx", Arrays.asList(sheet1)));
    }

    @Override
    public ItemResult<PageInfo<ExportSellNumDataDTO>> querySellNumDataList(ExportSellNumDataDTO queryCondition) {
        if (StringUtils.isEmpty(queryCondition.getStartDate()) && StringUtils.isEmpty(queryCondition.getEndDate())) {
            queryCondition.setStartDate(getLastYear());
            queryCondition.setEndDate(getNowMonth());
        }
        PageMethod.startPage(queryCondition.getPageNum(), queryCondition.getPageSize());
        List<ExportSellNumDataDTO> exportSellNumDataDTOS1 = biddingMapper.selectBiddingExportData(queryCondition);
        return new ItemResult<>(new PageInfo<>(exportSellNumDataDTOS1));
    }

    @Override
    public ItemResult<ExportExcelDTO> exportSellNumDataList(ExportSellNumDataDTO exportSellNumDataDTO) {
        String sheet1Name = "可售量变更列表";
        ExportSheetDTO sheet1 = new ExportSheetDTO(sheet1Name, ExportSellNumDataDTO.class, Collections.emptyList());

        if (StringUtils.isEmpty(exportSellNumDataDTO.getStartDate()) && StringUtils.isEmpty(exportSellNumDataDTO.getEndDate())) {
            exportSellNumDataDTO.setStartDate(getLastYear());
            exportSellNumDataDTO.setEndDate(getNowMonth());
        }
        List<ExportSellNumDataDTO> exportSellNumDataDTOS1 = biddingMapper.selectBiddingExportData(exportSellNumDataDTO);

        sheet1.setDataList(exportSellNumDataDTOS1);

        return new ItemResult<>(new ExportExcelDTO("可售量变更报表.xlsx", Arrays.asList(sheet1)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<String> importExcel(BiddingImportExcelInfoDTO dto) {
        if (BeanUtil.isEmpty(dto)) {
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "excel为空，请检查");
        }
        List<BiddingImportExcelDTO> excelDTOS = dto.getList();
        String realName = dto.getRealName();
        String accountId = dto.getAccountId();
        List<String> biddingNoList = excelDTOS.stream().map(BiddingImportExcelDTO::getBiddingNo).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<Bidding> biddingList = biddingMapper.queryBiddingInfoByExcelBiddingNo(biddingNoList);
        List<String> biddingDataList = biddingList.stream().map(Bidding::getBiddingNo).collect(Collectors.toList());
        biddingNoList.removeAll(biddingDataList);
        if (CollUtil.isNotEmpty(biddingNoList)) {
            String errorNo = String.join(",", biddingNoList);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "竞价场次编号为" + errorNo + "的数据不存在，请检查");
        }
        //待提交的数据 逆向流程新增竞价策略已撤回
        List<String> allowStatusList = Arrays.asList(BiddingStatusEnum.TO_SUBMIT_STRATEGY.getStatus(),
                BiddingStatusEnum.DRAFT.getStatus(),
                BiddingStatusEnum.REJECTED_STRATEGY.getStatus(),
                BiddingStatusEnum.WITHDRAWN.getStatus());
        List<String> toSubmitDataStatus = biddingList.stream()
                .filter(f -> CollUtil.contains(allowStatusList,f.getStatus()))
                .map(Bidding::getBiddingNo).collect(Collectors.toList());
        List<String> checkSubmitDataStatus = excelDTOS.stream().map(BiddingImportExcelDTO::getBiddingNo).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        checkSubmitDataStatus.removeAll(toSubmitDataStatus);
        if (CollUtil.isNotEmpty(checkSubmitDataStatus)) {
            String errorNo = String.join(",", checkSubmitDataStatus);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "竞价场次编号为" + errorNo + "的数据不能提交竞价策略，请检查");
        }
        Map<Boolean, String> booleanStringMap = checkNullData(excelDTOS);
        if (booleanStringMap.containsKey(false)) {
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), booleanStringMap.get(false) + "不能为空，请检查");
        }
        Map<Boolean, String> booleanNumberMap = checkNumberData(excelDTOS);
        if (booleanNumberMap.containsKey(false)) {
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), booleanNumberMap.get(false) + " 不是数值，请检查");
        }
        List<String> goodsCodeList = biddingList.stream().map(Bidding::getGoodsCode).collect(Collectors.toList());
        ItemResult<List<GoodsSimpleDTO>> goodsSimpleByCodes = goodsService.findGoodsSimpleByCodes(goodsCodeList);
        List<GoodsSimpleDTO> datalIst = goodsSimpleByCodes.getData();
        List<BiddingQueryCheckDataExcelDTO> biddingQueryCheckDataExcelDTOList = new ArrayList<>();
        biddingList.forEach(data -> {
            BiddingQueryCheckDataExcelDTO biddingQueryCheckDataExcelDTO = BeanUtil.copyProperties(data, BiddingQueryCheckDataExcelDTO.class);
            GoodsSimpleDTO goodsSimpleDTO = datalIst.stream().filter(f -> StringUtils.isNotEmpty(f.getGoodsCode()) && f.getGoodsCode().equals(data.getGoodsCode())).findFirst().orElse(null);
            if (Objects.nonNull(goodsSimpleDTO)) {
                biddingQueryCheckDataExcelDTO.setSapMaterialCode(goodsSimpleDTO.getSapMaterialCode());
                biddingQueryCheckDataExcelDTOList.add(biddingQueryCheckDataExcelDTO);
            }
        });
        for (BiddingImportExcelDTO data : excelDTOS) {
            String biddingNo = data.getBiddingNo();
            String sapMaterialCode = data.getSapMaterialCode();
            BiddingQueryCheckDataExcelDTO biddingQueryCheckDataExcelDTO = biddingQueryCheckDataExcelDTOList.stream().filter(f -> f.getBiddingNo().equals(biddingNo)).findFirst().orElse(null);
            if (Objects.isNull(biddingQueryCheckDataExcelDTO)) {
                return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "竞价场次编号为" + biddingNo + "的数据不能提交竞价策略，请检查");
            }
            String sapMaterialCodeForData = biddingQueryCheckDataExcelDTO.getSapMaterialCode();
            if (!sapMaterialCode.equals(sapMaterialCodeForData)) {
                return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "竞价场次编号为" + biddingNo + "的SAP物料编号错误，请检查");
            }
        }

        List<Bidding> updateList = new ArrayList<>();
        excelDTOS.forEach(excelData -> {
            Bidding data = new Bidding();
            data.setUpdateUser(realName);
            data.setUpdateTime(new Date());
            data.setSubmitUser(accountId);
            data.setSubmitTime(new Date());
            data.setSubmitUserName(realName);
            data.setSubmitUpdateUser(dto.getAccountId());
            data.setSubmitUpdateTime(new Date());
            data.setSubmitUpdateUserName(realName);
            data.setLastStandardPrice(NumberUtil.toBigDecimal(excelData.getStandardPrice()));
            data.setStatus(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus());
            data.setBiddingNo(excelData.getBiddingNo());
            data.setGrossMargin(NumberUtil.toBigDecimal(excelData.getGrossMargin()));
            data.setCostPrice(NumberUtil.toBigDecimal(excelData.getCostPrice()));
            data.setMarketPrice(excelData.getMarketPrice());
            data.setCurrentWeekExpectPrice(excelData.getCurrentWeekExpectPrice());
            data.setStandardPrice(NumberUtil.toBigDecimal(excelData.getStandardPrice()));
            data.setCurrApplySellableQuantity(NumberUtil.toBigDecimal(excelData.getCurrApplySellableQuantity()));
            data.setMinSellableQuantity(NumberUtil.toBigDecimal(excelData.getMinSellableQuantity()));
            data.setMarketSituation(excelData.getMarketSituation());
            data.setDescription(excelData.getDescription());
            updateList.add(data);
        });
        updateList.forEach(data -> biddingMapper.updateImportData(data));
        List<String> biddingIdList = biddingList.stream().map(Bidding::getId).collect(Collectors.toList());
        submitBiddingForSendEmail(biddingIdList, realName);
        return ItemResult.success();
    }

    @Override
    public List<String> buyerGoodsBiddingList(BiddingBuyerGoodsDTO dto) {
        return biddingBuyerService.buyerGoodsBiddingList(dto);
    }

    @Override
    public Integer queryApproveNumberList(BiddingSalesListDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(-1);
        PageInfo<BiddingSalesListViewDTO> pageInfo = queryApproveList(dto);
        if (Objects.nonNull(pageInfo)) {
            long total = pageInfo.getTotal();
            return (int) total;
        }
        return 0;
    }

    @Override
    public Boolean judgeBool(String memberCode) {
        List<String> status = List.of(BiddingStatusEnum.BIDDING.getStatus(), BiddingStatusEnum.TO_CONFIRM_RESULT.getStatus(), BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(), BiddingStatusEnum.APPROVING_RESULT.getStatus(), BiddingStatusEnum.REJECTED_RESULT.getStatus());
        return biddingMapper.judgeBool(memberCode, status, BiddingBuyerStatusEnum.DONE.getStatus());
    }

    public Map<Boolean, String> checkNullData(List<BiddingImportExcelDTO> excelDTOS) {
        Map<Boolean, String> returnMap = new HashMap<>();
        for (BiddingImportExcelDTO excelDTO : excelDTOS) {
            Map<Boolean, String> booleanStringMapFirst = checkDataFirst(returnMap, excelDTO);
            if (booleanStringMapFirst.containsKey(false)) {
                return booleanStringMapFirst;
            }
            Map<Boolean, String> booleanStringMapLast = checkDataLast(returnMap, excelDTO);
            if (booleanStringMapLast.containsKey(false)) {
                return booleanStringMapLast;
            }
        }
        returnMap.put(true, null);
        return returnMap;
    }

    public Map<Boolean, String> checkNumberData(List<BiddingImportExcelDTO> excelDTOS) {
        Map<Boolean, String> returnMap = new HashMap<>();
        for (BiddingImportExcelDTO excelDTO : excelDTOS) {
            //基于上周五原料毛利 Over C2/C3/PO
            String grossMargin = excelDTO.getGrossMargin();
            if (Boolean.FALSE.equals(NumberUtil.isNumber(grossMargin))) {
                returnMap.put(false, "基于上周五原料毛利 Over C2/C3/PO " + grossMargin);
                return returnMap;
            }
            //保本售价
            String costPrice = excelDTO.getCostPrice();
            if (Boolean.FALSE.equals(NumberUtil.isNumber(costPrice))) {
                returnMap.put(false, "保本售价 " + costPrice);
                return returnMap;
            }
            //申请基准价格
            String standardPrice = excelDTO.getStandardPrice();
            if (Boolean.FALSE.equals(NumberUtil.isNumber(standardPrice))) {
                returnMap.put(false, "申请基准价格 " + standardPrice);
                return returnMap;
            }
            //本次申请可售量
            String currApplySellableQuantity = excelDTO.getCurrApplySellableQuantity();
            if (Boolean.FALSE.equals(NumberUtil.isNumber(currApplySellableQuantity))) {
                returnMap.put(false, "本周可售量 " + currApplySellableQuantity);
                return returnMap;
            }
            //可销保底量
            String minSellableQuantity = excelDTO.getMinSellableQuantity();
            if (Boolean.FALSE.equals(NumberUtil.isNumber(minSellableQuantity))) {
                returnMap.put(false, "可销保底量 " + minSellableQuantity);
                return returnMap;
            }
        }
        returnMap.put(true, null);
        return returnMap;
    }

    public Map<Boolean, String> checkDataLast(Map<Boolean, String> returnMap, BiddingImportExcelDTO excelDTO) {
        //本周预计价格
        String currentWeekExpectPrice = excelDTO.getCurrentWeekExpectPrice();
        if (StringUtils.isEmpty(currentWeekExpectPrice)) {
            returnMap.put(false, "本周预计价格");
            return returnMap;
        }
        //申请基准价格
        String standardPrice = excelDTO.getStandardPrice();
        if (StringUtils.isEmpty(standardPrice)) {
            returnMap.put(false, "申请基准价格");
            return returnMap;
        }
        //本次申请可售量
        String currApplySellableQuantity = excelDTO.getCurrApplySellableQuantity();
        if (StringUtils.isEmpty(currApplySellableQuantity)) {
            returnMap.put(false, "本周可售量");
            return returnMap;
        }
        //可销保底量
        String minSellableQuantity = excelDTO.getMinSellableQuantity();
        if (StringUtils.isEmpty(minSellableQuantity)) {
            returnMap.put(false, "可销保底量");
            return returnMap;
        }
        //市场行情及判断
        String marketSituation = excelDTO.getMarketSituation();
        if (StringUtils.isEmpty(marketSituation)) {
            returnMap.put(false, "市场行情及判断");
            return returnMap;
        }
        //竞价策略说明
        String description = excelDTO.getDescription();
        if (StringUtils.isEmpty(description)) {
            returnMap.put(false, "竞价策略说明");
            return returnMap;
        }
        return Collections.emptyMap();
    }

    public Map<Boolean, String> checkDataFirst(Map<Boolean, String> returnMap, BiddingImportExcelDTO excelDTO) {
        //竞价场次编号
        String biddingNo = excelDTO.getBiddingNo();
        if (Boolean.TRUE.equals(checkNull(biddingNo))) {
            returnMap.put(false, "竞价场次编号");
            return returnMap;
        }
        //基于上周五原料毛利 Over C2/C3/PO
        String grossMargin = excelDTO.getGrossMargin();
        if (StringUtils.isEmpty(grossMargin)) {
            returnMap.put(false, "基于上周五原料毛利 Over C2/C3/PO");
            return returnMap;
        }
        //保本售价
        String costPrice = excelDTO.getCostPrice();
        if (StringUtils.isEmpty(costPrice)) {
            returnMap.put(false, "保本售价");
            return returnMap;
        }
        //对标市场价格
        String marketPrice = excelDTO.getMarketPrice();
        if (StringUtils.isEmpty(marketPrice)) {
            returnMap.put(false, "对标市场价格");
            return returnMap;
        }
        //SAP物料编号
        String sapMaterialCode = excelDTO.getSapMaterialCode();
        if (StringUtils.isEmpty(sapMaterialCode)) {
            returnMap.put(false, "SAP物料编号");
            return returnMap;
        }
        return Collections.emptyMap();
    }

    public Boolean checkNull(Object object) {
        return Objects.isNull(object);
    }

    @Override
    public List<BiddingStrategyDetailExportExcelDTO> exportExcel(BiddingExportDataDTO dto) {
        try {
            List<String> roleList = dto.getRoleList();
            if (CollUtil.isNotEmpty(roleList)) {
                List<String> statusList = getRoleList(roleList);
                if (CollUtil.isEmpty(statusList)) {
                    return Collections.emptyList();
                }
                dto.setStatusList(statusList);
                List<BiddingStrategyDetailExportExcelDTO> list = biddingMapper.queryDownloadDataList(dto);
                if (CollUtil.isNotEmpty(list)) {
                    Set<String> categoryCodeSet = list.stream().map(BiddingStrategyDetailExportExcelDTO::getCategoryName).collect(Collectors.toSet());
                    try {
                        List<GoodsCategorySimpleDTO> simpleList = goodsCategoryService.getSimpleList(categoryCodeSet);
                        if (CollUtil.isNotEmpty(simpleList)) {
                            list.forEach(data -> {
                                String name = simpleList.stream().filter(f -> StringUtils.isNotEmpty(f.getCategoryCode()) && f.getCategoryCode().equals(data.getCategoryName())).map(GoodsCategorySimpleDTO::getCategoryName).findFirst().orElse(null);
                                data.setCategoryName(name);
                            });
                        } else {
                            list.forEach(data -> data.setCategoryName(null));
                        }
                    } catch (Exception e) {
                        log.error("竞价审批列表竞价策略导出商品类查询异常：{}", e.getMessage());
                    }
                }
                return getExportDataList(list, dto);
            } else {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "竞价审批列表竞价策略导出接口用户角色为null");
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "竞价审批列表竞价策略导出接口异常" + e.getMessage());
        }
    }

    private List<BiddingStrategyDetailExportExcelDTO> getExportDataList(List<BiddingStrategyDetailExportExcelDTO> list, BiddingExportDataDTO dto) {
        List<BiddingStrategyDetailExportExcelDTO> returnList = new ArrayList<>();
        List<String> biddingNoList = dto.getBiddingNoList();
        if (CollUtil.isNotEmpty(biddingNoList)) {
            biddingNoList.forEach(data -> {
                List<BiddingStrategyDetailExportExcelDTO> collect = list.stream().filter(f -> f.getBiddingNo().equals(data)).collect(Collectors.toList());
                returnList.addAll(collect);
            });
            return returnList;
        }
        return list;
    }

    private List<String> getRoleList(List<String> roleList) {
        List<String> statusList;
        String CMMS = BaseRoleTypeEnum.SELLER_CMMS.getRoleCode();
        String CM = BaseRoleTypeEnum.SELLER_CM.getRoleCode();
        String SYSTEM_ADMIN = BaseRoleTypeEnum.SYSTEM_ADMIN.getRoleCode();
        if (roleList.contains(SYSTEM_ADMIN)) {
            statusList = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(), BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(), BiddingStatusEnum.APPROVING_STRATEGY.getStatus(), BiddingStatusEnum.APPROVING_RESULT.getStatus());
        } else if (roleList.contains(CMMS)) {
            statusList = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(), BiddingStatusEnum.TO_APPROVE_RESULT.getStatus());
        } else if (roleList.contains(CM)) {
            statusList = Arrays.asList(BiddingStatusEnum.APPROVING_STRATEGY.getStatus(), BiddingStatusEnum.APPROVING_RESULT.getStatus());
        } else {
            //没有符合的角色信息，直接返回
            return Collections.emptyList();
        }
        return statusList;
    }

    @Override
    public void notifyBuyerStartBidding(List<Bidding> biddingList, List<BiddingBuyer> buyers) {
        if (CollUtil.isEmpty(biddingList) || CollUtil.isEmpty(buyers))
            return;

        List<String> memberCodes = buyers.stream().map(v -> v.getMemberCode())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        List<String> goodsCodes = biddingList.stream().map(v -> v.getGoodsCode()).distinct().collect(Collectors.toList());

        if (CollUtil.isNotEmpty(memberCodes)) {

            QueryMemberAccountDTO query = new QueryMemberAccountDTO(memberCodes, Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()), DepositStatusEnum.PAID.getCode());
            List<AccountSimpleDTO> accountList = iMemberService.listAccountsByMemberCodes(query);
            //K:memberCode
            Map<String, List<AccountSimpleDTO>> accountGroup = CommonUtils.group(accountList, AccountSimpleDTO::getMemberCode);

            Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> accountPermission = iBiddingAccountService.queryAccountDataPermission(memberCodes, goodsCodes, DepositStatusEnum.PAID.getCode());
            //K:biddingNo
            Map<String, List<BiddingBuyer>> buyerGroup = CommonUtils.group(buyers, BiddingBuyer::getBiddingNo);
            for (Bidding bidding : biddingList) {
                String startTime = DateUtil.formatDateTime(bidding.getLastBiddingStartTime());
                //通知买家管理员
                List<BiddingBuyer> buyerList = CommonUtils.getByKey(buyerGroup, bidding.getBiddingNo());
                sendEmailAndMessage(buyerList, accountGroup, bidding, startTime, accountPermission);
            }
        }
    }

    private void sendEmailAndMessage(List<BiddingBuyer> buyerList, Map<String, List<AccountSimpleDTO>> accountGroup, Bidding bidding, String startTime, Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> accountPermission) {
        if (CollUtil.isNotEmpty(buyerList)) {
            for (BiddingBuyer buyer : buyerList) {
                List<AccountSimpleDTO> buyerAccountList = CommonUtils.getByKey(accountGroup, buyer.getMemberCode());
                if (CollUtil.isNotEmpty(buyerAccountList)) {
                    //短信通知
                    this.notifyStartBySms(bidding, buyerAccountList, startTime);
                    //站内信通知
                    this.notifyStartByMessage(bidding, buyerAccountList, startTime);
                }
            }

            List<String> buyerMemberCodes = buyerList.stream().map(BiddingBuyer::getMemberCode).distinct().collect(Collectors.toList());
            //通知客户产品相关人员
            List<QueryAccountDataPermissionDTO> keys = buyerMemberCodes.stream()
                    .distinct()
                    .map(v -> new QueryAccountDataPermissionDTO(v, bidding.getGoodsCode()))
                    .collect(Collectors.toList());

            keys.forEach(v -> {
                List<AccountSimpleDTO> accounts = CommonUtils.getByKey(accountPermission, v);
                if (CollUtil.isNotEmpty(accounts)) {
                    //短信通知
                    this.notifyStartBySms(bidding, accounts, startTime);
                    //站内信通知
                    this.notifyStartByMessage(bidding, accounts, startTime);
                }
            });
        }
    }

    @Override
    public BiddingInfoDTO queryBiddingInfo(String id) {
        return biddingMapper.queryBiddingInfo(id);
    }

    private void notifyStartBySms(Bidding bidding, List<AccountSimpleDTO> accountList, String startTime) {
        List<String> mobileList = accountList.stream().map(v -> v.getMobile())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        List<String> param = Arrays.asList(bidding.getBiddingNo(), startTime, bidding.getBiddingNo(), bidding.getBiddingName());
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.BIDDING_START_CODE.getCode(), mobileList, param);
        iSmsSendService.sendSms(sms);
    }

    private void notifyStartByMessage(Bidding bidding, List<AccountSimpleDTO> accountList, String startTime) {
        //异步发送站内信
        executor.execute(() -> {
            for (AccountSimpleDTO v : accountList) {
                MessageDTO message = new MessageDTO();
                message.setTitle(BiddingMessageTemplateEnum.NOTIFY_BIDDING_START.getTitle());
                message.setReceiveAccountIds(Arrays.asList(v.getAccountId()));
                message.setContent(BiddingMessageTemplateEnum.NOTIFY_BIDDING_START.getMsg(v.getMemberName(), bidding.getBiddingNo(), startTime,bidding.getGoodsName()));

                iMessageSendService.sendMessage(message);
            }
        });
    }

    private void notifyByStrategyApproved(List<Bidding> biddingList, BiddingApprovalDTO param) {
        if (CollUtil.isEmpty(biddingList))
            return;
        //一级审批人通过
        List<Bidding> leaveOneList = getApproveBiddingByLevel(biddingList, BiddingStatusEnum.TO_APPROVE_STRATEGY);
        if (CollUtil.isNotEmpty(leaveOneList)) {
            EmailDTO emailDTO = new EmailDTO();
            EmailTemplateEnum biddingSubmitTemplate = EmailTemplateEnum.BIDDING_SUBMIT_TEMPLATE;
            emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());

            //CM
            List<String> goodsCodeList = biddingList.stream().map(Bidding::getGoodsCode).collect(Collectors.toList());
            DataPermissionGoodsCodeDTO dataPermissionGoodsCodeDTO = new DataPermissionGoodsCodeDTO();
            dataPermissionGoodsCodeDTO.setGoodsCodeList(goodsCodeList);
            dataPermissionGoodsCodeDTO.setRoleCode(BaseRoleTypeEnum.SELLER_CM.getRoleCode());
            List<DataPermissionAccountInfoDTO> accountInfoDTOS = roleService.findAccountByGoodsCode(dataPermissionGoodsCodeDTO);
            if (CollUtil.isNotEmpty(leaveOneList)) {
                leaveOneList.forEach(data -> {
                    String goodsCode = data.getGoodsCode();
                    DataPermissionAccountInfoDTO dataPermissionAccountInfoDTO = accountInfoDTOS.stream().filter(f -> CharSequenceUtil.isNotEmpty(f.getGoodsCode()) && f.getGoodsCode().equals(goodsCode)).findFirst().orElse(null);
                    if (BeanUtil.isNotEmpty(dataPermissionAccountInfoDTO)) {
                        List<AccountInfoDTO> accountList = dataPermissionAccountInfoDTO.getAccountList();
                        List<String> cEmail = accountList.stream().map(AccountInfoDTO::getEmail).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                        List<String> cName = accountList.stream().map(AccountInfoDTO::getRealName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                        //Tos
                        emailDTO.setTos(cEmail);
                        //邮件模板参数
                        String biddingEmailNameParam = String.join(",", cName);
                        Map<String, Object> templateParam = new HashMap<>();
                        templateParam.put(REAL_NAME, biddingEmailNameParam);
                        templateParam.put(BIDDING_NO, data.getBiddingNo());
                        templateParam.put(BIDDING_NAME, data.getBiddingName());
                        templateParam.put("submitUser", param.getRealName());
                        emailDTO.setTemplateParam(templateParam);
                        //发送邮件
                        emailSendService.sendEmail(emailDTO);
                        //站内信
                        accountList.forEach(message -> {
                            MessageDTO messageDTO = new MessageDTO();
                            messageDTO.setReceiveAccountIds(Collections.singletonList(message.getAccountId()));
                            messageDTO.setTitle("竞价策略待审批");
                            messageDTO.setContent(" 尊敬的" + message.getRealName() + "，您有一条新的竞价策略需要审批，竞价场次编号为" + data.getBiddingNo() + MESSAGE_LAST);
                            messageSendService.sendMessage(messageDTO);
                        });
                    }
                });
            }
        }
        //二级审批人通过
        List<Bidding> leaveTwoList = getApproveBiddingByLevel(biddingList, BiddingStatusEnum.APPROVING_STRATEGY);
        if (CollUtil.isNotEmpty(leaveTwoList)) {
            EmailDTO emailDTO = new EmailDTO();
            EmailTemplateEnum biddingSubmitTemplate = EmailTemplateEnum.BIDDING_HAS_PASS_TEMPLATE;
            emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());
            //竞价策略提交人
            List<String> submitUserList = leaveTwoList.stream().map(Bidding::getSubmitUser).collect(Collectors.toList());
            List<AccountInfoReturnDTO> accountByAccountId = iAccountService.findAccountByAccountId(submitUserList);
            if (CollUtil.isNotEmpty(accountByAccountId)) {
                leaveTwoList.forEach(data -> {
                    AccountInfoReturnDTO accountInfoReturnDTO = accountByAccountId.stream().filter(f -> f.getAccountId().equals(data.getSubmitUser())).findFirst().orElse(null);
                    emailDTO.setTos(Collections.singletonList(accountInfoReturnDTO.getEmail()));
                    //邮件模板参数
                    Map<String, Object> templateParam = new HashMap<>();
                    templateParam.put(REAL_NAME, data.getSubmitUserName());
                    templateParam.put(BIDDING_NO, data.getBiddingNo());
                    templateParam.put(BIDDING_NAME, data.getBiddingName());
                    emailDTO.setTemplateParam(templateParam);
                    //发送邮件
                    emailSendService.sendEmail(emailDTO);
                    //站内信
                    MessageDTO messageDTO = new MessageDTO();
                    messageDTO.setReceiveAccountIds(Arrays.asList(data.getSubmitUser()));
                    messageDTO.setTitle("竞价策略审批已通过");
                    messageDTO.setContent(" 尊敬的" + accountInfoReturnDTO.getRealName() + "，您有一条竞价场次" + data.getBiddingNo() + "的竞价策略已通过审批，望知悉，谢谢。");
                    messageSendService.sendMessage(messageDTO);
                });
            }
        }
    }

    private void notifyByResultApproved(List<Bidding> biddingList, BiddingApprovalDTO param) {
        if (CollUtil.isEmpty(biddingList))
            return;
        List<String> dealOperatorIds = biddingList.stream()
                .map(Bidding::getDealOperator)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        List<AccountSimpleDTO> dealAccounts = iAccountService.listSimpleByIds(dealOperatorIds);
        Map<String, AccountSimpleDTO> dealAccountsMap = CommonUtils.getMap(dealAccounts, AccountSimpleDTO::getAccountId);

        //若为一级审批，则状态更新为：成交结果审批中，且实时向CM第二级审批人发送站内信/邮件通知，告知其待审批事项。
        List<Bidding> levelOne = getApproveBiddingByLevel(biddingList, BiddingStatusEnum.TO_APPROVE_RESULT);
        sendEmailForLevelOne(levelOne, dealAccountsMap);
        //二级审批 发送通知
        List<Bidding> levelTwo = getApproveBiddingByLevel(biddingList, BiddingStatusEnum.APPROVING_RESULT);
        sendEmailForLevelTwo(levelTwo, dealAccountsMap, biddingList);
    }

    /**
     * 二级审批
     */
    private void sendEmailForLevelTwo(List<Bidding> levelTwo, Map<String, AccountSimpleDTO> dealAccountsMap, List<Bidding> biddingList) {
        if (CollUtil.isNotEmpty(levelTwo)) {
            List<BiddingBuyerDeal> deals = iBiddingBuyerDealBiz.listByBiddingList(biddingList);
            List<String> detailIds = null;
            if (CollUtil.isNotEmpty(deals)) {
                detailIds = deals.stream().map(BiddingBuyerDeal::getBiddingBuyerDetailId).collect(Collectors.toList());
            }
            List<BiddingBuyerDetail> details = iBiddingBuyerDetailBiz.listByIds(detailIds);
            Map<String, List<BiddingBuyerDetail>> biddingBuyerDetailGroup = CommonUtils.group(details, BiddingBuyerDetail::getBiddingNo);
            Map<Bidding, List<BiddingBuyerDetail>> notifyParam = new HashMap<>();
            levelTwo.forEach(v -> {
                List<BiddingBuyerDetail> buyerDetails = CommonUtils.getByKey(biddingBuyerDetailGroup, v.getBiddingNo());
                if (CollUtil.isNotEmpty(buyerDetails))
                    notifyParam.put(v, buyerDetails);
            });

            //通知成交结果提交人 站内信+邮件
            MessageDTO message = new MessageDTO();
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_APPROVED_BY_CM.getTitle());
            EmailDTO dealEmail = new EmailDTO();
            dealEmail.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_DEAL_OPERATOR_APPROVAL_RESULT_TEMPLATE.getCode());

            for (Bidding v : levelTwo) {
                AccountSimpleDTO dealOperator = CommonUtils.getByKey(dealAccountsMap, v.getDealOperator());
                if (Objects.isNull(dealOperator))
                    continue;
                String dealOperatorName = dealOperator.getRealName();
                String dealTo = dealOperator.getEmail();
                message.setReceiveAccountIds(Collections.singletonList(v.getDealOperator()));
                message.setContent(BiddingMessageTemplateEnum.NOTIFY_APPROVED_BY_CM.getMsg(dealOperatorName, v.getBiddingNo()));
                //发送站内信
                iMessageSendService.sendMessage(message);
                //发送邮件
                dealEmail.setTos(Arrays.asList(dealTo));
                Map<String, Object> emailParam = new HashMap<>();
                emailParam.put(REAL_NAME, dealOperatorName);
                emailParam.put(BIDDING_NO, v.getBiddingNo());
                emailParam.put(BIDDING_NAME, v.getBiddingName());
                dealEmail.setTemplateParam(emailParam);
                iEmailSendService.sendEmail(dealEmail);
            }
            log.info("二级审批通过 开始通知 客户、产品、销售");
        }
    }

    /**
     * 一级审批
     */
    private void sendEmailForLevelOne(List<Bidding> levelOne, Map<String, AccountSimpleDTO> dealAccountsMap) {
        if (CollUtil.isNotEmpty(levelOne)) {
            List<String> goodsCodes = levelOne.stream().map(v -> v.getGoodsCode()).collect(Collectors.toList());
            Map<String, List<AccountNameDTO>> cmAccountMap = iBiddingAccountService.findApprover(BaseRoleTypeEnum.SELLER_CM.getRoleCode(), goodsCodes);
            levelOne.forEach(bidding -> {
                List<AccountNameDTO> cmAccounts = CommonUtils.getByKey(cmAccountMap, bidding.getGoodsCode());
                for (AccountNameDTO cmAccount : cmAccounts) {
                    //发送站内信
                    MessageDTO message = new MessageDTO();
                    message.setReceiveAccountIds(Arrays.asList(cmAccount.getAccountId()));
                    message.setTitle(BiddingMessageTemplateEnum.NOTIFY_WAIT_APPROVAL_BY_CM.getTitle());
                    message.setContent(BiddingMessageTemplateEnum.NOTIFY_WAIT_APPROVAL_BY_CM.getMsg(cmAccount.getRealName(), bidding.getBiddingNo()));
                    iMessageSendService.sendMessage(message);

                    //发送邮件 通知CM审批
                    if (CharSequenceUtil.isNotBlank(cmAccount.getEmail())) {
                        EmailDTO cmEmail = new EmailDTO();
                        cmEmail.setTos(Arrays.asList(cmAccount.getEmail()));
                        cmEmail.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_CM_WAIT_APPROVAL_BY_DEAL_TEMPLATE.getCode());
                        Map<String, Object> cmParam = new HashMap<>();
                        cmParam.put(REAL_NAME, cmAccount.getRealName());
                        cmParam.put(BIDDING_NO, bidding.getBiddingNo());
                        cmParam.put(BIDDING_NAME, bidding.getBiddingName());

                        AccountSimpleDTO dealOperator = CommonUtils.getByKey(dealAccountsMap, bidding.getDealOperator());
                        cmParam.put("submitUserName", Objects.nonNull(dealOperator) ? ObjectUtil.defaultIfBlank(dealOperator.getRealName(), "") : "");
                        cmEmail.setTemplateParam(cmParam);

                        iEmailSendService.sendEmail(cmEmail);
                    }
                }
            });
        }
    }

    private void notifyByStrategyReject(List<Bidding> biddingList, BiddingApprovalDTO param) {
        if (CollUtil.isEmpty(biddingList))
            return;
        //驳回
        EmailDTO emailDTO = new EmailDTO();
        EmailTemplateEnum biddingSubmitTemplate = EmailTemplateEnum.BIDDING_HAS_REJECT_TEMPLATE;
        emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());
        if (CollUtil.isNotEmpty(biddingList)) {
            emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());
            //竞价策略提交人
            List<String> submitUserList = biddingList.stream().map(Bidding::getSubmitUser).collect(Collectors.toList());
            List<AccountInfoReturnDTO> accountByAccountId = iAccountService.findAccountByAccountId(submitUserList);
            if (CollUtil.isNotEmpty(accountByAccountId)) {
                biddingList.forEach(data -> {
                    AccountInfoReturnDTO accountInfoReturnDTO = accountByAccountId.stream().filter(f -> f.getAccountId().equals(data.getSubmitUser())).findFirst().orElse(null);
                    emailDTO.setTos(Arrays.asList(accountInfoReturnDTO.getEmail()));
                    //邮件模板参数
                    Map<String, Object> templateParam = new HashMap<>();
                    templateParam.put(REAL_NAME, accountInfoReturnDTO.getRealName());
                    templateParam.put(BIDDING_NO, data.getBiddingNo());
                    templateParam.put(BIDDING_NAME, data.getBiddingName());
                    templateParam.put(REJECT_REASON, param.getReason());
                    templateParam.put(APPROVAL_NAME, param.getRealName());
                    emailDTO.setTemplateParam(templateParam);
                    //发送邮件
                    emailSendService.sendEmail(emailDTO);
                    //站内信
                    MessageDTO messageDTO = new MessageDTO();
                    messageDTO.setReceiveAccountIds(Arrays.asList(data.getSubmitUser()));
                    messageDTO.setTitle("竞价策略审批已驳回");
                    messageDTO.setContent(" 尊敬的" + accountInfoReturnDTO.getRealName() + "，您提交的竞价场次编号为" + data.getBiddingNo() + "的竞价策略申请已被驳回" + "，请及时查阅，谢谢。");
                    messageSendService.sendMessage(messageDTO);
                });
            }
        }
    }

    private void notifyByResultReject(List<Bidding> biddingList, BiddingApprovalDTO param) {
        if (CollUtil.isEmpty(biddingList))
            return;
        List<String> dealOperatorIds = biddingList.stream()
                .map(v -> v.getDealOperator())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        List<AccountSimpleDTO> dealAccounts = iAccountService.listSimpleByIds(dealOperatorIds);
        Map<String, AccountSimpleDTO> dealAccountsMap = CommonUtils.getMap(dealAccounts, AccountSimpleDTO::getAccountId);

        MessageDTO message = new MessageDTO();
        message.setTitle(BiddingMessageTemplateEnum.NOTIFY_REJECT_BY_APPROVAL.getTitle());
        for (Bidding bidding : biddingList) {
            AccountSimpleDTO dealAccount = CommonUtils.getByKey(dealAccountsMap, bidding.getDealOperator());
            if (Objects.nonNull(dealAccount)) {
                message.setReceiveAccountIds(Arrays.asList(dealAccount.getAccountId()));
                message.setContent(BiddingMessageTemplateEnum.NOTIFY_REJECT_BY_APPROVAL.getMsg(dealAccount.getRealName(), bidding.getBiddingNo()));
                //发送站内信
                iMessageSendService.sendMessage(message);
                if (CharSequenceUtil.isNotBlank(dealAccount.getEmail())) {
                    EmailDTO dealOperatorEmail = new EmailDTO();
                    dealOperatorEmail.setTos(Arrays.asList(dealAccount.getEmail()));
                    dealOperatorEmail.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_DEAL_OPERATOR_REJECT_RESULT_TEMPLATE.getCode());
                    Map<String, Object> emailParam = new HashMap<>();
                    emailParam.put(REAL_NAME, dealAccount.getRealName());
                    emailParam.put(BIDDING_NO, bidding.getBiddingNo());
                    emailParam.put(BIDDING_NAME, bidding.getBiddingName());
                    emailParam.put(APPROVAL_NAME, param.getRealName());
                    emailParam.put("reason", param.getReason());
                    dealOperatorEmail.setTemplateParam(emailParam);
                    //发送邮件通知成交结果提交人
                    iEmailSendService.sendEmail(dealOperatorEmail);
                }
            }
        }
    }

    public List<SapContractDTO> createSapContract(List<String> biddingIds, List<String> dealNos, boolean isCreateSapContract) {
        log.info("创建SAP合同开始 biddingIds:{}", biddingIds);
        if (CollUtil.isEmpty(biddingIds))
            return Collections.emptyList();
        List<Bidding> biddingList = this.listByIds(biddingIds);
        if (CollUtil.isEmpty(biddingList))
            return Collections.emptyList();
        List<BiddingBuyerDeal> deals = iBiddingBuyerDealBiz.listByBiddingAndDealNos(biddingList, dealNos);
        if (CollUtil.isEmpty(deals))
            return Collections.emptyList();
        Map<String, Bidding> biddingMap = CommonUtils.getMap(biddingList, Bidding::getBiddingNo);

        List<String> goodsCodes = biddingList.stream().map(v -> v.getGoodsCode()).distinct().collect(Collectors.toList());
        List<String> memberCodes = deals.stream().map(v -> v.getMemberCode()).distinct().collect(Collectors.toList());

        //商品信息
        ItemResult<List<GoodsSimpleDTO>> goodsList = goodsService.findGoodsSimpleByCodes(goodsCodes);
        Map<String, GoodsSimpleDTO> godsMap = CommonUtils.getMap(goodsList, GoodsSimpleDTO::getGoodsCode);
        //商品与SAP映射信息
        List<String> packList = deals.stream().map(v -> v.getPack())
                .filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        QueryGoodsMappingSimpleDTO queryMapping = new QueryGoodsMappingSimpleDTO(goodsCodes, packList);
        List<GoodsSapMappingSimpleDTO> goodsMappingList = iGoodsMappingSapService.querySimple(queryMapping);
        //K:goodsCode_Pack
        Map<String, GoodsSapMappingSimpleDTO> goodsMappingMap = setGoodsMappingMap(goodsMappingList);
        //客户信息
        List<MemberSimpleDTO> members = memberService.listSimpleMemberByCodes(memberCodes);
        Map<String, MemberSimpleDTO> memberMap = CommonUtils.getMap(members, MemberSimpleDTO::getMemberCode);
        //客户商品意向信息
        List<MemberPurchaseGoodsIntentionDTO> intentions = memberService.queryMemberBySaleInfo(new QueryIntentionInfoDTO(null, goodsCodes, memberCodes, null));
        //K: memberCode_goodsCode
        Map<String, MemberPurchaseGoodsIntentionDTO> intentionMap = null;
        //K:saleUserId V:customerService account
        Map<String, AccountSimpleDTO> customerServiceMap = null;
        if (CollUtil.isNotEmpty(intentions)) {
            customerServiceMap = getCustomerServiceMap(intentions);
            intentionMap = getIntentionMap(intentions);
        }

        //获取销售渠道，销售组，销售单位，运输方式字典
        Map<String, Map<String, ValueSetTreeDTO>> dictMap = this.getValueSetMap(Arrays.asList(VS_SALES_CHANNEL,
                VS_SALES_GROUP,
                VS_SALES_UNIT,
                VS_FACTORY));

        Map<String, ValueSetTreeDTO> factoryMap = CommonUtils.getByKey(dictMap, VS_FACTORY);
        Date now = new Date();

        List<SapContractDTO> contractList = new ArrayList<>();
        for (BiddingBuyerDeal deal : deals) {
            Bidding bidding = CommonUtils.getByKey(biddingMap, deal.getBiddingNo());
            if (Objects.isNull(bidding))
                continue;
            //商品
            GoodsSimpleDTO goods = CommonUtils.getByKey(godsMap, bidding.getGoodsCode());
            if (Objects.isNull(goods)) {
                log.info("成交单：{} 商品不存在：{}", deal.getDealNo(), bidding.getBiddingNo());
                continue;
            }
            //成交时间
            Date dealTime = ObjectUtil.defaultIfNull(bidding.getDealTime(), now);
            String dealTimeStr = DateUtil.formatDate(dealTime);

            SapContractDTO.HeaderDTO head = new SapContractDTO.HeaderDTO();
            SapContractDTO.ItemDTO item = new SapContractDTO.ItemDTO();
            //成交单号
            head.setZZVBELN(deal.getDealNo());
            //销售组
            Map<String, ValueSetTreeDTO> saleGroupMap = CommonUtils.getByKey(dictMap, VS_SALES_GROUP);
            ValueSetTreeDTO saleGroupOption = CommonUtils.getByKey(saleGroupMap, goods.getSalesGroup());
            setVKGRP(saleGroupOption, head, goods);
            //凭证日期
            head.setAUDAT(dealTimeStr);
            //定价日期
            head.setPRSDT(dealTimeStr);
            //客户成交日期
            head.setBSTDK(DateUtil.format(dealTime, "yyyy-MM-15"));

            MemberSimpleDTO member = CommonUtils.getByKey(memberMap, deal.getMemberCode());
            if (Objects.nonNull(member)) {
                //售达方
                head.setZKUNNR_SP(member.getCrmCode());

                String key = CharSequenceUtil.format(TEMPLATE, member.getMemberCode(), bidding.getGoodsCode());
                MemberPurchaseGoodsIntentionDTO intention = CommonUtils.getByKey(intentionMap, key);
                setCommonData(head, intention, customerServiceMap, dictMap);
                //贸易条款
                String iNcon1 = getINcon1(deal);

                head.setINCO1(iNcon1);
                //合同开始日期
                head.setANGDT(dealTimeStr);
                //合同结束日期
                head.setBNDDT(DateUtil.formatDate(DateUtil.offsetDay(bidding.getDeliveryEffectEndDate(), 3)));

                //线上销售平台行号
                item.setZZPOSNR("1");
                //物料编码 找映射表获取SAP物料号
                String key1 = CharSequenceUtil.format(TEMPLATE, bidding.getGoodsCode(), deal.getPack());
                GoodsSapMappingSimpleDTO goodsSapMapping = CommonUtils.getByKey(goodsMappingMap, key1);
                //默认先取商品中的数据 若在映射表中找到取映射中的数据
                item.setMATNR(goods.getSapMaterialCode());
                setMATNR(goodsSapMapping, item);
                //工厂
                ValueSetTreeDTO factoryOption = CommonUtils.getByKey(factoryMap, goods.getSapFactoryCode());
                setWERKS(factoryOption, item);
                //数量
                item.setZMENG(CommonUtils.toStr(deal.getWantQuantity()));
                //金额
                item.setKBETR(CommonUtils.toStr(bidding.getLastStandardPrice()));
                //定价日期
                item.setPRSDT(dealTimeStr);
                //价格条件类型
                setPriceType(item, iNcon1);
                item.setTEXT_LINE1(DateUtil.formatDate(bidding.getDeliveryEffectStartDate()));
                item.setTEXT_LINE2(DateUtil.formatDate(bidding.getDeliveryEffectEndDate()));
            }

            SapContractDTO sapContractParam = new SapContractDTO();
            SapContractDTO.ScDTO sc = new SapContractDTO.ScDTO();
            sc.setHEADER(head);
            sc.setITEM(Arrays.asList(item));
            sapContractParam.setIS_SC(sc);

            contractList.add(sapContractParam);
            log.info("创建SAP合同发送MQ消息 dealNo:{} isCreateSapContract:{} sapParam:{}", deal.getDealNo(), isCreateSapContract, sapContractParam);
            if (BooleanUtil.isTrue(isCreateSapContract))
                kafkaTemplate.send(MqTopicConstant.CREATE_SAP_CONTRACT_TOPIC, JSONUtil.toJsonStr(sapContractParam));
        }

        return contractList;
    }

    @Override
    public List<BiddingQueryCheckDataExcelDTO> queryBiddingCheckData(BiddingListCheckDataDTO dto) {
        List<String> biddingNoList = dto.getBiddingNoList();
        if (CollUtil.isEmpty(biddingNoList)) {
            return List.of();
        }
        BiddingSalesListDTO biddingSalesListDTO = new BiddingSalesListDTO();
        biddingSalesListDTO.setAccountId(dto.getAccountId());
        biddingSalesListDTO.setBiddingNoList(dto.getBiddingNoList());
        biddingSalesListDTO.setPageSize(-1);
        PageInfo<BiddingSalesListViewDTO> biddingSalesListViewDTOPageInfo = salesBiddingList(biddingSalesListDTO, 1);
        List<BiddingSalesListViewDTO> list = biddingSalesListViewDTOPageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return List.of();
        }
        List<String> goodsCodeList = list.stream().map(BiddingSalesListViewDTO::getGoodsCode).collect(Collectors.toList());
        ItemResult<List<GoodsSimpleDTO>> goodsSimpleByCodes = goodsService.findGoodsSimpleByCodes(goodsCodeList);
        List<GoodsSimpleDTO> datalIst = goodsSimpleByCodes.getData();
        if (CollUtil.isEmpty(datalIst)) {
            return List.of();
        }
        List<BiddingQueryCheckDataExcelDTO> biddingQueryCheckDataExcelDTOList = new ArrayList<>();
        list.forEach(data -> {
            BiddingQueryCheckDataExcelDTO biddingQueryCheckDataExcelDTO = BeanUtil.copyProperties(data, BiddingQueryCheckDataExcelDTO.class);
            GoodsSimpleDTO goodsSimpleDTO = datalIst.stream().filter(f -> StringUtils.isNotEmpty(f.getGoodsCode()) && f.getGoodsCode().equals(data.getGoodsCode())).findFirst().orElse(null);
            if (Objects.nonNull(goodsSimpleDTO)) {
                biddingQueryCheckDataExcelDTO.setSapMaterialCode(goodsSimpleDTO.getSapMaterialCode());
                biddingQueryCheckDataExcelDTOList.add(biddingQueryCheckDataExcelDTO);
            }
        });
        return biddingQueryCheckDataExcelDTOList;
    }

    @Override
    public void updateOverDealRemark(String remark, String id) {
        biddingMapper.updateOverDealRemark(remark,id);
    }

    @Override
    public Date getNewDeliveryEffectStartDate(Date lastBiddingStartTime, Bidding bidding) {
        if (Objects.isNull(lastBiddingStartTime) || Objects.isNull(bidding))
            return null;
        SalesPlan salesPlan = iSalesPlanBiz.getByNo(bidding.getSalesPlanNo());
        if(Objects.isNull(salesPlan))
            return null;
        Date deliveryEffectStartDate = salesPlan.getDeliveryEffectStartDate();

        if (DateUtil.compare(lastBiddingStartTime, deliveryEffectStartDate, DatePattern.NORM_DATE_PATTERN) >= 0)
            return DateUtil.beginOfDay(DateUtil.offsetDay(lastBiddingStartTime, 1));
        return deliveryEffectStartDate;
    }

    @Override
    public Boolean verifyBiddingStartTime(Date lastBiddingStartTime, String salesPlanNo,Boolean isThrow) {
        return this.verifyBiddingStartTime(lastBiddingStartTime,iSalesPlanBiz.getByNo(salesPlanNo),isThrow);
    }

    @Override
    public Boolean verifyBiddingStartTime(Date lastBiddingStartTime, SalesPlan salesPlan,Boolean isThrow) {
        if(Objects.isNull(salesPlan))
            return false;
        return this.verifyBiddingStartTime(lastBiddingStartTime, salesPlan.getDeliveryEffectEndDate(),isThrow);
    }

    @Override
    public Boolean verifyBiddingStartTime(Date lastBiddingStartTime, Date deliveryEffectEndDate,Boolean isThrow) {
        if(Objects.isNull(lastBiddingStartTime) || Objects.isNull(deliveryEffectEndDate))
            return false;
        if(DateUtil.compare(DateUtil.offsetDay(lastBiddingStartTime,1),deliveryEffectEndDate,DatePattern.NORM_DATE_PATTERN) > 0) {
            if(BooleanUtil.isTrue(isThrow))
                throw new BizException(OrderErrorCode.CUSTOM, "竞价开始日期不能大于销售计划的提货结束日期前一天，请重新选择竞价开始日期");
            return true;
        }

        return false;
    }

    @Override
    public Boolean existByEnquiryNo(String enquiryNo) {
        Condition condition = new Condition(Bidding.class);
        condition.createCriteria().andEqualTo(ColumnConstant.ENQUIRY_NO,enquiryNo);
        return biddingMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public void stopBiddingJob(String biddingNo) {
        if(CharSequenceUtil.isBlank(biddingNo))
            return;
        StopJobDTO param = new StopJobDTO();
        param.setExecutorParam(biddingNo);
        param.setAppName(xxlJobConfig.getAppName());
        param.setExecutorHandlers(Arrays.asList(XxlJobConstant.NOTIFY_BIDDING_START_JOB,
                XxlJobConstant.BIDDING_START_JOB,XxlJobConstant.BIDDING_END_JOB));
        iXxlJobService.stopXxlJob(param);
    }

    @Override
    public Bidding getById(String id) {
        Condition condition = new Condition(Bidding.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.ID, id)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        return CollUtil.getFirst(this.findByCondition(condition));
    }

    @Override
    public PageInfo<BiddingReportResultDTO> biddingReport(BiddingReportQueryDTO param) {
        //查询数据权限
        AccountRoleDTO queryDataPermission = new AccountRoleDTO();
        queryDataPermission.setAccountId(param.getOperatorBy());
        List<DataPermissionDTO> dataPermissionList = roleService.getDataPermissionList(queryDataPermission);
        if(CollUtil.isEmpty(dataPermissionList)){
            log.info("竞价报表 accountId:{} 无数据权限",param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        List<String> dataPermissionGoodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(dataPermissionList,DataPermissionDTO::getGoodsCode);
        if (CollUtil.isEmpty(dataPermissionGoodsCodes)){
            log.info("竞价报表 accountId:{} 无数据权限",param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        param.setDataPermissionGoodsCodes(dataPermissionGoodsCodes);
        //查询分类名称匹配到的编码
        if(CharSequenceUtil.isNotBlank(param.getCategoryName())){
            ItemResult<List<GoodsCategoryDTO>> goodsCategoryResult = goodsCategoryService.getByLikeName(param.getCategoryName());
            if(Objects.isNull(goodsCategoryResult) || CollUtil.isEmpty(goodsCategoryResult.getData())){
                log.info("竞价报表 accountId:{} categoryName:{} 产品分类名称未匹配到数据",param.getOperatorBy(),param.getCategoryName());
                return PageInfo.emptyPageInfo();
            }
            param.setCategoryCodeByCategoryName(CommonUtils.getListValueByDistinctAndFilterBank(goodsCategoryResult.getData(),GoodsCategoryDTO::getCategoryCode));
        }
        //查询SAP物料号匹配到的商品编码
        if(CharSequenceUtil.isNotBlank(param.getSapMaterialCode())){
            ItemResult<List<GoodsSimpleDTO>> goodsResult = goodsService.findGoodsSimpleByLikeSapMaterialCode(param.getSapMaterialCode());
            if(Objects.isNull(goodsResult) || CollUtil.isEmpty(goodsResult.getData())){
                log.info("竞价报表 accountId:{} sapMaterialCode:{} SAP物料号未匹配到数据",param.getOperatorBy(),param.getSapMaterialCode());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeBySapMaterialCode(CommonUtils.getListValueByDistinctAndFilterBank(goodsResult.getData(),GoodsSimpleDTO::getGoodsCode));
        }
        //查询CRM客户编码匹配到的会员编码
        if(CharSequenceUtil.isNotBlank(param.getCrmCode())){
            List<MemberSimpleDTO> memberResult = iMemberService.queryMemberByLikeCrmCode(param.getCrmCode());
            if(CollUtil.isEmpty(memberResult)){
                log.info("竞价报表 accountId:{} crmCode:{} 会员名称未匹配到数据",param.getOperatorBy(),param.getCrmCode());
                return PageInfo.emptyPageInfo();
            }
            param.setMemberCodeByCrmCode(CommonUtils.getListValueByDistinctAndFilterBank(memberResult,MemberSimpleDTO::getMemberCode));
        }

        if (BooleanUtil.isTrue(param.getNeedPage())) {
            return PageMethod
                    .startPage(param.getPageNum(), param.getPageSize())
                    .doSelectPageInfo(() -> biddingMapper.biddingReport(param));
        } else {
            return PageInfo.of(biddingMapper.biddingReport(param));
        }
    }

    @Override
    public List<BiddingAnalysisResultDTO> biddingAnalysis(Bidding bidding) {
        if (Objects.isNull(bidding))
            return Collections.emptyList();
        List<BiddingAnalysisInfoDTO> details = biddingMapper.biddingAnalysisInfo(bidding.getBiddingNo());
        if (CollUtil.isEmpty(details))
            return Collections.emptyList();
        //K:currentRound
        Map<Integer, List<BiddingAnalysisInfoDTO>> biddingAnalysisGroup = CommonUtils.group(details, BiddingAnalysisInfoDTO::getCurrentRound);

        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(details, BiddingAnalysisInfoDTO::getMemberCode);
        List<String> goodsCodes = Arrays.asList(bidding.getGoodsCode());
        QueryMemberIntentionInfoDTO query = new QueryMemberIntentionInfoDTO();
        query.setMemberCodes(memberCodes);
        query.setGoodsCodes(goodsCodes);
        //K:saleChannel
        Map<String, List<MemberIntentionInfoDTO>> intentionGroup = CommonUtils.allowNullGroupByKey(
                iMemberService.queryMemberIntentionInfo(query), MemberIntentionInfoDTO::getSaleChannel);

        List<BiddingAnalysisResultDTO> result = new ArrayList<>();
        for (Integer i = 1; i <= bidding.getCurrentRound(); i++) {
            List<BiddingAnalysisInfoDTO> list = CommonUtils.getByKey(biddingAnalysisGroup, i);
            if (CollUtil.isEmpty(list))
                continue;
            BiddingAnalysisInfoDTO first = CollUtil.getFirst(list);
            BiddingAnalysisResultDTO target = new BiddingAnalysisResultDTO();
            target.setRound(first.getCurrentRound());
            target.setRoundName(CharSequenceUtil.format("第{}轮竞价", Convert.numberToChinese(first.getCurrentRound(), false)));
            target.setPrice(first.getPrice());
            target.setTotalQuantity(list.stream()
                    .map(BiddingAnalysisInfoDTO::getWantQuantity)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //终端客户
            List<MemberIntentionInfoDTO> euIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_EU);
            BiddingAnalysisSaleChannelDTO euAnalysis = getSaleChannelAnalysis(euIntentions, list, StringConstant.VO_SALES_CHANNEL_EU);
            //贸易商
            List<MemberIntentionInfoDTO> rsIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_RS);
            BiddingAnalysisSaleChannelDTO rsAnalysis = getSaleChannelAnalysis(rsIntentions, list, StringConstant.VO_SALES_CHANNEL_RS);
            //分销商
            List<MemberIntentionInfoDTO> dsIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_DS);
            BiddingAnalysisSaleChannelDTO dsAnalysis = getSaleChannelAnalysis(dsIntentions, list, StringConstant.VO_SALES_CHANNEL_DS);
            //其它
            List<MemberIntentionInfoDTO> otherIntentions = CommonUtils.allowNullGetByKey(intentionGroup, null);
            BiddingAnalysisSaleChannelDTO otherAnalysis = getSaleChannelAnalysis(otherIntentions, list, null);

            List<BiddingAnalysisSaleChannelDTO> saleChannelAnalysis = new ArrayList<>();
            if (Objects.nonNull(euAnalysis))
                saleChannelAnalysis.add(euAnalysis);
            if (Objects.nonNull(rsAnalysis))
                saleChannelAnalysis.add(rsAnalysis);
            if (Objects.nonNull(dsAnalysis))
                saleChannelAnalysis.add(dsAnalysis);
            if (Objects.nonNull(otherAnalysis))
                saleChannelAnalysis.add(otherAnalysis);

            target.setSaleChannelAnalysis(saleChannelAnalysis);
            result.add(target);
        }

        return result;
    }

    @Override
    public PageInfo<MemberActivityReportResultDTO> memberActivityReport(MemberActivityReportQueryDTO param) {
        //查询数据权限
        AccountRoleDTO queryDataPermission = new AccountRoleDTO();
        queryDataPermission.setAccountId(param.getOperatorBy());
        List<DataPermissionDTO> dataPermissionList = roleService.getDataPermissionList(queryDataPermission);
        if (CollUtil.isEmpty(dataPermissionList)) {
            log.info("活跃度报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        List<String> dataPermissionGoodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(dataPermissionList, DataPermissionDTO::getGoodsCode);
        if (CollUtil.isEmpty(dataPermissionGoodsCodes)) {
            log.info("活跃度报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        param.setDataPermissionGoodsCodes(dataPermissionGoodsCodes);

        //查询CRM客户编码匹配到的会员编码
        if (CharSequenceUtil.isNotBlank(param.getCrmCode())) {
            List<MemberSimpleDTO> memberResult = iMemberService.queryMemberByLikeCrmCode(param.getCrmCode());
            if (CollUtil.isEmpty(memberResult)) {
                log.info("活跃度报表 accountId:{} crmCode:{} 会员名称未匹配到数据", param.getOperatorBy(), param.getCrmCode());
                return PageInfo.emptyPageInfo();
            }
            param.setMemberCodeByCrmCode(CommonUtils.getListValueByDistinctAndFilterBank(memberResult, MemberSimpleDTO::getMemberCode));
        }

        //查询SAP物料号匹配到的商品编码
        if (CharSequenceUtil.isNotBlank(param.getSapMaterialCode())) {
            ItemResult<List<GoodsSimpleDTO>> goodsResult = goodsService.findGoodsSimpleByLikeSapMaterialCode(param.getSapMaterialCode());
            if (Objects.isNull(goodsResult) || CollUtil.isEmpty(goodsResult.getData())) {
                log.info("活跃度报表 accountId:{} sapMaterialCode:{} SAP物料号未匹配到数据", param.getOperatorBy(), param.getSapMaterialCode());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeBySapMaterialCode(CommonUtils.getListValueByDistinctAndFilterBank(goodsResult.getData(), GoodsSimpleDTO::getGoodsCode));
        }
        //查询商品分类匹配到的商品编码
        if (CollUtil.isNotEmpty(param.getCategoryCodes())) {
            List<GoodsCategoryRelationSimpleDTO> relationResult = iGoodCategoryRelationService.queryRelationByCategoryCodes(param.getCategoryCodes());
            if (CollUtil.isEmpty(relationResult)) {
                log.info("活跃度报表 accountId:{} goodsCategoryCodes:{} 产品分类未匹配到数据", param.getOperatorBy(), param.getCategoryCodes());
                return PageInfo.emptyPageInfo();
            }
            List<String> goodsCodeByCategoryCode = CommonUtils.getListValueByDistinctAndFilterBank(relationResult, GoodsCategoryRelationSimpleDTO::getGoodsCode);
            if (CollUtil.isEmpty(goodsCodeByCategoryCode)) {
                log.info("活跃度报表 accountId:{} goodsCategoryCodes:{} 产品分类未匹配到商品编码", param.getOperatorBy(), param.getCategoryCodes());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeByCategoryCode(goodsCodeByCategoryCode);
        }
        //查询销售渠道 匹配产品_会员 goodsCode_memberCode
        if (CollUtil.isNotEmpty(param.getSaleChannels())) {
            List<MemberIntentionSimpleDTO> intentionResult = iMemberService.queryIntentionBySaleChannel(param.getSaleChannels());
            if (CollUtil.isEmpty(intentionResult)) {
                log.info("活跃度报表 accountId:{} saleChannels:{} 销售渠道未匹配到数据", param.getOperatorBy(), param.getSaleChannels());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeConcatMemberCodeBySaleChannel(
                    CommonUtils.getListValueByDistinctAndFilterBank(
                            intentionResult, MemberIntentionSimpleDTO::getGoodsCodeConcatMemberCode));
        }

        log.info("活跃度报表 accountId:{} 查询参数:{}", param.getOperatorBy(), param);
        if(BooleanUtil.isTrue(param.getNeedPage())){
            return PageMethod
                    .startPage(param.getPageNum(), param.getPageSize())
                    .doSelectPageInfo(() -> biddingMapper.memberActivityReport(param));

        }else{
            return PageInfo.of(biddingMapper.memberActivityReport(param));
        }
    }

    private String getINcon1(BiddingBuyerDeal deal) {
        String iNcon1 = "";
        if (CharSequenceUtil.isNotBlank(deal.getDeliveryMode())) {
            switch (deal.getDeliveryMode()) {
                case VO_DELIVERY_METHOD_BP:
                    iNcon1 = "EXW";
                    break;
                case VO_DELIVERY_METHOD_SD:
                    iNcon1 = "CIP";
                    break;
                default:
                    break;
            }
        }
        return iNcon1;
    }

    private void setWERKS(ValueSetTreeDTO factoryOption, SapContractDTO.ItemDTO item) {
        if (Objects.nonNull(factoryOption))
            item.setWERKS(factoryOption.getOptionInfo());
    }

    private void setMATNR(GoodsSapMappingSimpleDTO goodsSapMapping, SapContractDTO.ItemDTO item) {
        if (Objects.nonNull(goodsSapMapping))
            item.setMATNR(goodsSapMapping.getMappingSapMaterialCode());
    }

    private void setVKGRP(ValueSetTreeDTO saleGroupOption, SapContractDTO.HeaderDTO head, GoodsSimpleDTO goods) {
        if (Objects.nonNull(saleGroupOption))
            head.setVKGRP(ObjectUtil.defaultIfBlank(saleGroupOption.getOptionInfo(), goods.getSalesGroup()));
    }

    private void setPriceType(SapContractDTO.ItemDTO item, String iNcon1) {
        if ("EXW".equals(iNcon1)) {
            //自提
            item.setKSCHL("YYP0");
        } else if ("CIP".equals(iNcon1)) {
            //配送
            item.setKSCHL("YPRO");
        }
    }

    private Map<String, GoodsSapMappingSimpleDTO> setGoodsMappingMap(List<GoodsSapMappingSimpleDTO> goodsMappingList) {
        Map<String, GoodsSapMappingSimpleDTO> goodsMappingMap = null;
        if (CollUtil.isNotEmpty(goodsMappingList)) {
            goodsMappingMap = goodsMappingList.stream()
                    .collect(Collectors.toMap(v ->
                            CharSequenceUtil.format(TEMPLATE, v.getGoodsCode(), v.getPack()), Function.identity(), (v1, v2) -> v1));
        }
        return goodsMappingMap;
    }

    private void setCommonData(SapContractDTO.HeaderDTO head, MemberPurchaseGoodsIntentionDTO intention, Map<String, AccountSimpleDTO> customerServiceMap, Map<String, Map<String, ValueSetTreeDTO>> dictMap) {
        if (Objects.nonNull(intention)) {
            //销售代表
            head.setZKUNNR_ZR(intention.getSaleUserEmployeeId());
            //客户服务代表
            AccountSimpleDTO customerService = CommonUtils.getByKey(customerServiceMap, intention.getSaleUserId());
            if (Objects.nonNull(customerService) && CharSequenceUtil.isNotBlank(customerService.getEmployeeId())) {
                head.setZKUNNR_E1(customerService.getEmployeeId());
            } else {
                // 设置默认客户服务代表，避免SAP接口报错
                log.warn("客户服务代表信息缺失，设置默认值。saleUserId: {}, memberCode: {}", 
                    intention.getSaleUserId(), intention.getMemberCode());
                head.setZKUNNR_E1("DEFAULT_E1"); // 需要根据实际业务设置合适的默认值
            }
            //销售渠道
            Map<String, ValueSetTreeDTO> saleChannelMap = CommonUtils.getByKey(dictMap, VS_SALES_CHANNEL);
            ValueSetTreeDTO saleChannelOption = CommonUtils.getByKey(saleChannelMap, intention.getSaleChannel());
            //客户组1
            if (Objects.nonNull(saleChannelOption))
                head.setKVGR1(saleChannelOption.getOptionInfo());
        }
    }

    private Map<String, AccountSimpleDTO> getCustomerServiceMap(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        List<String> saleUserIds = intentions.stream()
                .map(v -> v.getSaleUserId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        return getSaleCustomerService(saleUserIds);
    }

    private Map<String, MemberPurchaseGoodsIntentionDTO> getIntentionMap(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        return intentions.stream()
                .collect(Collectors.toMap(v -> CharSequenceUtil.format(TEMPLATE, v.getMemberCode(), v.getGoodsCode()), Function.identity(), (v1, v2) -> v1));
    }

    private Map<String, AccountSimpleDTO> getSaleCustomerService(List<String> saleUserIds) {
        if (CollUtil.isEmpty(saleUserIds))
            return null;
        QueryAccountRelationDTO queryRelation = new QueryAccountRelationDTO();
        queryRelation.setAccountIds(saleUserIds);
        queryRelation.setLinkAccountRoles(Arrays.asList(BaseRoleTypeEnum.SELLER_CUSTOMER_SERVICE.getRoleCode()));
        List<AccountRelationInfoDTO> saleRelations = iAccountRelationService.queryAccountRelation(queryRelation);
        if (CollUtil.isNotEmpty(saleRelations)) {
            List<String> linkAccountIds = saleRelations.stream()
                    .map(v -> v.getLinkAccountId())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(linkAccountIds)) {
                Map<String, AccountSimpleDTO> customerServcieMap = CommonUtils.getMap(iAccountService.listSimpleByIds(linkAccountIds), AccountSimpleDTO::getAccountId);
                //K:saleUserId V:customerService account
                Map<String, AccountSimpleDTO> result = new HashMap<>();

                saleRelations.forEach(v -> {
                    if (!result.containsKey(v.getAccountId())) {
                        AccountSimpleDTO customerService = CommonUtils.getByKey(customerServcieMap, v.getLinkAccountId());
                        if (Objects.nonNull(customerService))
                            result.put(v.getAccountId(), customerService);
                    }
                });
                return result;
            }
        }
        return null;
    }

    private List<Bidding> listByBiddingNos(List<String> biddingNos) {
        if (CollUtil.isEmpty(biddingNos))
            return null;
        Condition condition = new Condition(Bidding.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(ColumnConstant.BIDDING_NO, biddingNos);
        return this.findByCondition(condition);
    }

    private Map<String, Map<String, ValueSetTreeDTO>> getValueSetMap(List<String> codes) {
        if (CollUtil.isEmpty(codes))
            return Collections.emptyMap();
        List<ValueSetDTO> list = iValueSetService.getValueTreeByCodes(codes);
        if (CollUtil.isEmpty(list))
            return Collections.emptyMap();
        Map<String, ValueSetDTO> map = list.stream().collect(Collectors.toMap(v -> v.getReferenceCode(), Function.identity(), (v1, v2) -> v1));
        Map<String, Map<String, ValueSetTreeDTO>> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (CollUtil.isNotEmpty(v.getOptions())) {
                Map<String, ValueSetTreeDTO> optionMap = v.getOptions().stream().collect(Collectors.toMap(v1 -> v1.getOptionKey(), Function.identity(), (v2, v3) -> v2));
                result.put(k, optionMap);
            }
        });
        return result;
    }


    private static ItemResult fail(String message) {
        return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), message);
    }

    private static List<Bidding> getApproveBiddingByLevel(List<Bidding> biddingList, BiddingStatusEnum status) {
        if(CollUtil.isEmpty(biddingList))
            return Collections.emptyList();
        return biddingList.stream().filter(v -> status.getStatus().equals(v.getStatus())).collect(Collectors.toList());
    }

    public String getNowMonth() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return currentDate.format(formatter);
    }

    public String getLastYear() {
        LocalDate currentDate = LocalDate.now();
        LocalDate lastYearSameDay = currentDate.minusYears(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return lastYearSameDay.format(formatter);
    }

    private void createEndJob(Bidding bidding) {
        String endJobDesc = CharSequenceUtil.format("竞价场次编号为：{}（{}） 的自动结束竞价任务", bidding.getBiddingNo(), bidding.getBiddingName());

        //竞价结束 自动任务
        CronPatternBuilder endCron = CronPatternBuilder.of();
        endCron.set(Part.SECOND, String.valueOf(DateUtil.second(bidding.getLastBiddingEndTime())));
        endCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(bidding.getLastBiddingEndTime())));
        endCron.set(Part.HOUR, String.valueOf(DateUtil.hour(bidding.getLastBiddingEndTime(), true)));
        endCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(bidding.getLastBiddingEndTime())));
        endCron.set(Part.MONTH, String.valueOf(DateUtil.month(bidding.getLastBiddingEndTime()) + 1));
        endCron.set(Part.DAY_OF_WEEK, "?");
        Integer endYear = DateUtil.year(bidding.getLastBiddingEndTime());
        endCron.setRange(Part.YEAR, endYear, endYear);
        XxlJobDTO endJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                endJobDesc,
                endCron.build(),
                XxlJobConstant.BIDDING_END_JOB,
                bidding.getBiddingNo());
        //竞价开始户定时任务
        iXxlJobService.createXxlJob(endJob);
    }

    private static List<String> getAccountIds(List<AccountSimpleDTO> accounts) {
        if (CollUtil.isEmpty(accounts))
            return null;
        return accounts.stream()
                .filter(Objects::nonNull)
                .map(AccountSimpleDTO::getAccountId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    private static List<String> getAccountIds(Collection<List<AccountSimpleDTO>> accounts) {
        if (CollUtil.isEmpty(accounts))
            return null;
        Set<String> result = new HashSet<>();
        for (List<AccountSimpleDTO> account : accounts) {
            List<String> accountIds = getAccountIds(account);
            if (CollUtil.isNotEmpty(accountIds))
                result.addAll(accountIds);
        }
        return result.stream().collect(Collectors.toList());
    }


    /**
     * 竞价不成交微信通知
     */
    private void notifyWechatByNotDeal(Bidding bidding, List<AccountSubscribeWechatDTO> wechatAccounts) {
        log.info("竞价不成交微信通知开始 biddingNo:{} accounts:{}", bidding.getBiddingNo(), wechatAccounts);
        if (CollUtil.isEmpty(wechatAccounts))
            return;

        for (AccountSubscribeWechatDTO wechatAccount : wechatAccounts) {
            //微信通知
            WechatSendDTO wechatDto = new WechatSendDTO();
            wechatDto.setTemplateCode(WechatTemplateEnum.BIDDING_RESULTS_TEMPLATE.getCode());
            wechatDto.setTemplate_id(wechatAccount.getTemplateId());
            wechatDto.setTouser(wechatAccount.getOpenId());
            wechatDto.setPage(CharSequenceUtil.format(WechatTemplateEnum.BIDDING_RESULTS_TEMPLATE.getPage(), bidding.getId(), bidding.getBiddingNo()));
            wechatDto.setData(WechatTemplateEnum.createBiddingResultDataMap(bidding.getBiddingNo(), bidding.getBiddingName(), "未成交", "很遗憾您此次竞价未能成功！"));
            iWechatSendService.sendWechat(wechatDto);
        }
    }


    private List<Bidding> getListByAfterLastEndTime(List<Bidding> list, Date date) {
        if (CollUtil.isEmpty(list) || Objects.isNull(date))
            return list;
        if (Objects.nonNull(faultToleranceTime))
            date = DateUtil.offsetMinute(date, faultToleranceTime);
        Date finalDate = date;
        return list.stream().filter(v -> finalDate.after(v.getLastBiddingEndTime())).collect(Collectors.toList());
    }

    private static List<Bidding> filterNotContainsById(List<Bidding> list, List<Bidding> notContains) {
        if (CollUtil.isEmpty(list) || CollUtil.isEmpty(notContains))
            return list;
        List<String> notContainsIds = notContains.stream().map(Bidding::getId).collect(Collectors.toList());
        return list.stream().filter(v -> !CollUtil.contains(notContainsIds, v.getId())).collect(Collectors.toList());
    }

    private static void handRound(List<BiddingSalesListViewDTO> list) {
        if (CollUtil.isEmpty(list))
            return;
        for (BiddingSalesListViewDTO v : list) {
            if (Objects.nonNull(v.getCurrentRound()))
                v.setCurrentRoundName(CharSequenceUtil.format("第{}轮", Convert.numberToChinese(v.getCurrentRound(), false)));
        }
    }

    private static String getEndTime(String dateStr){
        if(CharSequenceUtil.isBlank(dateStr))
            return dateStr;
        try{
            dateStr = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(dateStr)));
        }catch (Exception e){
            log.error("getEndTime 获取指定天 结束时间 异常：",e);
        }
        return dateStr;
    }


    /**
     * 审批操作对竞价单号加锁
     * 同一个单子只能由一个人审批
     * 锁有效时长3分钟
     */
    private void lock(List<Bidding> list, String operatorId) {
        if (CollUtil.isEmpty(list))
            return;
        for (Bidding bidding : list) {
            String key = CharSequenceUtil.format(BIDDING_APPROVAL_LOCK, bidding.getBiddingNo());
            Boolean lockRs = bizRedisService.getRedisTemplate().opsForValue().setIfAbsent(key, operatorId, 3, TimeUnit.MINUTES);
            if (!BooleanUtil.isTrue(lockRs))
                throw new BizException(OrderErrorCode.CUSTOM, CharSequenceUtil.format("竞价场次{}正在进行审批", bidding.getBiddingNo()));
        }
    }

    /**
     * 审批操作对竞价单号解锁
     * 释放当前操作人的拿到的锁
     * 同一个单子只能由一个人审批
     * 锁有效时长3分钟
     */
    private void unlock(List<Bidding> list, String operatorId) {
        if (CollUtil.isEmpty(list))
            return;
        for (Bidding bidding : list) {
            String key = CharSequenceUtil.format(BIDDING_APPROVAL_LOCK, bidding.getBiddingNo());
            if (!BooleanUtil.isTrue(bizRedisService.existKey(key)))
                continue;
            String value = bizRedisService.get(key);
            if (CharSequenceUtil.equals(value, operatorId))
                bizRedisService.del(key);
        }
    }

    /**
     * 通知撤回策略提交人 审批被拒绝
     *
     * @param biddingList 撤回策略审批记录
     * @param dto         审批操作参数
     */
    private void notifyWithdrawUserByReject(List<Bidding> biddingList, BiddingApprovalDTO dto) {
        log.info("通知撤回竞价策略提交人审批拒绝开始");
        if (CollUtil.isEmpty(biddingList))
            return;
        List<String> withdrawUsers = CommonUtils.getListValueByDistinctAndFilterBank(biddingList, Bidding::getWithdrawUser);
        log.info("通知撤回竞价策略提交人审批拒绝开始 biddingNos:{} withdrawUsers:{}",
                CommonUtils.getListValueByDistinctAndFilterBank(biddingList,Bidding::getBiddingNo),withdrawUsers);
        if (CollUtil.isEmpty(withdrawUsers))
            return;
        Map<String, AccountSimpleDTO> accountMap = iBiddingAccountService.getAccount(withdrawUsers);
        if (CollUtil.isEmpty(accountMap))
            return;
        log.info("通知撤回竞价策略提交人审批拒绝开始 biddingNos:{} withdrawUsersAccount:{}",
                CommonUtils.getListValueByDistinctAndFilterBank(biddingList,Bidding::getBiddingNo),accountMap.keySet());

        for (Bidding bidding : biddingList) {
            log.info("通知撤回竞价策略提交人审批拒绝开始发送消息 biddingNo:{} withdrawUser:{}",bidding.getBiddingNo(),bidding.getWithdrawUser());

            AccountSimpleDTO account = CommonUtils.getByKey(accountMap, bidding.getWithdrawUser());
            if (Objects.isNull(account))
                continue;
            //发送站内信
            log.info("通知撤回竞价策略提交人审批拒绝开始发送消息-站内信 biddingNo:{} withdrawUser:{}",bidding.getBiddingNo(),bidding.getWithdrawUser());
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(Arrays.asList(account.getAccountId()));
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_REJECTED_TO_WITHDRAW_USER.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_REJECTED_TO_WITHDRAW_USER.getMsg(account.getRealName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(siteMsg);

            //通知撤回人邮件
            log.info("通知撤回竞价策略提交人审批拒绝开始发送消息-邮件 biddingNo:{} withdrawUser:{}",bidding.getBiddingNo(),bidding.getWithdrawUser());
            EmailDTO email = new EmailDTO();
            email.setTos(Arrays.asList(account.getEmail()));
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_REJECTED_TO_WITHDRAW_USER.getCode());
            Map<String, Object> emailParam = new HashMap<>();
            emailParam.put(REAL_NAME, account.getRealName());
            emailParam.put(BIDDING_NO, bidding.getBiddingNo());
            emailParam.put(BIDDING_NAME, bidding.getBiddingName());
            emailParam.put(APPROVAL_NAME, dto.getRealName());
            emailParam.put(REJECT_REASON, dto.getReason());

            email.setTemplateParam(emailParam);
            iEmailSendService.sendEmail(email);
        }
    }


    /**
     * 通知取消竞价提交人 审批被拒绝
     *
     * @param biddingList 取消竞价审批记录
     * @param dto         审批操作参数
     */
    private void notifyCancelUserByReject(List<Bidding> biddingList, BiddingApprovalDTO dto) {
        log.info("通知取消竞价提交人审批拒绝开始");
        if (CollUtil.isEmpty(biddingList))
            return;

        List<String> cancelUsers = CommonUtils.getListValueByDistinctAndFilterBank(biddingList, Bidding::getCancelUser);
        log.info("通知取消竞价提交人审批拒绝开始 biddingNos:{} cancelUsers:{}",
                CommonUtils.getListValueByDistinctAndFilterBank(biddingList,Bidding::getBiddingNo),cancelUsers);
        if (CollUtil.isEmpty(cancelUsers))
            return;
        Map<String, AccountSimpleDTO> accountMap = iBiddingAccountService.getAccount(cancelUsers);
        if (CollUtil.isEmpty(accountMap))
            return;
        log.info("通知取消竞价提交人审批拒绝开始 biddingNos:{} cancelUsersAccount:{}",
                CommonUtils.getListValueByDistinctAndFilterBank(biddingList,Bidding::getBiddingNo),accountMap.keySet());

        for (Bidding bidding : biddingList) {
            log.info("通知取消竞价提交人审批拒绝开始发送消息 biddingNo:{} cancelUser:{}",bidding.getBiddingNo(),bidding.getCancelUser());
            AccountSimpleDTO account = CommonUtils.getByKey(accountMap, bidding.getCancelUser());
            if (Objects.isNull(account))
                continue;

            //发送站内信
            log.info("通知取消竞价提交人审批拒绝开始发送消息-站内信 biddingNo:{} cancelUser:{}",bidding.getBiddingNo(),bidding.getCancelUser());
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(Arrays.asList(account.getAccountId()));
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_REJECTED_TO_CANCEL_USER.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_REJECTED_TO_CANCEL_USER.getMsg(account.getRealName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(siteMsg);

            //通知取消人邮件
            log.info("通知取消竞价提交人审批拒绝开始发送消息-邮件 biddingNo:{} cancelUser:{}",bidding.getBiddingNo(),bidding.getCancelUser());
            EmailDTO email = new EmailDTO();
            email.setTos(Arrays.asList(account.getEmail()));
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_CANCEL_BIDDING_REJECTED_TO_CANCEL_USER.getCode());
            Map<String, Object> emailParam = new HashMap<>();
            emailParam.put(REAL_NAME, account.getRealName());
            emailParam.put(BIDDING_NO, bidding.getBiddingNo());
            emailParam.put(BIDDING_NAME, bidding.getBiddingName());
            emailParam.put(APPROVAL_NAME, dto.getRealName());
            emailParam.put(REJECT_REASON, dto.getReason());

            email.setTemplateParam(emailParam);
            iEmailSendService.sendEmail(email);
        }
        log.info("通知取消竞价提交人审批拒绝结束");
    }

    /**
     * 一级审批 通知二级审批人待审批消息
     * 二级审批通知撤回人审批通过
     *
     * @param dto              审批参数
     * @param cmmsApprovalList 一级审批数据
     * @param cmApprovalList   二级审批数据
     */
    private void notifyWithdrawApprovalByPass(BiddingApprovalDTO dto, List<Bidding> cmmsApprovalList, List<Bidding> cmApprovalList) {
        if (CollUtil.isEmpty(cmmsApprovalList) && CollUtil.isEmpty(cmApprovalList))
            return;

        if (CollUtil.isNotEmpty(cmmsApprovalList)) {
            List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(cmmsApprovalList, Bidding::getGoodsCode);
            Map<String, List<AccountNameDTO>> cmMap = iBiddingAccountService.findApprover(BaseRoleTypeEnum.SELLER_CM.getRoleCode(), goodsCodes);
            if (CollUtil.isNotEmpty(cmMap)) {
                for (Bidding bidding : cmmsApprovalList) {
                    List<AccountNameDTO> cmAccounts = CommonUtils.getByKey(cmMap, bidding.getGoodsCode());
                    if (CollUtil.isEmpty(cmAccounts))
                        continue;
                    for (AccountNameDTO cmAccount : cmAccounts) {
                        //发送站内信
                        MessageDTO siteMsg = new MessageDTO();
                        siteMsg.setReceiveAccountIds(Arrays.asList(cmAccount.getAccountId()));
                        siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CM.getTitle());
                        siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CM.getMsg(cmAccount.getRealName(), bidding.getBiddingNo()));
                        iMessageSendService.sendMessage(siteMsg);

                        //通知 CM审批人邮件
                        EmailDTO email = new EmailDTO();
                        email.setTos(Arrays.asList(cmAccount.getEmail()));
                        email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CM.getCode());
                        Map<String, Object> emailParam = new HashMap<>();
                        emailParam.put(REAL_NAME, cmAccount.getRealName());
                        emailParam.put(BIDDING_NO, bidding.getBiddingNo());
                        emailParam.put(BIDDING_NAME, bidding.getBiddingName());
                        emailParam.put("withdrawUsername", bidding.getWithdrawUserName());
                        emailParam.put("withdrawReason", bidding.getWithdrawReason());

                        email.setTemplateParam(emailParam);
                        iEmailSendService.sendEmail(email);
                    }
                }
            }
        }

        if (CollUtil.isEmpty(cmApprovalList))
            return;
        //通知提交人 审批通过
        List<String> withdrawUsers = CommonUtils.getListValueByDistinctAndFilterBank(cmApprovalList, Bidding::getWithdrawUser);
        if (CollUtil.isEmpty(withdrawUsers))
            return;
        Map<String, AccountSimpleDTO> accountMap = iBiddingAccountService.getAccount(withdrawUsers);
        if (CollUtil.isEmpty(accountMap))
            return;
        for (Bidding bidding : cmApprovalList) {
            AccountSimpleDTO account = CommonUtils.getByKey(accountMap, bidding.getWithdrawUser());
            if (Objects.isNull(account))
                continue;
            //发送站内信
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(Arrays.asList(account.getAccountId()));
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_APPROVED_TO_WITHDRAW_USER.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_APPROVED_TO_WITHDRAW_USER.getMsg(account.getRealName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(siteMsg);

            //通知撤回人邮件
            EmailDTO email = new EmailDTO();
            email.setTos(Arrays.asList(account.getEmail()));
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_APPROVED_TO_WITHDRAW_USER.getCode());
            Map<String, Object> emailParam = new HashMap<>();
            emailParam.put(REAL_NAME, account.getRealName());
            emailParam.put(BIDDING_NO, bidding.getBiddingNo());
            emailParam.put(BIDDING_NAME, bidding.getBiddingName());

            email.setTemplateParam(emailParam);
            iEmailSendService.sendEmail(email);
        }
    }

    /**
     * 一级审批 通知二级审批人待审批消息
     * 二级审批通知取消人审批通过
     *
     * @param dto              审批参数
     * @param cmmsApprovalList 一级审批数据
     * @param cmApprovalList   二级审批数据
     */
    private void notifyCancelApprovalByPass(BiddingApprovalDTO dto, List<Bidding> cmmsApprovalList, List<Bidding> cmApprovalList) {
        if (CollUtil.isEmpty(cmmsApprovalList) && CollUtil.isEmpty(cmApprovalList))
            return;

        if (CollUtil.isNotEmpty(cmmsApprovalList)) {
            List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(cmmsApprovalList, Bidding::getGoodsCode);
            Map<String, List<AccountNameDTO>> cmMap = iBiddingAccountService.findApprover(BaseRoleTypeEnum.SELLER_CM.getRoleCode(), goodsCodes);
            if (CollUtil.isNotEmpty(cmMap)) {
                for (Bidding bidding : cmmsApprovalList) {
                    List<AccountNameDTO> cmAccounts = CommonUtils.getByKey(cmMap, bidding.getGoodsCode());
                    if (CollUtil.isEmpty(cmAccounts))
                        continue;
                    for (AccountNameDTO cmAccount : cmAccounts) {
                        //发送站内信
                        MessageDTO siteMsg = new MessageDTO();
                        siteMsg.setReceiveAccountIds(Arrays.asList(cmAccount.getAccountId()));
                        siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CM.getTitle());
                        siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CM.getMsg(cmAccount.getRealName(), bidding.getBiddingNo()));
                        iMessageSendService.sendMessage(siteMsg);

                        //通知 CM审批人邮件
                        EmailDTO email = new EmailDTO();
                        email.setTos(Arrays.asList(cmAccount.getEmail()));
                        email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CM.getCode());
                        Map<String, Object> emailParam = new HashMap<>();
                        emailParam.put(REAL_NAME, cmAccount.getRealName());
                        emailParam.put(BIDDING_NO, bidding.getBiddingNo());
                        emailParam.put(BIDDING_NAME, bidding.getBiddingName());
                        emailParam.put("cancelUsername", bidding.getCancelUserName());
                        emailParam.put("cancelReason", bidding.getCancelReason());

                        email.setTemplateParam(emailParam);
                        iEmailSendService.sendEmail(email);
                    }
                }
            }
        }

        //竞价取消二级审批通过 通知开始
        if (CollUtil.isEmpty(cmApprovalList))
            return;
        //通知提交人 审批通过
        List<String> cancelUsers = CommonUtils.getListValueByDistinctAndFilterBank(cmApprovalList, Bidding::getCancelUser);
        Map<String, AccountSimpleDTO> accountMap = iBiddingAccountService.getAccount(cancelUsers);
        //通知可参与竞价的客户
        List<BiddingBuyer> biddingBuyers = iBiddingBuyerBiz.listByBidding(cmApprovalList);
        List<String> buyerCoeds = CommonUtils.getListValueByDistinctAndFilterBank(biddingBuyers,BiddingBuyer::getMemberCode);
        Map<String,List<BiddingBuyer>> biddingBuyerGroup = CommonUtils.group(biddingBuyers,BiddingBuyer::getBiddingNo);
        //客户管理员
        List<AccountSimpleDTO> buyerAdmins = iBiddingAccountService.queryMemberAccountByRoleCodes(buyerCoeds,Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()),null);
        Map<String,List<AccountSimpleDTO>> buyerAdminGroup = CommonUtils.group(buyerAdmins,AccountSimpleDTO::getMemberCode);
        //查询客户 产品人员
        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> buyerGoodsAccountMap = iBiddingAccountService
                .queryAccountDataPermission(buyerCoeds,CommonUtils.getListValueByDistinctAndFilterBank(cmApprovalList,Bidding::getGoodsCode));
        for (Bidding bidding : cmApprovalList) {
            //通知 取消提交人
            this.notifyCancelUserApprovalByPass(bidding, accountMap);

            //通知 客户管理员 产品人员竞价取消
            this.notifyBuyerBiddingCancel(bidding, CommonUtils.getByKey(biddingBuyerGroup,bidding.getBiddingNo()),
                    buyerAdminGroup, buyerGoodsAccountMap);
        }
    }


    /**
     * 通知客户管理员、产品人员竞价取消
     *
     * @param bidding              竞价数据
     * @param biddingBuyersList    竞价客户数据
     * @param buyerAdminGroup      客户管理员数据
     * @param buyerGoodsAccountMap 客户产品人员数据
     */
    private void notifyBuyerBiddingCancel(Bidding bidding, List<BiddingBuyer> biddingBuyersList,
                                          Map<String, List<AccountSimpleDTO>> buyerAdminGroup,
                                          Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> buyerGoodsAccountMap) {
        //通知客户
        if (CollUtil.isEmpty(biddingBuyersList))
            return;
        for (BiddingBuyer biddingBuyer : biddingBuyersList) {
            //客户管理员
            List<AccountSimpleDTO> biddingBuyerAdmins = CommonUtils.getByKey(buyerAdminGroup, biddingBuyer.getMemberCode());
            //客户产品人员
            List<AccountSimpleDTO> biddingBuyerGoodsAccounts = CommonUtils.getByKey(buyerGoodsAccountMap,
                    new QueryAccountDataPermissionDTO(biddingBuyer.getMemberCode(), bidding.getGoodsCode()));
            List<AccountSimpleDTO> allBuyerAccounts = CommonUtils.addAll(biddingBuyerAdmins, biddingBuyerGoodsAccounts);

            if (CollUtil.isEmpty(allBuyerAccounts))
                continue;

            String memberName = CollUtil.getFirst(allBuyerAccounts).getMemberName();
            List<String> buyerAccountIds = CommonUtils.getListValueByDistinctAndFilterBank(allBuyerAccounts, AccountSimpleDTO::getAccountId);
            List<String> buyerMobiles = CommonUtils.getListValueByDistinctAndFilterBank(allBuyerAccounts, AccountSimpleDTO::getMobile);

            //发送站内信通知客户竞价取消
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(buyerAccountIds);
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_BUYER.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_BUYER.getMsg(memberName, bidding.getBiddingNo(), bidding.getGoodsName()));
            iMessageSendService.sendMessage(siteMsg);

            //发送短信通知客户竞价取消
            SmsDTO sms = new SmsDTO();
            sms.setMobiles(buyerMobiles);
            sms.setTemplateCode(SmsTemplateEnum.NOTIFY_BIDDING_CANCEL.getCode());
            sms.setTemplateParams(Arrays.asList(bidding.getBiddingNo(), bidding.getGoodsName()));
            iSmsSendService.sendSms(sms);
        }
    }


    /**
     * 通知提交取消竞价的提交人 竞价取消审批通过
     * @param bidding 竞价数据
     * @param accountMap 提交人信息
     */
    private void notifyCancelUserApprovalByPass(Bidding bidding, Map<String, AccountSimpleDTO> accountMap) {
        AccountSimpleDTO account = CommonUtils.getByKey(accountMap, bidding.getCancelUser());
        if (Objects.isNull(account))
            return;
        //发送站内信
        MessageDTO siteMsg = new MessageDTO();
        siteMsg.setReceiveAccountIds(Arrays.asList(account.getAccountId()));
        siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_APPROVED_TO_CANCEL_USER.getTitle());
        siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_APPROVED_TO_CANCEL_USER.getMsg(account.getRealName(), bidding.getBiddingNo()));
        iMessageSendService.sendMessage(siteMsg);

        //通知取消人邮件
        EmailDTO email = new EmailDTO();
        email.setTos(Arrays.asList(account.getEmail()));
        email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_CANCEL_BIDDING_APPROVED_TO_CANCEL_USER.getCode());
        Map<String, Object> emailParam = new HashMap<>();
        emailParam.put(REAL_NAME, account.getRealName());
        emailParam.put(BIDDING_NO, bidding.getBiddingNo());
        emailParam.put(BIDDING_NAME, bidding.getBiddingName());

        email.setTemplateParam(emailParam);
        iEmailSendService.sendEmail(email);
    }

    /**
     * 处理竞价审批超时情况
     * 若 审批时间超过当前时间+若错时间 则作废竞价
     * 竞价时间中 或者竞价开始时间前 在方法updateBiddingStatusByIds 中更新状态至APPROVED_STRATEGY时已处理
     * @param dto 审批参数
     * @param date 与竞技结束时间比较的时间
     * @param biddingList 超时数据
     */
    private void handleOverBiddingEndTime(BiddingApprovalDTO dto, Date date, List<Bidding> biddingList) {
        if(CollUtil.isEmpty(biddingList))
            return;
        List<Bidding> afterEndTimeBiddingList = biddingList.stream()
                .filter(v -> date.after(v.getLastBiddingEndTime()))
                .collect(Collectors.toList());
        this.updateBiddingStatusByIds(BiddingStatusEnum.CANCELLED, dto.getAccountId(), afterEndTimeBiddingList, false);
    }

    /**
     * 竞价审批通过
     *
     * @param dto         审批参数
     * @param now         当前时间
     * @param biddingList 审批的竞价数据
     */
    private void biddingApprovalPass(BiddingApprovalDTO dto, Date now, List<Bidding> biddingList) {
        //策略待审批数据
        List<String> allowStrategyApproveStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_STRATEGY.getStatus());
        List<Bidding> waitApproveStrategy = biddingList.stream()
                .filter(v -> CollUtil.contains(allowStrategyApproveStatus, v.getStatus()))
                .collect(Collectors.toList());

        //成交结果待审批数据
        List<String> allowDealResultApproveStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(),
                BiddingStatusEnum.APPROVING_RESULT.getStatus());
        List<Bidding> waitApproveResult = biddingList.stream()
                .filter(v -> CollUtil.contains(allowDealResultApproveStatus, v.getStatus()))
                .collect(Collectors.toList());

        //撤回策略待审批数据
        List<String> allowStrategyWithdrawStatus = Arrays.asList(BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus());
        List<Bidding> waitWithdrawStrategy = biddingList.stream()
                .filter(v -> CollUtil.contains(allowStrategyWithdrawStatus, v.getStatus()))
                .collect(Collectors.toList());

        //取消竞价待审批数据
        List<String> allowCancelStatus = Arrays.asList(BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.CANCEL_APPROVING.getStatus());
        List<Bidding> waitCancel = biddingList.stream()
                .filter(v -> CollUtil.contains(allowCancelStatus, v.getStatus()))
                .collect(Collectors.toList());

        //更新竞价状态
        //策略待审批->策略审批中
        this.updateBiddingStatusByIds(BiddingStatusEnum.APPROVING_STRATEGY, dto.getAccountId(), getApproveBiddingByLevel(waitApproveStrategy, BiddingStatusEnum.TO_APPROVE_STRATEGY), false);
        //策略审批中->策略审批通过
        List<Bidding> approvedStrategyList = getApproveBiddingByLevel(waitApproveStrategy, BiddingStatusEnum.APPROVING_STRATEGY);
        //bug 3618 CM审批通过竞价策略时，如果已经超过了竞价结束时间，则处理成为已作废状态
        //获取需要作废的数据
        List<Bidding> needCancelList = getListByAfterLastEndTime(approvedStrategyList, now);
        approvedStrategyList = filterNotContainsById(approvedStrategyList, needCancelList);
        // 过滤掉需要作废的数据
        waitApproveStrategy = filterNotContainsById(waitApproveStrategy, needCancelList);
        //作废 cm审批策略超时的数据
        this.updateBiddingStatusByIds(BiddingStatusEnum.CANCELLED, dto.getAccountId(), needCancelList, false);
        //cm审批竞价策略通过
        this.updateBiddingStatusByIds(BiddingStatusEnum.APPROVED_STRATEGY, dto.getAccountId(), approvedStrategyList, false);
        //成交结果待审批->成交结果审批中
        this.updateBiddingStatusByIds(BiddingStatusEnum.APPROVING_RESULT, dto.getAccountId(), getApproveBiddingByLevel(waitApproveResult, BiddingStatusEnum.TO_APPROVE_RESULT), false);
        //成交结果审批中->已完成
        this.updateBiddingStatusByIds(BiddingStatusEnum.COMPLETED, dto.getAccountId(), getApproveBiddingByLevel(waitApproveResult, BiddingStatusEnum.APPROVING_RESULT), false);
        //撤回待审批 -> 撤回审批中 一级审批
        List<Bidding> cmmsApprovalWithdrawBiddingList = getApproveBiddingByLevel(waitWithdrawStrategy,BiddingStatusEnum.WITHDRAW_TO_APPROVAL);
        this.updateBiddingStatusByIds(BiddingStatusEnum.WITHDRAWING_APPROVING,dto.getAccountId(),cmmsApprovalWithdrawBiddingList,false);
        //撤回审批中 -> 已撤回 二级审批
        List<Bidding> cmApprovalWithdrawBiddingList = getApproveBiddingByLevel(waitWithdrawStrategy,BiddingStatusEnum.WITHDRAWING_APPROVING);
        this.updateBiddingStatusByIds(BiddingStatusEnum.WITHDRAWN,dto.getAccountId(),cmApprovalWithdrawBiddingList,false);

        //取消待审批 -> 取消审批中 一级审批
        List<Bidding> cmmsApprovalCancelBiddingList = getApproveBiddingByLevel(waitCancel,BiddingStatusEnum.CANCEL_TO_APPROVAL);
        this.updateBiddingStatusByIds(BiddingStatusEnum.CANCEL_APPROVING,dto.getAccountId(),cmmsApprovalCancelBiddingList,false);
        //取消审批中 -> 已取消  二级审批
        List<Bidding> cmApprovalCancelBiddingList = getApproveBiddingByLevel(waitCancel,BiddingStatusEnum.CANCEL_APPROVING);
        this.updateBiddingStatusByIds(BiddingStatusEnum.BIDDING_CANCELLED,dto.getAccountId(),cmApprovalCancelBiddingList,false);

        //策略 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitApproveStrategy, ApproveTypeEnum.STRATEGY, ApproveResultEnum.APPROVED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //成交结果 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitApproveResult, ApproveTypeEnum.RESULT, ApproveResultEnum.APPROVED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //保存策略审批超时作废 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(needCancelList, ApproveTypeEnum.STRATEGY, ApproveResultEnum.APPROVED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //撤回 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitWithdrawStrategy,ApproveTypeEnum.STRATEGY_WITHDRAW,ApproveResultEnum.APPROVED,
                dto.getReason(),dto.getAccountId(),dto.getRealName());
        //取消 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitCancel,ApproveTypeEnum.BIDDING_CANCEL,ApproveResultEnum.APPROVED,
                dto.getReason(),dto.getAccountId(),dto.getRealName());

        //审批通过通知 异步处理
        List<Bidding> finalWaitApproveStrategy = waitApproveStrategy;
        executor.execute(() -> this.notifyByStrategyApproved(finalWaitApproveStrategy, dto));
        executor.execute(() -> this.notifyByResultApproved(waitApproveResult, dto));
        executor.execute(() -> this.notifyWithdrawApprovalByPass(dto, cmmsApprovalWithdrawBiddingList, cmApprovalWithdrawBiddingList));
        executor.execute(() -> this.notifyCancelApprovalByPass(dto, cmmsApprovalCancelBiddingList, cmApprovalCancelBiddingList));

    }

    /**
     * 竞价审批拒绝
     *
     * @param dto         审批参数
     * @param now         当前时间
     * @param biddingList 审批的竞价数据
     */
    private void biddingApprovalReject(BiddingApprovalDTO dto, Date now, List<Bidding> biddingList) {
        //策略待审批数据
        List<String> allowStrategyApproveStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_STRATEGY.getStatus(),
                BiddingStatusEnum.APPROVING_STRATEGY.getStatus());
        List<Bidding> waitApproveStrategy = biddingList.stream()
                .filter(v -> CollUtil.contains(allowStrategyApproveStatus, v.getStatus()))
                .collect(Collectors.toList());

        //成交结果待审批数据
        List<String> allowDealResultApproveStatus = Arrays.asList(BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(),
                BiddingStatusEnum.APPROVING_RESULT.getStatus());
        List<Bidding> waitApproveResult = biddingList.stream()
                .filter(v -> CollUtil.contains(allowDealResultApproveStatus, v.getStatus()))
                .collect(Collectors.toList());

        //撤回策略待审批数据
        List<String> allowStrategyWithdrawStatus = Arrays.asList(BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.WITHDRAWING_APPROVING.getStatus());
        List<Bidding> waitWithdrawStrategy = biddingList.stream()
                .filter(v -> CollUtil.contains(allowStrategyWithdrawStatus, v.getStatus()))
                .collect(Collectors.toList());

        //取消竞价待审批数据
        List<String> allowCancelStatus = Arrays.asList(BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus(),
                BiddingStatusEnum.CANCEL_APPROVING.getStatus());
        List<Bidding> waitCancel = biddingList.stream()
                .filter(v -> CollUtil.contains(allowCancelStatus, v.getStatus()))
                .collect(Collectors.toList());

        Date date = DateUtil.offsetSecond(now, reverseRejectFaultToleranceTime);

        //策略->策略驳回
        this.updateBiddingStatusByIds(BiddingStatusEnum.REJECTED_STRATEGY, dto.getAccountId(), waitApproveStrategy, false);
        //成交结果->成交结果已驳回
        this.updateBiddingStatusByIds(BiddingStatusEnum.REJECTED_RESULT, dto.getAccountId(), waitApproveResult, false);

        //撤回策略 驳回至竞价策略已通过
        //超过竞价结束时间
        List<Bidding> beforeEndTimeWithdrawBiddingList = waitWithdrawStrategy.stream()
                .filter(v -> DateUtil.compare(date, v.getLastBiddingEndTime(), DatePattern.NORM_DATETIME_PATTERN) <= 0)
                .collect(Collectors.toList());
        List<Bidding> afterEndTimeWithdrawBiddingList = waitWithdrawStrategy.stream()
                .filter(v -> DateUtil.compare(date, v.getLastBiddingEndTime(), DatePattern.NORM_DATETIME_PATTERN) > 0)
                .collect(Collectors.toList());
        this.updateBiddingStatusByIds(BiddingStatusEnum.APPROVED_STRATEGY, dto.getAccountId(), beforeEndTimeWithdrawBiddingList, false);
        //处理撤回竞价策略审批驳回超时
        this.handleOverBiddingEndTime(dto, date, afterEndTimeWithdrawBiddingList);

        //取消竞价 审批驳回至竞价策略已通过
        //超过竞价结束时间
        List<Bidding> beforeEndTimeCancelBiddingList = waitCancel.stream()
                .filter(v -> DateUtil.compare(date, v.getLastBiddingEndTime(), DatePattern.NORM_DATETIME_PATTERN) <= 0)
                .collect(Collectors.toList());
        List<Bidding> afterEndTimeCancelBiddingList = waitCancel.stream()
                .filter(v -> DateUtil.compare(date, v.getLastBiddingEndTime(), DatePattern.NORM_DATETIME_PATTERN) > 0)
                .collect(Collectors.toList());
        this.updateBiddingStatusByIds(BiddingStatusEnum.APPROVED_STRATEGY, dto.getAccountId(), beforeEndTimeCancelBiddingList, false);
        //处理取消竞价审批驳回超时
        this.handleOverBiddingEndTime(dto, date, afterEndTimeCancelBiddingList);

        //策略 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitApproveStrategy, ApproveTypeEnum.STRATEGY, ApproveResultEnum.REJECTED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //成交结果 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitApproveResult, ApproveTypeEnum.RESULT, ApproveResultEnum.REJECTED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //撤回策略 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitWithdrawStrategy, ApproveTypeEnum.STRATEGY_WITHDRAW, ApproveResultEnum.REJECTED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());
        //取消竞价 审批记录
        iBiddingApproveRecordBiz.saveApproveRecord(waitCancel, ApproveTypeEnum.BIDDING_CANCEL, ApproveResultEnum.REJECTED,
                dto.getReason(), dto.getAccountId(), dto.getRealName());

        //重置买家成交结果
        iBiddingBuyerDealBiz.resetBuyerDealByBiddingList(waitApproveResult);

        //审批拒绝通知 异步处理
        List<Bidding> finalWaitApproveStrategy1 = waitApproveStrategy;
        executor.execute(() -> {
            notifyByStrategyReject(finalWaitApproveStrategy1, dto);
        });
        executor.execute(() -> {
            notifyByResultReject(waitApproveResult, dto);
        });
        executor.execute(() -> this.notifyWithdrawUserByReject(waitWithdrawStrategy, dto));
        executor.execute(() -> this.notifyCancelUserByReject(waitCancel, dto));
    }

    /**
     * @param saleChannelIntentions 销售渠道客户明细
     * @param roundBiddingDetails 指定轮次最新竞价明细
     * @param type 销售渠道
     * @return 指定轮次指定渠道竞技明细统计
     */
    private static BiddingAnalysisSaleChannelDTO getSaleChannelAnalysis(List<MemberIntentionInfoDTO> saleChannelIntentions,
                                                                        List<BiddingAnalysisInfoDTO> roundBiddingDetails,
                                                                        String type) {
        if (CollUtil.isEmpty(saleChannelIntentions) || CollUtil.isEmpty(roundBiddingDetails))
            return null;
        List<BiddingAnalysisInfoDTO> saleChannelRoundBiddingDetails = roundBiddingDetails.stream()
                .filter(v -> saleChannelIntentions.stream()
                        .anyMatch(v1 -> CharSequenceUtil.equals(v.getMemberCode(), v1.getMemberCode())))
                .collect(Collectors.toList());
        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(saleChannelRoundBiddingDetails, BiddingAnalysisInfoDTO::getMemberCode);
        BiddingAnalysisSaleChannelDTO target = new BiddingAnalysisSaleChannelDTO();
        String otherType = "OTHER";
        //没有设置销售渠道则认为是其它类型
        target.setType(ObjectUtil.defaultIfBlank(type, otherType));
        if(CharSequenceUtil.equals(otherType,type)){
            target.setTypeName("其他");
        }else{
            target.setTypeName(SalesChannelEnum.getNameByCode(type));
        }
        target.setCount(CollUtil.size(memberCodes));
        target.setMemberCodes(memberCodes);
        if (CollUtil.isNotEmpty(saleChannelRoundBiddingDetails)) {
            target.setTotalQuantity(saleChannelRoundBiddingDetails.stream()
                    .map(v -> v.getWantQuantity())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return target;
    }
}
