package com.cnoocshell.order.tasks;


import com.cnoocshell.common.dto.WechatSendDTO;
import com.cnoocshell.common.service.IWechatSendService;
import com.cnoocshell.integration.enums.WechatTemplateEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 更新询价开始状态
 */
@Component
@Slf4j
public class WechatScheduler {

    @Autowired
    private IWechatSendService iWechatSendService;

    @XxlJob("sendWechatMsg")
    public void doJob(String no) {
        XxlJobHelper.log("WechatScheduler is start");
        try {
            WechatSendDTO wechatDto = new WechatSendDTO();
            wechatDto.setTemplateCode(WechatTemplateEnum.BIDDING_RESULTS_TEMPLATE.getCode());
            wechatDto.setTemplate_id("OR31MHDcc8TObNI-ROxpB6SCTC89gsemCF3qx4i3nDY");
            wechatDto.setTouser("oRzr36ymrzjWzgthtzzqxeewzN00");
            wechatDto.setPage("/pages/sub/bid/buyer-detail/index?id=75fef3df07d345769bc3fef6a15c41c6&no=A20250312015");
            wechatDto.setData(WechatTemplateEnum.createBiddingResultDataMap("A20250312015", "test竞价", "已成交", "恭喜您！快来查看您的详细结果吧！"));
            iWechatSendService.sendWechat(wechatDto);
            XxlJobHelper.log("WechatScheduler send Success");
        }catch (Exception e) {
            XxlJobHelper.log("WechatScheduler is error:{}", e);
        }
        XxlJobHelper.log("WechatScheduler is end");
    }

}
