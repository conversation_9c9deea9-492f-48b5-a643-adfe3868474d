package com.cnoocshell.integration.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.socket.ChannelUtil;
import com.cnoocshell.integration.biz.sms.IMessageHistoryEmailBiz;
import com.cnoocshell.integration.dao.vo.MessageHistoryEmail;
import com.cnoocshell.integration.dto.email.EmailSendDTO;
import com.cnoocshell.integration.enums.EmailSendStatusEnum;
import com.cnoocshell.integration.service.IEmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: <EMAIL>
 * @Date: 12/04/2019 18:15
 * @DESCRIPTION:
 */
@Slf4j
@Service
public class EmailSendServiceImpl implements IEmailSendService {
    @Autowired
    private JavaMailSender mailSender;

    //邮件发件人地址
    @Value("${mail.from.emailAddress:}")
    private String from;
    //邮件发件人名称
    @Value("${mail.from.userName}")
    private String fromName;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    @Qualifier("myTaskAsyncPool")
    private Executor executor;
    @Autowired
    private IMessageHistoryEmailBiz iMessageHistoryEmailBiz;

    @Override
    public Boolean sendEmailCode(EmailSendDTO dto) {
        //处理模板参数中的空数据为字符串
        this.handParam(dto);

        executor.execute(()->{
            MessageHistoryEmail emailHis = new MessageHistoryEmail();
            emailHis.setReceiverEmail(CollUtil.join(dto.getTos(),","));
            emailHis.setCcEmail(CollUtil.join(dto.getToCcs(),","));
            emailHis.setSendStatus(EmailSendStatusEnum.INIT.getStatus());
            emailHis.setMessageHistoryMqId("");
            emailHis.setSendParam(JSONUtil.toJsonStr(dto.getTemplateParam()));
            try {
                log.info("======> send email code : {}",dto.getEmailTemplate().getCode());
                Map<String, Object> templateParam = dto.getTemplateParam();
                Context context = new Context();
                // 将templateParam中的数据放入Context
                for (Map.Entry<String, Object> entry : templateParam.entrySet()) {
                    context.setVariable(entry.getKey(), ObjectUtil.defaultIfNull(entry.getValue(),""));
                }
                //将模块引擎内容解析成html字符串
                String content = templateEngine.process(dto.getEmailTemplate().getCode(), context);
                emailHis.setConfigId(dto.getEmailTemplate().getCode());
                emailHis.setSendMessage(content);

                //复杂的邮件
                MimeMessage message = mailSender.createMimeMessage();
                //true表示需要创建一个multipart message
                MimeMessageHelper helper = new MimeMessageHelper(message, true,"utf-8");

                String nick="";
                try {
                    nick= MimeUtility.encodeText(fromName);
                } catch (UnsupportedEncodingException e) {
                }

                helper.setFrom(new InternetAddress(from,nick));
                //设置发件人
                List<String> toList = dto.getTos();
                List<String> toCcList = dto.getToCcs();

                if (CollUtil.isNotEmpty(toList))
                    helper.setTo(toList.stream().filter(CharSequenceUtil::isNotBlank).toArray(String[]::new));
                if(CollUtil.isNotEmpty(toCcList))
                    helper.setCc(toCcList.stream().filter(CharSequenceUtil::isNotBlank).toArray(String[]::new));
                String title = dto.getEmailTemplate().getTitle();
                title = replacePlaceholders(title, templateParam);
                helper.setSubject(title);
                helper.setText(content, true);

                Map<String, File> atts = dto.getAttachments();
                if (atts != null && !atts.isEmpty()) {
                    atts.forEach((key, value) -> {
                        try {
                            helper.addAttachment(key, value);
                        } catch (MessagingException ignore) { // 忽略此异常
                            log.warn("邮件附件(" + value.getAbsolutePath() + ")设置异常", ignore);
                        }
                    });
                }

                emailHis.setSendTime(new Date());
                mailSender.send(message);
                log.info("html邮件发送成功,to:{},cc:{},title:{},content:{}",toList,toCcList,title,content);
                emailHis.setSendResponseStatus(200);
                emailHis.setSendStatus(EmailSendStatusEnum.SUCCESS.getStatus());
            } catch (Exception e) {
                log.error("EmailTemplate code:{}, EmailTemplate title:{}",dto.getEmailTemplate().getCode(),
                        dto.getEmailTemplate().getTitle());
                log.error("发送html邮件时发生异常: ", e);
                emailHis.setSendStatus(EmailSendStatusEnum.FAIL.getStatus());
                emailHis.setSendException(ExceptionUtil.stacktraceToString(e,1024));
            }finally {
                emailHis.setCreateTime(new Date());
                emailHis.setUpdateTime(new Date());
                iMessageHistoryEmailBiz.save(emailHis);
            }
        });
        log.info("invoke success");
        return true;
    }

    /**
     * 将字符串中的 #{key} 替换成 map 中对应的 value。
     *
     * @param inputStr 含有 #{key} 占位符的字符串
     * @param paramMap 用于替换的键值对映射
     * @return 替换后的字符串
     */
    public static String replacePlaceholders(String inputStr, Map<String, Object> paramMap) {
        Pattern pattern = Pattern.compile("#\\{([^}]*)}");
        Matcher matcher = pattern.matcher(inputStr);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            Object replacement = paramMap.getOrDefault(key, matcher.group());
            matcher.appendReplacement(sb, String.valueOf(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private void handParam(EmailSendDTO param){
        if(Objects.nonNull(param) && Objects.nonNull(param.getTemplateParam())){
            Map<String,Object> newParam = new HashMap<>(CollUtil.size(param.getTemplateParam()));
            param.getTemplateParam().forEach((k,v)->{
                Object newValue = ObjectUtil.defaultIfNull(v,"");
                newParam.put(k,newValue);
            });
            param.setTemplateParam(newParam);
        }
    }
}
