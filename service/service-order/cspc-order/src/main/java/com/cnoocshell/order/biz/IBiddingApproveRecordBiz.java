package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.api.dto.bidding.BiddingApproveListDTO;
import com.cnoocshell.order.api.enums.ApproveResultEnum;
import com.cnoocshell.order.api.enums.ApproveTypeEnum;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingApproveRecord;

import java.util.List;

public interface IBiddingApproveRecordBiz extends IBaseBiz<BiddingApproveRecord> {
    void saveApproveRecord(List<Bidding> biddingList, ApproveTypeEnum approveType, ApproveResultEnum approveResult,
                           String reason, String operatorId, String operatorName);

    List<BiddingApproveRecord> listByBiddingNo(String biddingNo);

    List<BiddingApproveListDTO> queryApproveList(String biddingId, String status);
}
