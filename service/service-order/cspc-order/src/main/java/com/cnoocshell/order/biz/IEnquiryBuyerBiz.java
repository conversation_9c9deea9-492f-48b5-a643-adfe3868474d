package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;

import java.util.List;

public interface IEnquiryBuyerBiz extends IBaseBiz<EnquiryBuyer> {
    /**
     * 根据询价对象获取买家询价列表。
     *
     * @param enquiry 询价对象，包含询价编号等信息
     * @return 买家询价列表，若传入的询价对象为空，则返回 null
     */
    List<EnquiryBuyer> listByNo(Enquiry enquiry);

    /**
     * 根据询价编号获取买家询价列表。
     *
     * @param enquiryNo 询价编号
     * @return 买家询价列表，若询价编号为空或空白字符串，则返回 null
     */
    List<EnquiryBuyer> listByNo(String enquiryNo);
}
