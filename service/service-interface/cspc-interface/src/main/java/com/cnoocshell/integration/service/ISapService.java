package com.cnoocshell.integration.service;

import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractDTO;

public interface ISapService {

    /**
     *创建SAP合同
     * @param param SAP参数
     * @param messageId
     * @param isAsync 是否异步通知创建结果
     */
    BiddingDealContractDTO createSapContract(SapContractDTO param, String messageId, boolean isAsync);

    String getSapToken();
}
