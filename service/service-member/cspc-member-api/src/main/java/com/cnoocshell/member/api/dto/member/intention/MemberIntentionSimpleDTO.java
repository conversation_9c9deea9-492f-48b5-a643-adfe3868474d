package com.cnoocshell.member.api.dto.member.intention;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberIntentionSimpleDTO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("memberId")
    private String memberId;

    @ApiModelProperty("memberCode")
    private String memberCode;

    @ApiModelProperty("意向类型 1:主要意向 2:次要意向")
    private Integer intentionType;

    @ApiModelProperty("商品类别ID")
    private String goodsCategoryId;

    @ApiModelProperty("商品ID")
    private String goodsId;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("销售渠道(字典)")
    private String saleChannel;

    @ApiModelProperty(value = "商品编码+会员编码",notes = "goodsCode_memberCode")
    private String goodsCodeConcatMemberCode;

}
