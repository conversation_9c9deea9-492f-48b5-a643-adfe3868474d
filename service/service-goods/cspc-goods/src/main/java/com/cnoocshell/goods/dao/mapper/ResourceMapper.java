package com.cnoocshell.goods.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.goods.api.dto.ReqPromptOnsaleResourceDTO;
import com.cnoocshell.goods.api.dto.ReqResourceSellerDTO;
import com.cnoocshell.goods.dao.vo.Resource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ResourceMapper extends IBaseMapper<Resource> {
    Resource selectForUpdate(String resourceId);

    /**
     * 分页查询挂牌列表信息（数据权限全表销售区域）
     * @param resource
     * @return
     */
    List<Resource> pageQueryWithDataPerm(@Param("resource") ReqResourceSellerDTO resource);

    /**
     * 分页查询挂牌列表条数（数据权限全表销售区域）
     * @param resource
     * @return
     */
    int countQueryWithDataPerm(@Param("resource") ReqResourceSellerDTO resource);

    /**
     * 分页查询已挂牌列表信息（数据权限全表销售区域）
     * @param resource
     * @return
     */
    List<Resource> promptOnsaleResourceWithDataPerm(@Param("resource") ReqPromptOnsaleResourceDTO resource);

    /**
     * @return 符合条件的资源列表
     */
    List<Resource> selectPutOnAndOffResourceList();

}