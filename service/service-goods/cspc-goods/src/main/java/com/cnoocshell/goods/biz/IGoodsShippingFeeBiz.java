package com.cnoocshell.goods.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.GoodsShippingFeePagOfBuyerReqDTO;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.GoodsShippingFeePageReqDTO;
import com.cnoocshell.goods.dao.vo.GoodsShippingFee;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IGoodsShippingFeeBiz extends IBaseBiz<GoodsShippingFee> {

    PageInfo<GoodsShippingFee> findAllByCondiftion(GoodsShippingFeePageReqDTO requestDTO);

    PageInfo<GoodsShippingFee> findAllByConditionOfBuyer(GoodsShippingFeePagOfBuyerReqDTO requestDTO, List<String> goodsCodeList);
}
