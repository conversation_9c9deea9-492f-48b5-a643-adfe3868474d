package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerDealDTO;
import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingDealQuantityDTO;
import com.cnoocshell.order.dao.vo.BiddingBuyerDeal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BiddingBuyerDealMapper extends IBaseMapper<BiddingBuyerDeal> {
    List<BiddingDealQuantityDTO> getBiddingDealTotalQuantity(@Param("biddingNos") List<String> biddingNos);

    List<BiddingBuyerDealDTO> selectMemberBiddingDealDataByMemberCodes(List<String> memberCodes, String startDate, String endDate);

    int updateBiddingDealContractNo(@Param("param") BiddingDealContractDTO param);

}
