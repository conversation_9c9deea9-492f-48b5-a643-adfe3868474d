package com.cnoocshell.goods.cache;

import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.common.service.RedisService;
import com.cnoocshell.goods.api.dto.CategoryAccountRelationDTO;
import com.cnoocshell.goods.api.dto.CategoryTreeDTO;
import com.cnoocshell.goods.api.dto.GoodsDTO;
import com.cnoocshell.goods.biz.IGoodCategoryRelationBiz;
import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.dao.mapper.GoodsCategoryMapper;
import com.cnoocshell.goods.dao.vo.GoCategoryAccountRelation;
import com.cnoocshell.goods.dao.vo.GoodCategoryRelation;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.GoodsCategory;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryTreeCacheService implements ICategoryTreeCacheService {

    private static final String CATEGORY_TREE_CACHE_NAME = "category_tree_cache";
    private static final String DEL_FLG = "delFlg";
    private static final String PARENT_ID = "parentId";
    private static final String CATEGORY_CODE = "categoryCode";

    private final RedisService redisService;

    @Resource
    private GoodsCategoryMapper categoryMapper;

    private final IGoodCategoryRelationBiz goodCategoryRelationBiz;

    private final IGoodsBiz iGoodsBiz;


    @Async
    @Override
    public void setCategoryTreeCache() {
        CategoryTreeCache cache = new CategoryTreeCache();
        cache.setKey(CATEGORY_TREE_CACHE_NAME);
        List<CategoryTreeDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        criteria.andIsNull(PARENT_ID);
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> fathers = categoryMapper.selectByCondition(condition);
        if(CollectionUtils.isNotEmpty(fathers)){
            fathers.forEach(father ->{
                CategoryTreeDTO dto = new CategoryTreeDTO();
                BeanUtils.copyProperties(father,dto);
                dto.setIfSpareParts("备件".equals(father.getCategoryName()));
                childBuild(father,dto, false,null, null);
                list.add(dto);
            });
        }
        cache.setList(list);
        redisService.del(CATEGORY_TREE_CACHE_NAME);
        redisService.set(CATEGORY_TREE_CACHE_NAME, cache);
    }

    /**
     * 构建子分类
     */
    public void childBuild(GoodsCategory father,CategoryTreeDTO dto, boolean isGoods,
                           Map<String, List<GoodCategoryRelation>> goodCategoryRelationMap,
                           Map<String, List<Goods>> goodsMap){
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        criteria.andEqualTo(PARENT_ID,father.getCategoryId());
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> childCategories = categoryMapper.selectByCondition(condition);
        List<CategoryTreeDTO> childs = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(childCategories)){
            childCategories.forEach(child ->{
                CategoryTreeDTO childDto = new CategoryTreeDTO();
                BeanUtils.copyProperties(child,childDto);
                childDto.setParentName(father.getCategoryName());
                dto.setIfSpareParts("备件".equals(father.getCategoryName()));
                childs.add(childDto);
                childBuild(child,childDto, isGoods, goodCategoryRelationMap, goodsMap);
            });
            dto.setChilds(childs);
        }
        if (isGoods) {
            setGoods(dto, goodCategoryRelationMap, goodsMap, null);
        }
    }

    /**
     * 构建子分类
     */
    public void childBuildByRole(GoodsCategory father,CategoryTreeDTO dto, boolean isGoods,
                           Map<String, List<GoodCategoryRelation>> goodCategoryRelationMap,
                           Map<String, List<Goods>> goodsMap,
                           List<String> categoryIds,
                           List<String> goodsCodes){
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        criteria.andEqualTo(PARENT_ID,father.getCategoryId());
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> childCategories = categoryMapper.selectByCondition(condition);
        List<CategoryTreeDTO> childs = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(childCategories)){
            childCategories.forEach(child ->{
                CategoryTreeDTO childDto = new CategoryTreeDTO();
                BeanUtils.copyProperties(child,childDto);
                childDto.setParentName(father.getCategoryName());
                dto.setIfSpareParts("备件".equals(father.getCategoryName()));
                childs.add(childDto);
                childBuildByRole(child,childDto, isGoods, goodCategoryRelationMap, goodsMap, categoryIds, goodsCodes);
            });
            dto.setChilds(childs);
        }
        if (categoryIds.contains(father.getCategoryId())) {
            setGoods(dto, goodCategoryRelationMap, goodsMap, goodsCodes);
        }
    }

    @Override
    public CategoryTreeCache getCategoryTreeCache(boolean isGoods) {
        log.info("缓存数据为空,重新缓存并返回!");
        CategoryTreeCache newCache = new CategoryTreeCache();
        newCache.setKey(CATEGORY_TREE_CACHE_NAME);
        List<CategoryTreeDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        criteria.andIsNull(PARENT_ID);
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> fathers = categoryMapper.selectByCondition(condition);

        Condition newCondition = goodCategoryRelationBiz.newCondition();
        List<GoodCategoryRelation> byCondition = goodCategoryRelationBiz.findByCondition(newCondition);
        List<String> goodsIds = byCondition.stream().map(GoodCategoryRelation::getGoodId).collect(Collectors.toList());
        Map<String, List<GoodCategoryRelation>> goodCategoryRelationMap = byCondition.stream().collect(Collectors.groupingBy(GoodCategoryRelation::getCategoryId));

        Condition goodsCondition = iGoodsBiz.newCondition();
        if (CollectionUtils.isNotEmpty(goodsIds)) {
            goodsCondition.createCriteria().andIn("goodsId", goodsIds);
        }
        goodsCondition.createCriteria().andEqualTo("goodsStatus", "2")
                .andEqualTo(DEL_FLG, Boolean.FALSE);
        List<Goods> goodsList = iGoodsBiz.findByCondition(goodsCondition);
        Map<String, List<Goods>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(Goods::getGoodsId));

        if(CollectionUtils.isNotEmpty(fathers)){
            fathers.forEach(father ->{
                CategoryTreeDTO dto = new CategoryTreeDTO();
                BeanUtils.copyProperties(father,dto);
                dto.setIfSpareParts("备件".equals(father.getCategoryName()));
                childBuild(father,dto, isGoods, goodCategoryRelationMap, goodsMap);
                list.add(dto);
            });
        }
        newCache.setList(list);
        redisService.set(CATEGORY_TREE_CACHE_NAME, newCache);
        return newCache;
    }

    @Override
    public CategoryTreeCache getCategoryTreeGoodsByRole(List<String> categoryIds, List<String> goodsCodes, boolean isGoods) {
        CategoryTreeCache newCache = new CategoryTreeCache();
        newCache.setKey(CATEGORY_TREE_CACHE_NAME);
        List<CategoryTreeDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        criteria.andIsNull(PARENT_ID);
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> fathers = categoryMapper.selectByCondition(condition);

        Condition newCondition = goodCategoryRelationBiz.newCondition();
        List<GoodCategoryRelation> byCondition = goodCategoryRelationBiz.findByCondition(newCondition);
        List<String> goodsIds = byCondition.stream().map(GoodCategoryRelation::getGoodId).collect(Collectors.toList());
        Map<String, List<GoodCategoryRelation>> goodCategoryRelationMap = byCondition.stream().collect(Collectors.groupingBy(GoodCategoryRelation::getCategoryId));

        Condition goodsCondition = iGoodsBiz.newCondition();
        Example.Criteria conditionCriteria = goodsCondition.createCriteria();
        if (CollectionUtils.isNotEmpty(goodsIds)) {
            conditionCriteria.andIn("goodsId", goodsIds);
        }
        conditionCriteria.andEqualTo("goodsStatus", "2")
                .andEqualTo(DEL_FLG, Boolean.FALSE);
        List<Goods> goodsList = iGoodsBiz.findByCondition(goodsCondition);
        Map<String, List<Goods>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(Goods::getGoodsId));

        if(CollectionUtils.isNotEmpty(fathers)){
            fathers.forEach(father ->{
                CategoryTreeDTO dto = new CategoryTreeDTO();
                BeanUtils.copyProperties(father,dto);
                dto.setIfSpareParts("备件".equals(father.getCategoryName()));
                childBuildByRole(father,dto, isGoods, goodCategoryRelationMap, goodsMap, categoryIds, goodsCodes);
                list.add(dto);
            });
        }
        newCache.setList(list);
        return newCache;
    }

    public void setGoods(CategoryTreeDTO dto, Map<String,
                         List<GoodCategoryRelation>> goodCategoryRelationMap,
                         Map<String, List<Goods>> goodsMap,
                         List<String> goodsCodes) {
        List<GoodCategoryRelation> goodCategoryRelations = goodCategoryRelationMap.get(dto.getCategoryId());
        if (CollectionUtils.isEmpty(goodCategoryRelations)) {
            return;
        }
        List<GoodCategoryRelation> goodCategoryRelationList = goodCategoryRelationMap.get(dto.getCategoryId());
        if (CollectionUtils.isEmpty(goodCategoryRelationList)) {
            return;
        }
        List<GoodsDTO> goodsDTOList = new ArrayList<>();
        for (GoodCategoryRelation goodCategoryRelation1 : goodCategoryRelationList) {
            List<Goods> goodsList = goodsMap.get(goodCategoryRelation1.getGoodId());
            if (CollectionUtils.isEmpty(goodsList)) {
                continue;
            }

            for (Goods goods : goodsList) {
                if (CollectionUtils.isNotEmpty(goodsCodes) && !goodsCodes.contains(goods.getGoodsCode())) {
                    continue;
                }
                GoodsDTO goodsDTO = new GoodsDTO();
                BeanUtils.copyProperties(goods, goodsDTO);
                goodsDTO.setCategoryCode(dto.getCategoryCode());
                goodsDTO.setCategoryName(dto.getCategoryName());
                goodsDTOList.add(goodsDTO);
            }

//            List<GoodsDTO> collect = goodsList.stream().map(g -> {
//                GoodsDTO goodsDTO = new GoodsDTO();
//                BeanUtils.copyProperties(g, goodsDTO);
//                goodsDTO.setCategoryCode(dto.getCategoryCode());
//                goodsDTO.setCategoryName(dto.getCategoryName());
//                return goodsDTO;
//            }).collect(Collectors.toList());
//            goodsDTOList.addAll(collect);
        }
        dto.setGoodsDTOS(goodsDTOList);
    }

}
