package com.cnoocshell.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.security.SecureRandom;

/**
 * 图片处理工具类
 * Created by bbt on 2016/11/2.
 */
public class CheckCodeUtils {

    private static final SecureRandom RANDOM = new SecureRandom();

    private CheckCodeUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取随机字符串
     * @param length 长度
     * @return
     */
    public static String getRandomString(int length) {
        //去除易混淆字符的常见字母数字字符
        String[] beforeShuffle = new String[] { "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E",
                "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
                "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "n", "p", "q", "r", "s", "t", "u", "v", "w",
                "x", "y", "z" };
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(beforeShuffle[RANDOM.nextInt(beforeShuffle.length)]);
        }
        return sb.toString();
    }

    /**
     * 获得一个随机数
     * @param length 长度
     * @return
     */
    public static long getRandomNumber(int length){
        return (long)(RANDOM.nextDouble() * Math.pow(10, length));
    }

    /**
     * 验证码图片生成
     * @param randomCode
     * @param width
     * @param height
     * @return BufferedImage
     */
    public static BufferedImage getCheckCodeImage(String randomCode, int width, int height) {
        if (StringUtils.isBlank(randomCode)) {
            randomCode = getRandomString(4);
        }
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 创建新图
        Graphics2D graphics2d = image.createGraphics();
        // 设置“抗锯齿”的属性
        graphics2d.setStroke(new BasicStroke(3.5f, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND));
        graphics2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics2d.drawImage(image.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
        graphics2d.setColor(getRandColor(160, 220));
        graphics2d.fillRect(0, 0, width, height);
        graphics2d.setFont(new Font("Microsoft YaHei", Font.PLAIN, 32));
        Color fontColor = getRandColor(20, 80);
        int fontWidth = (width / randomCode.length()) - 1;
        int fontHeight = height / 2 + 10;
        for (int i = 0;i < randomCode.length();i++) {
            graphics2d.setColor(fontColor);
            graphics2d.drawString(randomCode.charAt(i) + "", fontWidth * i + 5, fontHeight);
        }
        graphics2d.dispose();
        return image;
    }

    /**
     * 随机获得颜色，RGB为fc到bc之间的整数
     * @param fc 范围开始值0~255
     * @param bc 范围结束值0~255
     * @return Color
     */
    public static Color getRandColor(int fc, int bc) {
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + RANDOM.nextInt(bc - fc);
        int g = fc + RANDOM.nextInt(bc - fc);
        int b = fc + RANDOM.nextInt(bc - fc);
        return new Color(r, g, b);
    }

}