package com.cnoocshell.integration.dto.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

@Data
@AllArgsConstructor
public class WechatSendRequest {

    @ApiModelProperty("微信端用户唯一识别号")
    private String openId;

    @ApiModelProperty("消息模板ID")
    private String templateId;

    /**
     * 小程序跳转页面路径
     */
    @ApiModelProperty("小程序跳转页面路径")
    private String pagePath;

    /**
     * 消息体参数
     */
    @ApiModelProperty("消息体参数")
    private Map<String, String> params;
}
