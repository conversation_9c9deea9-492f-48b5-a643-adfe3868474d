package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AccountLoginInfoDTO {
    @ApiModelProperty("会话id")
    private String sessionId;

    @ApiModelProperty("账户id")
    private String accountId;

    @ApiModelProperty("登录id")
    private String loginName;

    @ApiModelProperty("登录类型")
    private String loginType;

    @ApiModelProperty("登录时间")
    private Date loginTime;

    @ApiModelProperty("登录结果")
    private String loginComment;

    @ApiModelProperty("登出时间")
    private Date logoutTime;

    @ApiModelProperty("登出结果")
    private String logoutComment;

    @ApiModelProperty("登录ip")
    private String ip;

    @ApiModelProperty("登录mac")
    private String mac;

    @ApiModelProperty("登录终端")
    private String terminal;

    @ApiModelProperty("终端系统")
    private String os;

    @ApiModelProperty("终端系统版本")
    private String osVersion;

    @ApiModelProperty("设备品牌")
    private String deviceBrand;

    @ApiModelProperty("国际移动设备识别码")
    private String imei;

    @ApiModelProperty("d")
    private String driverToken;

    @ApiModelProperty("浏览器信息")
    private String userAgent;
}
