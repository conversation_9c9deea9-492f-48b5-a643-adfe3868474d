package com.cnoocshell.order.service;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.BiddingReverseDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.RemoveBiddingDTO;
import com.cnoocshell.order.dao.vo.Bidding;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

public interface IBiddingService {

    /**
     * 买家报量
     */
    Boolean joinBiddingByBuyer(BiddingBuyerDetailDTO param);

    ExportExcelDTO exportAdjustRecord(String biddingNo);


    /**
     *
     */
    ExportExcelDTO exportBiddingBuyerDetail(String biddingNo);

    ItemResult<Boolean> biddingAdjust(BiddingAdjustDTO param);

    ItemResult<Boolean> biddingNotDeal(BiddingDealConfirmDTO param);

    ItemResult<Boolean> biddingDeal(BiddingDealConfirmDTO param);

    ItemResult<Boolean> withdrawDealResult(WithdrawDealResultDTO param);



    ItemResult<BiddingDetailDTO> biddingDetail(QueryBiddingDetailDTO param);

    ItemResult<Boolean> createSapContractBySeller(String dealNo);


    /**
     *支持多个撤回场景（竞价策略待提交、竞价策略已驳回） 撤回到草稿
     * @param biddingNo 竞价单号
     * @param operatorId 操作人ID
     * @return true:撤回成功
     */
    Boolean biddingStrategyWithdrawDraft(String biddingNo,String operatorId);


    /**
     *竞价策略审批通过 撤回竞价策略
     * @param param 竞价单号 撤回原因
     */
    void withDrawStrategy(BiddingReverseDTO param);

    /**
     *竞价策略审批通过 取消竞价场次
     * @param param 竞价单号 取消原因
     */
    void cancelBidding(BiddingReverseDTO param);

    /**
     * 删除草稿
     * @param param 删除参数
     */
    void removeDraft(RemoveBiddingDTO param);

    /**
     * 周经营会汇报报表
     * 竞价报表
     * 限制只能查询当前月往前一年（12个月）内的数据，含当前月
     *
     * @param param 周经营会汇报报表参数
     * @return 周经营会汇报报表结果
     */
    PageInfo<BiddingReportResultDTO> biddingReport(BiddingReportQueryDTO param);


    /**
     * 活跃度报表
     *
     * @param param 查询条件
     * @return 活跃报表结果
     */
    PageInfo<MemberActivityReportResultDTO> memberActivityReport(MemberActivityReportQueryDTO param);
}
