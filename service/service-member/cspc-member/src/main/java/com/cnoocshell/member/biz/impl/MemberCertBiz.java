package com.cnoocshell.member.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.api.service.IAttachmentService;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberCertAttachmentDTO;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.MemberCertTypeEnum;
import com.cnoocshell.member.biz.IMemberCertBiz;
import com.cnoocshell.member.dao.mapper.MemberCertMapper;
import com.cnoocshell.member.dao.vo.MemberCert;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberCertBiz extends BaseBiz<MemberCert> implements IMemberCertBiz {

    private final MemberCertMapper certMapper;
    private final IAttachmentService iAttachmentService;
    private final UUIDGenerator uuidGenerator;

    @Override
    public void setMemberCertAttachment(String attachmentId, MemberCertDTO memberCertDTO) {
        try {
            log.info("memberId:{},cert_type:{},cert_id:{},attachmentId:{}.", memberCertDTO.getMemberId(), memberCertDTO.getCertType(), memberCertDTO.getCertId(), attachmentId);
            if (StringUtils.isBlank(attachmentId)) {
                return;
            }
            List<AttachmentinfoDTO> attachments = iAttachmentService.getByIds(attachmentId);
            if (!CollectionUtils.isEmpty(attachments)) {
                log.info("查询证件信息attachments:{}", attachments);
                List<MemberCertAttachmentDTO> collect = attachments.stream()
                        .map(item -> getMemberCertAttachmentDTO(item) ).collect(Collectors.toList());
                memberCertDTO.setMemberCertAttachmentDTOS(collect);
            } else {
                log.error("根据附件id:" + attachmentId + "没有找到附件信息");
            }
        } catch (Exception e) {
            log.error("根据附件id:" + attachmentId + "获取附件信息错误：" + e.getMessage(), e);
        }
    }
    @Override
    public List<MemberCertDTO> findByMemberId(String memberId) {
        MemberCert cert = new MemberCert();
        cert.setMemberId(memberId);
        cert.setDelFlg(Boolean.FALSE);
        List<MemberCert> select = certMapper.select(cert);
        return cert2CertDTO(select);
    }

    @Override
    public List<MemberCertDTO> cert2CertDTO(List<MemberCert> list) {
        return list.stream()
                .map(item -> {
                    MemberCertDTO dto = new MemberCertDTO();
                    BeanUtils.copyProperties(item, dto);
                    setMemberCertAttachment(item.getAttachmentId(), dto);
                    return dto;
                }).collect(Collectors.toList());
    }

    @Override
    public void saveCertDTOList(List<MemberCertDTO> memberCertDTOList, String memberId, String accountId, String operatorId) {
        if (!CollectionUtils.isEmpty(memberCertDTOList)) {
            for (MemberCertDTO memberCertDTO : memberCertDTOList) {
                checkCertType(memberCertDTO);
                //如果是道路运输许可证并且0-不需要上传图片，则跳过，不做处理
                if (StringUtils.equals(memberCertDTO.getCertType(), MemberCertTypeEnum.ROAD_TRANSPORT_LICENSE.getCode()) && memberCertDTO.getNeedCert() == 0) {
                    continue;
                }

                MemberCert cert = new MemberCert();
                BeanUtils.copyProperties(memberCertDTO, cert);
                cert.setStatus(AdvertStatusEnum.APPROVED.getCode());
                if (cert.getCertType().equals(MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
                    cert.setAccountId(accountId);
                }
                cert.setMemberId(memberId);
                setOperInfo(cert, operatorId, true);

                updateByConditionSelective(memberId, cert);
                if (StringUtils.isBlank(cert.getCertId())) {
                    cert.setCertId(uuidGenerator.gain());
                }
                certMapper.insertSelective(cert);
            }
        }
    }

    @Override
    public void signDeleteSameCertType(MemberCert cert, String memberId) {
        Condition condition = new Condition(MemberCert.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.CERT_TYPE, cert.getCertType())
                .andEqualTo(DuplicateString.MEMBER_ID, memberId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        MemberCert t = new MemberCert();
        t.setDelFlg(Boolean.TRUE);
        certMapper.updateByConditionSelective(t, condition);
    }

    @Override
    public List<MemberCert> findByMemberId(String memberId, List<MemberCertTypeEnum> certType) {
        Condition condition = new Condition(MemberCert.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.MEMBER_ID,memberId);
        if(CollUtil.isNotEmpty(certType)){
            criteria.andIn(DuplicateString.CERT_TYPE,certType.stream().map(v->v.getCode()).collect(Collectors.toList()));
        }
        return this.findByCondition(condition);
    }

    @Override
    public void handleMemberCert(List<MemberCertDTO> certs) {
        if(CollUtil.isEmpty(certs))
            return;
        List<String> attachmentIds = new ArrayList<>();
        for (MemberCertDTO cert : certs) {
            if(CharSequenceUtil.isNotBlank(cert.getAttachmentId()))
                attachmentIds.addAll(CharSequenceUtil.split(cert.getAttachmentId(),","));
        }
        if(CollUtil.isEmpty(certs))
            return;
        List<AttachmentinfoDTO> attachments = iAttachmentService.listByIds(attachmentIds);
        if(CollUtil.isEmpty(attachments))
            return;

        Map<String,MemberCertAttachmentDTO> certAttachmentMap = CommonUtils.getMap(BeanUtil.copyToList(attachments,MemberCertAttachmentDTO.class),MemberCertAttachmentDTO::getAttachmentId);
        for (MemberCertDTO v : certs) {
            //处理身份证信息
            if(MemberCertTypeEnum.IDENTITY_LICENSE.getCode().equals(v.getCertType())){
                List<MemberCertAttachmentDTO> idInfos = new ArrayList<>(2);
                List<String> idInfoAttachmentIds = CharSequenceUtil.split(v.getAttachmentId(),",");
                //正面
                MemberCertAttachmentDTO index1 = CommonUtils.getByKey(certAttachmentMap,CollUtil.getFirst(idInfoAttachmentIds));
                //反面
                MemberCertAttachmentDTO index2 = CommonUtils.getByKey(certAttachmentMap,CollUtil.get(idInfoAttachmentIds,1));
                idInfos.add(index1);
                idInfos.add(index2);
                v.setMemberCertAttachmentDTOS(idInfos);
            }else {
                //其它资质信息
                if(CharSequenceUtil.isBlank(v.getAttachmentId()))
                    continue;
                v.setMemberCertAttachmentDTOS(CommonUtils.getListByKey(certAttachmentMap, CharSequenceUtil.split(v.getAttachmentId(), ",")));
            }
        }
    }

    private void updateByConditionSelective(String memberId, MemberCert cert) {
        Condition condition = new Condition(MemberCert.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.CERT_TYPE, cert.getCertType());
        criteria.andEqualTo(DuplicateString.MEMBER_ID, memberId);
        criteria.andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        int count = certMapper.selectCountByCondition(condition);
        if (count > 0) {
            MemberCert c = new MemberCert();
            c.setDelFlg(Boolean.TRUE);
            certMapper.updateByConditionSelective(c, condition);
        }
    }

    private static void checkCertType(MemberCertDTO memberCertDTO) {
        if (StringUtils.isEmpty(memberCertDTO.getCertType())) {
            throw new BizException(MemberCode.UNKNOWN_CERTIFICATES, memberCertDTO.getCertName());
        }
        if (StringUtils.isEmpty(memberCertDTO.getAttachmentId())) {
            throw new BizException(MemberCode.IMAGE_IS_EMPTY);
        }
    }

    private static MemberCertAttachmentDTO getMemberCertAttachmentDTO(AttachmentinfoDTO item) {
        MemberCertAttachmentDTO memberCertAttachmentDTO = new MemberCertAttachmentDTO();
        memberCertAttachmentDTO.setAttachmentId(item.getAttachmentId());
        memberCertAttachmentDTO.setAttcName(item.getAttcName());
        memberCertAttachmentDTO.setAttcSize(item.getAttcSize());
        memberCertAttachmentDTO.setAttcPath(item.getAttcPath());
        memberCertAttachmentDTO.setAttcType(item.getAttcType());
        return memberCertAttachmentDTO;
    }

}
