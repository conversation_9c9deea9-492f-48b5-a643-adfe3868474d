package com.cnoocshell.member.api.dto.report;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MemberAccountReportResultDTO {
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty(value = "客户编码")
    @Excel(index = 1, title = "客户编码")
    private String crmCode;
    @ApiModelProperty(value = "会员编码")
    private String memberCode;
    @ApiModelProperty(value = "客户名称")
    @Excel(index = 5, title = "客户名称")
    private String memberName;

    @ApiModelProperty(value = "统一社会信用代码")
    @Excel(index = 10, title = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "客户状态",notes = "1:禁用 0：启用")
    private Integer ableStatus;
    @ApiModelProperty(value = "客户状态显示名称",notes = "1:禁用 0：启用")
    @Excel(index = 15, title = "客户状态")
    private String ableStatusName;


    @ApiModelProperty(value = "账号ID")
    private String accountId;
    /**
     *{@link com.cnoocshell.member.api.dto.member.enums.AccountTypeEnum}
     */
    @ApiModelProperty(value = "账号类型")
    private Integer accountType;
    @ApiModelProperty(value = "账号类型名称")
    @Excel(index = 20, title = "账号类别")
    private String accountTypeName;

    @ApiModelProperty(value = "账号状态",notes = "账号状态（ 0 可用 1 禁用 ）")
    private Boolean status;
    @ApiModelProperty(value = "账号状态")
    @Excel(index = 25, title = "账号状态")
    private String accountStatusName;

    @ApiModelProperty(value = "姓名")
    @Excel(index = 30, title = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    @Excel(index = 35, title = "手机号")
    private String mobile;

    @ApiModelProperty(value = "角色编码")
    private List<String> roleCodes;

    @ApiModelProperty(value = "角色名称",notes = "多个用逗号隔开")
    @Excel(index = 40, title = "角色")
    private String roleName;

    @ApiModelProperty(value = "产品一级分类ID", notes = "ID")
    private String categoryIdLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "编码")
    private String categoryCodeLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "名称")
    @Excel(index = 45, title = "负责产品一级分类")
    private String categoryNameLevelOne;

    @ApiModelProperty(value = "产品二级分类ID", notes = "ID")
    private String categoryIdLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "名称")
    @Excel(index = 50, title = "负责产品二级分类")
    private String categoryNameLevelTwo;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;

    @ApiModelProperty(value = "意向类型")
    private Integer intentionType;

    @ApiModelProperty(value = "产品名称")
    @Excel(index = 55, title = "负责产品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码")
    @Excel(index = 60, title = "负责产品物料编号")
    private String sapMaterialCode;

    @ApiModelProperty(value = "销售渠道", notes = "销售渠道编码")
    private String saleChannel;
    @ApiModelProperty(value = "销售渠道", notes = "销售渠道名称")
    @Excel(index = 65, title = "销售渠道")
    private String saleChannelName;
}
