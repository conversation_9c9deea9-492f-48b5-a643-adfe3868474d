package com.cnoocshell.web.seller.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BaseGoodDetailAuditDto;
import com.cnoocshell.goods.api.dto.BaseGoodsAuditDTO;
import com.cnoocshell.goods.api.dto.BaseGoodsDTO;
import com.cnoocshell.goods.api.dto.PageBaseGoodsDTO;
import com.cnoocshell.goods.api.enums.AuditAction;
import com.cnoocshell.goods.api.service.IBaseGoodAuditService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(tags = "新增", position = -1)
@RestController
@RequestMapping("/base/good")
public class BaseGoodController {

    private final IBaseGoodAuditService baseGoodAuditService;
    private final IGoodsService goodsService;

    public BaseGoodController(IBaseGoodAuditService baseGoodAuditService, IGoodsService goodsService) {
        this.baseGoodAuditService = baseGoodAuditService;
        this.goodsService = goodsService;
    }

    @ApiOperation(value = "分页查看标准商品列表", tags = "商品")
    @PostMapping(value = "/pageBaseGoods")
    public ItemResult<PageInfo<BaseGoodsDTO>> pageBaseGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody PageBaseGoodsDTO pageBaseGoodsDTO) {
        pageBaseGoodsDTO.setSellerId(loginInfo.getMemberId());
        return goodsService.pageBaseGoods(pageBaseGoodsDTO);
    }

    @ApiOperation(value = "发起审批", tags = "审批")
    @PostMapping("apply")
    public ItemResult<Boolean> apply(@ApiIgnore LoginInfo loginInfo, @RequestBody BaseGoodsAuditDTO dto, @RequestParam("action") AuditAction action) {
        dto.setAction(action);
        dto.setOperator(loginInfo.getMemberId());
        return baseGoodAuditService.applyGoodsAudit(dto);
    }

    @ApiOperation(value = "获取标准商品详细", tags = "商品")
    @GetMapping("{goodId}")
    public ItemResult<BaseGoodDetailAuditDto> details(@ApiIgnore LoginInfo loginInfo, @PathVariable String goodId) {
        loginInfo.getMemberId();
        ItemResult<BaseGoodDetailAuditDto> details = baseGoodAuditService.baseGoodDetails(goodId);
        BaseGoodDetailAuditDto data = details.getData();
        System.out.println("=======details msg=======" + Optional.ofNullable(data).orElse(new BaseGoodDetailAuditDto()).getMsg());
        return details;
    }

    @ApiOperation(value = "发起批量删除审批", tags = "审批")
    @PostMapping("batchDelete")
    public ItemResult<Boolean> batchDelete(@ApiIgnore LoginInfo loginInfo, @RequestBody List<String> goodsId) {
        return baseGoodAuditService.batchDelete(goodsId, loginInfo.getMemberId());
    }
}
