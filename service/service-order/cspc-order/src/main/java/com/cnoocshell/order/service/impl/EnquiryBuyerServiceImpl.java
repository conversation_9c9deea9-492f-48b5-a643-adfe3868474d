package com.cnoocshell.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.enums.RoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountAgreementDTO;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.member.MemberDetailDTO;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.QueryIntentionInfoDTO;
import com.cnoocshell.member.api.enums.AgreementBusinessTypeEnum;
import com.cnoocshell.member.api.enums.AgreementTypeEnum;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.mq.core.MQMessage;
import com.cnoocshell.mq.core.service.IMQProducer;
import com.cnoocshell.order.api.dto.*;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingCustomerInfoDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingCustomerListDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.api.enums.EnquiryStatusEnum;
import com.cnoocshell.order.api.enums.ParticipationStatusEnum;
import com.cnoocshell.order.biz.impl.EnquiryBiz;
import com.cnoocshell.order.biz.impl.EnquiryBuyerBiz;
import com.cnoocshell.order.biz.impl.EnquiryBuyerDetailBiz;
import com.cnoocshell.order.biz.impl.SalesPlanBiz;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerDetailMapper;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerMapper;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;
import com.cnoocshell.order.service.EnquiryBuyerService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Condition;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 买家询价
 */
@Service
@Slf4j
public class EnquiryBuyerServiceImpl implements EnquiryBuyerService {

    @Resource
    private IMemberService memberService;

    @Resource
    private IAccountService accountService;

    @Resource
    private EnquiryBuyerBiz enquiryBuyerBiz;

    @Resource
    private EnquiryBuyerDetailBiz enquiryBuyerDetailBiz;

    @Resource
    private EnquiryBuyerDetailMapper enquiryBuyerDetailMapper;

    @Resource
    private EnquiryBuyerMapper enquiryBuyerMapper;

    @Resource
    private EnquiryBiz enquiryBiz;

    @Resource
    private SalesPlanBiz salesPlanBiz;

    @Resource
    private IRoleService roleService;

    @Resource
    private IMQProducer mqProducer;

    private static  final String ENQUIRY_NO ="enquiryNo";
    private static  final String DEL_FLG ="delFlg";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<String> createEnquiryBuyer(EnquiryBuyerDTO enquiryBuyerDTO) {
        Enquiry enquiry = enquiryBiz.get(enquiryBuyerDTO.getId());
        if(Objects.isNull(enquiry))
            return ItemResult.fail(BasicCode.DATA_EXIST.getCode(),"询价场次不存在！");
        if(!CharSequenceUtil.equals(EnquiryStatusEnum.ENQUIRING.getCode(),enquiry.getStatus()))
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"不在询价中，不能进行询价");

        EnquiryBuyerDetailDTO enquiryBuyerDetailDTO = enquiryBuyerDTO.getEnquiryBuyerDetailDTO();
        String checked = checkEnquiryBuyerDTO(enquiryBuyerDTO, enquiryBuyerDetailDTO);
        if (StringUtils.isNotEmpty(checked)) {
            return new ItemResult<>(BasicCode.INVALID_PARAM.getCode(), checked);
        }
        AccountDTO accountDTO = accountService.findById(enquiryBuyerDTO.getCreateUser());
        MemberDetailDTO member = memberService.findMemberById(accountDTO.getMemberId());
        if (Objects.isNull(member)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "操作人不存在！");
        }
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(accountDTO.getAccountId());
        List<DataPermissionDTO> goodsByCategoryAccountId = roleService.getDataPermissionList(accountRoleDTO);
        if (CollectionUtils.isEmpty(goodsByCategoryAccountId)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "您暂无权限参与此产品相关的询价，请与您的企业管理员确认相关权限");
        }
        //判断有无该产品意向数据
        QueryIntentionInfoDTO queryIntention = new QueryIntentionInfoDTO();
        queryIntention.setMemberCodes(Arrays.asList(member.getMemberCode()));
        queryIntention.setGoodsCodes(Arrays.asList(enquiry.getGoodsCode()));
        List<MemberPurchaseGoodsIntentionDTO> intentions = memberService.queryMemberBySaleInfo(queryIntention);
        if(CollUtil.isEmpty(intentions)){
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(),"您无此产品的操作权限，如需参与此询价，请前往企业信息管理处添加此意向产品。");
        }
        List<String> goodsCode = goodsByCategoryAccountId.stream().map(DataPermissionDTO::getGoodsCode).collect(Collectors.toList());
        //查询是否已经生成
        Condition condition = enquiryBuyerBiz.newCondition();
        condition.createCriteria().andEqualTo(ENQUIRY_NO, enquiryBuyerDTO.getEnquiryNo())
                .andEqualTo("memberCode", member.getMemberCode())
                .andEqualTo(DEL_FLG, false);
        List<EnquiryBuyer> selectEnquiry = enquiryBuyerBiz.findByCondition(condition);

        if (!goodsCode.contains(enquiry.getGoodsCode())) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "您暂无权限参与此产品相关的询价，请与您的企业管理员确认相关权限");
        }
        if (CollectionUtils.isEmpty(selectEnquiry)) {
            EnquiryBuyer enquiryBuyer = EnquiryBuyer.builder().build();
            BeanUtils.copyProperties(enquiryBuyerDTO, enquiryBuyer);
            enquiryBuyer.setId(IdUtil.simpleUUID());
            enquiryBuyer.setCreateTime(new Date());
            enquiryBuyer.setMemberName(member.getMemberName());
            enquiryBuyer.setMemberCode(member.getMemberCode());
            enquiryBuyer.setParticipationStatus(ParticipationStatusEnum.DONE.getCode());
            enquiryBuyer.setGoodsCode(enquiry.getGoodsCode());
            enquiryBuyer.setGoodsName(enquiry.getGoodsName());
            int enquiryResult = enquiryBuyerBiz.insert(enquiryBuyer);
            if (enquiryResult == 0) {
                return new ItemResult<>(BasicCode.DB_ERROR, "创建买家询价失败！");
            }
        }else {
            EnquiryBuyer enquiryBuyer = EnquiryBuyer.builder().build();
            enquiryBuyer.setId(selectEnquiry.get(0).getId());
            enquiryBuyer.setUpdateTime(new Date());
            enquiryBuyer.setUpdateUser(member.getMemberId());
            enquiryBuyer.setParticipationStatus(ParticipationStatusEnum.DONE.getCode());
            enquiryBuyerBiz.save(enquiryBuyer);
        }

        EnquiryBuyerDetail enquiryBuyerDetail = EnquiryBuyerDetail.builder()
                .id(IdUtil.simpleUUID())
                .createTime(new Date())
                .createUser(member.getMemberId())
                .createUserName(StringUtils.isEmpty(accountDTO.getRealName()) ? accountDTO.getAccountName() : accountDTO.getRealName())
                .enquiryNo(enquiryBuyerDTO.getEnquiryNo())
                .memberName(member.getMemberName())
                .memberCode(member.getMemberCode())
                .goodsCode(enquiry.getGoodsCode())
                .goodsName(enquiry.getGoodsName())
                .deliveryMode(enquiryBuyerDetailDTO.getDeliveryMode())
                .pack(enquiryBuyerDetailDTO.getPack())
                .quantity(enquiryBuyerDetailDTO.getQuantity())
                .price(enquiryBuyerDetailDTO.getPrice())
                .delFlg(false)
                .build();
        log.info("EnquiryBuyerServiceImpl createEnquiryBuyer enquiryBuyerDetail:{}", enquiryBuyerDetail);
        int enquiryBuyerInsert = enquiryBuyerDetailBiz.insert(enquiryBuyerDetail);
        if (enquiryBuyerInsert == 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ItemResult<>(BasicCode.DB_ERROR, "创建买家询价详情失败！");
        }
        //保存客户报量协议记录
        try {
            MQMessage<List<AccountAgreementDTO>> mq = new MQMessage<>();
            mq.setExchange(MqTopicConstant.SAVE_AGREEMENT_TOPIC);
            AccountAgreementDTO biddingBuyerAgreement = new AccountAgreementDTO();
            biddingBuyerAgreement.setAccountId(accountDTO.getAccountId());
            biddingBuyerAgreement.setBusinessNo(enquiryBuyerDetail.getId());
            biddingBuyerAgreement.setBusinessType(AgreementBusinessTypeEnum.ENQUIRY_BUYER_DETAIL_ID.getType());
            biddingBuyerAgreement.setAgreementType(AgreementTypeEnum.TRANSACTION_AGREEMENT.getType());
            mq.setData(Arrays.asList(biddingBuyerAgreement));
            mqProducer.send(mq);
        }catch (Exception e) {
            log.error("EnquiryBuyerServiceImpl createEnquiryBuyer mq send is error:", e);
        }

        return new ItemResult<>("询价成功！");
    }

    private String checkEnquiryBuyerDTO(EnquiryBuyerDTO enquiryBuyerDTO, EnquiryBuyerDetailDTO enquiryBuyerDetailDTO) {
        if (StringUtils.isEmpty(enquiryBuyerDTO.getEnquiryNo())) {
            return "询价场次编号为空";
        }
        if (Objects.isNull(enquiryBuyerDetailDTO.getPrice())) {
            return "询价价格为空";
        }
        if (Objects.isNull(enquiryBuyerDetailDTO.getQuantity())) {
            return "询价数量为空";
        }
        if (StringUtils.isEmpty(enquiryBuyerDetailDTO.getDeliveryMode())) {
            return "配送方式为空";
        }
        if (StringUtils.isEmpty(enquiryBuyerDetailDTO.getPack())) {
            return "包装为空";
        }
        return null;
    }

    @Override
    public ItemResult<EnquiryDTO> getEnquiryBuyDetail(EnquiryBuyerDTO enquiryBuyerDTO) {
        Enquiry enquiry = enquiryBiz.get(enquiryBuyerDTO.getId());
        if (Objects.isNull(enquiry)) {
            return new ItemResult<>(BasicCode.DATA_EXIST.getCode(), "询价不存在");
        }
        AccountDTO accountDTO = accountService.findDetailById(enquiryBuyerDTO.getOperator());
        if (Objects.isNull(accountDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "操作人不存在！");
        }
        List<com.cnoocshell.base.api.dto.role.RoleDTO> roleByAccountId = roleService.getRoleByAccountId(accountDTO.getAccountId());
        List<String> roleCodes = roleByAccountId.stream().map(com.cnoocshell.base.api.dto.role.RoleDTO::getRoleCode).collect(Collectors.toList());
        EnquiryDTO enquiryDTO = EnquiryDTO.builder().build();
        BeanUtils.copyProperties(enquiry, enquiryDTO);
        List<String> goodsCodes = new ArrayList<>();
        //如果只有一个角色并且是买家
        if (CollectionUtils.isNotEmpty(roleCodes) && roleCodes.size() == 1 && roleCodes.get(0).equals(RoleTypeEnum.BUYER.getRoleType())) {
            QueryIntentionInfoDTO queryIntentionInfoDTO = new QueryIntentionInfoDTO();
            queryIntentionInfoDTO.setMemberCodes(Lists.newArrayList(accountDTO.getMemberCode()));
            List<MemberPurchaseGoodsIntentionDTO> membersBySalesUserIdAndGoodsCode = memberService.queryMemberBySaleInfo(queryIntentionInfoDTO);
            goodsCodes = membersBySalesUserIdAndGoodsCode.stream().map(MemberPurchaseGoodsIntentionDTO::getGoodsCode).collect(Collectors.toList());
        }

        List<EnquiryBuyerDetail> enquiryBuyerDetailList;
        enquiryBuyerDetailList = getEnquiryBuyerDetails(enquiry.getEnquiryNo(), accountDTO.getMemberCode(), goodsCodes);
        if (CollectionUtils.isNotEmpty(enquiryBuyerDetailList)) {
            enquiryDTO.setEnquiryBuyerDetailDTOS(BeanUtil.copyToList(enquiryBuyerDetailList, EnquiryBuyerDetailDTO.class));
        }

        Condition salesCondition = salesPlanBiz.newCondition();
        salesCondition.createCriteria().andEqualTo("planNo", enquiry.getSalesPlanNo());
        List<SalesPlan> salesPlans = salesPlanBiz.findByCondition(salesCondition);
        if (CollectionUtils.isEmpty(salesPlans)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), "销售计划不存在！");
        }

        SalesPlanInfoDTO salesPlanInfoDTO = SalesPlanInfoDTO.builder().build();
        BeanUtil.copyProperties(salesPlans.get(0), salesPlanInfoDTO);
        enquiryDTO.setSalesPlanInfoDTO(salesPlanInfoDTO);
        enquiryDTO.setDeliveryCostStandard(salesPlanInfoDTO.getDeliveryCostStandard());
        enquiryDTO.setSelfPickupCarrier(salesPlanInfoDTO.getSelfPickupCarrier());
        enquiryDTO.setSelfPickupGuide(salesPlanInfoDTO.getSelfPickupGuide());

        //补充是否参与字段
        List<EnquiryBuyerDTO> enquiryBuyerDTOS = enquiryBuyerMapper.selectByEnquiryNoMemberCode(enquiry.getEnquiryNo(), accountDTO.getMemberCode());
        enquiryDTO.setParticipationStatus(CollectionUtils.isEmpty(enquiryBuyerDTOS) ? "TODO" : enquiryBuyerDTOS.get(0).getParticipationStatus());

        //买家端详情，如果询价状态为 已作废，前端展示为 已结束
        if(EnquiryStatusEnum.CANCELLED.getCode().equals(enquiryDTO.getStatus())){
            enquiryDTO.setStatus(EnquiryStatusEnum.END.getCode());
        }

        return new ItemResult<>(enquiryDTO);
    }

    @Override
    public List<EnquiryBuyerDetail> getEnquiryBuyerDetailsByEnquiryNo(String enquiryNo, List<String> memberIds) {
        Condition condition = enquiryBuyerDetailBiz.newCondition();
        condition.createCriteria().andEqualTo(ENQUIRY_NO, enquiryNo)
                .andEqualTo(DEL_FLG, false);
        if (CollectionUtils.isNotEmpty(memberIds)) {
            condition.createCriteria().andIn("memberId", memberIds);
        }
        return enquiryBuyerDetailMapper.selectByEnquiryAndMemberIds(enquiryNo, memberIds);
    }

    @Override
    public List<BiddingCustomerListDTO> queryBuyerUserInfo(BiddingCustomerInfoDTO dto, String status) {
        return enquiryBuyerMapper.queryBuyerUserInfo(dto, status);
    }

    @Override
    public List<BiddingBuyerDTO> selectBuyerList(List<String> noList, String enquiryStatus) {
        return enquiryBuyerMapper.selectBuyerList(noList, enquiryStatus);
    }

    @Override
    public List<BiddingBuyerDTO> queryCheckBuyerUserInfo(List<String> buyerList, String enquiryTODOStatus, List<String> ids) {
        return enquiryBuyerMapper.queryCheckBuyerUserInfo(buyerList,enquiryTODOStatus,ids);
    }

    public List<EnquiryBuyerDetail> getEnquiryBuyerDetails(String enquiryNo, String memberCode, List<String> goodsCodes) {
        Condition condition = enquiryBuyerDetailBiz.newCondition();
        condition.createCriteria().andEqualTo(ENQUIRY_NO, enquiryNo)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo(DEL_FLG, false);
        if (CollectionUtils.isNotEmpty(goodsCodes)) {
            condition.createCriteria().andIn("goodsCode", goodsCodes);
        }
        condition.orderBy("createTime").desc();
        return enquiryBuyerDetailBiz.findByCondition(condition);
    }

    @Override
    public PageInfo<EnquiryBuyerListViewDTO> buyerQueryEnquiryList(EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO){
        //组装产品查询范围
        setGoodsCodeList(enquiryBuyerListQueryDTO);

        //组装状态值
        List<String> statusList = setStatusList(enquiryBuyerListQueryDTO);
        enquiryBuyerListQueryDTO.setStatuslist(statusList);

        //查询询价列表
        PageMethod.startPage(enquiryBuyerListQueryDTO.getPageNum(), enquiryBuyerListQueryDTO.getPageSize());
        List<EnquiryBuyerListViewDTO> list = enquiryBuyerMapper.buyerQueryEnquiryList(new Page<>(enquiryBuyerListQueryDTO.getPageNum(), enquiryBuyerListQueryDTO.getPageSize()), enquiryBuyerListQueryDTO);

        //组装列表显示的状态值
        setViewStatus(list);

        return new PageInfo<>(list);
    }

    private void setGoodsCodeList(EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO){
        List<String> roleList = enquiryBuyerListQueryDTO.getRoleList();
        String roleCode = BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode();
        if (CollUtil.isEmpty(roleList) || !roleList.contains(roleCode)) {
            AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
            accountRoleDTO.setAccountId(enquiryBuyerListQueryDTO.getAccountId());
            List<DataPermissionDTO> dataPermissionDTOList;
            dataPermissionDTOList = roleService.getDataPermissionList(accountRoleDTO);
            if (CollUtil.isEmpty(dataPermissionDTOList)) {
                throw new BizException(OrderErrorCode.CUSTOM, "买家询价场次列表查询没有数据权限");
            }
            //产品数据权限
            List<String> goodsCodeList = dataPermissionDTOList.stream().map(DataPermissionDTO::getGoodsCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            if (CollUtil.isEmpty(goodsCodeList)) {
                throw new BizException(OrderErrorCode.CUSTOM, "买家询价场次列表查询没有数据权限");
            }
            //小程序端，产品是多选；判断前端是否传入产品
            if(CollUtil.isNotEmpty(enquiryBuyerListQueryDTO.getGoodsCodeList())){
                //将前端传入的产品list与账号的产品list取交集
                List<String> queryGoodsCodeList = enquiryBuyerListQueryDTO.getGoodsCodeList();
                List<String> intersection = queryGoodsCodeList.stream()
                        .filter(goodsCodeList::contains)
                        .collect(Collectors.toList());
                enquiryBuyerListQueryDTO.setGoodsCodeList(intersection);
            }else{
                enquiryBuyerListQueryDTO.setGoodsCodeList(goodsCodeList);
            }
        }
    }

    private List<String> setStatusList(EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO){
        //根据前端传入的询价状态，组装底层状态逻辑
        //如果没有传值，则查询非草稿状态的数据
        //如果传值 已结束，则查询 已结束和已作废的数据
        //如果传值 非已结束，则查询 对应状态的数据
        List<String> statusList = new ArrayList<>();
        if(StringUtils.isEmpty(enquiryBuyerListQueryDTO.getStatus())){
            statusList.add(EnquiryStatusEnum.ENQUIRING.getCode());
            statusList.add(EnquiryStatusEnum.END.getCode());
            statusList.add(EnquiryStatusEnum.CANCELLED.getCode());
            statusList.add(EnquiryStatusEnum.TO_START.getCode());
            statusList.add(EnquiryStatusEnum.ENQUIRY_CANCEL.getCode());
        }else if (EnquiryStatusEnum.END.getCode().equals(enquiryBuyerListQueryDTO.getStatus())){
            statusList.add(EnquiryStatusEnum.END.getCode());
            statusList.add(EnquiryStatusEnum.CANCELLED.getCode());
        }else{
            statusList.add(enquiryBuyerListQueryDTO.getStatus());
        }
        return statusList;
    }

    private void setViewStatus(List<EnquiryBuyerListViewDTO> list){

        if(CollUtil.isNotEmpty(list)){
            list.forEach(enquiryBuyerListViewDTO -> {
                //买家端询价列表，如果询价状态为 已作废，前端界面显示为 已结束
                if(EnquiryStatusEnum.CANCELLED.getCode().equals(enquiryBuyerListViewDTO.getStatus())){
                    enquiryBuyerListViewDTO.setStatus(EnquiryStatusEnum.END.getCode());
                }
            });
        }
    }
}
