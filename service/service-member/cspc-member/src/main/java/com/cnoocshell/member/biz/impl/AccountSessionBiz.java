package com.cnoocshell.member.biz.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cnoocshell.base.api.dto.role.RoleDTO;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.member.api.dto.account.RefreshLoginInfoDTO;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.member.biz.IAccountLoginInfoBiz;
import com.cnoocshell.member.biz.IAccountSessionBiz;
import com.cnoocshell.member.dao.mapper.AccountLoginInfoMapper;
import com.cnoocshell.member.dao.vo.Account;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountSessionBiz implements IAccountSessionBiz {
    public static final String USER_SESSION_REFRESH_IS_FALSE = "user.session.refresh is false";
    public static final String LOGIN_INFO = "_loginInfo";
    public static final String LAST_LOGIN_SESSION_ID_WAS_NOT_FOUND = "last login sessionId was not found.";
    public static final String NOT_LOGIN_INFO = "value is not LoginInfo";
    private static final String SESSION_KEY_PREFIX = "spring:session:sessions:";
    private static final String SESSION_ATTR_PREFIX = "sessionAttr:";

    @Autowired
    @Qualifier("defaultRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Qualifier("myTaskAsyncPool")
    private final Executor executor;
    private final UUIDGenerator uuidGenerator;
    private final IAccountLoginInfoBiz accountLoginInfoBiz;
    private final IRoleService roleService;
    private final AccountLoginInfoMapper accountLoginInfoMapper;

    @Value("${user.session.forceOffLine:true}")
    private boolean forceOffLine = true;

    @Value("${user.session.refresh:true}")
    private boolean refreshAccountSession;

    private void setHashValueSerializer() {
        if (redisTemplate != null && !(redisTemplate.getHashValueSerializer() instanceof JdkSerializationRedisSerializer)) {
            redisTemplate.setHashValueSerializer(new JdkSerializationRedisSerializer());
        }
    }


    @Override
    public void forceOffLineSync(String accountId, String terminal, String exceptionSessionId, String msg, String operator) {
        if (!forceOffLine) {
            log.info("user.session.forceOffLine is false by forceOffLineSync");
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName(uuidGenerator.gain());
                forceOffLine(accountId, terminal, exceptionSessionId, msg, operator);
            } catch (Exception e) {
                log.error("forceOffLineSync error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    @Override
    public void updateSessionLoginInfoMobile(String accountId, String mobile) {
        if (!refreshAccountSession) {
            log.info(USER_SESSION_REFRESH_IS_FALSE);
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName("crc4" + uuidGenerator.gain());
                List<String> sessionIdList = accountLoginInfoBiz.findLastSessionId(accountId, null);
                if (CollectionUtils.isEmpty(sessionIdList)) {
                    log.info(LAST_LOGIN_SESSION_ID_WAS_NOT_FOUND);
                    return;
                }
                setHashValueSerializer();
                String field = SESSION_ATTR_PREFIX + LOGIN_INFO;
                for (String sessionId : sessionIdList) {
                    String key = SESSION_KEY_PREFIX + sessionId;
                    Object obj = redisTemplate.opsForHash().get(key, field);
                    if (!(obj instanceof LoginInfo)) {
                        log.warn(NOT_LOGIN_INFO);
                        continue;
                    }
                    LoginInfo loginInfo = (LoginInfo) obj;
                    String oldMobile = loginInfo.getMobile();
                    loginInfo.setMobile(mobile);
                    loginInfo.getOperator().setMobile(mobile);
                    if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field))) {
                        redisTemplate.opsForHash().put(key, field, loginInfo);
                    }
                    log.info("session:{},accountId:{},accountName:{},mobile:{},newMobile:{}更新成功",
                            sessionId, loginInfo.getAccountId(), loginInfo.getAccountName(), oldMobile, mobile);
                }
            } catch (Exception e) {
                log.error("updateAccountName error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    @Override
    public void updateSessionLoginInfoAccountInfo(Account account) {
        if (!refreshAccountSession) {
            log.info(USER_SESSION_REFRESH_IS_FALSE);
            return;
        }
        if (account == null || StringUtils.isBlank(account.getAccountId())) {
            log.info("account is null.");
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName("crc2" + uuidGenerator.gain());
                setHashValueSerializer();
                List<String> sessionIdList = accountLoginInfoBiz.findLastSessionId(account.getAccountId(), null);
                if (CollectionUtils.isEmpty(sessionIdList)) {
                    return;
                }
                String field = SESSION_ATTR_PREFIX + LOGIN_INFO;
                for (String sessionId : sessionIdList) {
                    String key = SESSION_KEY_PREFIX + sessionId;
                    Object obj = redisTemplate.opsForHash().get(key, field);
                    if (!(obj instanceof LoginInfo)) {
                        log.warn(NOT_LOGIN_INFO);
                        continue;
                    }
                    LoginInfo loginInfo = (LoginInfo) obj;
                    LoginInfo oldLoginInfo = new LoginInfo();
                    BeanUtils.copyProperties(loginInfo, oldLoginInfo);
                    loginInfo.setAccountName(account.getAccountName());
                    loginInfo.setAccountNickname(account.getAccountNickname());
                    loginInfo.setHeadPic(account.getHeadPic());
                    loginInfo.setAccountType(account.getAccountType());
                    loginInfo.setRealName(account.getRealName());
                    loginInfo.setMobile(account.getMobile());
                    loginInfo.setEmail(account.getEmail());
                    loginInfo.setMemberName(account.getMemberName());
                    loginInfo.setMemberShortName(account.getMemberShortName());
                    loginInfo.getOperator().setMemberName(account.getMemberName());
                    loginInfo.getOperator().setMemberShortName(account.getMemberShortName());
                    loginInfo.getOperator().setAccountName(account.getAccountName());
                    loginInfo.getOperator().setMobile(account.getMobile());
                    loginInfo.getOperator().setRealName(account.getRealName());
                    if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field))) {
                        redisTemplate.opsForHash().put(key, field, loginInfo);
                    }
                    log.info("session:{},accountId:{},old:{},new:{}更新成功", sessionId, loginInfo.getAccountId(), oldLoginInfo, loginInfo);
                }
            } catch (Exception e) {
                log.error("updateLoginInfo error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    @Override
    public void updateSessionLoginInfoAccountName(String accountId, String accountName) {
        if (!refreshAccountSession) {
            log.info(USER_SESSION_REFRESH_IS_FALSE);
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName("crc3" + uuidGenerator.gain());
                List<String> sessionIdList = accountLoginInfoBiz.findLastSessionId(accountId, null);
                if (CollectionUtils.isEmpty(sessionIdList)) {
                    log.info(LAST_LOGIN_SESSION_ID_WAS_NOT_FOUND);
                    return;
                }
                setHashValueSerializer();
                String field = SESSION_ATTR_PREFIX + LOGIN_INFO;
                for (String sessionId : sessionIdList) {
                    String key = SESSION_KEY_PREFIX + sessionId;
                    Object obj = redisTemplate.opsForHash().get(key, field);
                    if (!(obj instanceof LoginInfo)) {
                        log.warn(NOT_LOGIN_INFO);
                        continue;
                    }
                    LoginInfo loginInfo = (LoginInfo) obj;
                    String oldAccountName = loginInfo.getAccountName();
                    loginInfo.setAccountName(accountName);
                    loginInfo.getOperator().setAccountName(accountName);
                    if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field))) {
                        redisTemplate.opsForHash().put(key, field, loginInfo);
                    }
                    log.info("session:{},accountId:{},accountName:{},newAccountName:{} 更新成功", sessionId, loginInfo.getAccountId(), loginInfo.getAccountName(), oldAccountName);
                }
            } catch (Exception e) {
                log.error("updateAccountName error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    @Override
    public void updateSessionLoginInfoMemberName(String memberId, String memberName, String memberShortName) {
        if (!refreshAccountSession) {
            log.info(USER_SESSION_REFRESH_IS_FALSE);
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName("crc1" + uuidGenerator.gain());
                int pageNum = 0;
                setHashValueSerializer();
                while (true) {
                    List<String> sessionIdList = accountLoginInfoBiz.findLoginSessionId(pageNum++, 1000);
                    if (CollectionUtils.isEmpty(sessionIdList)) {
                        break;
                    }
                    String field = SESSION_ATTR_PREFIX + LOGIN_INFO;
                    for (String sessionId : sessionIdList) {
                        updateSessionLoginInfoMemberNameItem(memberId, memberName, memberShortName, field, sessionId);
                    }
                }

            } catch (Exception e) {
                log.error("updateMemberName error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    //账户数据权限修改时，刷新用户会话信息
    @Override
    public void refreshLoginInfo(RefreshLoginInfoDTO dto) {
        if (!refreshAccountSession) {
            log.info(USER_SESSION_REFRESH_IS_FALSE);
            return;
        }
        log.info("1RefreshLoginInfoDTO的数据是============》" + dto);
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String oldName = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName("crc5" + uuidGenerator.gain());
                refreshLoginInfoAsync(dto);
            } catch (Exception e) {
                log.error("error: {},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(oldName);
            }
        });
    }

    @Override
    public List<String> findLoginSessionId(Set<String> accountIdSet, Integer pageNum, Integer pageSize) {
        if (accountIdSet == null || accountIdSet.isEmpty()) {
            return Lists.newArrayList();
        }
        pageNum = ObjectUtil.defaultIfNull(pageNum,0);
        pageSize = ObjectUtil.defaultIfNull(pageSize,10);
        return accountLoginInfoMapper.findLoginSessionId2(accountIdSet,
                DateUtil.format(LocalDateTime.now().minusDays(1), DatePattern.NORM_DATETIME_PATTERN),
                pageNum * pageSize, pageSize);
    }

    @Override
    public void forceOffLineSyncBySameTerminal(String accountId, String terminal, String exceptionSessionId, String msg, String operator) {
        if (!forceOffLine) {
            log.info("user.session.forceOffLine is false by forceOffLineSyncBySameTerminal");
            return;
        }
        executor.execute(() -> {
            long s = System.currentTimeMillis();
            String old = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName(uuidGenerator.gain());
                forceOffLine(accountId, terminal, exceptionSessionId, msg, operator);
            } catch (Exception e) {
                log.error("forceOffLineSyncBySameTerminal error:{},cost : {}", (System.currentTimeMillis() - s), e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(old);
            }
        });
    }

    @Override
    public void removeSession(String sessionId) {
        String key = SESSION_KEY_PREFIX + sessionId;
        if(redisTemplate.hasKey(key))
            redisTemplate.delete(key);
    }

    private void refreshLoginInfoAsync(RefreshLoginInfoDTO dto) {
        int pageNum = 0;
        setHashValueSerializer();
        while (true) {
            log.info("2RefreshLoginInfoDTO的数据是============》" + dto);
            List<String> sessionIdList = BooleanUtils.isTrue(dto.getRefreshAll()) ? accountLoginInfoBiz.findLoginSessionId(pageNum++, 1000) :
                    accountLoginInfoBiz.findLoginSessionId(dto.getAccountIdSet(), pageNum++, 1000);
            log.info("sessionIdList===========》" + sessionIdList);
            if (CollectionUtils.isEmpty(sessionIdList)) {
                break;
            }
            String field = SESSION_ATTR_PREFIX + LOGIN_INFO;
            for (String sessionId : sessionIdList) {
                refreshLoginInfoItem(field, sessionId);
            }
        }
    }

    private void refreshLoginInfoItem(String field, String sessionId) {
        try {
            String key = SESSION_KEY_PREFIX + sessionId;
            Object obj = redisTemplate.opsForHash().get(key, field);
            if (!(obj instanceof LoginInfo)) {
                return;
            }
            LoginInfo loginInfo = (LoginInfo) obj;
            LoginInfo oldLoginInfo = new LoginInfo();
            BeanUtils.copyProperties(loginInfo, oldLoginInfo);
            List<RoleDTO> accountRole = roleService.getRoleByAccountId(loginInfo.getAccountId());
            loginInfo.setRoleList(accountRole == null ? null : accountRole.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList()));
            loginInfo.setRoleNameList(accountRole == null ? null : accountRole.stream().map(RoleDTO::getRoleName).collect(Collectors.toList()));
            loginInfo.setRoleTypeList(accountRole == null ? null : accountRole.stream().map(RoleDTO::getRoleType).distinct().collect(Collectors.toList()));

            log.info("loginInfo数据为============》{}",loginInfo);
            if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field))) {
                redisTemplate.opsForHash().put(key, field, loginInfo);
            }
            log.info("session:{},accountId:{},old:{},new:{}更新成功", sessionId, loginInfo.getAccountId(), oldLoginInfo, loginInfo);
        } catch (Exception e) {
            log.error("获取权限、数据权限、销售区域信息出错,session:{}刷新失败：", sessionId, e);
        }
    }

    private void updateSessionLoginInfoMemberNameItem(String memberId, String memberName, String memberShortName, String field, String sessionId) {
        try {
            String key = SESSION_KEY_PREFIX + sessionId;
            Object obj = redisTemplate.opsForHash().get(key, field);
            if (!(obj instanceof LoginInfo)) {
                log.warn(NOT_LOGIN_INFO);
                return;
            }
            LoginInfo loginInfo = (LoginInfo) obj;
            if (!StringUtils.equals(loginInfo.getMemberId(), memberId)) {
                return;
            }
            String oldMemberName = loginInfo.getMemberName();
            String oldMemberShortName = loginInfo.getMemberShortName();
            loginInfo.setMemberName(memberName);
            loginInfo.setMemberShortName(memberShortName);
            loginInfo.getOperator().setMemberName(memberName);
            loginInfo.getOperator().setMemberShortName(memberShortName);
            if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field))) {
                redisTemplate.opsForHash().put(key, field, loginInfo);
            }
            log.info("session:{},accountId:{},accountName:{},memberName:{},memberShortName:{},newMemberName:{},newMemberShortName:{}更新成功",
                    sessionId, loginInfo.getAccountId(), loginInfo.getAccountName(), oldMemberName, oldMemberShortName, memberName, memberShortName);
        } catch (Exception e) {
            log.error("updateMemberName error: {}", e.getMessage(), e);
        }
    }


    public void forceOffLine(String accountId, String terminal, String exceptionSessionId, String msg, String operator) {
        if (!forceOffLine) {
            log.info("user.session.forceOffLine is false");
            return;
        }
        List<String> sessionIdList = accountLoginInfoBiz.findLastSessionIdByTerminal(accountId, terminal);
        if (CollectionUtils.isEmpty(sessionIdList)) {
            log.info(LAST_LOGIN_SESSION_ID_WAS_NOT_FOUND);
            return;
        }
        setHashValueSerializer();
        for (String sessionId : sessionIdList) {
            //跳过当前session
            if (StringUtils.equals(sessionId, exceptionSessionId)) {
                continue;
            }
            String key = SESSION_KEY_PREFIX + sessionId;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                redisTemplate.opsForHash().put(key, SESSION_ATTR_PREFIX + "forceOffLine", msg);
                //更新登录日志登录状态信息
                accountLoginInfoBiz.offline(sessionId, exceptionSessionId, msg, operator);
                if (StringUtils.isBlank(operator)) {
                    log.info("sessionId:{},forceOffLine:{}", key, msg);
                } else {
                    log.info("sessionId:{},forceOffLine:{},operator: {}", key, msg, operator);
                }
            }
        }
    }

}
