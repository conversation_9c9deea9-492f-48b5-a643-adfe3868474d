package com.cnoocshell.base.api.service;

import com.cnoocshell.base.api.dto.permission.PermissionTreeDTO;
import com.cnoocshell.base.api.dto.permission.QueryRolePermissionDTO;
import com.cnoocshell.base.api.dto.permission.RoleMenuPermissionDTO;
import com.cnoocshell.base.api.dto.permission.VerifyPermissionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = {"IPermissionService"}, description = "权限相关service")
@FeignClient(name = "cspc-service-base")
public interface IPermissionService {

    @ApiOperation("查询权限菜单")
    @RequestMapping(method = RequestMethod.POST, path = "/permission/findPermissionMenu", consumes = "application/json")
    PermissionTreeDTO findPermissionMenu(@RequestParam("roleCode") String roleCode, @RequestParam("roleType") String roleType);

    @ApiOperation(value = "查询角色菜单权限，元素标签权限 平铺展示",notes = "根据数据排序字段进行排序")
    @PostMapping("/permission/queryRolePermissions")
    List<RoleMenuPermissionDTO> queryRolePermissions(@RequestBody QueryRolePermissionDTO param);

    @ApiOperation("校验接口权限")
    @PostMapping("/permission/verifyInterfacePermission")
    Boolean verifyInterfacePermission(@RequestBody VerifyPermissionDTO param);
}
