package com.cnoocshell.information.biz.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.information.api.constant.InfoColumnConstant;
import com.cnoocshell.information.biz.IPrivateMessageContentBiz;
import com.cnoocshell.information.dao.mapper.PrivateMessageContentMapper;
import com.cnoocshell.information.dao.vo.PrivateMessageContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.Collections;
import java.util.List;

@Service
public class PrivateMessageContentBiz extends BaseBiz<PrivateMessageContent> implements IPrivateMessageContentBiz {

    @Autowired
    private PrivateMessageContentMapper privateMessageContentMapper;

    @Override
    public PrivateMessageContent save(PrivateMessageContent privateMessageContent) {
        privateMessageContentMapper.insert(privateMessageContent);
        return privateMessageContent;
    }

    @Override
    public List<PrivateMessageContent> findByIds(List<String> msgIds) {
        if (CollUtil.isEmpty(msgIds))
            return Collections.emptyList();
        Condition condition = new Condition(PrivateMessageContent.class);
        condition.createCriteria()
                .andEqualTo(InfoColumnConstant.DEL_FLG, Boolean.FALSE)
                .andIn(InfoColumnConstant.MSG_ID, msgIds);
        return this.findByCondition(condition);
    }
}
