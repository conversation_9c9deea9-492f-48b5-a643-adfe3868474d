package com.cnoocshell.information.api.dto.announcement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * @Author: <EMAIL>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Data
public class AnnouncementDetailDTO {

    /**
     * id
     */
    @ApiModelProperty("id")
    private String announcementId;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 生效开始时间
     */
    @ApiModelProperty("生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty("生效结束时间")
    private Date endTime;

    /**
     * 公告内容url
     */
    @ApiModelProperty("公告内容url")
    private String contentUrl;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    private String content;

    /**
     * 可用状态 0启用  1暂停
     */
    @ApiModelProperty("可用状态 0启用  1暂停")
    private Integer availableStatus;

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @ApiModelProperty("审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @ApiModelProperty("公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    /**
     * 排序序号
     */
    @ApiModelProperty("排序序号")
    private Integer weight;

    /**
     * 删除状态
     */
    @ApiModelProperty("删除状态")
    private Integer delFlg;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人/审批人
     */
    @ApiModelProperty("创建人")
    private String createUserName;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 审批记录
     */
    @ApiModelProperty("审批记录")
    private List<AnnouncementApprovalDTO> list;

    /**
     * 附件id
     */
    @ApiModelProperty("附件id")
    private String attachmentIds;
}
