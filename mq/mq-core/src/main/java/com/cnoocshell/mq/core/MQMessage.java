package com.cnoocshell.mq.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MQMessage<T> implements Serializable {
    private static final long serialVersionUID = 1905122041950151207L;

    private String exchange;

    private String key;

    private String msgId;

    private T data;

    private int retryTimes;

    private String messageType;

}
