package com.cnoocshell.order.exception;

import com.cnoocshell.common.exception.CodeMeta;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午4:50 20/8/17
 */
public class OrderErrorCode {

    // 添加私有构造函数以防止实例化
    private OrderErrorCode() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static final CodeMeta PRIMARY_SUBMIT_FAILED = new CodeMeta("330801", "PRIMARY_SUBMIT_FAILED", "{}",
            "{}");

    public static final CodeMeta PRIMARY_PAY_FAILED = new CodeMeta("330802", "PRIMARY_PAY_FAILED", "{}",
            "{}");
    public static final CodeMeta CUSTOM = new CodeMeta("330901", "CUSTOM", "{}","{}");
}
