package com.cnoocshell.common.excel;

import org.apache.commons.lang.StringUtils;
import com.cnoocshell.common.utils.Date2Util;
import com.cnoocshell.common.utils.String2Util;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

/**
 * 读取和写入Excel
 *
 * @Author: <EMAIL>
 */
public class XssExcel
{
    private final static Logger logger = LoggerFactory.getLogger(XssExcel.class);

    /**
     * Excel作者
     */
    private final static String AUTHOR = "路遥";

    /**
     * Excel文字字体
     */
    private final static String FONT_NAME = "等线";

    /**
     * Excel文字大小
     */
    private final static Short FONT_SIZE = 10;

    /**
     * Excel列宽度
     */
    private final static int COLUMN_WIDTH = 15;

    /**
     * Excel列高度
     */
    private final static int COLUMN_HEIGHT = 15;

    /**
     * 读取Excel数据
     *
     * @param file  Excel文件地址
     * @param clazz 导出类型
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readFromExcel(String file, Class<T> clazz) throws Exception
    {
        return readFromExcel(file, null, clazz, 1);
    }

    /**
     * 读取Excel数据
     *
     * @param file    Excel文件地址
     * @param clazz   导出类型
     * @param skipRow 要跳过读取的行数
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readFromExcel(String file, Class<T> clazz, int skipRow) throws Exception
    {
        return readFromExcel(file, null, clazz, skipRow);
    }

    /**
     * 读取Excel数据
     *
     * @param file      Excel文件地址
     * @param sheetName sheet名称
     * @param clazz     导出类型
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readFromExcel(String file, String sheetName, Class<T> clazz) throws Exception
    {
        return readFromExcel(file, sheetName, clazz, 1);
    }

    /**
     * 读取Excel数据
     *
     * @param file      Excel文件地址
     * @param sheetName sheet名称
     * @param clazz     导出类型
     * @param skipRow   要跳过读取的行数
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readFromExcel(String file, String sheetName, Class<T> clazz, int skipRow) throws Exception
    {
        long timestamp = Date2Util.timestamp(true);

        File xlsxFile = new File(file);
        XSSFWorkbook workbook = null;
        try(FileInputStream stream = new FileInputStream(xlsxFile)) {
            workbook = new XSSFWorkbook(stream);

            // 定义一个map用于存放列的序号和field.
            Map<Integer, Field> fieldsMap = getFieldsMap(clazz);

            // 获取sheet
            XSSFSheet sheet = sheetName == null ? workbook.getSheetAt(0) : workbook.getSheet(sheetName);

            // 得到所有的行数
            int row = sheet.getPhysicalNumberOfRows();

            // 所有数据
            List<T> dataList = new ArrayList<>();
            // 遍历每一行数据 第一行是标题不用读取
            extracted(clazz, skipRow, row, sheet, fieldsMap, dataList);
            logger.info("读取Excel: {}, 耗时: {}ms", file, Date2Util.timestamp(true) - timestamp);
            return dataList;
        } finally {
            IOUtils.closeQuietly(workbook);
        }
    }

    private static <T> void extracted(Class<T> clazz, int skipRow, int row, XSSFSheet sheet, Map<Integer, Field> fieldsMap, List<T> dataList) throws InstantiationException, IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        for(int i = skipRow; i <= row; i++)
        {
            // 得到一行的单元格的数据
            XSSFRow xssfRow = sheet.getRow(i);
            if(xssfRow == null)
            {
                continue;
            }

            // 此处不初始化
            T entity = null;
            // 遍历每一列数据
            for(Map.Entry<Integer, Field> entry : fieldsMap.entrySet())
            {
                int index = entry.getKey();
                Field field = entry.getValue();

                Class<?> fieldType = field.getType();
                String fieldName = field.getName();

                XSSFCell cell = xssfRow.getCell(index);
                if(cell == null)
                {
                    continue;
                }

                // 单元格类型不为STRING的设置为STRING
                CellType cellType = cell.getCellType();
                setCellType(cellType, cell);

                // 取得类型,并根据对象类型设置值
                Object object = stringToObject(field, cell.getStringCellValue().trim());
                if(object == null)
                {
                    continue;
                }

                // 此处初始化entity对象
                entity = getEntity(clazz, entity);

                // 动态调用方赋值
                // 取得对应setXxx()方法
                String methodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                // 泛型为Object以及所有Object的子类
                Class<?> tClass = entity.getClass();
                // 通过方法名得到对应的方法
                Method method = tClass.getMethod(methodName, fieldType);
                method.invoke(entity, object);
            }

            // 这一列为空数据的话就不用加入到dataList中
            if(entity == null)
            {
                continue;
            }

            dataList.add(entity);
        }
    }

    private static void setCellType(CellType cellType, XSSFCell cell) {
        if(cellType != CellType.STRING)
        {
            cell.setCellType(CellType.STRING);
        }
    }

    private static <T> @NotNull T getEntity(Class<T> clazz, T entity) throws InstantiationException, IllegalAccessException {
        if(entity == null)
        {
            entity = clazz.newInstance();
        }
        return entity;
    }

    /**
     * 数据写入Excel
     *
     * @param dataList 待写入的数据
     * @param clazz    写入类型（主要获取表格标题）
     * @param <T>
     * @throws Exception
     */
    public static <T> void writeToExcel(String fileName, Collection<T> dataList, Class<T> clazz, HttpServletResponse response) throws Exception
    {
        long timestamp = Date2Util.timestamp(true);

        // 创建一个webbook，对应一个Excel文件
        // SXSSFWorkbook workbook = new SXSSFWorkbook(100);
        SXSSFWorkbook workbook = new SXSSFWorkbook(100);

        // 压缩临时文件
        workbook.setCompressTempFiles(true);

        // 设置作者等信息

        // 在webbook中添加一个sheet,对应Excel文件中的sheet
        SXSSFSheet sheet = workbook.createSheet("Sheet1");

        // 内容行样式
        CellStyle style = getCellStyle(workbook);

        // 设置默认宽
        // sheet.setDefaultColumnWidth(COLUMN_WIDTH);
        // sheet.setDefaultRowHeightInPoints(COLUMN_HEIGHT);

        // 将标题写入excel中
        SXSSFRow xssRow = sheet.createRow(0);
        Map<Integer, Field> fieldsMap = getFieldsMap(clazz);
        for(Map.Entry<Integer, Field> entry : fieldsMap.entrySet())
        {
            int index = entry.getKey();
            Field field = entry.getValue();
            String title = field.getAnnotation(Excel.class).title();
            title = "".equals(title) ? field.getName() : title;

            SXSSFCell cell = xssRow.createCell(index);
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            // 写数据
            cell.setCellValue(title);
        }

        // 0号位被标题占用
        int idx = 1;
        // 遍历集合数据，产生数据行
        for(T t : dataList)
        {
            SXSSFRow xssfRow = sheet.createRow(idx);

            // 利用反射，根据传过来的字段名数组，动态调用对应的getXxx()方法得到属性值
            for(Map.Entry<Integer, Field> entry : fieldsMap.entrySet())
            {
                int index = entry.getKey();
                Field field = entry.getValue();
                String fieldName = field.getName();

                // 取得对应getXxx()方法
                String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                // 通过方法名得到对应的方法
                Method method = t.getClass().getMethod(methodName);
                // 反射取值 执行实例化后的object里面的method方法
                Object object = method.invoke(t);
                if(object == null)
                {
                    continue;
                }

                SXSSFCell cell = xssfRow.createCell(index);
                cell.setCellStyle(style);
                cell.setCellType(CellType.STRING);
                // 写数据
                cell.setCellValue(objectToString(object, field.getAnnotation(Excel.class).dateFormat()));
            }

            idx++;
        }

        response.setHeader("Content-Type", "application/octet-stream;charset=utf-8");
        // response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + ".xlsx\"");
        response.setHeader("Content-Disposition", fileName + ".xlsx");

        OutputStream stream = response.getOutputStream();

        workbook.write(stream);
        // 删除临时文件
        workbook.dispose();
        workbook.close();

        logger.info("生成Excel: {}, 耗时: {}ms", fileName, Date2Util.timestamp(true) - timestamp);

        stream.flush();
        stream.close();
    }

    /**
     * 获取带有@Excel注解的字段
     *
     * @param clazz
     * @return
     */
    private static Map<Integer, Field> getFieldsMap(Class<?> clazz)
    {
        Field[] fields = clazz.getDeclaredFields();

        // TreeMap 默认按字典升序排序
        Map<Integer, Field> fieldsMap = new TreeMap<>();
        for(Field field : fields)
        {
            Excel excel = field.getAnnotation(Excel.class);
            if(excel == null)
            {
                continue;
            }

            // 这里减一是因为序号是从一开始的
            fieldsMap.put(excel.index() - 1, field);
        }

        return fieldsMap;
    }

    /**
     * String数据转Object
     *
     * @param field
     * @param str
     * @return
     */
    private static Object stringToObject(Field field, String str)
    {
        if(StringUtils.isEmpty(str))
        {
            return null;
        }

        Class<?> fieldType = field.getType();
        if(field.getAnnotation(Excel.class).isDate())
        {
            return getFormatDate(str);
        }

        if(Integer.TYPE == fieldType || Integer.class == fieldType)
        {
            return Integer.valueOf(str);
        }

        if(Long.TYPE == fieldType || Long.class == fieldType)
        {
            return Long.valueOf(str);
        }

        if(Double.TYPE == fieldType || Double.class == fieldType)
        {
            return Double.valueOf(str);
        }

        if(Float.TYPE == fieldType || Float.class == fieldType)
        {
            return Float.valueOf(str);
        }

        if(Boolean.TYPE == fieldType || Boolean.class == fieldType)
        {
            return Boolean.valueOf(str);
        }

        if(BigDecimal.class == fieldType)
        {
            return new BigDecimal(str);
        }

        return str;
    }

    /**
     * Object数据转String
     *
     * @param object
     * @param format
     * @return
     */
    private static String objectToString(Object object, String format)
    {
        if(object instanceof BigDecimal)
        {
            return ((BigDecimal) object).toPlainString();
        }

        if(object instanceof Date)
        {
            return Date2Util.format(((Date) object).getTime(), format, true);
        }

        return object.toString();
    }

    /**
     * 设置字体样式
     *
     * @param workbook
     * @return
     */
    private static CellStyle getCellStyle(SXSSFWorkbook workbook)
    {
        // 设置字体
        Font font = workbook.createFont();
        // 文字字体
        font.setFontName(FONT_NAME);
        // 文字大小
        font.setFontHeightInPoints(FONT_SIZE);

        // 设置样式
        CellStyle style = workbook.createCellStyle();
        style.setFont(font);
        // 设置单元格格式为"文本"
        style.setDataFormat(workbook.createDataFormat().getFormat("@"));

        return style;
    }

    /**
     * 格式化非常规日期
     *
     * @param date
     * @return
     */
    private static String getFormatDate(String date)
    {
        if(String2Util.isDate(date))
        {
            return date;
        }

        if(String2Util.isNumber(date))
        {
            Date date1 = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(Double.parseDouble(date));
            return Date2Util.format(date1.getTime(), Date2Util.FORMAT_DATE, true);
        }


        try
        {
            date = date.replace('.', '-').replace('/', '-');
            long timestamp = Date2Util.parse(date, "yyyy-M-d");
            date = Date2Util.format(timestamp, Date2Util.FORMAT_DATE);
        }
        catch(Exception ignored)
        {
        }

        return date;
    }
}
