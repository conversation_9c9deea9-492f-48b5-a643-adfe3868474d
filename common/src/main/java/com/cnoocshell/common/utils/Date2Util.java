package com.cnoocshell.common.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日期处理类
 *
 * @Author: <EMAIL>
 */
public class Date2Util
{
    /**
     * 示例：2020-10-21
     */
    public final static String FORMAT_DATE = "yyyy-MM-dd";

    /**
     * 示例：2020-10-21 12:00:00
     */
    public final static String FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * 示例：2020-10-21 12:00:00.678
     */
    public final static String FORMAT_DATETIME_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * rfc3339 示例：2021-01-08T10:38:47+08:00
     */
    public final static String FORMAT_DATETIME_RFC3339 = "yyyy-MM-dd'T'HH:mm:ssXXX";

    /**
     * 时间时区 示例：Asia/Hong_Kong
     *
     * @link
     */
    private final static String TIMEZONE = "Asia/Hong_Kong";

    /**
     * DateTimeFormatter存储器
     */
    private final static Map<String, DateTimeFormatter> DATETIME_FORMATTER = new HashMap<>();

    private Date2Util()
    {
    }

    /**
     * 获取当前时间戳 单位：秒
     *
     * @return 时间戳
     */
    public static Long timestamp()
    {
        return timestamp(false);
    }

    /**
     * 获取当前时间戳 单位：毫秒
     *
     * @param isMillis true：返回毫秒级的时间戳，false：返回秒级的时间戳
     * @return 时间戳
     */
    public static Long timestamp(boolean isMillis)
    {
        return isMillis ? System.currentTimeMillis() : System.currentTimeMillis() / 1000;
    }

    /**
     * 获取今天凌晨的时间戳 单位：秒
     *
     * @return 时间戳
     */
    public static Long todayTimestamp()
    {
        return parse(format(Date2Util.FORMAT_DATE), Date2Util.FORMAT_DATE);
    }

    /**
     * 格式化时间
     *
     * @param timestamp 时间戳 单位：秒
     * @return 格式化时间
     */
    public static String format(long timestamp)
    {
        return format(timestamp, FORMAT_DATETIME);
    }

    /**
     * 格式化时间
     *
     * @param format 时间格式
     * @return 格式化时间
     */
    public static String format(String format)
    {
        return format(timestamp(true), format, true);
    }

    /**
     * 格式化时间
     *
     * @param date 时间
     * @return 格式化时间
     */
    public static String format(Date date)
    {
        return format(date.getTime(), FORMAT_DATETIME, true);
    }

    /**
     * 格式化时间
     *
     * @param date   时间
     * @param format 时间格式
     * @return 格式化时间
     */
    public static String format(Date date, String format)
    {
        return format(date.getTime(), format, true);
    }

    /**
     * 格式化时间
     *
     * @param timestamp 时间戳 单位：秒
     * @param format    时间格式
     * @return 格式化时间
     */
    public static String format(long timestamp, String format)
    {
        return format(timestamp, format, false);
    }

    /**
     * 格式化时间
     *
     * @param timestamp 时间戳
     * @param format    时间格式
     * @param isMillis  时间单位是否为毫秒 true: 是、flase: 否
     * @return 格式化时间
     */
    public static String format(long timestamp, String format, boolean isMillis)
    {
        Instant instant = isMillis ? Instant.ofEpochMilli(timestamp) : Instant.ofEpochSecond(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.of(TIMEZONE)).format(getFormatter(format));
    }

    /**
     * 时间转时间戳
     *
     * @param datetime 格式化时间
     * @return 时间戳
     */
    public static Long parse(String datetime)
    {
        return parse(datetime, FORMAT_DATETIME);
    }

    /**
     * 时间转时间戳
     *
     * @param datetime 格式化时间
     * @param format   时间格式
     * @return 时间戳
     */
    public static Long parse(String datetime, String format)
    {
        return LocalDateTime.parse(datetime, getFormatter(format)).atZone(ZoneId.of(TIMEZONE)).toEpochSecond();
    }

    /**
     * 获取月份的最后一天
     *
     * @param year  年
     * @param month 月
     * @return 最后一天的day
     */
    public static int getMonthLastDayInMonth(int year, int month)
    {
        return LocalDateTime.of(year, month, 1, 0, 0).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
    }

    /**
     * 获取本月的最后一天的日期
     *
     * @return 最后一天的day
     */
    public static int getLastDayCurrentMonth()
    {
        // TemporalAdjusters.firstDayOfMonth()
        // TemporalAdjusters.lastDayOfMonth()
        return LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
    }

    /**
     * 获取两个时间区间的每一个时间
     *
     * @param startDate 开始时间 格式：yyyy-MM-dd
     * @param endDate   结束时间 格式：yyyy-MM-dd
     * @return 日期列表
     */
    public static List<String> getBetweenDates(String startDate, String endDate)
    {
        DateTimeFormatter formatter = getFormatter(FORMAT_DATE);
        LocalDateTime localStartDate = LocalDateTime.parse(startDate, formatter);
        LocalDateTime localEndDate = LocalDateTime.parse(endDate, formatter);

        List<String> result = new ArrayList<>();
        while(localStartDate.compareTo(localEndDate) <= 0)
        {
            result.add(localStartDate.format(formatter));
            localStartDate = localStartDate.plusDays(1);
        }

        return result;
    }

    /**
     * 时区转换
     *
     * @param datetime 格式化的时间
     * @param fromZone 旧时区
     * @param toZone   新时区
     * @param format   时间格式
     * @return 新时区时间
     */
    public static String switchTimezone(String datetime, String fromZone, String toZone, String format)
    {
        DateTimeFormatter formatter = getFormatter(format);
        return LocalDateTime.parse(datetime, formatter).atZone(ZoneId.of(fromZone)).withZoneSameInstant(ZoneId.of(toZone)).format(formatter);
    }

    /**
     * 获取时间格式话的DateTimeFormatter对象
     *
     * @param format
     * @return 格式化对象
     */
    private static DateTimeFormatter getFormatter(String format)
    {
        // DateTimeFormatter.ofPattern(format);

        DateTimeFormatter formatter = DATETIME_FORMATTER.get(format);
        if(formatter == null)
        {
            synchronized(Date2Util.class)
            {
                if(formatter == null)
                {
                    formatter = new DateTimeFormatterBuilder().appendPattern(format).parseDefaulting(ChronoField.HOUR_OF_DAY, 0).parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0).parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0).toFormatter();
                    DATETIME_FORMATTER.put(format, formatter);
                }
            }
        }
        return formatter;
    }
}
