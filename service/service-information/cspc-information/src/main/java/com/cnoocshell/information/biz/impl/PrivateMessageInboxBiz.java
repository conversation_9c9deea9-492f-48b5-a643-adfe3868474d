package com.cnoocshell.information.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.information.api.constant.InfoColumnConstant;
import com.cnoocshell.information.api.constant.InfoNumberConstant;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageDeleteDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageQueryDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageReadStatusUpdateDTO;
import com.cnoocshell.information.biz.IPrivateMessageInboxBiz;
import com.cnoocshell.information.dao.mapper.PrivateMessageInboxMapper;
import com.cnoocshell.information.dao.vo.PrivateMessageInbox;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class PrivateMessageInboxBiz extends BaseBiz<PrivateMessageInbox> implements IPrivateMessageInboxBiz {

    @Autowired
    private PrivateMessageInboxMapper privateMessageInboxMapper;



    @Override
    public List<PrivateMessageInbox> batchInsert(List<PrivateMessageInbox> t) {
        privateMessageInboxMapper.insertList(t);
        return t;
    }

    @Override
    public int countByAccountId(String accountId) {
        Condition condition = newCondition();
        condition.createCriteria().andEqualTo(InfoColumnConstant.DEL_FLG,0).andEqualTo(InfoColumnConstant.RECEIVE_USER_ID,accountId).andEqualTo("status",0);
        return privateMessageInboxMapper.selectCountByCondition(condition);
    }

    @Override
    public Boolean delete(InternalMessageDeleteDTO internalMessageDeleteDTO) {
        if( CollectionUtils.isEmpty(internalMessageDeleteDTO.getIds()) ){
            return false;
        }
        if(StringUtils.isBlank(internalMessageDeleteDTO.getMemberId())){
            throw new BizException(BasicCode.INVALID_PARAM,":memberId不可为空");
        }
        if(StringUtils.isBlank(internalMessageDeleteDTO.getAccountId())){
            throw new BizException(BasicCode.INVALID_PARAM,":accountId不可为空");
        }
        Condition condition = newCondition();
        Example.Criteria criteria = condition.createCriteria().andEqualTo(InfoColumnConstant.DEL_FLG,0)
                .andEqualTo(InfoColumnConstant.RECEIVE_MEMBER_ID,internalMessageDeleteDTO.getMemberId())
                .andEqualTo(InfoColumnConstant.RECEIVE_USER_ID,internalMessageDeleteDTO.getAccountId());
        if( internalMessageDeleteDTO.getIds().size() == 1 ){
            criteria.andEqualTo("id",internalMessageDeleteDTO.getIds().get(0));
        }else{
            criteria.andIn("id",internalMessageDeleteDTO.getIds());
        }
        PrivateMessageInbox privateMessageInbox = new PrivateMessageInbox();
        privateMessageInbox.setDelFlg(Boolean.TRUE);
        privateMessageInbox.setUpdateTime(new Date());
        privateMessageInbox.setUpdateUser(internalMessageDeleteDTO.getOperator());
        privateMessageInboxMapper.updateByConditionSelective(privateMessageInbox,condition);
        //content表内容不能删除，可能有其他人还需要
        return true;
    }

    @Override
    public Boolean updateReadStatus(InternalMessageReadStatusUpdateDTO param) {
        if (CollUtil.isEmpty(param.getIds()) && !BooleanUtil.isTrue(param.getAllRead()))
            return false;
        if (CharSequenceUtil.isBlank(param.getMemberId()) || CharSequenceUtil.isBlank(param.getAccountId()))
            return false;

        Condition condition = newCondition();
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(InfoColumnConstant.DEL_FLG, InfoNumberConstant.ZERO_INTEGER)
                .andEqualTo(InfoColumnConstant.RECEIVE_MEMBER_ID, param.getMemberId())
                .andEqualTo(InfoColumnConstant.RECEIVE_USER_ID, param.getAccountId())
                .andEqualTo(InfoColumnConstant.STATUS, InfoNumberConstant.ZERO_INTEGER);
        if (!BooleanUtil.isTrue(param.getAllRead())) {
            if (Objects.equals(InfoNumberConstant.ONE_INTEGER, CollUtil.size(param.getIds()))) {
                criteria.andEqualTo(InfoColumnConstant.ID, CollUtil.getFirst(param.getIds()));
            } else {
                criteria.andIn(InfoColumnConstant.ID, param.getIds());
            }
        }
        PrivateMessageInbox privateMessageInbox = new PrivateMessageInbox();
        //已读
        privateMessageInbox.setStatus(InfoNumberConstant.ONE_INTEGER);
        privateMessageInbox.setReadTime(new Date());
        privateMessageInbox.setUpdateTime(new Date());
        privateMessageInbox.setUpdateUser(param.getOperator());
        return privateMessageInboxMapper.updateByConditionSelective(privateMessageInbox, condition) > 0;
    }

    @Override
    public PageInfo<PrivateMessageInbox> findAll(InternalMessageQueryDTO internalMessageQueryDTO) {
        if( StringUtils.isBlank(internalMessageQueryDTO.getAccountId()) && StringUtils.isBlank(internalMessageQueryDTO.getMemberId()) ){
            throw new BizException(BasicCode.INVALID_PARAM,":accountId,memberId不能都为空");
        }
        Condition condition = newCondition();
        Example.Criteria criteria = condition.createCriteria().andEqualTo(InfoColumnConstant.DEL_FLG,0);
        if( StringUtils.isNotBlank(internalMessageQueryDTO.getAccountId()) ){
            criteria.andEqualTo(InfoColumnConstant.RECEIVE_USER_ID,internalMessageQueryDTO.getAccountId());
        }
        if( StringUtils.isNotBlank(internalMessageQueryDTO.getMemberId()) ){
            criteria.andEqualTo(InfoColumnConstant.RECEIVE_MEMBER_ID,internalMessageQueryDTO.getMemberId());
        }
        if( StringUtils.isNotBlank(internalMessageQueryDTO.getTitle()) ){
            criteria.andLike("title","%"+internalMessageQueryDTO.getTitle()+"%");
        }
        if( internalMessageQueryDTO.getStatus() != null ){
            criteria.andEqualTo("status",internalMessageQueryDTO.getStatus() );
        }
        condition.orderBy("createTime").desc();
        condition.orderBy(InfoColumnConstant.ID).desc();

        log.info("分页查询站内信 num:{} size:{}",internalMessageQueryDTO.getPageNum(),internalMessageQueryDTO.getPageSize());
        return PageMethod
                .startPage(internalMessageQueryDTO.getPageNum(),internalMessageQueryDTO.getPageSize())
                .doSelectPageInfo(()-> privateMessageInboxMapper.selectByCondition(condition));
    }
}
