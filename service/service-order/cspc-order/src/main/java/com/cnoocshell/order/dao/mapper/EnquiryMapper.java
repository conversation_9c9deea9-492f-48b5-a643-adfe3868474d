package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.EnquiryDTO;
import com.cnoocshell.order.api.dto.GoodsSimpleDTO;
import com.cnoocshell.order.api.dto.analysis.EnquiryAnalysisInfoDTO;
import com.cnoocshell.order.dao.vo.Enquiry;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface EnquiryMapper extends IBaseMapper<Enquiry> {

    List<Enquiry> selectList(EnquiryDTO enquiryDTO);

    List<Enquiry> buyerSelectList(EnquiryDTO enquiryDTO);

    List<Enquiry> selectStartList();

    List<Enquiry> selectEndList();

    void updateStatusByIds(List<String> ids, String status);

    void batchUpdatePrice(List<Enquiry> enquiryList);


    List<GoodsSimpleDTO> selectGoodsSimpleList(String memberCode);

    Integer selectEnquiryTimesByMemberCode(String memberCode, List<String> goodsCodes);

    Integer selectEnquiryBySaleId(String planId);

    @Update("UPDATE enquiry set cancel_bidding_notify_flag=#{cancelBiddingNotifyFlag} where enquiry_no=#{enquiryNo}")
    Integer updateCancelBiddingNotifyFlag(@Param("enquiryNo") String enquiryNo, @Param("cancelBiddingNotifyFlag") Boolean cancelBiddingNotifyFlag);

    @Update("UPDATE enquiry set del_flg=1 where id=#{id}")
    int removeEnquiryById(@Param("id") String id);

    List<EnquiryAnalysisInfoDTO> queryEnquiryInfo(@Param("enquiryNo")String enquiryNo);
}
