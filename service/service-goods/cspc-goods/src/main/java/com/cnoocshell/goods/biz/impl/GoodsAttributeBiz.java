package com.cnoocshell.goods.biz.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.utils.CommonConstants;
import com.cnoocshell.goods.biz.IGoodsAttributeBiz;
import com.cnoocshell.goods.dao.vo.GoodsAttribute;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 
 * @Author: <EMAIL>
 * @Description  GoodsAttributeBiz
 * @date   2018年8月15日 下午4:56:00
 */
@Service
public class GoodsAttributeBiz extends BaseBiz<GoodsAttribute> implements IGoodsAttributeBiz {

    private static final String DEL_FLG = "delFlg";
    private static final String ATTRI_NAME = "attriName";

    @Override
    public List<GoodsAttribute> findByAttrNameAndValueType(String attrName, int valueType) {
        Condition goodsAttributeCondition = new Condition(GoodsAttribute.class);
        Example.Criteria goodsAttributeConditionCriteria = goodsAttributeCondition.createCriteria();
        goodsAttributeConditionCriteria.andEqualTo(ATTRI_NAME,attrName);
        goodsAttributeConditionCriteria.andEqualTo("valueType", valueType);
        goodsAttributeConditionCriteria.andEqualTo(DEL_FLG,false);
        return getMapper().selectByCondition(goodsAttributeCondition);
    }

    @Override
    public List<GoodsAttribute> findByAttrNameLike(String attrName) {
        Condition condition = new Condition(GoodsAttribute.class);
        Example.Criteria criteria = condition.createCriteria();
        if(!StringUtils.isBlank(attrName)){
            criteria.andLike(ATTRI_NAME, "%" + attrName + "%");
        }
        criteria.andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        condition.orderBy(ATTRI_NAME).asc();
        return getMapper().selectByCondition(condition);
    }

    @Override
    public GoodsAttribute findByAttrName(String attrName) {
        Condition condition = new Condition(GoodsAttribute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(ATTRI_NAME, attrName);
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        List<GoodsAttribute> list = getMapper().selectByCondition(condition);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public GoodsAttribute findByAttrNameAndAttrType(String attrName,int attrType) {
        Condition condition = new Condition(GoodsAttribute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(ATTRI_NAME, attrName);
        criteria.andEqualTo("attriType", attrType);
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        List<GoodsAttribute> list = getMapper().selectByCondition(condition);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
}
