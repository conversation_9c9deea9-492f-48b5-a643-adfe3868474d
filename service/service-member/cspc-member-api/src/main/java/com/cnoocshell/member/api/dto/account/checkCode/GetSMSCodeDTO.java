package com.cnoocshell.member.api.dto.account.checkCode;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import javax.validation.constraints.NotBlank;

import java.util.regex.Pattern;

/**
 * @DESCRIPTION:
 */
@Data
public class GetSMSCodeDTO {

    /**
     * 宽松校验，可能以后会新增号段
     */
    @ApiModelProperty("宽松校验，可能以后会新增号段")
    private static final String PHONE_NUMBER_REG = "^1\\d{10}$";

    /**
     * 注册
     */
    @ApiModelProperty("注册")
    public static final String USE_REGISTER = "register";

    /**
     * 修改手机
     */
    @ApiModelProperty("修改手机")
    public static final String USE_MODIFY_MOBILE = "modify_mobile";

    /**
     * 登录
     */
    @ApiModelProperty("登录")
    public static final String USE_LOGIN_PC = "login_pc";

    /**
     * 默认
     */
    @ApiModelProperty("默认")
    public static final String USE_DEFAULT = "default";

    /**
     * 用途
     */
    @NotBlank
    @ApiModelProperty("用途")
    private String use;

    /**
     * 手机号码
     */
    @NotBlank
    @ApiModelProperty("要收验证码的手机")
    private String mobile;

    /**
     * 是否校验手机号
     */
    @ApiModelProperty("是否需要校验手机号在数据库中，如果在才发送验证码")
    private Boolean checkMobile = Boolean.TRUE;

    /**
     * 会话id
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的前端sessionId")
    private String sessionId="sessionId";

    /**
     * 签名
     */
    @ApiModelProperty("专用签名，如果有则使用")
    private String sign;

    /**
     * 签名，优先级低于sign
     */
    @ApiModelProperty("专用签名，根据账户ID获取其专用签名，如果有则使用，优先级低于sign")
    private String signAccountId;

    /**
     * 是否需要校验图形验证码
     */
    @ApiModelProperty("发送短信验证码是否需要校验图形验证码，默认true")
    private Boolean checkCaptcha = Boolean.TRUE;

    /**
     * 图形验证码
     */
    @ApiModelProperty("图形验证码")
    private String captcha;

    /**
     * 是否需要清除该图形验证码
     */
    @ApiModelProperty("是否需要清除该图形验证码")
    private Boolean deleteCaptcha = Boolean.FALSE;

    public GetSMSCodeDTO(){}

    public GetSMSCodeDTO(String use,String mobile,String sessionId,String signAccountId){
        this(use,mobile,sessionId,signAccountId,true,null,null,false,false);
    }

    public GetSMSCodeDTO(String use,String mobile,String sessionId,String signAccountId,Boolean checkMobile,String sign,String captcha){
        this(use,mobile,sessionId,signAccountId,checkMobile,sign,captcha,true,false);
    }

    public GetSMSCodeDTO(String use,String mobile,String sessionId,String signAccountId,Boolean checkMobile,String sign,String captcha,Boolean checkCaptcha,Boolean deleteCaptcha){
        if( StringUtils.isBlank(mobile) || !Pattern.matches(PHONE_NUMBER_REG,mobile)){
            throw new BizException(MemberCode.VALIDATION_ERROR,"手机号不正确");
        }
        this.use = use;
        this.mobile = mobile;
        this.checkMobile = checkMobile == null || checkMobile;
        this.sessionId = sessionId;
        this.sign = sign;
        this.signAccountId = signAccountId;
        this.checkCaptcha = StringUtils.isNotBlank(captcha) || (checkCaptcha != null && checkCaptcha);
        this.captcha = captcha;
        this.deleteCaptcha = deleteCaptcha != null && deleteCaptcha;
    }



    public CheckCaptchaDTO getCheckCaptchaDTO(){
        CheckCaptchaDTO checkCaptchaDTO = new CheckCaptchaDTO();
        checkCaptchaDTO.setUse(use);
        checkCaptchaDTO.setDeleteCaptcha(deleteCaptcha);
        checkCaptchaDTO.setCaptcha(captcha);
        checkCaptchaDTO.setSessionId(sessionId);
        return checkCaptchaDTO;
    }

}
