


package com.cnoocshell.base.api.dto.role;

import com.cnoocshell.base.api.dto.authRes.ButtonDTO;
import com.cnoocshell.base.api.dto.authRes.MenuDTO;
import com.cnoocshell.base.api.dto.authRes.PageDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RoleDTO implements Serializable{
	/**
	 * 角色id
	 */
	@ApiModelProperty("角色id")
	private Integer roleId;

	@ApiModelProperty("角色代码")
	private String roleCode;

	@ApiModelProperty("角色名称")
	private String roleName;

	/**
	 * 角色名称
	 */
	@ApiModelProperty("角色描述")
	private String roleInfo;
	/**
	 * 使用范围
	 */
	@ApiModelProperty("使用范围")
	private String roleRange;
	/**
	 * 角色类型
	 */
	@ApiModelProperty("角色类型,取值集JZ0003")
	private String roleType;

	@ApiModelProperty("角色类型名称（仅显示）")
	private String roleTypeName;

	/**
	 * 角色描述
	 */
	@ApiModelProperty("角色描述")
	private String description;

	/**
	 * 是否系统内置角色
	 */
	@ApiModelProperty("是否系统内置角色")
	private Integer isSys;

	@ApiModelProperty("是否会员默认角色")
	private Integer isMemberDefault;
	/**
	 * 启用状态
	 */
	@ApiModelProperty("启用状态 0禁用 1启用")
	private Integer status;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人id")
	private String createUser;

	@ApiModelProperty("创建人名称")
	private String createUserName;

	/**
	 * 创建人所在会员id
	 */
	@ApiModelProperty("创建人所在会员id")
	private String createMemberId;

	@ApiModelProperty("创建人所在会员名称")
	private String createMemberName ;

	@ApiModelProperty("创建时间")
	private Date createTime;
	@ApiModelProperty("修改人id")
	private String updateUser;
	@ApiModelProperty("修改人名称")
	private String updateUserName;
	@ApiModelProperty("修改时间")
	private Date updateTime;


	//---------附加字段,其它操作使用
	/**
	 * 会员id
	 */
	@ApiModelProperty("会员id")
	private String memberId;
    @ApiModelProperty("会员名称")
	private String memberName;
	@ApiModelProperty("会员简称")
	private String memberShortName;
    @ApiModelProperty("会员代码")
	private String memberCode;

	/**
	 * 账号id
	 */
	@ApiModelProperty("账号id")
	private String accountId;
	@ApiModelProperty("账号名称")
	private String accountName;
    @ApiModelProperty("账号代码")
	private String accountCode;

	/**
	 * 是否选择
	 */
	@ApiModelProperty("是否选择 会员/账户授权时使用")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean selected;

	/**
	 * 角色资质DTO的List
	 */
	@ApiModelProperty("角色资质DTO的List")
	private List<RoleCertDTO> roleCertList;

	/**
	 * 角色拥有的菜单
	 */
	@ApiModelProperty("角色拥有的菜单code集合")
	private List<String> menuCodeList;
    @ApiModelProperty("角色拥有的菜单")
	private List<MenuDTO> menuList;

	/**
	 * 角色拥有的页面
	 */
	@ApiModelProperty("菜单对应的所有页面 + 页面的子页面 + 没有绑定菜单的页面 code集合")
	private List<String> pageCodeList;
    @ApiModelProperty("菜单对应的所有页面 + 页面的子页面 + 没有绑定菜单的页面")
	private List<PageDTO> pageList;

	/**
	 * 角色拥有的按钮
	 */
	@ApiModelProperty("角色拥有的按钮")
	private List<String> btnCodeList;
    @ApiModelProperty("角色拥有的按钮")
	private List<ButtonDTO> buttonList;
}
