package com.cnoocshell.base.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "ba_valueset_option")
@EqualsAndHashCode(callSuper = true)
public class ValueSetOption extends BaseEntity {
    @Id
    @Column(name = "option_id")
    private String optionId;

    /**
     * 值集id
     */
    @Column(name = "id")
    private String id;

    /**
     * option_info
     */
    @Column(name = "option_key")
    private String optionKey;

    @Column(name = "option_value")
    private String optionValue;

    /**
     * 值集简介
     */
    @Column(name = "option_info")
    private String optionInfo;

    /**
     * 顺序
     */
    @Column(name = "option_order")
    private Integer optionOrder;

    @Column(name = "parent_id")
    private String parentId;
}