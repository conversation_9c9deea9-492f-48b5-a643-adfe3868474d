package com.cnoocshell.web.base.util;

import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * @DESCRIPTION:
 */
public class IPUtils {

    private static final String UNKNOWN = "unknown";

    public static String getIpAddr(HttpServletRequest request){
        if (request == null) {
            return UNKNOWN;
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }

        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if( ip == null ){
            return null;
        }
        int idx = ip.indexOf(",");
        return idx == -1 ? ip : ip.substring(0,idx);
    }

    public static String getHeaderUserAgent(HttpServletRequest request) {
        String header = request.getHeader("user-agent");
        return StringUtils.isBlank( header )  ? "" : header ;
    }

    public static String getMac(HttpServletRequest request){
        //TODO 没有找到适用的方法
        
        return null;
    }
}
