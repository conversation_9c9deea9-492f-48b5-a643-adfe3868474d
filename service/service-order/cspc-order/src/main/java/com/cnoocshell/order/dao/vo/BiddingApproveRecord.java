package com.cnoocshell.order.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "bidding_approve_record")
public class BiddingApproveRecord extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "bidding_no")
    private String biddingNo;

    @Column(name = "approve_type")
    private String approveType;

    @Column(name = "approve_result")
    private String approveResult;


    /**
     *拒绝原因原因/审批通过备注
     * CR 2025.5.15 审批通过填写备注 共用原因字段
     */
    @Column(name = "rejected_reason")
    private String rejectedReason;

    @Column(name = "create_user_name")
    private String createUserName;

}