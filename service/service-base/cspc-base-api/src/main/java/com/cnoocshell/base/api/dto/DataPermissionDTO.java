package com.cnoocshell.base.api.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类和账户关系表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataPermissionDTO {
    private String memberId;

    private String categoryId;

    private String categoryCode;

    private String categoryName;

    private String accountId;

    private String accountRealName;

    private String goodsCode;

    private String goodsName;
}
