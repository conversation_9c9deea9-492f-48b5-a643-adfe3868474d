package com.cnoocshell.goods.biz.message;

import com.cnoocshell.common.exception.DistributeLockException;
import com.cnoocshell.common.service.ILockService;
import com.cnoocshell.goods.api.dto.externalexception.ExternalExceptionRetryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * @Description: RedisKeyExpirationListener
 * @Author: <EMAIL>
 * @Date: 2021-05-19 10:54
 */
@Slf4j
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    public static final String ERP_EXCEPTION = "ERP_EXCEPTION_GOODS";

    @Autowired
    private ILockService redisLockService;



    public RedisKeyExpirationListener(@Qualifier("expiredRedisMessageListenerContainer") RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        //此处业务处理即可,注意message.toString()可以获取失效的key
        String expiredKey = message.toString();
        if (expiredKey.startsWith(ERP_EXCEPTION)) {
            String identifier = null;
            try {
                identifier = redisLockService.lockFast(expiredKey);
                log.info("发起异常重试：" + expiredKey);
                String[] keyStr = StringUtils.split(expiredKey, ":");
                if (keyStr != null && keyStr.length > 1) {
                    ExternalExceptionRetryDTO externalExceptionRetryDTO = new ExternalExceptionRetryDTO();
                    externalExceptionRetryDTO.setExternalExceptionHandleId(keyStr[1]);
                    externalExceptionRetryDTO.setOperatorUserId("system");
                    externalExceptionRetryDTO.setOperatorUserName("system");
                }
            } catch (DistributeLockException e) {
                log.info("自动异常重试已在另一台负载发起,此次忽略:{},{}", expiredKey, e.getMessage());
            } finally {
                if (StringUtils.isNotBlank(identifier)) {
                    redisLockService.unlock(expiredKey, identifier);
                }
            }
        }

    }
}
