package com.cnoocshell.order.biz;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisResultDTO;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingEnquiryDataDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.enums.BiddingStatusEnum;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyer;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IBiddingBiz extends IBaseBiz<Bidding> {


    /**
     * 获取大于thanDate竞价开始时间的待开始的竞价数据
     *
     * @param startDateTime 竞价开始时间大于的这个时间
     * @param endDateTime   竞价结束时间小于的这个时间
     */
    List<SimpleBiddingDTO> getWaitStartBidding(Date startDateTime, Date endDateTime);

    BiddingCreateReturnDTO batchCreateBidding(BiddingCreateBiddingDTO biddingCreateBiddingDTO, int dataFrom);

    BiddingCreateReturnDTO singleCreateBidding(BiddingCreateBiddingDTO biddingCreateBiddingDTO, int dataFrom);

    BiddingQueryCreateDataDTO queryCreateBiddingData(String id);

    List<BiddingCustomerListDTO> queryCustomerInfoList(BiddingCustomerInfoDTO dto);

    PageInfo<BiddingSalesListViewDTO> salesBiddingList(BiddingSalesListDTO biddingSalesListDTO, int DataFrom);

    List<BiddingCustomerListDTO> queryEditQueryData(String biddingNo);

    Bidding getByNo(String biddingNo);

    int updateBiddingStatus(String biddingNo, BiddingStatusEnum status);

    void createJob(Bidding bidding);

    Boolean submitSingleData(BiddingCreateBiddingSaveDTO dto);

    Boolean submitStrategyData(BiddingSubmitStrategySaveDTO dto);

    PageInfo<BiddingSalesListViewDTO> queryApproveList(BiddingSalesListDTO dto);

    ItemResult<Boolean> batchApprovalOrRejected(BiddingApprovalDTO dto);

    Boolean singleWithdrawData(String bidding);

    BiddingStrategyViewDTO queryStrategy(String bidding);

    List<Bidding> listByIds(Collection<String> ids);


    /**
     * @param status      竞价状态
     * @param operatorId  操作人
     * @param biddingList 竞价数据
     * @param isDeal      是否确认成交操作
     */
    void updateBiddingStatusByIds(BiddingStatusEnum status, String operatorId, List<Bidding> biddingList, boolean isDeal);

    List<Bidding> listBySalePlanNo(List<String> salePlanNos, List<BiddingStatusEnum> status);


    void notifyByNotDeal(Bidding bidding, List<BiddingBuyerDetail> buyerDetails);

    PageInfo<BiddingBuyerListViewDTO> buyerBiddingList(BiddingBuyerQueryDTO biddingBuyerListDTO);

    ItemResult<PageInfo<ExportBiddingEnquiryDataDTO>> queryBiddingEnquiryDataList(ExportBiddingEnquiryDataDTO queryCondition);

    List<BiddingStrategyDetailExportExcelDTO> exportExcel(BiddingExportDataDTO biddingExportDataDTO);

    ItemResult<ExportExcelDTO> exportBiddingEnquiryDataList(ExportBiddingEnquiryDataDTO queryCondition);

    ItemResult<PageInfo<ExportSellNumDataDTO>> querySellNumDataList(ExportSellNumDataDTO queryCondition);

    ItemResult<ExportExcelDTO> exportSellNumDataList(ExportSellNumDataDTO exportSellNumDataDTO);

    ItemResult<String> importExcel(BiddingImportExcelInfoDTO dto);

    List<String> buyerGoodsBiddingList(BiddingBuyerGoodsDTO dto);

    Integer queryApproveNumberList(BiddingSalesListDTO dto);

    Boolean judgeBool(String memberCode);


    /**
     * 通知可参与竞价客户竞价开始
     */
    void notifyBuyerStartBidding(List<Bidding> biddingList, List<BiddingBuyer> buyers);

    BiddingInfoDTO queryBiddingInfo(String id);

    /**
     * 创建SAP合同
     *
     * @param biddingIds          竞价ID列表
     * @param dealNos             成交编号列表
     * @param isCreateSapContract 是否创建SAP合同
     * @return 返回创建的SAP合同DTO列表
     */
    List<SapContractDTO> createSapContract(List<String> biddingIds, List<String> dealNos, boolean isCreateSapContract);

    /**
     * 竞价列表勾选数据查询接口
     * @param dto
     * @return
     */
    List<BiddingQueryCheckDataExcelDTO> queryBiddingCheckData(BiddingListCheckDataDTO dto);

    void updateOverDealRemark(String remark,String id);

    /**
     * 获取提货开始时间
     * 若竞价开始时间（天比较） >= 销售计划提货开始时间（天比较）则竞价上提货开始时间为竞价开始时间+1天
     */
    Date getNewDeliveryEffectStartDate(Date lastBiddingStartTime,Bidding bidding);


    /**
     * 判断竞价开始时间 +1天 是否大于 销售计划提货结束时间
     * 若大于 且isThrow=true则 提示 竞价开始日期不能大于销售计划的提货结束日期前一天，请重新选择竞价开始日期
     *
     * @param lastBiddingStartTime 竞价开始时间
     * @param salesPlanNo          销售计划单号
     * @param isThrow              满足条件是否抛出异常
     * @return true:满足竞价开始时间+1day > 销售计划提货结束时间 false:不满足
     */
    Boolean verifyBiddingStartTime(Date lastBiddingStartTime, String salesPlanNo, Boolean isThrow);

    /**
     * 判断竞价开始时间 +1天 是否大于 销售计划提货结束时间
     * 若大于 且isThrow=true则 提示 竞价开始日期不能大于销售计划的提货结束日期前一天，请重新选择竞价开始日期
     *
     * @param lastBiddingStartTime 竞价开始时间
     * @param salesPlan            销售计划
     * @param isThrow              满足条件是否抛出异常
     * @return true:满足竞价开始时间+1day > 销售计划提货结束时间 false:不满足
     */
    Boolean verifyBiddingStartTime(Date lastBiddingStartTime, SalesPlan salesPlan, Boolean isThrow);

    /**
     * 判断竞价开始时间 +1天 是否大于 销售计划提货结束时间
     * 若大于 且isThrow=true则 提示 竞价开始日期不能大于销售计划的提货结束日期前一天，请重新选择竞价开始日期
     *
     * @param lastBiddingStartTime  竞价开始时间
     * @param deliveryEffectEndDate 提货结束时间
     * @param isThrow               满足条件是否抛出异常
     * @return true:满足竞价开始时间+1day > 销售计划提货结束时间 false:不满足
     */
    Boolean verifyBiddingStartTime(Date lastBiddingStartTime, Date deliveryEffectEndDate, Boolean isThrow);

    Boolean existByEnquiryNo(String enquiryNo);

    /**
     * 停止竞价任务 通知任务、开始任务、结束任务
     */
    void stopBiddingJob(String biddingNo);


    Bidding getById(String id);

    PageInfo<BiddingReportResultDTO> biddingReport(BiddingReportQueryDTO param);

    List<BiddingAnalysisResultDTO> biddingAnalysis(Bidding bidding);

    /**
     * 活跃度报表
     * @param param 报表入参 查询条件
     */
    PageInfo<MemberActivityReportResultDTO> memberActivityReport(MemberActivityReportQueryDTO param);
}
