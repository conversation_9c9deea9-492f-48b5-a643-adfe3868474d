package com.cnoocshell.integration.dao.vo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 短信发送记录表
 */
@Data
@Table(name = "op_message_history_sms")
public class MessageHistorySms implements Serializable {

    private static final long serialVersionUID = 4122873182908404130L;

    @Id
    @Column(name = "message_history_mq_sms")
    private String messageHistoryMqSms;

    @Column(name = "message_history_mq_id")
    private String messageHistoryMqId;

    /**
     * 短信接收人电话
     */
    @Column(name = "receiver_mobile")
    private String receiverMobile;

    /**
     * 消息配置Id
     */
    @Column(name = "config_id")
    private String configId;

    /**
     * 短信签名
     */
    @Column(name = "send_sign")
    private String sendSign;

    /**
     * 国家代码
     */
    @Column(name = "send_country_code")
    private String sendCountryCode;

    /**
     * 消息发送参数json
     */
    @Column(name = "send_param")
    private String sendParam;

    /**
     * 消息发送响应码
     */
    @Column(name = "send_response_status")
    private Integer sendResponseStatus;

    /**
     * 短信发送结果状态
     */
    @Column(name = "send_status")
    private String sendStatus;

    /**
     * 消息发送时间
     */
    @Column(name = "send_time")
    private Date sendTime;

    @Column(name = "del_flg")
    private Boolean delFlg;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user")
    private String updateUser;

    /**
     * 消息发送响应json
     */
    @Column(name = "send_response")
    private String sendResponse;

    /**
     * 消息发送异常消息
     */
    @Column(name = "send_exception")
    private String sendException;

    /**
     * 发送的短信(短信模板+变量)
     */
    @Column(name = "send_message")
    private String sendMessage;

}