package com.cnoocshell.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.biz.IAttachmentInfoBiz;
import com.cnoocshell.base.dao.vo.AttachmentInfo;
import com.cnoocshell.base.exception.DuplicateString;
import com.cnoocshell.base.service.IAttachmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentServiceImpl implements IAttachmentService {

    private final IAttachmentInfoBiz iAttachmentInfoBiz;

    @Override
    public String insertFileInfo(List<AttachmentinfoDTO> list, String operatorId) {
        if(CollUtil.isEmpty(list))
            return null;
        return iAttachmentInfoBiz.insertFileInfo(list,operatorId);
    }

    @Override
    public AttachmentinfoDTO getById(String id) {
        Condition condition = new Condition(AttachmentInfo.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.ATTACHMENT_ID, id)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        AttachmentInfo file = CollUtil.getFirst(iAttachmentInfoBiz.findByCondition(condition));
        if(Objects.isNull(file))
            return null;

        return convertToDto(file);
    }

    @Override
    public List<AttachmentinfoDTO> getByIds(List<String> attachmentIds) {
        if(CollUtil.isEmpty(attachmentIds))
            return Collections.emptyList();

        Condition condition = new Condition(AttachmentInfo.class);
        condition.createCriteria()
                .andIn(DuplicateString.ATTACHMENT_ID, attachmentIds)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);

        List<AttachmentInfo> files = iAttachmentInfoBiz.findByCondition(condition);
        if(CollUtil.isEmpty(files))
            return Collections.emptyList();

        return files.stream().map(v->convertToDto(v)).collect(Collectors.toList());
    }

    private static AttachmentinfoDTO convertToDto(AttachmentInfo file){
        AttachmentinfoDTO result = new AttachmentinfoDTO();
        result.setAttachmentId(file.getAttachmentId());
        result.setAttcName(file.getAttachmentName());
        result.setAttcPath(file.getAttachmentPath());
        result.setAttcType(file.getAttachmentType());
        result.setAttcSize(file.getAttachmentSize());
        result.setUploadIp(file.getUploadIp());
        result.setDelFlg(file.getDelFlg());
        result.setCreateTime(file.getCreateTime());

        return result;
    }
}
