package com.cnoocshell.goods.cache;

import com.cnoocshell.common.service.ICacheEntry;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @Author: <EMAIL>
 * @Description  资源缓存
 * @date   2018年9月26日 上午11:50:11
 */
public class ResourceCache  implements ICacheEntry<String> {
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品分类
     */
    private Integer goodsType;

    /**
     * 商品描述
     */
    private String goodsDescribe;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源code
     */
    private String resourceCode;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 价格描述
     */
    private String priceDescribe;

    /**
     * 计价方式
     */
    private Integer priceWay;

    /**
     * 到位价
     */
    private BigDecimal arrivePrice;

    /**
     * 优惠后到位价
     */
    private BigDecimal discountArrivePrice;

    /**
     * 出厂价
     */
    private BigDecimal factoryPrice;

    /**
     * 优惠后出厂价
     */
    private BigDecimal discountFactoryPrice;

    /**
     * 是否最低价
     */
    private Integer minPrice;

    /**
     * 从到位价
     */
    private BigDecimal subArrivePrice;

    /**
     * 从优惠后到位价
     */
    private BigDecimal subDiscountArrivePrice;

    /**
     * 从出厂价
     */
    private BigDecimal subFactoryPrice;

    /**
     * 从优惠后出厂价
     */
    private BigDecimal subDiscountFactoryPrice;

    /**
     * 是否最低价2
     */
    private Boolean minPrice2;

    /**
     * 计价单位2
     */
    private String priceUnit2;

    /**
     * 计价单位
     */
    private String priceUnit;

    /**
     * 锁价方式
     */
    private Byte lockWay;

    /**
     * 仓库id
     */
    private String storeId;

    /**
     * 仓库名称
     */
    private String storeName;

    /**
     * 仓库类型
     */
    private String storeType;

    /**
     * 仓库详细地址
     */
    private String storeAddress;

    /**
     * 版本号
     */
    private Integer resourceVersion;

    /**
     * 资源状态
     */
    private String status;

    /**
     * 审批结果
     */
    private String approvalMessage;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 卖家姓名
     */
    private String sellerName;

    /**
     * 销售员id
     */
    private String salesId;

    /**
     * 销售员姓名
     */
    private String salesName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 总销售数量
     */
    private BigDecimal saleNum;

    /**
     * 可售数量
     */
    private BigDecimal cansaleNum;

    /**
     * 锁定数量
     */
    private BigDecimal orderLockNum;

    /**
     * 绝对容差
     */
    private BigDecimal absoluteTolerance;

    /**
     * 相对容差
     */
    private BigDecimal relativeTolerance;

    /**
     * 单笔最大购买量
     */
    private BigDecimal ordermaxNum;

    /**
     * 单笔最小购买量
     */
    private BigDecimal orderminNum;

    /**
     * 单笔最小变动量
     */
    private BigDecimal orderminchangeNum;

    /**
     * 单日最大购买量
     */
    private BigDecimal daymaxNum;

    /**
     * 是否区域定价
     */
    private Integer ifareaPrice;

    /**
     * 配送方式
     */
    private String deliveryWay;

    /**
     * 支付方式
     */
    private String payWay;

    /**
     * 是否有加价项
     */
    private Integer ifMarkup;

    /**
     * 加价项id
     */
    private String additemId;

    /**
     * 加价项名称
     */
    private String additemName;

    /**
     * 加价项版本
     */
    private Integer additemVersion;

    /**
     * 是否需要双方确认
     */
    private Integer ifdubboCheck;

    /**
     * 是否立即上架
     */
    private Integer ifup;

    /**
     * 定时上架时间
     */
    private Date fixUptime;

    /**
     * 定时下架时间
     */
    private Date fixDowntime;

    /**
     * 交易开始时间
     */
    private Date tradeStarttime;

    /**
     * 交易结束时间
     */
    private Date tradeEndtime;

    /**
     * 是否流量管控
     */
    private Integer ifFlowcontrol;

    /**
     * 是否允许分批发货
     */
    private Integer allowPartial;

    /**
     * 销售区域
     */
    private String saleArea;

    /**
     * 一级销售区域编码
     */
    private String saleAreaCode;

    /**
     * 二级销售区域编码
     */
    private String saleAreaCode2;

    /**
     * 三级销售区域编码
     */
    private String saleAreaCode3;

    /**
     * 四级销售区域编码
     */
    private String saleAreaCode4;

    /**
     * 五级销售区域编码
     */
    private String saleAreaCode5;

    /**
     * 一级销售区域名称
     */
    private String saleAreaName;

    /**
     * 二级销售区域名称
     */
    private String saleAreaName2;

    /**
     * 三级销售区域名称
     */
    private String saleAreaName3;

    /**
     * 四级销售区域名称
     */
    private String saleAreaName4;

    /**
     * 五级销售区域名称
     */
    private String saleAreaName5;

    /**
     * 支付有效期类型
     */
    private Integer paydateType;

    /**
     * 支付有效期/秒
     */
    private Long paydateLimit;

    /**
     * 提货有效期类型
     */
    private Integer takedateType;

    /**
     * 提货有效期/秒
     */
    private Long takedateLimit;

    /**
     * 删除标记
     */
    private Integer delFlg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    private static final long serialVersionUID = 1L;

    /**
     * 获取资源id
     *
     * @return resource_id - 资源id
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源id
     *
     * @param resourceId 资源id
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * 获取商品id
     *
     * @return goods_id - 商品id
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品id
     *
     * @param goodsId 商品id
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取商品分类
     *
     * @return goods_type - 商品分类
     */
    public Integer getGoodsType() {
        return goodsType;
    }

    /**
     * 设置商品分类
     *
     * @param goodsType 商品分类
     */
    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    /**
     * 获取商品描述
     *
     * @return goods_describe - 商品描述
     */
    public String getGoodsDescribe() {
        return goodsDescribe;
    }

    /**
     * 设置商品描述
     *
     * @param goodsDescribe 商品描述
     */
    public void setGoodsDescribe(String goodsDescribe) {
        this.goodsDescribe = goodsDescribe == null ? null : goodsDescribe.trim();
    }

    /**
     * 获取资源名称
     *
     * @return resource_name - 资源名称
     */
    public String getResourceName() {
        return resourceName;
    }

    /**
     * 设置资源名称
     *
     * @param resourceName 资源名称
     */
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    /**
     * 获取资源code
     *
     * @return resource_code - 资源code
     */
    public String getResourceCode() {
        return resourceCode;
    }

    /**
     * 设置资源code
     *
     * @param resourceCode 资源code
     */
    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode == null ? null : resourceCode.trim();
    }

    /**
     * 获取生效时间
     *
     * @return effect_time - 生效时间
     */
    public Date getEffectTime() {
        return effectTime;
    }

    /**
     * 设置生效时间
     *
     * @param effectTime 生效时间
     */
    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    /**
     * 获取币种
     *
     * @return currency - 币种
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 设置币种
     *
     * @param currency 币种
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * 获取币种符号
     *
     * @return currency_symbol - 币种符号
     */
    public String getCurrencySymbol() {
        return currencySymbol;
    }

    /**
     * 设置币种符号
     *
     * @param currencySymbol 币种符号
     */
    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol == null ? null : currencySymbol.trim();
    }

    /**
     * 获取币种名称
     *
     * @return currency_name - 币种名称
     */
    public String getCurrencyName() {
        return currencyName;
    }

    /**
     * 设置币种名称
     *
     * @param currencyName 币种名称
     */
    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName == null ? null : currencyName.trim();
    }

    /**
     * 获取价格描述
     *
     * @return price_describe - 价格描述
     */
    public String getPriceDescribe() {
        return priceDescribe;
    }

    /**
     * 设置价格描述
     *
     * @param priceDescribe 价格描述
     */
    public void setPriceDescribe(String priceDescribe) {
        this.priceDescribe = priceDescribe;
    }

    /**
     * 获取计价方式
     *
     * @return price_way - 计价方式
     */
    public Integer getPriceWay() {
        return priceWay;
    }

    /**
     * 设置计价方式
     *
     * @param priceWay 计价方式
     */
    public void setPriceWay(Integer priceWay) {
        this.priceWay = priceWay;
    }

    /**
     * 获取到位价
     *
     * @return arrive_price - 到位价
     */
    public BigDecimal getArrivePrice() {
        return arrivePrice;
    }

    /**
     * 设置到位价
     *
     * @param arrivePrice 到位价
     */
    public void setArrivePrice(BigDecimal arrivePrice) {
        this.arrivePrice = arrivePrice;
    }

    /**
     * 获取优惠后到位价
     *
     * @return discount_arrive_price - 优惠后到位价
     */
    public BigDecimal getDiscountArrivePrice() {
        return discountArrivePrice;
    }

    /**
     * 设置优惠后到位价
     *
     * @param discountArrivePrice 优惠后到位价
     */
    public void setDiscountArrivePrice(BigDecimal discountArrivePrice) {
        this.discountArrivePrice = discountArrivePrice;
    }

    /**
     * 获取出厂价
     *
     * @return factory_price - 出厂价
     */
    public BigDecimal getFactoryPrice() {
        return factoryPrice;
    }

    /**
     * 设置出厂价
     *
     * @param factoryPrice 出厂价
     */
    public void setFactoryPrice(BigDecimal factoryPrice) {
        this.factoryPrice = factoryPrice;
    }

    /**
     * 获取优惠后出厂价
     *
     * @return discount_factory_price - 优惠后出厂价
     */
    public BigDecimal getDiscountFactoryPrice() {
        return discountFactoryPrice;
    }

    /**
     * 设置优惠后出厂价
     *
     * @param discountFactoryPrice 优惠后出厂价
     */
    public void setDiscountFactoryPrice(BigDecimal discountFactoryPrice) {
        this.discountFactoryPrice = discountFactoryPrice;
    }

    /**
     * 获取是否最低价
     *
     * @return min_price - 是否最低价
     */
    public Integer getMinPrice() {
        return minPrice;
    }

    /**
     * 设置是否最低价
     *
     * @param minPrice 是否最低价
     */
    public void setMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
    }

    /**
     * 获取从到位价
     *
     * @return sub_arrive_price - 从到位价
     */
    public BigDecimal getSubArrivePrice() {
        return subArrivePrice;
    }

    /**
     * 设置从到位价
     *
     * @param subArrivePrice 从到位价
     */
    public void setSubArrivePrice(BigDecimal subArrivePrice) {
        this.subArrivePrice = subArrivePrice;
    }

    /**
     * 获取从优惠后到位价
     *
     * @return sub_discount_arrive_price - 从优惠后到位价
     */
    public BigDecimal getSubDiscountArrivePrice() {
        return subDiscountArrivePrice;
    }

    /**
     * 设置从优惠后到位价
     *
     * @param subDiscountArrivePrice 从优惠后到位价
     */
    public void setSubDiscountArrivePrice(BigDecimal subDiscountArrivePrice) {
        this.subDiscountArrivePrice = subDiscountArrivePrice;
    }

    /**
     * 获取从出厂价
     *
     * @return sub_factory_price - 从出厂价
     */
    public BigDecimal getSubFactoryPrice() {
        return subFactoryPrice;
    }

    /**
     * 设置从出厂价
     *
     * @param subFactoryPrice 从出厂价
     */
    public void setSubFactoryPrice(BigDecimal subFactoryPrice) {
        this.subFactoryPrice = subFactoryPrice;
    }

    /**
     * 获取从优惠后出厂价
     *
     * @return sub_discount_factory_price - 从优惠后出厂价
     */
    public BigDecimal getSubDiscountFactoryPrice() {
        return subDiscountFactoryPrice;
    }

    /**
     * 设置从优惠后出厂价
     *
     * @param subDiscountFactoryPrice 从优惠后出厂价
     */
    public void setSubDiscountFactoryPrice(BigDecimal subDiscountFactoryPrice) {
        this.subDiscountFactoryPrice = subDiscountFactoryPrice;
    }

    /**
     * 获取是否最低价2
     *
     * @return min_price2 - 是否最低价2
     */
    public Boolean getMinPrice2() {
        return minPrice2;
    }

    /**
     * 设置是否最低价2
     *
     * @param minPrice2 是否最低价2
     */
    public void setMinPrice2(Boolean minPrice2) {
        this.minPrice2 = minPrice2;
    }

    /**
     * 获取计价单位2
     *
     * @return price_unit2 - 计价单位2
     */
    public String getPriceUnit2() {
        return priceUnit2;
    }

    /**
     * 设置计价单位2
     *
     * @param priceUnit2 计价单位2
     */
    public void setPriceUnit2(String priceUnit2) {
        this.priceUnit2 = priceUnit2 == null ? null : priceUnit2.trim();
    }

    /**
     * 获取计价单位
     *
     * @return price_unit - 计价单位
     */
    public String getPriceUnit() {
        return priceUnit;
    }

    /**
     * 设置计价单位
     *
     * @param priceUnit 计价单位
     */
    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit == null ? null : priceUnit.trim();
    }

    /**
     * 获取锁价方式
     *
     * @return lock_way - 锁价方式
     */
    public Byte getLockWay() {
        return lockWay;
    }

    /**
     * 设置锁价方式
     *
     * @param lockWay 锁价方式
     */
    public void setLockWay(Byte lockWay) {
        this.lockWay = lockWay;
    }

    /**
     * 获取仓库id
     *
     * @return store_id - 仓库id
     */
    public String getStoreId() {
        return storeId;
    }

    /**
     * 设置仓库id
     *
     * @param storeId 仓库id
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId == null ? null : storeId.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return store_name - 仓库名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 设置仓库名称
     *
     * @param storeName 仓库名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName == null ? null : storeName.trim();
    }

    /**
     * 获取仓库类型
     *
     * @return store_type - 仓库类型
     */
    public String getStoreType() {
        return storeType;
    }

    /**
     * 设置仓库类型
     *
     * @param storeType 仓库类型
     */
    public void setStoreType(String storeType) {
        this.storeType = storeType == null ? null : storeType.trim();
    }

    /**
     * 获取仓库详细地址
     *
     * @return store_address - 仓库详细地址
     */
    public String getStoreAddress() {
        return storeAddress;
    }

    /**
     * 设置仓库详细地址
     *
     * @param storeAddress 仓库详细地址
     */
    public void setStoreAddress(String storeAddress) {
        this.storeAddress = storeAddress == null ? null : storeAddress.trim();
    }

    /**
     * 获取版本号
     *
     * @return resource_version - 版本号
     */
    public Integer getResourceVersion() {
        return resourceVersion;
    }

    /**
     * 设置版本号
     *
     * @param resourceVersion 版本号
     */
    public void setResourceVersion(Integer resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    /**
     * 获取资源状态
     *
     * @return status - 资源状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置资源状态
     *
     * @param status 资源状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取审批结果
     *
     * @return approval_message - 审批结果
     */
    public String getApprovalMessage() {
        return approvalMessage;
    }

    /**
     * 设置审批结果
     *
     * @param approvalMessage 审批结果
     */
    public void setApprovalMessage(String approvalMessage) {
        this.approvalMessage = approvalMessage == null ? null : approvalMessage.trim();
    }

    /**
     * 获取卖家id
     *
     * @return seller_id - 卖家id
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家id
     *
     * @param sellerId 卖家id
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家姓名
     *
     * @return seller_name - 卖家姓名
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家姓名
     *
     * @param sellerName 卖家姓名
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取销售员id
     *
     * @return sales_id - 销售员id
     */
    public String getSalesId() {
        return salesId;
    }

    /**
     * 设置销售员id
     *
     * @param salesId 销售员id
     */
    public void setSalesId(String salesId) {
        this.salesId = salesId == null ? null : salesId.trim();
    }

    /**
     * 获取销售员姓名
     *
     * @return sales_name - 销售员姓名
     */
    public String getSalesName() {
        return salesName;
    }

    /**
     * 设置销售员姓名
     *
     * @param salesName 销售员姓名
     */
    public void setSalesName(String salesName) {
        this.salesName = salesName == null ? null : salesName.trim();
    }

    /**
     * 获取联系电话
     *
     * @return contact_phone - 联系电话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置联系电话
     *
     * @param contactPhone 联系电话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    /**
     * 获取组织机构id
     *
     * @return org_id - 组织机构id
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * 设置组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    /**
     * 获取组织机构名称
     *
     * @return org_name - 组织机构名称
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * 设置组织机构名称
     *
     * @param orgName 组织机构名称
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    /**
     * 获取总销售数量
     *
     * @return sale_num - 总销售数量
     */
    public BigDecimal getSaleNum() {
        return saleNum;
    }

    /**
     * 设置总销售数量
     *
     * @param saleNum 总销售数量
     */
    public void setSaleNum(BigDecimal saleNum) {
        this.saleNum = saleNum;
    }

    /**
     * 获取可售数量
     *
     * @return cansale_num - 可售数量
     */
    public BigDecimal getCansaleNum() {
        return cansaleNum;
    }

    /**
     * 设置可售数量
     *
     * @param cansaleNum 可售数量
     */
    public void setCansaleNum(BigDecimal cansaleNum) {
        this.cansaleNum = cansaleNum;
    }

    /**
     * 获取锁定数量
     *
     * @return order_lock_num - 锁定数量
     */
    public BigDecimal getOrderLockNum() {
        return orderLockNum;
    }

    /**
     * 设置锁定数量
     *
     * @param orderLockNum 锁定数量
     */
    public void setOrderLockNum(BigDecimal orderLockNum) {
        this.orderLockNum = orderLockNum;
    }

    /**
     * 获取绝对容差
     *
     * @return absolute_tolerance - 绝对容差
     */
    public BigDecimal getAbsoluteTolerance() {
        return absoluteTolerance;
    }

    /**
     * 设置绝对容差
     *
     * @param absoluteTolerance 绝对容差
     */
    public void setAbsoluteTolerance(BigDecimal absoluteTolerance) {
        this.absoluteTolerance = absoluteTolerance;
    }

    /**
     * 获取相对容差
     *
     * @return relative_tolerance - 相对容差
     */
    public BigDecimal getRelativeTolerance() {
        return relativeTolerance;
    }

    /**
     * 设置相对容差
     *
     * @param relativeTolerance 相对容差
     */
    public void setRelativeTolerance(BigDecimal relativeTolerance) {
        this.relativeTolerance = relativeTolerance;
    }

    /**
     * 获取单笔最大购买量
     *
     * @return ordermax_num - 单笔最大购买量
     */
    public BigDecimal getOrdermaxNum() {
        return ordermaxNum;
    }

    /**
     * 设置单笔最大购买量
     *
     * @param ordermaxNum 单笔最大购买量
     */
    public void setOrdermaxNum(BigDecimal ordermaxNum) {
        this.ordermaxNum = ordermaxNum;
    }

    /**
     * 获取单笔最小购买量
     *
     * @return ordermin_num - 单笔最小购买量
     */
    public BigDecimal getOrderminNum() {
        return orderminNum;
    }

    /**
     * 设置单笔最小购买量
     *
     * @param orderminNum 单笔最小购买量
     */
    public void setOrderminNum(BigDecimal orderminNum) {
        this.orderminNum = orderminNum;
    }

    /**
     * 获取单笔最小变动量
     *
     * @return orderminchange_num - 单笔最小变动量
     */
    public BigDecimal getOrderminchangeNum() {
        return orderminchangeNum;
    }

    /**
     * 设置单笔最小变动量
     *
     * @param orderminchangeNum 单笔最小变动量
     */
    public void setOrderminchangeNum(BigDecimal orderminchangeNum) {
        this.orderminchangeNum = orderminchangeNum;
    }

    /**
     * 获取单日最大购买量
     *
     * @return daymax_num - 单日最大购买量
     */
    public BigDecimal getDaymaxNum() {
        return daymaxNum;
    }

    /**
     * 设置单日最大购买量
     *
     * @param daymaxNum 单日最大购买量
     */
    public void setDaymaxNum(BigDecimal daymaxNum) {
        this.daymaxNum = daymaxNum;
    }

    /**
     * 获取是否区域定价
     *
     * @return ifarea_price - 是否区域定价
     */
    public Integer getIfareaPrice() {
        return ifareaPrice;
    }

    /**
     * 设置是否区域定价
     *
     * @param ifareaPrice 是否区域定价
     */
    public void setIfareaPrice(Integer ifareaPrice) {
        this.ifareaPrice = ifareaPrice;
    }

    /**
     * 获取配送方式
     *
     * @return delivery_way - 配送方式
     */
    public String getDeliveryWay() {
        return deliveryWay;
    }

    /**
     * 设置配送方式
     *
     * @param deliveryWay 配送方式
     */
    public void setDeliveryWay(String deliveryWay) {
        this.deliveryWay = deliveryWay == null ? null : deliveryWay.trim();
    }

    /**
     * 获取支付方式
     *
     * @return pay_way - 支付方式
     */
    public String getPayWay() {
        return payWay;
    }

    /**
     * 设置支付方式
     *
     * @param payWay 支付方式
     */
    public void setPayWay(String payWay) {
        this.payWay = payWay == null ? null : payWay.trim();
    }

    /**
     * 获取是否有加价项
     *
     * @return if_markup - 是否有加价项
     */
    public Integer getIfMarkup() {
        return ifMarkup;
    }

    /**
     * 设置是否有加价项
     *
     * @param ifMarkup 是否有加价项
     */
    public void setIfMarkup(Integer ifMarkup) {
        this.ifMarkup = ifMarkup;
    }

    /**
     * 获取加价项id
     *
     * @return additem_id - 加价项id
     */
    public String getAdditemId() {
        return additemId;
    }

    /**
     * 设置加价项id
     *
     * @param additemId 加价项id
     */
    public void setAdditemId(String additemId) {
        this.additemId = additemId == null ? null : additemId.trim();
    }

    /**
     * 获取加价项名称
     *
     * @return additem_name - 加价项名称
     */
    public String getAdditemName() {
        return additemName;
    }

    /**
     * 设置加价项名称
     *
     * @param additemName 加价项名称
     */
    public void setAdditemName(String additemName) {
        this.additemName = additemName == null ? null : additemName.trim();
    }

    /**
     * 获取加价项版本
     *
     * @return additem_version - 加价项版本
     */
    public Integer getAdditemVersion() {
        return additemVersion;
    }

    /**
     * 设置加价项版本
     *
     * @param additemVersion 加价项版本
     */
    public void setAdditemVersion(Integer additemVersion) {
        this.additemVersion = additemVersion;
    }

    /**
     * 获取是否需要双方确认
     *
     * @return ifdubbo_check - 是否需要双方确认
     */
    public Integer getIfdubboCheck() {
        return ifdubboCheck;
    }

    /**
     * 设置是否需要双方确认
     *
     * @param ifdubboCheck 是否需要双方确认
     */
    public void setIfdubboCheck(Integer ifdubboCheck) {
        this.ifdubboCheck = ifdubboCheck;
    }

    /**
     * 获取是否立即上架
     *
     * @return ifup - 是否立即上架
     */
    public Integer getIfup() {
        return ifup;
    }

    /**
     * 设置是否立即上架
     *
     * @param ifup 是否立即上架
     */
    public void setIfup(Integer ifup) {
        this.ifup = ifup;
    }

    /**
     * 获取定时上架时间
     *
     * @return fix_uptime - 定时上架时间
     */
    public Date getFixUptime() {
        return fixUptime;
    }

    /**
     * 设置定时上架时间
     *
     * @param fixUptime 定时上架时间
     */
    public void setFixUptime(Date fixUptime) {
        this.fixUptime = fixUptime;
    }

    /**
     * 获取定时下架时间
     *
     * @return fix_downtime - 定时下架时间
     */
    public Date getFixDowntime() {
        return fixDowntime;
    }

    /**
     * 设置定时下架时间
     *
     * @param fixDowntime 定时下架时间
     */
    public void setFixDowntime(Date fixDowntime) {
        this.fixDowntime = fixDowntime;
    }

    /**
     * 获取交易开始时间
     *
     * @return trade_starttime - 交易开始时间
     */
    public Date getTradeStarttime() {
        return tradeStarttime;
    }

    /**
     * 设置交易开始时间
     *
     * @param tradeStarttime 交易开始时间
     */
    public void setTradeStarttime(Date tradeStarttime) {
        this.tradeStarttime = tradeStarttime;
    }

    /**
     * 获取交易结束时间
     *
     * @return trade_endtime - 交易结束时间
     */
    public Date getTradeEndtime() {
        return tradeEndtime;
    }

    /**
     * 设置交易结束时间
     *
     * @param tradeEndtime 交易结束时间
     */
    public void setTradeEndtime(Date tradeEndtime) {
        this.tradeEndtime = tradeEndtime;
    }

    /**
     * 获取是否流量管控
     *
     * @return if_flowcontrol - 是否流量管控
     */
    public Integer getIfFlowcontrol() {
        return ifFlowcontrol;
    }

    /**
     * 设置是否流量管控
     *
     * @param ifFlowcontrol 是否流量管控
     */
    public void setIfFlowcontrol(Integer ifFlowcontrol) {
        this.ifFlowcontrol = ifFlowcontrol;
    }

    /**
     * 获取是否允许分批发货
     *
     * @return allow_partial - 是否允许分批发货
     */
    public Integer getAllowPartial() {
        return allowPartial;
    }

    /**
     * 设置是否允许分批发货
     *
     * @param allowPartial 是否允许分批发货
     */
    public void setAllowPartial(Integer allowPartial) {
        this.allowPartial = allowPartial;
    }

    /**
     * 获取销售区域
     *
     * @return sale_area - 销售区域
     */
    public String getSaleArea() {
        return saleArea;
    }

    /**
     * 设置销售区域
     *
     * @param saleArea 销售区域
     */
    public void setSaleArea(String saleArea) {
        this.saleArea = saleArea == null ? null : saleArea.trim();
    }

    /**
     * 获取一级销售区域编码
     *
     * @return sale_area_code - 一级销售区域编码
     */
    public String getSaleAreaCode() {
        return saleAreaCode;
    }

    /**
     * 设置一级销售区域编码
     *
     * @param saleAreaCode 一级销售区域编码
     */
    public void setSaleAreaCode(String saleAreaCode) {
        this.saleAreaCode = saleAreaCode == null ? null : saleAreaCode.trim();
    }

    /**
     * 获取二级销售区域编码
     *
     * @return sale_area_code2 - 二级销售区域编码
     */
    public String getSaleAreaCode2() {
        return saleAreaCode2;
    }

    /**
     * 设置二级销售区域编码
     *
     * @param saleAreaCode2 二级销售区域编码
     */
    public void setSaleAreaCode2(String saleAreaCode2) {
        this.saleAreaCode2 = saleAreaCode2 == null ? null : saleAreaCode2.trim();
    }

    /**
     * 获取三级销售区域编码
     *
     * @return sale_area_code3 - 三级销售区域编码
     */
    public String getSaleAreaCode3() {
        return saleAreaCode3;
    }

    /**
     * 设置三级销售区域编码
     *
     * @param saleAreaCode3 三级销售区域编码
     */
    public void setSaleAreaCode3(String saleAreaCode3) {
        this.saleAreaCode3 = saleAreaCode3 == null ? null : saleAreaCode3.trim();
    }

    /**
     * 获取四级销售区域编码
     *
     * @return sale_area_code4 - 四级销售区域编码
     */
    public String getSaleAreaCode4() {
        return saleAreaCode4;
    }

    /**
     * 设置四级销售区域编码
     *
     * @param saleAreaCode4 四级销售区域编码
     */
    public void setSaleAreaCode4(String saleAreaCode4) {
        this.saleAreaCode4 = saleAreaCode4 == null ? null : saleAreaCode4.trim();
    }

    /**
     * 获取五级销售区域编码
     *
     * @return sale_area_code5 - 五级销售区域编码
     */
    public String getSaleAreaCode5() {
        return saleAreaCode5;
    }

    /**
     * 设置五级销售区域编码
     *
     * @param saleAreaCode5 五级销售区域编码
     */
    public void setSaleAreaCode5(String saleAreaCode5) {
        this.saleAreaCode5 = saleAreaCode5 == null ? null : saleAreaCode5.trim();
    }

    /**
     * 获取一级销售区域名称
     *
     * @return sale_area_name - 一级销售区域名称
     */
    public String getSaleAreaName() {
        return saleAreaName;
    }

    /**
     * 设置一级销售区域名称
     *
     * @param saleAreaName 一级销售区域名称
     */
    public void setSaleAreaName(String saleAreaName) {
        this.saleAreaName = saleAreaName == null ? null : saleAreaName.trim();
    }

    /**
     * 获取二级销售区域名称
     *
     * @return sale_area_name2 - 二级销售区域名称
     */
    public String getSaleAreaName2() {
        return saleAreaName2;
    }

    /**
     * 设置二级销售区域名称
     *
     * @param saleAreaName2 二级销售区域名称
     */
    public void setSaleAreaName2(String saleAreaName2) {
        this.saleAreaName2 = saleAreaName2 == null ? null : saleAreaName2.trim();
    }

    /**
     * 获取三级销售区域名称
     *
     * @return sale_area_name3 - 三级销售区域名称
     */
    public String getSaleAreaName3() {
        return saleAreaName3;
    }

    /**
     * 设置三级销售区域名称
     *
     * @param saleAreaName3 三级销售区域名称
     */
    public void setSaleAreaName3(String saleAreaName3) {
        this.saleAreaName3 = saleAreaName3 == null ? null : saleAreaName3.trim();
    }

    /**
     * 获取四级销售区域名称
     *
     * @return sale_area_name4 - 四级销售区域名称
     */
    public String getSaleAreaName4() {
        return saleAreaName4;
    }

    /**
     * 设置四级销售区域名称
     *
     * @param saleAreaName4 四级销售区域名称
     */
    public void setSaleAreaName4(String saleAreaName4) {
        this.saleAreaName4 = saleAreaName4 == null ? null : saleAreaName4.trim();
    }

    /**
     * 获取五级销售区域名称
     *
     * @return sale_area_name5 - 五级销售区域名称
     */
    public String getSaleAreaName5() {
        return saleAreaName5;
    }

    /**
     * 设置五级销售区域名称
     *
     * @param saleAreaName5 五级销售区域名称
     */
    public void setSaleAreaName5(String saleAreaName5) {
        this.saleAreaName5 = saleAreaName5 == null ? null : saleAreaName5.trim();
    }

    /**
     * 获取支付有效期类型
     *
     * @return paydate_type - 支付有效期类型
     */
    public Integer getPaydateType() {
        return paydateType;
    }

    /**
     * 设置支付有效期类型
     *
     * @param paydateType 支付有效期类型
     */
    public void setPaydateType(Integer paydateType) {
        this.paydateType = paydateType;
    }

    /**
     * 获取支付有效期/秒
     *
     * @return paydate_limit - 支付有效期/秒
     */
    public Long getPaydateLimit() {
        return paydateLimit;
    }

    /**
     * 设置支付有效期/秒
     *
     * @param paydateLimit 支付有效期/秒
     */
    public void setPaydateLimit(Long paydateLimit) {
        this.paydateLimit = paydateLimit;
    }

    /**
     * 获取提货有效期类型
     *
     * @return takedate_type - 提货有效期类型
     */
    public Integer getTakedateType() {
        return takedateType;
    }

    /**
     * 设置提货有效期类型
     *
     * @param takedateType 提货有效期类型
     */
    public void setTakedateType(Integer takedateType) {
        this.takedateType = takedateType;
    }

    /**
     * 获取提货有效期/秒
     *
     * @return takedate_limit - 提货有效期/秒
     */
    public Long getTakedateLimit() {
        return takedateLimit;
    }

    /**
     * 设置提货有效期/秒
     *
     * @param takedateLimit 提货有效期/秒
     */
    public void setTakedateLimit(Long takedateLimit) {
        this.takedateLimit = takedateLimit;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
    
    @Override
    public String getKey() {
        return null;
    }
}
