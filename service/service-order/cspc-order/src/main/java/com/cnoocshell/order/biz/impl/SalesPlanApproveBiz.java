package com.cnoocshell.order.biz.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanPageApproveRequestDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.biz.ISalesPlanApproveBiz;
import com.cnoocshell.order.biz.ISalesPlanBiz;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.dao.vo.SalesPlanApproveRecord;
import com.cnoocshell.order.exception.DuplicateString;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SalesPlanApproveBiz extends BaseBiz<SalesPlanApproveRecord> implements ISalesPlanApproveBiz {


    @Override
    public PageInfo<SalesPlanApproveRecord> findSalesPlanApprovelByCondiftion(SalesPlanPageApproveRequestDTO requestDTO, List<String> planNo) {
        Condition condition = new Condition(SalesPlanApproveRecord.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        if (StringUtils.isNotBlank(requestDTO.getPlanNo())) {
            criteria.andLike(DuplicateString.PLAN_NO, "%" + requestDTO.getPlanNo() + "%");
        }
        criteria.andIn(DuplicateString.PLAN_NO, planNo);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(requestDTO.getPageSize() < 10 ? 10 : requestDTO.getPageSize());
        pageInfo.setPageNum(requestDTO.getPageNum() < 0 ? 1 : requestDTO.getPageNum());
        return super.pageInfo(condition, pageInfo);
    }

}
