package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.exception.BasicRuntimeException;
import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.common.service.common.BaseBiz;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Lazy;

import java.lang.reflect.ParameterizedType;
import java.util.List;

@Slf4j
public class BaseCacheBiz<T> extends BaseBiz<T> {
    /**
     * 缓存管理器
     */
    @Autowired
    @Lazy
    @Qualifier("memberCacheManager")
    private CacheManager cacheManager;
    private Cache cache = null;
    /**
     * 缓存池名称
     */
    protected String cacheName = null;

    /**
     * 缓存key前缀(T对象属性集合)
     */
    protected List<String> cacheKeyFieldNameList = Lists.newArrayList();
    /**
     * 主键字段名称 不可为空
     */
    protected String idFieldName = null;

    protected IBaseMapper<T> mapper;

    @Autowired
    @Lazy
    public void setCacheMapper(IBaseMapper<T> mapper) {
        this.mapper = mapper;
    }

    protected void initCache(String cacheName, String idFieldName, List<String> cacheKeyFieldNameList) {
        cacheName = StringUtils.isBlank(cacheName) ? "member:cache:" : cacheName;
        cacheName = cacheName.endsWith(":") ? cacheName : cacheName + ":";
        if (StringUtils.isBlank(idFieldName)) {
            throw new BasicRuntimeException("idFieldName 不可为空");
        }
        cacheName += ((Class) ((Class) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0])).getSimpleName() + ":";
        cache = cacheManager.getCache(cacheName);
        this.cacheName = cacheName;
        this.cacheKeyFieldNameList = cacheKeyFieldNameList;

        this.idFieldName = idFieldName;
    }

    protected void cacheClear() {
        log.debug("cacheName:{}, cache clear", cacheName);
        this.cache.clear();
    }

    protected void cacheEvict(String idFieldName, String idFieldValue) {
        if (idFieldValue == null || StringUtils.isBlank(idFieldValue)) {
            return;
        }
        String key = cacheName + idFieldName + "-" + idFieldValue;
        log.debug("cache evict key:{}", key);
        this.cache.evict(key);
    }

    protected <T> T cacheGet(String idFieldName, String idFieldValue) {
        String key = cacheName + idFieldName + "-" + idFieldValue;
        log.debug("cache get key:{}", key);
        if (StringUtils.isBlank(key)) {
            return null;
        }
        Cache.ValueWrapper value = cache.get(key);
        if (value == null) {
            return null;
        }
        return (T) value.get();
    }

    protected void cachePut(String idFieldName, String idFieldValue, Object value) {
        if (idFieldValue == null || value == null || StringUtils.isBlank(idFieldValue)) {
            return;
        }
        String key = cacheName + idFieldName + "-" + idFieldValue;
        log.debug("cacheName:{}, put key:{}", cacheName, key);
        this.cache.put(key, value);
    }

    protected void cachePut(T t) {
        if (t == null) {
            return;
        }
        String idValue = getFieldValue(t, idFieldName);
        if (idValue == null || StringUtils.isBlank(idValue)) {
            return;
        }
        cachePut(idFieldName, idValue, t);
        cacheKeyFieldNameList.forEach(item -> cachePut(item, getFieldValue(t, item), idValue));
    }

    public void cacheEvict(T t) {
        if (t == null) {
            return;
        }
        String idValue = getFieldValue(t, idFieldName);
        if (idValue == null || StringUtils.isBlank(idValue)) {
            return;
        }
        T t2 = cacheGet(idFieldName, idValue);
        if (t2 != null) {
            cacheKeyFieldNameList.forEach(item -> cacheEvict(item, getFieldValue(t2, item)));
            cacheEvict(idFieldName, idValue);
        }
    }

    public T findByCacheField(String fieldName, String fieldValue) {
        if (fieldName == null || fieldValue == null || StringUtils.isBlank(fieldName) || StringUtils.isBlank(fieldValue)) {
            return null;
        }
        String idValue = cacheGet(fieldName, fieldValue);
        if (idValue == null || StringUtils.isBlank(idValue)) {
            return null;
        }
        return cacheGet(idFieldName, idValue);
    }

    ///////////////Override mapper method 重写影响缓存相关的方法//////////////////////

    public void deleteByPrimaryKey(String key) {
        T t = get(key);
        if (t != null) {
            delete(t);
        }
    }

    @Override
    public void delete(T rec) {
        cacheEvict(rec);
        super.delete(rec);
        cacheEvict(rec);
    }

    public int updateByPrimaryKey(T rec) {
        return updateByPrimaryKey(rec, true);
    }

    public int updateByPrimaryKey(T rec, boolean cachePut) {
        int rs = mapper.updateByPrimaryKey(rec);
        if (rs > 0) {
            if (cachePut) {
                cachePut(rec);
            } else {
                cacheEvict(rec);
            }
        }
        return rs;
    }

    public int updateByPrimaryKeySelective(T rec) {
        return updateByPrimaryKeySelective(rec, true);
    }

    public int updateByPrimaryKeySelective(T rec, boolean cachePut) {
        int rs = mapper.updateByPrimaryKeySelective(rec);
        if (rs > 0) {
            if (cachePut) {
                cachePut(rec);
            } else {
                cacheEvict(rec);
            }
        }
        return rs;
    }

    public int deleteByCondition(Object condition) {
        int rs = mapper.deleteByCondition(condition);
        if (rs > 0) {
            cacheClear();
        }
        return rs;
    }

    public int updateByCondition(T rec, Object condition) {
        int rs = mapper.updateByCondition(rec, condition);
        if (rs > 0) {
            cacheClear();
        }
        return rs;
    }

    public int updateByConditionSelective(T rec, Object condition) {
        int rs = mapper.updateByConditionSelective(rec, condition);
        if (rs > 0) {
            cacheClear();
        }
        return rs;
    }

    public int updateByExample(T rec, Object example) {
        int rs = mapper.updateByExample(rec, example);
        if (rs > 0) {
            cacheClear();
        }
        return rs;
    }

    public int updateByExampleSelective(T rec, Object example) {
        int rs = mapper.updateByExampleSelective(rec, example);
        if (rs > 0) {
            cacheClear();
        }

        return rs;
    }

    public int deleteByIds(String ids) {
        int rs = mapper.deleteByIds(ids);
        if (rs > 0) {
            cacheClear();
        }
        return rs;
    }

    ///////////////Override BaseBiz 重写影响缓存相关的方法//////////////////////

    @Override
    public T get(String id) {
        T t = cacheGet(idFieldName, id);
        if (t != null) {
            return t;
        }
        t = super.get(id);
        cachePut(t);
        return t;
    }

    @Override
    public T updateSelective(T t) {
        return updateSelective(t, true);
    }

    public T updateSelective(T t, boolean cachePut) {
        t = super.updateSelective(t);
        if (cachePut) {
            cachePut(t);
        } else {
            cacheEvict(t);
        }
        return t;
    }


    @Override
    public T save(T t) {
        return save(t, null);
    }

    @Override
    public T save(T t, Object operUserId) {
        return save(t, operUserId, true);
    }

    public T save(T t, Object operUserId, boolean cachePut) {
        t = super.save(t, operUserId);
        if (cachePut) {
            cachePut(t);
        } else {
            cacheEvict(t);
        }
        return t;
    }

    private String getFieldValue(Object obj, String fieldName) {
        try {
            return BeanUtils.getProperty(obj, fieldName);
        } catch (Exception e) {
            log.error("获取属性值时发生错误", e);
            throw new BasicRuntimeException("获取属性值失败", e);
        }
    }

}
