package com.cnoocshell.base.api.dto.region;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @DESCRIPTION:
 */
@Data
public class RegionLabelValueDTO implements Serializable {

    private static final long serialVersionUID = -3174138670542114319L;
    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private String value;

    /**
     * 行政区名称
     */
    @ApiModelProperty("行政区名称")
    private String label;

    /**
     * 子节点
     */
    @ApiModelProperty("子节点")
    private List<RegionLabelValueDTO>  childs;

}
