package com.cnoocshell.member.biz.impl;

import com.cnoocshell.base.api.service.IAttachmentService;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.MemberCertTypeEnum;
import com.cnoocshell.member.biz.IMemberCertHistoryBiz;
import com.cnoocshell.member.dao.mapper.MemberCertHistoryMapper;
import com.cnoocshell.member.dao.vo.MemberCertHistory;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberCertHistoryBiz extends BaseBiz<MemberCertHistory> implements IMemberCertHistoryBiz {

    public static final String REQUEST_ID = "requestId";
    public static final String LOG_MEMBER_CERT_CHECK = "memberCertCheck:{}";

    private final MemberCertHistoryMapper memberCertHistoryMapper;
    private final MemberCertBiz memberCertBiz;
    private final UUIDGenerator uuidGenerator;
    private final IAttachmentService attachmentService;

    @Override
    public void saveCertHistoryList(List<MemberCertDTO> memberCertDTOList, String memberId, String accountId, String requestId, String operatorId) {
        if ( !CollectionUtils.isEmpty(memberCertDTOList) ) {
            boolean hasShipIdentity = false;
            for (MemberCertDTO memberCertDTO : memberCertDTOList) {
                if( !MemberCertTypeEnum.IDENTITY_LICENSE_SHIPOWNER.getCode().equals(memberCertDTO.getCertType())){
                    continue;
                }
                memberCertCheck(memberCertDTO);
                MemberCertHistory certHistory = getMemberCertHistory(memberId, accountId, requestId, memberCertDTO);
                save(certHistory, operatorId);
                memberCertDTO.setCertId(certHistory.getCertId());
                hasShipIdentity = true;
            }
            for (MemberCertDTO memberCertDTO : memberCertDTOList) {
                if (checkCertTypeForContinue(memberCertDTO, hasShipIdentity)) continue;
                memberCertCheck(memberCertDTO);
                MemberCertHistory certHistory = getMemberCertHistory(memberId, accountId, requestId, memberCertDTO);
                save(certHistory, operatorId);
                memberCertDTO.setCertId(certHistory.getCertId());
            }
        }
    }

    @Override
    public List<MemberCertHistory> findByRequestId(String requestId) {
        Condition condition = new Condition(MemberCertHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(REQUEST_ID, requestId);
        return memberCertHistoryMapper.selectByCondition(condition);
    }

    @Override
    public void updateByConditionSelective(MemberCertHistory memberCertHistory, Condition condition) {
        memberCertHistoryMapper.updateByConditionSelective(memberCertHistory, condition);
    }

    @Override
    public void update(List<MemberCertDTO> certList, String requestId,String operatorId) {
        if(certList == null || certList.isEmpty() ){
            return;
        }
        Date now = new Date();
        certList.forEach(item ->{
            if( StringUtils.isNotBlank(item.getCertId()) ){
                MemberCertHistory memberCertHistory = this.findByCertIdAndRequestId(item.getCertId(),requestId);
                if( memberCertHistory != null ) {
                    memberCertHistory.setCertName(item.getCertName());
                    memberCertHistory.setAttachmentId(item.getAttachmentId());
                    memberCertHistory.setEffectiveTime(item.getEffectiveTime());
                    memberCertHistory.setUpdateUser(operatorId);
                    memberCertHistory.setUpdateTime(now);
                    if (memberCertHistory.getEffectiveTime() != null &&
                            memberCertHistory.getEffectiveTime().getTime() < System.currentTimeMillis()) {
                        throw new BizException(MemberCode.CERT_IS_OVER_TIME, memberCertHistory.getCertName());
                    }
                    updateSelective(memberCertHistory);
                }
            }
        });
    }

    @Override
    public MemberCertHistory findByCertIdAndRequestId(String certId, String requestId) {
        Condition condition = new Condition(MemberCertHistory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("certId", certId);
        criteria.andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        criteria.andEqualTo(REQUEST_ID, requestId);
        List<MemberCertHistory> list = memberCertHistoryMapper.selectByCondition(condition);
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public void updateCert(MemberCertDTO certDTO,String memberId,String requestId, String operatorId, MemberCertTypeEnum certType) {
        if( certDTO == null ){
            return;
        }
        MemberCertHistory certHistory = findByCertId(certDTO.getCertId());
        if( certHistory == null || StringUtils.isBlank(certHistory.getId()) ){
            return;
        }
        Date now = new Date();
        BeanUtils.copyProperties(certDTO, certHistory);
        certHistory.setRequestId(requestId);
        certHistory.setMemberId(memberId);
        certHistory.setCertType(certType.getCode());
        certHistory.setUpdateUser(operatorId);
        certHistory.setUpdateTime(now);
        if (certHistory.getEffectiveTime() != null &&
                certHistory.getEffectiveTime().getTime() < System.currentTimeMillis()  ) {
            throw new BizException(MemberCode.CERT_IS_OVER_TIME, certHistory.getCertName());
        }
        updateSelective(certHistory);
    }

    @Override
    public MemberCertHistory findByCertId(String certId){
        if( StringUtils.isBlank(certId) ){
            return null;
        }
        Condition condition = new Condition(MemberCertHistory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("certId", certId);
        criteria.andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        criteria.andEqualTo(DuplicateString.STATUS, AdvertStatusEnum.NEW_REQUEST.getCode());

        List<MemberCertHistory> list = memberCertHistoryMapper.selectByCondition(condition);
        if( list != null && !list.isEmpty() ){
            if( list.size() == 1 ){
                return list.get(0);
            }else{
                throw new BizException(MemberCode.MEMBER_REQUEST_APPROVAL_ERROR,certId + "有多个未审批的结果");
            }
        }
        return null;
    }

    private static boolean checkCertTypeForContinue(MemberCertDTO memberCertDTO, boolean hasShipIdentity) {
        if( MemberCertTypeEnum.IDENTITY_LICENSE_SHIPOWNER.getCode().equals(memberCertDTO.getCertType())){
            return true;
        }
        //如果是道路运输许可证并且0-不需要上传图片，则跳过，不做处理
        if (StringUtils.equals(memberCertDTO.getCertType(),MemberCertTypeEnum.ROAD_TRANSPORT_LICENSE.getCode()) && memberCertDTO.getNeedCert() == 0) {
            return true;
        }
        //如果是营业执照，且当前有个体船东身份证，则不校验营业执照
        return hasShipIdentity && MemberCertTypeEnum.BUSINESS_LICENSE.getCode().equals(memberCertDTO.getCertType());
    }

    private MemberCertHistory getMemberCertHistory(String memberId, String accountId, String requestId, MemberCertDTO memberCertDTO) {
        MemberCertHistory certHistory = new MemberCertHistory();
        BeanUtils.copyProperties(memberCertDTO, certHistory);
        certHistory.setRequestId(requestId);
        if (certHistory.getCertType().equals(MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
            certHistory.setAccountId(accountId);
        }
        certHistory.setMemberId(memberId);
        certHistory.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
        certHistory.setCertId(uuidGenerator.gain());
        certHistory.setDelFlg(Boolean.FALSE);
        return certHistory;
    }

    private void memberCertCheck(MemberCertDTO memberCertDTO) {
        if (StringUtils.isEmpty(memberCertDTO.getAttachmentId())) {
            log.error(LOG_MEMBER_CERT_CHECK,memberCertDTO);
            throw new BizException(MemberCode.IMAGE_IS_EMPTY);
        }
        if (StringUtils.isEmpty(memberCertDTO.getCertType())) {
            log.error(LOG_MEMBER_CERT_CHECK,memberCertDTO);
            throw new BizException(MemberCode.UNKNOWN_CERTIFICATES, memberCertDTO.getCertName());
        }
        if (memberCertDTO.getEffectiveTime() != null && memberCertDTO.getEffectiveTime().getTime() < System.currentTimeMillis() ) {
            log.error(LOG_MEMBER_CERT_CHECK,memberCertDTO);
            throw new BizException(MemberCode.CERT_IS_OVER_TIME, memberCertDTO.getCertName());
        }
    }
}
