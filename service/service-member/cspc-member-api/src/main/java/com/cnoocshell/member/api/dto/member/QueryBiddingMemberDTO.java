package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryBiddingMemberDTO {
    @ApiModelProperty("客户名称")
    private String memberName;
    @ApiModelProperty("CRM代码")
    private String crmCode;
    @ApiModelProperty("主要意向商品ID")
    private String mainGoods;
    @ApiModelProperty("次要意向商品ID")
    private String minorGoods;

    @ApiModelProperty("竞价产品编码")
    private String goodsCode;

    @ApiModelProperty("不在查询中的客户编码")
    private List<String> notInMemberCodes;

    @ApiModelProperty("在改会员编码范围内")
    private List<String> inMemberCodes;
}
