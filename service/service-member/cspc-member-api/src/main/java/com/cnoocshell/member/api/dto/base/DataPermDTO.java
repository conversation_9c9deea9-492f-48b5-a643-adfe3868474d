package com.cnoocshell.member.api.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataPermDTO implements Serializable {

    private static final long serialVersionUID = 6299253452756734922L;
    @ApiModelProperty("标准区域权限列表")
    private List<String> standardRegionPerms;
    @ApiModelProperty("销售区域权限列表")
    private List<String> saleRegionPerms;
}
