package com.cnoocshell.member.api.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberIntentionReportResultDTO {
    @ApiModelProperty(value = "会员ID")
    private String memberId;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     *{@link com.cnoocshell.member.api.enums.IntentionTypeEnum}
     */
    @ApiModelProperty(value = "意向类型")
    private Integer intentionType;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;
}
