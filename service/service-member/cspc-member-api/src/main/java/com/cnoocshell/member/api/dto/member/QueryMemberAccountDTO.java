package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMemberAccountDTO {
    private List<String> memberCodes;

    private List<String> roleCodes;

    public QueryMemberAccountDTO(List<String> memberCodes, List<String> roleCodes){
        this.memberCodes = memberCodes;
        this.roleCodes = roleCodes;
        // 保证金状态默认为null，表示不查询保证金状态
        this.depositStatus = null;
    }

    @ApiModelProperty("保证金状态")
    private String depositStatus;
}
