package com.cnoocshell.integration.consumer;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.integration.service.ISapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class SapConsumer {
    private final ISapService iSapService;

    @KafkaListener(topics = MqTopicConstant.CREATE_SAP_CONTRACT_TOPIC)
    public void sendEmailConsumer(ConsumerRecord<String, String> data) {
        log.info("创建SAP合同开始");
        if(Objects.isNull(data) || CharSequenceUtil.isBlank(data.value())){
            log.info("创建SAP合同开始 消费 入参为空");
            return;
        }
        try{
            SapContractDTO param = JSONUtil.toBean(data.value(),SapContractDTO.class);
            if(Objects.isNull(param)){
                log.error("创建SAP合同开始 消费 入参转JSON为空");
                return;
            }
            iSapService.createSapContract(param,null,true);
            log.info("创建SAP合同成功");
        }catch (Exception e){
            log.error("创建SAP合同 异常：",e);
            throw new BizException(BasicCode.CUSTOM_ERROR,"创建合同异常");
        }
        log.info("创建SAP合同结束");
    }
}
