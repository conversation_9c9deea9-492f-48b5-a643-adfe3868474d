package com.cnoocshell.plugin;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenxi
 * @Date: 2018-08-10 10:56
 * @description: 注释解析
 **/
@Slf4j
public class JavaDocReader {

    // 显示DocRoot中的基本信息
    public Map<String,String> createDoc(Class<?> clazz){
        Map<String,String> map = new HashMap<>();
        log.info("createDoc:{}", clazz.getSimpleName());
        return map;
    }

}