package com.cnoocshell.base.api.dto.cloud;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 
 * @Author: <EMAIL>
 *
 */
@ApiModel("获取上传签名dto")
@Data
public class AttachmentSignDTO {

	/**
	 * 读写级别：0,默认, 1 私有读写, 2 公有读私有写, 3 公有读写
	 */
	@ApiModelProperty("读写级别：0,默认, 1 私有读写, 2 公有读私有写, 3 公有读写")
	private Integer readWriteLevel = 0;

	/**
	 * 文件的全名称，包含后缀名
	 */
	@NotBlank
	@ApiModelProperty(value= "文件的全名称，包含后缀名", name = "fileName", example = "yyzz")
	private String fileName;

	/**
	 * 系统账户id
	 */
	@ApiModelProperty(value = "系统账户id", name = "accountId")
	private String accountId;

	/**
	 * 业务场景的英文简写
	 */
	@ApiModelProperty(value = "业务场景的英文简写", name = "businessScenario")
	private String businessScenario;
}
