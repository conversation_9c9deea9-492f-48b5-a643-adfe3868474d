package com.cnoocshell.order.service;

import com.cnoocshell.order.api.dto.salesPlan.*;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestParam;

public interface SalesPlanService {

    PageInfo<SalesPlanPageResponseDTO> findSalePlanByCondition(SalesPlanPageRequestDTO requestDTO);

    String addSalesPlan(SalesPlanInfoDTO requestDTO);

    SalesDetailDTO findSalePlanById(String id,String accountId,String memberId);

    int updateSalesPlan(SalesPlanInfoDTO requestDTO);

    int revokeSalesPlan(String id);

    void removeDraft(RemoveSalesPlanDTO param);
}
