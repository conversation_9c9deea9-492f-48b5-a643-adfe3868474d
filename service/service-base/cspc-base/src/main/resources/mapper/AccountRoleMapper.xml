<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.AccountRoleMapper">

    <select id="findAccountByGoodsCode"  resultType="com.cnoocshell.base.api.dto.DataPermissionAccountQueryDTO">
        SELECT
        DISTINCT
        sdp.goods_code ,
        sdp.account_id
        FROM
        sys_data_permission sdp
        inner join sys_account_role sar on sar.account_id = sdp.account_id
        where
        sdp.goods_code in
        <foreach item="item" index="index" collection="accountInfoDTO.goodsCodeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        AND sar.role_code = #{accountInfoDTO.roleCode}
        and sdp.del_flg = 0
        and sar.del_flg = 0
    </select>

    <select id="queryAccountRole" resultType="com.cnoocshell.base.api.dto.account.SimpleAccountRoleDTO">
        select tb2.account_id,tb1.role_id,tb1.role_code,tb1.role_name,tb1.role_type,tb1.description,tb1.status from
        sys_role tb1
        left join sys_account_role tb2 on tb1.role_code=tb2.role_code
        where tb1.del_flg=0 and tb2.del_flg=0
        and tb1.status=1
        <if test="param.roleNameByLike != null and param.roleNameByLike !=''">
            AND tb1.role_name like CONCAT('%',#{param.roleNameByLike},'%')
        </if>
        <if test="param.accountIds != null and param.accountIds.size>0">
            AND tb2.account_id IN
            <foreach collection="param.accountIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="param.roleCodes != null and param.roleCodes.size>0">
            AND tb1.role_code IN
            <foreach collection="param.roleCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        order by tb2.create_time desc
    </select>

    <select id="querySimpleAccountRole" resultType="com.cnoocshell.base.api.dto.report.AccountReportRoleResultDTO">
        select
        tb1.account_id ,
        tb1.role_code ,
        tb2.role_name
        from sys_account_role tb1
        left join sys_role tb2 on tb1.role_code =tb2.role_code and tb2.del_flg = 0
        where tb1.del_flg =0
        and tb1.account_id IN
        <foreach collection="accountIds" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
    </select>
</mapper>