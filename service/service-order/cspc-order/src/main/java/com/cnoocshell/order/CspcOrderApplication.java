package com.cnoocshell.order;

import com.cnoocshell.common.annotation.ExcludeFromComponetScan;
import com.cnoocshell.kafka.annotation.EnableKafka;
import com.cnoocshell.order.config.MyBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@SpringBootApplication
@EnableFeignClients({
		"com.cnoocshell.order.api",
		"com.cnoocshell.member.api",
		"com.cnoocshell.goods.api",
		"com.cnoocshell.base.api",
		"com.cnoocshell.integration.service"
})
@EnableKafka
@EnableAsync
@ComponentScan(value = {
		"com.cnoocshell.order",
		"com.cnoocshell.common.config",
		"com.cnoocshell.common.service.common",
		"com.cnoocshell.common.service.lock",
		"com.cnoocshell.common.aop",
		"com.cnoocshell.common.service.reference",
		"com.cnoocshell.common.service",
		"com.cnoocshell.common.filter",
		"com.cnoocshell.common.cache",
		"com.cnoocshell.common.utils",
		"com.cnoocshell.common.configurer",
		"com.cnoocshell.kafka"
	},
	excludeFilters = {@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)},
	nameGenerator = MyBeanNameGenerator.class)
@MapperScan(value = "com.cnoocshell.order.dao", nameGenerator = MyBeanNameGenerator.class)
@EnableDiscoveryClient
@Slf4j
public class CspcOrderApplication {

	public static void main(String[] args) {
		SpringApplication.run(CspcOrderApplication.class, args);
		log.info("-------------CspcOrderApplication is started--------------");
	}

	@Bean(destroyMethod = "shutdown")
	public ThreadPoolExecutor threadPoolExecutor() {
		return new ThreadPoolExecutor(
				16, 32,
				60, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(1000),
				Executors.defaultThreadFactory(),
				new ThreadPoolExecutor.AbortPolicy()
		);
	}
}
