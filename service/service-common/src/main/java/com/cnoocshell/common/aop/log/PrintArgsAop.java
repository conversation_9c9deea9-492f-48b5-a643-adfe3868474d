package com.cnoocshell.common.aop.log;

import com.cnoocshell.common.annotation.PrintArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Aspect
public class PrintArgsAop {

    @Value("${spring.profiles.active}")
    private String profile;

    @Pointcut("@annotation(com.cnoocshell.common.annotation.PrintArgs)")
    public void entryPoint() {
        //do nothing
    }

    private String className = null;
    private String methodName = null;

    @Before(value = "entryPoint() && @annotation(sl)")
    public void before(JoinPoint joinPoint, PrintArgs sl) {
        className = null;
        methodName = null;
        try {
            String [] envs = sl.env();
            if(envs == null || envs.length == 0 ){
                return;
            }
            for (String env : envs) {
                if(StringUtils.equals(env,profile)){
                    className = joinPoint.getTarget().getClass().getSimpleName();
                    methodName = joinPoint.getSignature().getName();
                    Object[] args = joinPoint.getArgs();
                    if( args == null || args.length == 0){
                        log.info("{}.{},args.length=0",className,methodName);
                    }else{
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(className).append(".").append(methodName);
                        for (int i = 0; i <joinPoint.getArgs().length ; i++) {
                            stringBuilder.append(",arg").append(i).append(":{}");
                        }
                        log.info(stringBuilder.toString(),joinPoint.getArgs());
                    }
                    break;
                }
            }
        } catch (Throwable e) {
            log.warn("参数打印出错: {}",e.getMessage(),e);
        }
    }

    @AfterReturning(pointcut = "entryPoint() && @annotation(sl)",returning = "ret")
    public void after(JoinPoint joinPoint,PrintArgs sl, Object ret) {
        try {
            String [] envs = sl.env();
            if(envs == null || envs.length == 0 ){
                return;
            }
            for (String env : envs) {
                if (StringUtils.equals(env, profile)) {
                    log.info("{}.{} return:{}",className,methodName,ret);
                    break;
                }
            }
        }catch (Throwable e) {
            log.warn("参数打印出错: {}",e.getMessage(),e);
        }
    }
}
