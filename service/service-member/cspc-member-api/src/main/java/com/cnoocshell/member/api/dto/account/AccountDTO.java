package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.member.api.dto.BaseDTO;
import com.cnoocshell.member.api.dto.base.DataPermDTO;
import com.cnoocshell.member.api.dto.base.RoleDTO;
import com.cnoocshell.member.api.dto.member.MemberSimpleDTO;
import com.cnoocshell.member.api.session.LoginInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @DESCRIPTION:
 */
@ApiModel("账号DTO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 8471628931641880848L;

    /**
     * 账户id
     */
    @ApiModelProperty("账户id")
    private String accountId;

    /**
     * 账户code
     */
    @ApiModelProperty("账户code")
    private String accountCode;

    /**
     * 账户名(登陆使用，唯一)
     */
    @ApiModelProperty("账户名(登陆使用，唯一)")
    private String accountName;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 账户昵称
     */
    @ApiModelProperty("账户昵称")
    private String accountNickname;

    /**
     * 0个人/1企业主账号/2企业子账号
     */
    @ApiModelProperty("账号类型0个人/1企业主账号/2企业子账号")
    private Integer accountType;

    @ApiModelProperty("账号平台类型(员工管理列表查询显示专用 buyer-买家子账号/seller-卖家子账号)")
    private String platform;
    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private String memberId;

    /**
     * 会员code
     */
    @ApiModelProperty("会员code")
    private String memberCode;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名称")
    private String memberName;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名称")
    private String memberShortName;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    /**
     * 账号绑定id(多个绑定在一起的账号该id相同)
     */
    @ApiModelProperty("账号绑定id(多个绑定在一起的账号该id相同)")
    private String bindingId;

    /**
     * 默认登陆账号（如果有多个绑定在一起的账号，如果有一个是默认登陆账号，则自动登陆该账号）
     */
    @ApiModelProperty("默认登陆账号（如果有多个绑定在一起的账号，如果有一个是默认登陆账号，则自动登陆该账号）")
    private Boolean defaultAccount;

    /**
     * 是否个人司机
     */
    @ApiModelProperty("是否个人司机")
    private Boolean personDriver;

    /**
     * 是否承运商司机
     */
    @ApiModelProperty("是否承运商司机")
    private Boolean entDriver;

    /**
     * 是否业务员
     */
    @ApiModelProperty("是否业务员")
    private Boolean salesman;

    /**
     * 是否可以手机登陆（默认都可以手机登陆，如果手机号不唯一，且没有绑定，则不可一手机号登陆）
     */
    @ApiModelProperty("是否可以手机登陆（默认都可以手机登陆）")
    private Boolean phoneLogin;

    /**
     * 账号状态（ 0 可用 1 禁用 ）
     */
    @ApiModelProperty("账号状态（ 0 可用 1 禁用 ）")
    private Boolean status;

    /**
     * 锁定状态
     */
    @ApiModelProperty("锁定状态（ 0 未锁定 1 锁定 ）")
    private Boolean lockStatus;

    /**
     * 锁定结束时间
     */
    @ApiModelProperty("锁定结束时间")
    private Date lockEndTime;

    /**
     * 锁定原因
     */
    @ApiModelProperty("锁定原因")
    private String lockReason;

    /**
     * 是否需要修改密码
     */
    @ApiModelProperty("锁定原因")
    private Boolean needUpdatePassword;

    /**
     * 手机号上次修改时间
     */
    @ApiModelProperty("手机号上次修改时间")
    private Date mobileUpdateTime;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 性别
     */
    @ApiModelProperty("性别（0男    1女    2保密）")
    private Integer sex;

    /**
     * 头像大图URL
     */
    @ApiModelProperty("头像大图URL")
    private String headPic;

    /**
     * 头像小图URL
     */
    @ApiModelProperty("头像小图URL")
    private String headPicMin;

    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private Date birthDay;

    /**
     * 微信号
     */
    @ApiModelProperty("微信号")
    private String wechatId;

    /**
     * 微信免登id
     */
    @ApiModelProperty("微信openId")
    private String wechatOpenId;

    /**
     * 微信名称
     */
    @ApiModelProperty("微信名称")
    private String wechatName;

    /**
     * 微信头像URL
     */
    @ApiModelProperty("微信头像URL")
    private String wechatAvatarUrl;

    /**
     * 登陆设备token(手机每次登陆需更新该字段)
     */
    @ApiModelProperty("登陆设备token(手机每次登陆需更新该字段)")
    private String driverToken;

    /**
     * 登陆设备系统（ios或android）
     */
    @ApiModelProperty("登陆设备系统（ios或android）")
    private String mobileOs;

    /**
     * 登录日期
     */
    @ApiModelProperty("上次登录时间")
    private Date lastLoginDate;

    /**
     * 上次登录地址ip
     */
    @ApiModelProperty("上次登录地址ip")
    private String lastLoginIp;

    /**
     * 注册来源(0 默认  1 手机app   2微信程序  以后有其它方式再加)
     */
    @ApiModelProperty("注册来源(0 默认  1 手机app   2微信程序  以后有其它方式再加)")
    private Integer registerType;

    /**
     * 注册时使用的应用名称
     */
    @ApiModelProperty("注册时使用的应用名称")
    private String registerApp;

    /**
     * 短信签名
     */
    @ApiModelProperty("短信签名")
    private String registerSign;

    /**
     * 是否接入erp
     */
    @ApiModelProperty("是否接入ERP")
    private Boolean hasErp = false;

    /**
     * 角色列表
     */
    @ApiModelProperty("角色列表")
    private List<RoleDTO> roleList;

    @ApiModelProperty("角色列表")
    private List<String> roleTypeList;

    /**
     * 销售区域id列表
     */
    @ApiModelProperty("人员数据权限-销售区域id列表")
    private List<String> saleRegionInfoIdList;

    /**
     * 销售区域map列表
     */
    @ApiModelProperty("人员数据权限-销售区域id列表")
    private List<Map<String,String>> saleRegionMapList;

    @ApiModelProperty("人员数据权限-行政区域adCode列表")
    private List<String> regionAdCodeList;

    @ApiModelProperty("数据权限-账号对应的仓库id")
    private List<String> accountStoreIdList;
    /**
     * 数据权限列表
     */
    @ApiModelProperty("据权限列表")
    private DataPermDTO datapermDTO;

    /**
     * 会员信息
     */
    @ApiModelProperty("会员信息")
    private MemberSimpleDTO memberSimpleDTO;

    /**
     * 来自mb_account_other_info
     */
    @ApiModelProperty("登录后是否需要短信二次认证")
    private Boolean loginSmsCheck;

    @ApiModelProperty("金融留资页面权限")
    private Integer loanFinancePermission = 0;

    @ApiModelProperty("注册返回登录信息")
    private LoginInfo loginInfo;
}
