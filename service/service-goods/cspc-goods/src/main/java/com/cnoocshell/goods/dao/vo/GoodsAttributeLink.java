package com.cnoocshell.goods.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "go_goods_attribute_link")
public class GoodsAttributeLink implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "goods_attri_link_id")
    private String goodsAttriLinkId;

    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品属性id
     */
    @Column(name = "goods_attri_id")
    private String goodsAttriId;

    /**
     * 商品属性名
     */
    @Column(name = "attri_name")
    private String attriName;

    /**
     * 商品属性值id
     */
    @Column(name = "value_id")
    private String valueId;

    /**
     * 商品属性值code
     */
    @Column(name = "value_code")
    private String valueCode;
    
    /**
     * 商品属性值
     */
    @Column(name = "value")
    private String value;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 修改者
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return goods_attri_link_id - 主键
     */
    public String getGoodsAttriLinkId() {
        return goodsAttriLinkId;
    }

    /**
     * 设置主键
     *
     * @param goodsAttriLinkId 主键
     */
    public void setGoodsAttriLinkId(String goodsAttriLinkId) {
        this.goodsAttriLinkId = goodsAttriLinkId == null ? null : goodsAttriLinkId.trim();
    }

    /**
     * 获取商品id
     *
     * @return goods_id - 商品id
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品id
     *
     * @param goodsId 商品id
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品属性id
     *
     * @return goods_attri_id - 商品属性id
     */
    public String getGoodsAttriId() {
        return goodsAttriId;
    }

    /**
     * 设置商品属性id
     *
     * @param goodsAttriId 商品属性id
     */
    public void setGoodsAttriId(String goodsAttriId) {
        this.goodsAttriId = goodsAttriId == null ? null : goodsAttriId.trim();
    }

    /**
     * 获取商品属性名
     *
     * @return attri_name - 商品属性名
     */
    public String getAttriName() {
        return attriName;
    }

    /**
     * 设置商品属性名
     *
     * @param attriName 商品属性名
     */
    public void setAttriName(String attriName) {
        this.attriName = attriName == null ? null : attriName.trim();
    }

    /**
     * 获取商品属性值id
     *
     * @return value_id - 商品属性值id
     */
    public String getValueId() {
        return valueId;
    }

    /**
     * 设置商品属性值id
     *
     * @param valueId 商品属性值id
     */
    public void setValueId(String valueId) {
        this.valueId = valueId == null ? null : valueId.trim();
    }

    /**
     * 获取商品属性值
     *
     * @return value - 商品属性值
     */
    public String getValue() {
        return value;
    }

    /**
     * 设置商品属性值
     *
     * @param value 商品属性值
     */
    public void setValue(String value) {
        this.value = value == null ? null : value.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建者
     *
     * @return create_user - 创建者
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建者
     *
     * @param createUser 创建者
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取修改者
     *
     * @return update_user - 修改者
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改者
     *
     * @param updateUser 修改者
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取删除标志,1-删除，0-正常
     *
     * @return del_flg - 删除标志,1-删除，0-正常
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标志,1-删除，0-正常
     *
     * @param delFlg 删除标志,1-删除，0-正常
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    public String getValueCode() {
		return valueCode;
	}

	public void setValueCode(String valueCode) {
		this.valueCode = valueCode;
	}

	@Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", goodsAttriLinkId=").append(goodsAttriLinkId);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsAttriId=").append(goodsAttriId);
        sb.append(", attriName=").append(attriName);
        sb.append(", valueId=").append(valueId);
        sb.append(", valueCode=").append(valueCode);
        sb.append(", value=").append(value);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}