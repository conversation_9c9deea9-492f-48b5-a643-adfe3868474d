<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.RolePermissionMapper">
    <select id="existPermission" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM sys_role_permission
        WHERE del_flg = 0
        AND role_code IN
        <foreach collection="roleCodes" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        AND permission_code IN
        <foreach collection="permissionCodes" item="item2" open="(" separator="," close=")">
            #{item2}
        </foreach>
    </select>
</mapper>