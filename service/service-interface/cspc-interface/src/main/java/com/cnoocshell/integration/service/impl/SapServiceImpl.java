package com.cnoocshell.integration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cnoocshell.common.config.feign.ServiceException;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.integration.biz.sms.IInterfaceLogInfoBiz;
import com.cnoocshell.integration.dao.vo.InterfaceLogInfo;
import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractResultDTO;
import com.cnoocshell.integration.dto.sap.SapTokenDTO;
import com.cnoocshell.integration.enums.InterfaceEnum;
import com.cnoocshell.integration.service.ISapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class SapServiceImpl implements ISapService {
    private final IInterfaceLogInfoBiz iInterfaceLogInfoBiz;
    private final KafkaTemplate<String,String> kafkaTemplate;
    private static final String SUCCESS_CODE = "S";

    public static final String SAP_TOKEN = "SAP_TOKEN";
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BizRedisService redisService;

    @Value("${interface.sap.tokenUrl}")
    String tokenUrl;

    @Value("${interface.sap.authorization}")
    String authorizationa;


    /**
     *创建合同调用SAP 接口地址
     */
    @Value("${interface.sap.contract.create}")
    private String createSapContractApi;

    @Override
    public BiddingDealContractDTO createSapContract(SapContractDTO param,String messageId,boolean isAsync) {
        InterfaceLogInfo logInfo = new InterfaceLogInfo();
        logInfo.setInterfaceId(InterfaceEnum.SAP_CREATE_CONTRACT.getCode());
        logInfo.setMessageId(messageId);
        logInfo.setInterfaceName(InterfaceEnum.SAP_CREATE_CONTRACT.getName());

        logInfo.setCreateTime(new Date());
        logInfo.setStartTime(new Date());

        String bodyParam = JSON.toJSONString(param);
        logInfo.setParams(bodyParam);
        String dealNo = param.getIS_SC().getHEADER().getZZVBELN();

        BiddingDealContractDTO contractInfo = new BiddingDealContractDTO();
        contractInfo.setDealNo(dealNo);
        try{
            String response = HttpUtil.createGet(createSapContractApi)
                    .header(Header.CONTENT_TYPE,"application/json")
                    .bearerAuth(this.getSapToken())
                    .body(bodyParam)
                    .execute()
                    .body();
            log.info("调用创建SAP合同接口 结果 response：{}",response);
            logInfo.setReturnMessage(response);

            if(!JSONUtil.isTypeJSON(response)){
                contractInfo.setCreateContractFailReason(response);
                logInfo.setErrorMessage(response);
                log.error("创建SAP合同失败：{}",response);
                return contractInfo;
            }
            JSONObject respJson = JSON.parseObject(response);

            SapContractResultDTO result = JSONUtil.toBean(respJson.getString("n0:ZFM_SD_ECONTRACT_CREATE_WRAPResponse"),SapContractResultDTO.class);

            String status = null;
            String message = null;
            String sapContractNo = null;
            if (Objects.nonNull(result)) {
                if (Objects.nonNull(result.getET_RESULT())) {
                    if (Objects.nonNull(result.getET_RESULT().getItem())) {
                        SapContractResultDTO.Item item = result.getET_RESULT().getItem();
                        status = item.getSTATUS();
                        message = item.getMESSAGE();
                        sapContractNo = item.getVBELN();
                        dealNo = item.getZZVBELN();
                    }
                }
            }

            logInfo.setReturnCode(status);

            if(SUCCESS_CODE.equals(status)) {
                contractInfo.setSapContractNo(sapContractNo);
                contractInfo.setCreateContractStatus(true);
                //success 通知更新合同号
            }else{
                contractInfo.setCreateContractFailReason(message);
                log.error("调用创建SAP合同接口 失败：{}",message);
            }
            log.info("通知更新合同信息结束");
        }catch (Exception e){
            log.error("调用创建SAP合同接口异常 error:",e);
            String errorMsg = ExceptionUtil.stacktraceToString(e);
            contractInfo.setCreateContractFailReason(errorMsg);
            logInfo.setErrorMessage(errorMsg);
            throw new BizException(BasicCode.CUSTOM_ERROR,"调用创建SAP合同接口异常");
        }finally {
            logInfo.setEndTime(new Date());
            iInterfaceLogInfoBiz.insert(logInfo);

            log.info("通知更新合同信息开始 dealNo:{} isAsync:{} contractInfo:{}", dealNo,isAsync, contractInfo);
            if(BooleanUtil.isTrue(isAsync))
                kafkaTemplate.send(MqTopicConstant.CREATE_SAP_CONTRACT_SUCCESS_TOPIC, JSONUtil.toJsonStr(contractInfo));
        }
        return contractInfo;
    }
    @Override
    public String getSapToken() {
        String sapToken = redisService.get(SAP_TOKEN);
        if (StringUtils.isBlank(sapToken)) {
            return getToken();
        }
        return sapToken;
    }

    private String getToken(){
        Map<String, Object> body = new HashMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization",authorizationa);
        ResponseEntity<String> exchange = restTemplate.exchange(tokenUrl,
                HttpMethod.POST, new HttpEntity<>(body,headers), String.class);
        if (exchange.getBody() == null) {
            throw new RuntimeException("调用SAP鉴权服务获取Token发⽣错误,result:"+ exchange.getBody());
        }
        SapTokenDTO sapTokenDTO = JSON.parseObject(exchange.getBody(), SapTokenDTO.class);
        redisService.setex(SAP_TOKEN,1800 ,sapTokenDTO.getAccessToken());
        return sapTokenDTO.getAccessToken();
    }
}
