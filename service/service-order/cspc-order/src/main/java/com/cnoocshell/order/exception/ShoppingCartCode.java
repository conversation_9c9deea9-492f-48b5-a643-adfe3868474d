package com.cnoocshell.order.exception;

import com.cnoocshell.common.exception.CodeMeta;

public class ShoppingCartCode {

    // 添加私有构造函数
    private ShoppingCartCode() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    public static final CodeMeta NOT_ADD_CART = new CodeMeta("000001", "NOT_ADD_CART", "加入购物车出错{}", "NOT_ADD_CART error, see: {}");
    public static final CodeMeta NOT_FOUND_CART = new CodeMeta("000002", "NOT_FOUND_CART", "查询购物车出错{}", "NOT_FOUND_CART error, see: {}");
    public static final CodeMeta NOT_UPDATE_CART = new CodeMeta("000003", "NOT_UPDATE_CART", "更新购物车出错{}", "NOT_UPDATE_CART error, see: {}");
    public static final CodeMeta NOT_DELETE_CART = new CodeMeta("000004", "NOT_DELETE_CART", "删除购物车出错{}", "NOT_DELETE_CART error, see: {}");

}
