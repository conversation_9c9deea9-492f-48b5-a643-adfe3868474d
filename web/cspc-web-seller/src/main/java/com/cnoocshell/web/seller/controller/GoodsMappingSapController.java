package com.cnoocshell.web.seller.controller;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsSapMappingDTO;
import com.cnoocshell.goods.api.service.IGoodsSapMappingService;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/goodsMappingSap")
@Api(tags = {"GoodsController"}, description = "商品与SAP映射服务")
public class GoodsMappingSapController {
    private final IGoodsSapMappingService goodsSapMappingService;

    @PostMapping("/selectSapGoodsMappingList")
    @ApiOperation(value = "保存商品映射sap商品", notes = "保存商品映射sap商品")
    public ItemResult<PageInfo<GoodsSapMappingDTO>> selectSapGoodsMappingList(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        checkLogin(loginInfo);
        return goodsSapMappingService.selectSapGoodsMappingList(goodsSapMappingDTO);
    }

    @PostMapping("/createSapGoodsMapping")
    @ApiOperation(value = "保存商品映射sap商品", notes = "保存商品映射sap商品")
    public ItemResult<String> createSapGoodsMapping(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        goodsSapMappingDTO.setCreateUser(loginInfo.getAccountId());
        return goodsSapMappingService.createSapGoodsMapping(goodsSapMappingDTO);
    }

    @PostMapping("/updateSapGoodsMapping")
    @ApiOperation(value = "修改商品映射sap商品", notes = "修改商品映射sap商品")
    public ItemResult<String> updateSapGoodsMapping(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        checkLogin(loginInfo);
        goodsSapMappingDTO.setUpdateUser(loginInfo.getAccountId());
        return goodsSapMappingService.updateSapGoodsMapping(goodsSapMappingDTO);
    }

    @PostMapping("/deleteSapGoodsMapping")
    @ApiOperation(value = "删除商品映射sap商品", notes = "删除商品映射sap商品")
    public ItemResult<String> deleteSapGoodsMapping(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        checkLogin(loginInfo);
        goodsSapMappingDTO.setUpdateUser(loginInfo.getAccountId());
        return goodsSapMappingService.deleteSapGoodsMapping(goodsSapMappingDTO);
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

}

