package com.cnoocshell.integration.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.integration.dto.email.EmailSendDTO;
import com.cnoocshell.integration.service.IEmailSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags={"EmailMessageController"},description = "邮件服务")
@RestController
@RequestMapping("/message/email")
@RequiredArgsConstructor
public class EmailMessageController {

    @Autowired
    private IEmailSendService emailSendService;

    @ApiOperation("发送邮件")
    @PostMapping(value="/send")
    public ItemResult<Boolean> sendEmailCode(@RequestBody EmailSendDTO dto){
        return ItemResult.success(emailSendService.sendEmailCode(dto));
    }
}