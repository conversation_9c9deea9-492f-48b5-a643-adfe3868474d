package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSaveDepositStatusDTO implements Serializable {

    @ApiModelProperty("会员id")
    private String memberId;

    @ApiModelProperty("保证金状态")
    private String depositStatus;

    @ApiModelProperty("accountId")
    private String accountId;
}
