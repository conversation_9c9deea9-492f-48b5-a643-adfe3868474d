package com.cnoocshell.web.base.dto.member;

import com.cnoocshell.member.api.dto.member.MemberCertQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberBusinessInfoQueryDTO {

    private String requestId;
    @NotBlank
    private String memberId;
    private String memberName;
    @ApiModelProperty("会员简称")
    private String memberShortName;
    @ApiModelProperty("会员code")
    private String memberCode;
    @ApiModelProperty("商标URL")
    private String trademarkUrl;
    private Integer isSyncretic;
    private String organizationCode;
    private String businessLicenseCode;
    private String taxCode;
    private String creditCode;
    private String rigistCountryCode;
    private String rigistProvinceCode;
    private String rigistCityCode;
    private String rigistAreaCode;
    private String rigistStreetCode;
    private String rigistAddressDetail;
    private String legalName;
    private Integer businessStatus;
    private Date registerTime;
    private BigDecimal registerFund;
    private String mainProducts;
    private MemberCertQueryDTO businessCert;
    private MemberCertQueryDTO organizationCert;
    private MemberCertQueryDTO taxCert;
}
