package com.cnoocshell.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cnoocshell.base.api.dto.permission.PermissionMenuTreeDTO;
import com.cnoocshell.base.api.dto.permission.PermissionTreeDTO;
import com.cnoocshell.base.api.dto.permission.QueryRolePermissionDTO;
import com.cnoocshell.base.api.dto.permission.RoleMenuPermissionDTO;
import com.cnoocshell.base.api.enums.PermissionTypeEnum;
import com.cnoocshell.base.biz.IRoleBiz;
import com.cnoocshell.base.biz.impl.PermissionBiz;
import com.cnoocshell.base.biz.impl.RolePermissionBiz;
import com.cnoocshell.base.dao.mapper.PermissionMapper;
import com.cnoocshell.base.dao.vo.Permission;
import com.cnoocshell.base.dao.vo.Role;
import com.cnoocshell.base.dao.vo.RolePermission;
import com.cnoocshell.base.exception.DuplicateString;
import com.cnoocshell.base.exception.DuplicateString;
import com.cnoocshell.base.service.IPermissionService;
import com.cnoocshell.common.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements IPermissionService {

    private final RolePermissionBiz rolePermissionBiz;

    private final PermissionBiz permissionBiz;
    private final IRoleBiz iRoleBiz;
    private final PermissionMapper permissionMapper;

    @Override
    public PermissionTreeDTO findPermissionMenu(String roleCode, String roleType) {
        PermissionTreeDTO resDTO = new PermissionTreeDTO();
        //获取该角色绑定的权限列表
        resDTO.setSelectedMenuCodeList(rolePermissionBiz.findPermissionByRoleCode(roleCode,DuplicateString.PC));
        resDTO.setSelectedMpMenuCodeList(rolePermissionBiz.findPermissionByRoleCode(roleCode,DuplicateString.MP));
        //根据角色类型获取全量菜单列表
        List<Permission> permissionAllList = permissionBiz.findPermissionByRoleType(roleType);
        Map<String, List<Permission>> treeMap = permissionAllList.stream().collect(Collectors.groupingBy(Permission::getParentPermissionCode));
        List<PermissionMenuTreeDTO> menuTreeDTOList = new ArrayList<>();
        treeMap.forEach((key, value) -> {
            //筛选父级节点
            List<PermissionMenuTreeDTO> parentList = value.stream()
                    .filter(permission -> permission.getParentPermissionCode().equals("0"))
                    .map(permission -> {
                        PermissionMenuTreeDTO parentNode = new PermissionMenuTreeDTO();
                        BeanUtils.copyProperties(permission, parentNode);
                        return parentNode;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(parentList)) {
                parentList.forEach(parent -> {
                    //递归获取子节点
                    List<PermissionMenuTreeDTO> childList = menuTree(permissionAllList, parent.getPermissionCode());
                    parent.setChild(childList);
                    menuTreeDTOList.add(parent);
                });
            }
        });
        //区分pc和小程序
        if (CollectionUtils.isNotEmpty(menuTreeDTOList)) {
            menuTreeDTOList.forEach(menu -> {
                if (DuplicateString.PC.equals(menu.getAppType())) {
                    resDTO.getMenuList().add(menu);
                } else {
                    resDTO.getMpMenuList().add(menu);
                }
            });
        }
        return resDTO;
    }

    @Override
    public List<RoleMenuPermissionDTO> queryRolePermissions(QueryRolePermissionDTO param) {
        if (CollUtil.isEmpty(param.getRoleCodes()))
            return null;
        List<Role> roles = iRoleBiz.findEffectRoleByCodes(param.getRoleCodes());
        if (CollUtil.isEmpty(roles))
            return null;

        List<String> effectRoleCodes = roles.stream().map(Role::getRoleCode).collect(Collectors.toList());

        Condition condition = new Condition(RolePermission.class);
        condition.createCriteria()
                .andIn(DuplicateString.ROLE_CODE, effectRoleCodes)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);

        List<RolePermission> rolePermissions = rolePermissionBiz.findByCondition(condition);
        if (CollUtil.isEmpty(rolePermissions))
            return null;

        Set<String> permissionCodes = rolePermissions.stream().map(RolePermission::getPermissionCode).collect(Collectors.toSet());

        List<Permission> permissions = permissionMapper.queryRolePermissions(permissionCodes, param.getAppType());
        if (CollUtil.isEmpty(permissions))
            return null;

        //K: permissionType
        List<PermissionMenuTreeDTO> all =BeanUtil.copyToList(permissions,PermissionMenuTreeDTO.class);
        Map<String, List<PermissionMenuTreeDTO>> permissionGroup = CommonUtils.group(all,PermissionMenuTreeDTO::getPermissionType);
        //K:roleType
        Map<String,List<PermissionMenuTreeDTO>> allGroupByRoleType = CommonUtils.group(all,PermissionMenuTreeDTO::getRoleType);

        //K:MENU
        List<PermissionMenuTreeDTO> menuPermissions = BeanUtil.copyToList(CommonUtils.getByKey(permissionGroup, PermissionTypeEnum.MENU.getType()),PermissionMenuTreeDTO.class);
        Map<String,List<PermissionMenuTreeDTO>> menuGroupByRoleType = null;
        if(CollUtil.isNotEmpty(menuPermissions)){
            menuGroupByRoleType = menuPermissions.stream().collect(Collectors.groupingBy(PermissionMenuTreeDTO::getRoleType));
        }

        //ELEMENT
        List<PermissionMenuTreeDTO> elements = BeanUtil.copyToList(CommonUtils.getByKey(permissionGroup, PermissionTypeEnum.ELEMENT.getType()), PermissionMenuTreeDTO.class);
        Map<String,List<PermissionMenuTreeDTO>> elementGroupByRoleType = null;
        if(CollUtil.isNotEmpty(elements)){
            elementGroupByRoleType = elements.stream().collect(Collectors.groupingBy(PermissionMenuTreeDTO::getRoleType));
        }

        Map<String, List<PermissionMenuTreeDTO>> finalElementGroupByRoleType = elementGroupByRoleType;
        Map<String, List<PermissionMenuTreeDTO>> finalMenuGroupByRoleType = menuGroupByRoleType;

        List<RoleMenuPermissionDTO> result = roles.stream().map(Role::getRoleType).distinct().map(v->{
            RoleMenuPermissionDTO roleMenuPermissionDTO = new RoleMenuPermissionDTO();
            roleMenuPermissionDTO.setRoleType(v);
            roleMenuPermissionDTO.setMenuList(CommonUtils.getByKey(finalMenuGroupByRoleType,v));

            roleMenuPermissionDTO.setElementList(CommonUtils.getByKey(finalElementGroupByRoleType,v));
            roleMenuPermissionDTO.setAllList(CommonUtils.getByKey(allGroupByRoleType,v));
            return roleMenuPermissionDTO;
        }).collect(Collectors.toList());

        return result;
    }

    private List<PermissionMenuTreeDTO> menuTree(List<Permission> nodeList, String parentCode) {
        if(CollUtil.isEmpty(nodeList))
            return null;
        List<PermissionMenuTreeDTO> childList = new ArrayList<>();
        nodeList.forEach(child -> {
            if (child.getParentPermissionCode().equals(parentCode)) {
                PermissionMenuTreeDTO childNode = new PermissionMenuTreeDTO();
                BeanUtils.copyProperties(child, childNode);
                childNode.setChild(menuTree(nodeList, child.getPermissionCode()));
                childList.add(childNode);
            }
        });
        return childList;
    }
}
