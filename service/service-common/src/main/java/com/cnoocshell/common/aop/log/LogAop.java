package com.cnoocshell.common.aop.log;

/**
 * @Author: <EMAIL>
 * @Date: 09/11/2018 14:42
 * @DESCRIPTION:记录登陆日志
 */

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.LogAddDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.mq.core.MQMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Component
@Aspect
public class LogAop {

    private LogAddDTO logAddDTO = null;

    @Value("${spring.application.name:unknown}")
    private String centerName;

    @Value("${spring.cloud.client.ipAddress:unknown}")
    private String centerIp;

    @Autowired
    private KafkaTemplate<String,String> kafkaTemplate;

    @Qualifier("myTaskAsyncPool")
    private Executor executor;

    /**
     * 切入点
     */
    @Pointcut("@annotation(com.cnoocshell.common.annotation.AddLog)")
    public void entryPoint() {
        //do nothing
    }

    @Before(value = "entryPoint() && @annotation(sl)")
    public void before(JoinPoint joinPoint,AddLog sl) {
        try {
            logAddDTO = new LogAddDTO();
            logAddDTO.setModuleName(joinPoint.getTarget().getClass().getName());
            logAddDTO.setOperMethod(joinPoint.getSignature().getName());

            Object[] arguments = joinPoint.getArgs();

            getBaseInfo(arguments,sl);

            if(StringUtils.isNotBlank(sl.moduleName())){
                logAddDTO.setModuleName(sl.moduleName());
            }
            if(StringUtils.isNotBlank(sl.operMethod())){
                logAddDTO.setOperMethod(sl.operMethod());
            }
            if(StringUtils.isNotBlank(sl.operSubMethod())){
                logAddDTO.setOperSubMethod(sl.operSubMethod());
            }
            if(StringUtils.isNotBlank(sl.remark())){
                logAddDTO.setRemark(sl.remark());
            }

            logAddDTO.setOperStartTime(System.currentTimeMillis());
        } catch (Throwable e) {
            log.error("before 日志记录出错: {}",e.getMessage(),e);
        }
    }

    private void getBaseInfo(Object[] arguments, AddLog sl) {
        if (ArrayUtil.isEmpty(arguments))
            return;

        if (sl.operatorIndex() != -1 && sl.operatorIndex() < arguments.length) {
            //取操作人id
            logAddDTO.setCreateUser(getFieldValue(arguments[sl.operatorIndex()], sl.operatorFieldName()));
        }
        if (sl.operatorIPIndex() != -1 && sl.operatorIPIndex() < arguments.length) {
            //取操作人ip
            logAddDTO.setCreateUserIp(getFieldValue(arguments[sl.operatorIPIndex()], sl.operatorIPFieldName()));
        }

        if (sl.sessionIdIndex() != -1 && sl.sessionIdIndex() < arguments.length) {
            //取sessionId
            logAddDTO.setSessionId(getFieldValue(arguments[sl.sessionIdIndex()], sl.sessionIdFieldName()));
        }

        bizParam(arguments, sl);
    }

    private void bizParam(Object[] arguments, AddLog sl) {
        if (sl.businessNumberIndex() != -1 && sl.businessNumberIndex() < arguments.length) {
            //取业务编号
            logAddDTO.setBusinessNumber(getFieldValue(arguments[sl.businessNumberIndex()], sl.businessNumberFieldName()));
        }

        if ((sl.encryptedFieldName() != null && sl.encryptedFieldName().length > 0) || (sl.encryptedArgIndex() != null && sl.encryptedArgIndex().length > 0)) {
            //入参加密字段值清空
            encrypted(arguments, sl.encryptedFieldName(), sl.encryptedArgIndex());
        } else {
            //取入参
            try {
                logAddDTO.setOperInputParams(JSON.toJSONString(arguments));
            } catch (Exception e) {
                logAddDTO.setOperInputParams(Arrays.toString(arguments));
            }
        }
    }

    @AfterThrowing(pointcut = "entryPoint()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {
        try {
            if( e != null ) {
                logAddDTO.setException(e.getClass().getName());
                logAddDTO.setErrorMessage(e.getMessage());
                if( e instanceof BizException ){
                    logAddDTO.setExceptionCode(((BizException)e).getErrorCode().getMsg());
                    logAddDTO.setExceptionCode(((BizException)e).getErrorCode().getCode());
                }

                logAddDTO.setStackTraceInfo(ExceptionUtils.getStackTrace(e));
            }
            logAddDTO.setOperResult(LogAddDTO.OPER_RESULT_EXCEPTION);
            sendSysLog();
        }catch (Throwable ex) {
            log.warn("doAfterThrowing 日志记录出错: {}",e,ex);
        }
    }

    @AfterReturning(pointcut = "entryPoint() && @annotation(sl)",returning = "ret")
    public void after(AddLog sl,Object ret) {
        try {
            logAddDTO.setOperEndTime(System.currentTimeMillis());

            if( ret != null ) {
                if( sl != null && sl.encryptedFieldName() != null && sl.encryptedFieldName().length > 0  ){
                    //加密字段值清空
                    encrypted(new Object[]{ret},sl.encryptedFieldName(),sl.encryptedArgIndex());
                }else{
                    //取返回
                    try {
                        logAddDTO.setOperInputParams(JSON.toJSONString(ret));
                    }catch(Exception e){
                        logAddDTO.setOperInputParams(ret.toString());
                    }
                }

                logAddDTO.setOperReturnValue(JSON.toJSONString(ret));
            }

            logAddDTO.setOperResult(LogAddDTO.OPER_RESULT_SUCCESS);

            if( logAddDTO.getOperStartTime() != null ){
                logAddDTO.setOperCostTime(logAddDTO.getOperEndTime().longValue() - logAddDTO.getOperStartTime().longValue());
            }
            sendSysLog();
        }catch (Throwable e) {
            log.warn("after 日志记录出错: {}",e.getMessage(),e);
        }
    }

    public void sendSysLog(){
        initKfkAndExecutor();
        if( logAddDTO == null ){
            return;
        }
        if( StringUtils.isBlank(logAddDTO.getCenterName()) ){
            logAddDTO.setCenterName(centerName);
        }
        if( StringUtils.isBlank(logAddDTO.getCerterIp()) ){
            logAddDTO.setCerterIp(centerIp);
        }

        MQMessage<LogAddDTO> message = new MQMessage<>();
        message.setData(logAddDTO);
        message.setMessageType("sysLog");
        message.setExchange(MqTopicConstant.SYS_LOG_TOPIC);
        message.setMsgId(UUID.randomUUID().toString());
        executor.execute(()->{
            try {
                kafkaTemplate.send(message.getExchange(),JSON.toJSONString(message.getData()));
            } catch (Throwable e) {
                log.error(e.getMessage(),e);
            }
        });
    }

    private void initKfkAndExecutor(){
        if (executor == null ) {
            synchronized (LogAop.class) {
                if (executor == null) {
                    executor = myTaskAsyncPool();
                }
            }
        }
    }

    private Executor myTaskAsyncPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(20);
        //最大线程数
        executor.setMaxPoolSize(40);
        //队列容量
        executor.setQueueCapacity(300);
        //活跃时间
        executor.setKeepAliveSeconds(50);
        //线程名字前缀
        executor.setThreadNamePrefix("openExecutor-");

        // setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        log.info("create Executor success.");
        return executor;
    }

    private void encrypted(Object[] args,String[] fields,int[] index){
        boolean checkIndex = index != null && index.length>0;
        boolean checkFields = fields != null && fields.length>0;
        Set<String> fieldNameSet = checkFields ? Sets.newHashSet(fields) : null;
        List<Object> result = Lists.newArrayListWithExpectedSize(args.length);
        for (int i=0;i<args.length;i++) {
            checkIndexAndFields(args, index, checkIndex, i, checkFields, fieldNameSet, result);
        }
        try {
            logAddDTO.setOperInputParams(JSON.toJSONString(result));
        }catch(Exception e){
            logAddDTO.setOperInputParams(result.toString());
        }
    }

    private void checkIndexAndFields(Object[] args, int[] index, boolean checkIndex, int i, boolean checkFields, Set<String> fieldNameSet, List<Object> result) {
        if(checkIndex) {
            boolean continueFlg = false;
            for (int j : index) {
                if (i ==j){
                    continueFlg=true;
                    break;
                }
            }
            if(continueFlg){
                return;
            }
        }
        if(checkFields){
            Object obj = setFieldValue(args[i], fieldNameSet);
            if( obj != null ) {
                result.add(obj);
            }
        }else{
            result.add(args[i]);
        }
    }

    private Object setFieldValue(Object obj,Set<String> fieldNameSet){
        if( obj == null || fieldNameSet == null || fieldNameSet.isEmpty()){
            return null;
        }

        Field[] fields = obj.getClass().getDeclaredFields();
        if( fields == null || fields.length == 0 ){
            return null;
        }
        Class clazz = obj.getClass();
        Object result = clone(obj);
        if( result == null ){
            return null;
        }
        for (String s : fieldNameSet) {
            try {
                //获取属性
                Field field = clazz.getDeclaredField(s);
                //打开私有访问
                field.setAccessible(true);
                //设置属性值为空
                field.set(result,(Object)null);
            }catch(Exception e){

            }
        }
        return result;
    }

    private String getFieldValue(Object obj,String fieldName){
        if( obj == null ){
            return null;
        }
        if( obj instanceof String ){
            return (String)obj;
        }
        if(StringUtils.isBlank(fieldName)){
            return null;
        }
        try {
            //获取属性
            Field field = obj.getClass().getDeclaredField(fieldName);
            //打开私有访问
            field.setAccessible(true);
            //获取属性值
            return (String)field.get(obj);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }

    private Object clone(Object obj){
        try {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(bout);
            out.writeObject(obj);
            out.close();

            ByteArrayInputStream bin = new ByteArrayInputStream(bout.toByteArray());
            ObjectInputStream in = new ObjectInputStream(bin);
            Object ret = in.readObject();
            in.close();

            return ret;
        } catch (Exception e){
            return null;
        }
    }
}
