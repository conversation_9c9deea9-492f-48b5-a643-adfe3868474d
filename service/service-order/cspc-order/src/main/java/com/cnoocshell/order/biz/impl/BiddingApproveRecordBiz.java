package com.cnoocshell.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.api.dto.bidding.BiddingApproveListDTO;
import com.cnoocshell.order.api.enums.ApproveResultEnum;
import com.cnoocshell.order.api.enums.ApproveTypeEnum;
import com.cnoocshell.order.biz.IBiddingApproveRecordBiz;
import com.cnoocshell.order.dao.mapper.BiddingApproveRecordMapper;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingApproveRecord;
import com.cnoocshell.order.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingApproveRecordBiz extends BaseBiz<BiddingApproveRecord> implements IBiddingApproveRecordBiz {
    @Resource
    private BiddingApproveRecordMapper biddingApproveRecordMapper;

    @Override
    public void saveApproveRecord(List<Bidding> biddingList, ApproveTypeEnum approveType, ApproveResultEnum approveResult,
                                  String reason, String operatorId, String operatorName) {
        if (CollUtil.isEmpty(biddingList))
            return;

        //CR 2025.5.15 审批通过填写备注
        List<BiddingApproveRecord> records = biddingList.stream().map(v -> {
            BiddingApproveRecord target = new BiddingApproveRecord();
            target.setId(super.getUuidGeneratorGain());
            target.setBiddingNo(v.getBiddingNo());
            target.setApproveResult(approveResult.getStatus());
            target.setApproveType(approveType.getStatus());
            target.setCreateUserName(operatorName);
            target.setRejectedReason(reason);

            target.handleUser(operatorId, true);
            return target;
        }).collect(Collectors.toList());

        biddingApproveRecordMapper.insertList(records);
    }

    @Override
    public List<BiddingApproveRecord> listByBiddingNo(String biddingNo) {
        Condition condition = new Condition(BiddingApproveRecord.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);
        condition.orderBy(ColumnConstant.CREATE_TIME).desc();
        return this.findByCondition(condition);
    }

    @Override
    public List<BiddingApproveListDTO> queryApproveList(String biddingId, String status) {
        return biddingApproveRecordMapper.queryApproveList(biddingId, status);
    }
}