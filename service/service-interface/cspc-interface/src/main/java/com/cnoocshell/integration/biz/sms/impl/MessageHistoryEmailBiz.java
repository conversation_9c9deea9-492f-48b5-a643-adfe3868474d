package com.cnoocshell.integration.biz.sms.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.integration.biz.sms.IMessageHistoryEmailBiz;
import com.cnoocshell.integration.dao.mapper.MessageHistoryEmailMapper;
import com.cnoocshell.integration.dao.vo.MessageHistoryEmail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageHistoryEmailBiz  extends BaseBiz<MessageHistoryEmail> implements IMessageHistoryEmailBiz {
    private final MessageHistoryEmailMapper messageHistoryEmailMapper;
}
