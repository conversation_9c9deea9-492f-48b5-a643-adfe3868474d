package com.cnoocshell.common.utils;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.converters.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class BeanConvertUtils {

	private BeanConvertUtils() {
		throw new IllegalStateException("Utility class");
	}

	private static final Logger logger = LoggerFactory.getLogger(BeanConvertUtils.class);
	
	private static BeanUtilsBean beanUtilsBean = new BeanUtilsBean();
	private static BeanUtilsBean beanUtilsBean2 = new CopyFromNotNullBeanUtilsBean();
	
	static {
		beanUtilsBean.getConvertUtils().register(new BigDecimalConverter(null), BigDecimal.class);  
    	beanUtilsBean.getConvertUtils().register(new DateConverter(null), java.util.Date.class);  
    	beanUtilsBean.getConvertUtils().register(new SqlTimestampConverter(null), java.sql.Timestamp.class);  
    	beanUtilsBean.getConvertUtils().register(new SqlDateConverter(null), java.sql.Date.class);  
    	beanUtilsBean.getConvertUtils().register(new SqlTimeConverter(null), java.sql.Time.class);
	}

	public static void copyProperties(Object source, Object target) {
		try {
			beanUtilsBean.copyProperties(target, source);
		} catch (IllegalAccessException | InvocationTargetException e) {
			logger.error("Error occurred while copying properties", e);
			throw new RuntimeException(e);
		}
	}

	public static void copyPropertiesIgnoreNull(Object source, Object target) {
		try {
			beanUtilsBean2.copyProperties(target, source);
		} catch (IllegalAccessException | InvocationTargetException e) {
			logger.error("An error occurred while copying properties with ignore null.", e);
			throw new RuntimeException(e);
		}
	}
	
	public static <T> Page<T> convertPage(Page<?> source, Class<T> targetClass){
		if(source == null) {
			return null;
		}
		Page<T> page = new Page<>(source.getPageNum(),source.getPageSize());
		page.setEndRow(source.getEndRow());
		page.setStartRow(source.getStartRow());
		page.setTotal(source.getTotal());
		if(source.getResult() != null){
			source.getResult().forEach(item -> page.add(BeanConvertUtils.convert(item, targetClass)));
		}
        return page;
    }

    public static <T> List<T> convertList(Collection<?> collection, Class<T> itemClass){
		if((collection == null) || collection.isEmpty()){
			return Lists.newArrayList();
		}
		return collection.stream().filter(Objects::nonNull).map(item->convert(item,itemClass)).collect(Collectors.toList());
	}

	public static <T> Set<T> convertSet(Collection<?> collection, Class<T> itemClass){
		if( collection == null || collection.isEmpty() ){
			return Sets.newHashSet();
		}
		return collection.stream().filter(Objects::nonNull).map(item->convert(item,itemClass)).collect(Collectors.toSet());
	}

	public static <T> T convert(Object source, Class<T> targetClass) {
		if (source == null) {
			return null;
		}

		T instance;
		try {
			instance = targetClass.getDeclaredConstructor().newInstance();
			beanUtilsBean2.copyProperties(instance, source);
			return instance;
		} catch (InstantiationException | IllegalAccessException | InvocationTargetException |
				 NoSuchMethodException e) {
			logger.error("Error occurred during property conversion.", e);
			throw new RuntimeException(e);
		}
	}
	
	/**
	 * 如果source中某字段没值（为null），则该字段不复制，也就是不要把null复制到target当中。
	 * @Author: <EMAIL>
	 *
	 */
	private static class CopyFromNotNullBeanUtilsBean extends BeanUtilsBean {
	    @Override  
	    public void copyProperty(Object bean, String name, Object value) throws IllegalAccessException, InvocationTargetException {  
	        if (value == null) {  
	            return;  
	        }  
	        super.copyProperty(bean, name, value);  
	    }  
	}
}
