package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportSheetDTO{
    @ApiModelProperty("sheetName")
    private String sheetName;

    @ApiModelProperty("数据类型")
    private Class<?> clazz;

    @ApiModelProperty("数据")
    private List<?> dataList;
}
