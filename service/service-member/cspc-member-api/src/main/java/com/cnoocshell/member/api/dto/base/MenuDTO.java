
package com.cnoocshell.member.api.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Author:   jacoyang
 * Date:     05/03/2019
 */
@Data
public class MenuDTO implements Serializable {

    /**
     * 菜单代码
     */
    @ApiModelProperty("菜单代码")
    private String menuId;

    /**
     * 菜单Code
     */
    @ApiModelProperty("菜单Code")
    private String menuCode;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("页面代码")
    private String pageCode;

    /**
     * 菜单对应的URL
     */
    @ApiModelProperty("菜单对应的URL")
    private String menuUrl;

    /**
     * 默认打开的页面
     */
    @ApiModelProperty("默认打开的页面")
    private String defaultPage;

    /**
     * 上级菜单代码
     */
    @ApiModelProperty("级菜单代码")
    private String parentCode;

    /**
     * 上级菜单名称
     */
    @ApiModelProperty("上级菜单名称")
    private String parentName;

    /**
     * 菜单所属项目取值集JZ0001
     */
    @ApiModelProperty("菜单所属项目(取值集JZ0001)")
    private String appType;

    @ApiModelProperty("菜单图标")
    private String icon;

    /**
     * 菜单根
     */
    @ApiModelProperty("菜单根")
    private String menuRoot;

    /**
     * 当前有效状态
     */
    @ApiModelProperty("当前有效状态")
    private String aliveFlg;


    @ApiModelProperty("排序字段 顺序排列")
    private Integer orderNumber;

    @ApiModelProperty("子权限")
    private List<MenuDTO> children=new ArrayList<>();


    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @ApiModelProperty("更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;


    @ApiModelProperty("默认开始页数")
    private Integer pageNum = 1;
    @ApiModelProperty("一页显示数据量")
    private Integer pageSize = 20;

    /**
     * 菜单关联的数据权限
     */
    @ApiModelProperty("菜单关联的数据权限")
    private List<DataPermissionDTO> dataPermList;
}
