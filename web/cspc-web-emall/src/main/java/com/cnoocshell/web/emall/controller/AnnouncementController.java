package com.cnoocshell.web.emall.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.information.api.dto.announcement.AnnouncementApprovalDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementDetailDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementQueryDTO;
import com.cnoocshell.information.api.service.IAnnouncementService;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Created：Wed Aug 29 16:12:44 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:公告服务接口
*/

@Api(tags={"Announcement"},description = "公告服务")
@RestController
@RequestMapping("/announcement")
public class AnnouncementController {

   @Autowired 
   private IAnnouncementService iAnnouncementService;
   @Autowired
   private IAccountService accountService;

   @ApiOperation("按条件翻页查询公告")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<AnnouncementDTO>> findAll(@RequestBody AnnouncementQueryDTO query,@ApiIgnore HttpServletRequest request)throws Exception{
      query.setAnnouncementType(0);
      query.setApprovalStatus(1);
      query.setStartTime(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
      LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
      Date newDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
      query.setEndTime(newDate);
      return new ItemResult<>(iAnnouncementService.findAll(query));
   }

   @ApiOperation("查询单条公告详情(不含审批记录)")
   @PostMapping(value="/findById")
   public ItemResult<AnnouncementDTO> findById(@RequestParam("id") String id)throws Exception{
      return new ItemResult<>(iAnnouncementService.findById(id));
   }


   @ApiOperation("查询单条公告详情(含审批记录)")
   @PostMapping(value="/findDetailById")
   public ItemResult<AnnouncementDetailDTO> findDetailById(@RequestParam("id") String id)throws Exception{
      AnnouncementDetailDTO announcementDetailDTO = iAnnouncementService.findDetailById(id);
      List<AnnouncementApprovalDTO> list = announcementDetailDTO.getList();
      List<AnnouncementApprovalDTO> newList = new ArrayList<>();
      if(list != null && !list.isEmpty() ){
         Set<String> ids = list.stream().map(AnnouncementApprovalDTO::getCreateUser).collect(Collectors.toSet());
         List<AccountDTO> accountList = Lists.newArrayList();
         if( ids != null && !ids.isEmpty() ) {
            accountList = accountService.findByIds(Lists.newArrayList(ids));
         }
         Map<String,String> accountMap = accountList.stream().collect(Collectors.toMap(AccountDTO::getAccountId,AccountDTO::getAccountName));
         newList = list.stream().map(item -> {
            if(accountMap.containsKey(item.getUpdateUser())){
               item.setCreateUserName(accountMap.get(item.getUpdateUser()));
            }
            return item;
         }).collect(Collectors.toList());
      }
      announcementDetailDTO.setList(newList);
      return new ItemResult<>(announcementDetailDTO);
   }

   @ApiOperation("首页公告")
   @GetMapping(value="/homePageList")
   public ItemResult<PageInfo<AnnouncementDTO>> homePageList()throws Exception{
      return new ItemResult<>(iAnnouncementService.homePageList());
   }
}
