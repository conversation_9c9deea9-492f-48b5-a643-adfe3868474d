package com.cnoocshell.member.api.dto.report;

import com.cnoocshell.common.dto.OperatorDTO;
import com.cnoocshell.member.api.dto.member.enums.MemberStatusEnum;
import com.cnoocshell.member.api.enums.IntentionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MemberReportQueryDTO extends OperatorDTO {
    @ApiModelProperty(value = "分页参数 是否分页",notes = "控制分页列表和导出查询是否分页处理",hidden = true)
    private Boolean needPage = Boolean.TRUE;
    @ApiModelProperty(value = "分页参数 页码")
    private Integer pageNum = 1;
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "产品分类", notes = "多选匹配")
    private List<String> categoryCodes;
    @ApiModelProperty(value = "产品分类ID",notes = "实际查询条件 根据categoryCodes查询数据转换得到",hidden = true)
    private List<String> categoryIdByCategoryCodes;

    @ApiModelProperty(value = "产品名称", notes = "模糊匹配")
    private String goodsName;
    @ApiModelProperty(value = "产品名称模糊匹配到的产品编码",notes = "实际查询条件", hidden = true)
    private List<String> goodsCodeByGoodsName;

    @ApiModelProperty(value = "SAP物料编码", notes = "模糊查询 前端传值")
    private String sapMaterialCode;
    @ApiModelProperty(value = "SAP物料编码模糊匹配到的产品编码", hidden = true)
    private List<String> goodsCodeBySapMaterialCode;

    @ApiModelProperty(value = "会员名称", notes = "模糊查询")
    private String memberName;

    @ApiModelProperty(value = "CRM编码", notes = "模糊查询 前端传值")
    private String crmCode;

    /**
     * {@link MemberStatusEnum}
     * 会员启用禁用状态 在查询时会将该数据做条件转换查询
     */
    @ApiModelProperty(value = "启用禁用状态", notes = "启用禁用状态 201：禁用状态 其它状态为正常  1：禁用 0：启用")
    private Integer ableStatus;

    /**
     * {@link IntentionTypeEnum}
     */
    @ApiModelProperty(value = "意向类型", notes = "意向类型单选匹配 1：主要 2：次要")
    private Integer intentionType;

    /**
     * 字典编码：VS_SALES_CHANNEL
     */
    @ApiModelProperty(value = "销售渠道", notes = "字典数据 多选匹配")
    private List<String> saleChannel;

    @ApiModelProperty(value = "数据权限 商品编码",hidden = true)
    private List<String> dataPermissionGoodsCodes;
}
