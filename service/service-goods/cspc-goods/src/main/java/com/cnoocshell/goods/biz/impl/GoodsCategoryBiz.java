package com.cnoocshell.goods.biz.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.common.utils.CsStringUtils;
import com.cnoocshell.goods.api.dto.GoodsCategoryDTO;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.biz.IGoodsCategoryBiz;
import com.cnoocshell.goods.common.ColumnConstant;
import com.cnoocshell.goods.dao.mapper.GoodsCategoryMapper;
import com.cnoocshell.goods.dao.mapper.GoodsMapper;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.GoodsCategory;
import com.cnoocshell.goods.exception.GoodsCode;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 
 * @Author: <EMAIL>
 * @Description  GoodsCategoryBiz
 * @date   2018年8月15日 下午4:56:00
 */
@Slf4j
@Service
public class GoodsCategoryBiz extends BaseBiz<GoodsCategory> implements IGoodsCategoryBiz {

    private static final String CATEGORY_TYPE = "categoryType";
    private static final String DEL_FLG = "delFlg";

    @Autowired
    private GoodsCategoryMapper goodsCategoryMapper;
    
    @Autowired
	private GoodsMapper goodsMapper;

    @Override
    public Boolean getSplitBillNode(Integer categoryType) {
        if(CsStringUtils.isNullOrBlank(categoryType)){
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY,"商品类型");
        }
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CATEGORY_TYPE,categoryType);
        criteria.andEqualTo(DEL_FLG,false);
        List<GoodsCategory> list = this.findByCondition(condition);
        Boolean splitBillNode = false;
        return splitBillNode;
    }

    @Override
    public List<GoodsCategorySimpleDTO> findCategorySimpleList(Set<String> categoryCodeSet) {
        List<GoodsCategorySimpleDTO> simpleDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(categoryCodeSet)) {
            return simpleDTOList;
        }
        Set<String> querySet = Sets.newHashSet();
        querySet.addAll(categoryCodeSet);
        querySet.addAll(supCategoryCodeSet(categoryCodeSet));

        List<GoodsCategorySimpleDTO> categorySimpleList = goodsCategoryMapper.findCategorySimpleList(querySet);

        if (CollectionUtils.isEmpty(categorySimpleList)) {
            return simpleDTOList;
        }

        Map<String, String> code2NameMap = categorySimpleList.stream()
                .collect(Collectors.toMap(GoodsCategorySimpleDTO::getCategoryCode, GoodsCategorySimpleDTO::getCategoryName));

        for (String code : categoryCodeSet) {
            String lastName = code2NameMap.get(code);
            if (StringUtils.isBlank(lastName)) {
                continue;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 12; i <= code.length() - 3; i = i + 3) {
                String frontName = code2NameMap.get(code.substring(0, i));
                if (StringUtils.isBlank(frontName)) {
                    break;
                }
                sb.append(frontName).append(" ");
            }
            String fullName = sb.append(lastName).toString();
            GoodsCategorySimpleDTO simpleDTO = new GoodsCategorySimpleDTO();
            simpleDTO.setCategoryCode(code);
            simpleDTO.setCategoryName(fullName);
            simpleDTOList.add(simpleDTO);
        }

        return simpleDTOList;
    }

    private Set<String> supCategoryCodeSet(Set<String> categoryCodeSet) {
        Set<String> supSet = Sets.newHashSet();
        for (String code : categoryCodeSet) {
            for (int i = 12; i < code.length(); i = i + 3) {
                supSet.add(code.substring(0, i));
            }
        }
        return supSet;
    }

    @Override
    public GoodsCategory findCategoryByCategoryCode(String categoryCode) {
        if(StringUtils.isBlank(categoryCode)){
            return null;
        }
        Condition condition = newCondition();
        condition.createCriteria().andEqualTo("categoryCode",categoryCode.trim().length() < 9 ? categoryCode : categoryCode.substring(0,9)).andEqualTo(DEL_FLG,false);
        List<GoodsCategory> list = findByCondition(condition);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

	@Override
	public GoodsCategoryDTO getGoodsCategoryByGoodsId(String goodsId) {
		GoodsCategoryDTO categoryDTO = new GoodsCategoryDTO();
		GoodsCategory category = new GoodsCategory();
		BeanUtils.copyProperties(category, categoryDTO);
		return categoryDTO;
	}

    @Override
    public Boolean existCategoryByParentId(String parentId, String categoryName) {
        Condition condition = new Condition(GoodsCategory.class);
        condition.createCriteria()
                .andEqualTo(ColumnConstant.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.PARENT_ID,parentId)
                .andEqualTo(ColumnConstant.CATEGORY_NAME,categoryName);

        return goodsCategoryMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public Boolean existCategoryBySapCode(String sapCode) {
        if(CharSequenceUtil.isBlank(sapCode))
            return false;
        Condition condition = new Condition(GoodsCategory.class);
        condition.createCriteria()
                .andEqualTo(ColumnConstant.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.SAP_CODE,sapCode);

        return goodsCategoryMapper.selectCountByCondition(condition) > 0;
    }


}
