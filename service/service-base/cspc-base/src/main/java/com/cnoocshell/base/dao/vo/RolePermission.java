package com.cnoocshell.base.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@Table(name = "sys_role_permission")
@EqualsAndHashCode(callSuper = true)
public class RolePermission extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     *角色代码
     */
    @Column(name = "role_code")
    private String roleCode;

    /**
     *权限代码
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     *是否选中
     */
    @Column(name = "is_choosed")
    private Boolean isChoosed;
}