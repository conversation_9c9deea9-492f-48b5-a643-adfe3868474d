package com.cnoocshell.integration.dto.sap;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class SapContractDTO {

    private ScDTO IS_SC;


    @Data
    public static class ScDTO{
        private HeaderDTO HEADER;
        private List<ItemDTO> ITEM;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class HeaderDTO extends SapTextDTO {
        @ApiModelProperty("成交单号")
        private String ZZVBELN;
        @ApiModelProperty("订单类型")
        private String AUART = "ZCQQ";
        @ApiModelProperty("销售组织")
        private String VKORG = "CN01";
        @ApiModelProperty("分销渠道")
        private String VTWEG = "01";
        @ApiModelProperty("产品组")
        private String SPART = "01";
        @ApiModelProperty("销售办公室")
        private String VKBUR = "DYW";


        /**
         *"1. 销售组将维护进线上销售平台产品牌号上，可选：固体（CN2）、液体（CN1）；
         * 2. 创建合同时，线上销售平台根据销售计划中选择的产品牌号，自动匹配销售组，传值对应Code给SAP"
         */
        @ApiModelProperty(value = "销售组",notes = "")
        private String VKGRP;
        @ApiModelProperty(value = "销售地区",notes = "默认传：CN0001")
        private String BZIRK = "CN0001";
        @ApiModelProperty(value = "凭证日期",notes = "默认传：创建合同的日期（竞价场次成交的日期） yyyy-MM-dd")
        private String AUDAT;
        @ApiModelProperty(value = "定价日期",notes = "默认传：创建合同的日期（竞价场次成交的日期） yyyy-MM-dd")
        private String PRSDT;
        @ApiModelProperty(value = "客户成交日期",notes = "为成交日对应的月份，统一月中15日 yyyy-MM-dd")
        private String BSTDK;

        @ApiModelProperty("Your Reference")
        private String IHREZ = "";
        @ApiModelProperty(value = "售达方",notes = "默认传：客户的customer code (member表 crm_code)")
        private String ZKUNNR_SP;
        @ApiModelProperty(value = "收货方",notes = "SAP团队已确认：此字段接口可支持不填，已调整为非必填项，后续由客服人员线下沟通后，在SAP补充")
        private String ZKUNNR_SH  ="";
        @ApiModelProperty(value = "付款方",notes = "不传，由客服人员在SAP补充")
        private String ZKUNNR_PY  ="";
        @ApiModelProperty(value = "收票方",notes = "不传，由客服人员在SAP补充")
        private String ZKUNNR_BP  ="";
        @ApiModelProperty(value = "供货工厂",notes = "不传，由客服人员在SAP补充")
        private String ZKUNNR_ZP  ="";


        @ApiModelProperty(value = "销售代表",notes = "根据线上销售平台维护的销售人员和客户的关系表，自动读取，传值对应销售人员的员工号")
        private String ZKUNNR_ZR;
        @ApiModelProperty(value = "客户服务代表",notes = "根据线上销售平台维护的销售人员和客服人员的关系表，自动读取，传值对应客服人员的员工号（如果找到多个，默认取第一个）")
        private String ZKUNNR_E1;
        @ApiModelProperty(value = "贸易条款",notes = "传值为：客户在竞价的时间选择的运输方式（配送：CIP，自提：EXW）")
        private String INCO1;
        @ApiModelProperty(value = "贸易条款位置1",notes = "默认传：必填项（TBD）")
        private String INCO2 = "TBD";
        @ApiModelProperty(value = "付款条件",notes = "默认传：CBD")
        private String ZTERM = "CBD";
        @ApiModelProperty(value = "客户组1",notes = "传值为：客户+产品牌号对应的销售渠道CODE")
        private String KVGR1;
        @ApiModelProperty(value = "客户组2",notes = "不传，由客服人员在SAP补充")
        private String KVGR2 = "";


        /**
         * 2025-04-08 调整为E-spot 对应代码 40
         */
        @ApiModelProperty(value = "客户组3",notes = "默认传：spot  默认传：spot，传值 的话要按照代码传，SPOT的代码是30，")
        private String KVGR3 = "40";
        @ApiModelProperty(value = "合同开始日期",notes = "默认传：创建合同的当天（竞价场次成交的日期） yyyy-MM-dd")
        private String ANGDT;
        @ApiModelProperty(value = "合同结束日期",notes = "提货结束日期+3天 yyyy-MM-dd")
        private String BNDDT;
        @ApiModelProperty(value = "凭证货币",notes = "默认传：CNY")
        private String STWAT = "CNY";

        @ApiModelProperty(value = "贸易版本",notes = "默认传：2024")
        private String INCOV = "2024";

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ItemDTO extends SapTextTemDTO{
        @ApiModelProperty(value = "线上销售平台行号",notes = "成交单号-1")
        private String ZZPOSNR;
        @ApiModelProperty(value = "SAP物料编码",notes = "默认传：产品牌号在SAP的物料编码（在线上销售平台需维护产品主数据，到牌号）")
        private String MATNR;
        @ApiModelProperty(value = "工厂",notes = "默认传：产品物料编码在SAP对应的工厂（在线上销售平台需维护好mapping关系）")
        private String WERKS;
        @ApiModelProperty(value = "数量",notes = "默认传：客户报量时填写的数量")
        private String ZMENG;
        @ApiModelProperty(value = "销售单位",notes = "默认传：TO")
        private String ZIEME = "TO";
        @ApiModelProperty(value = "贸易条款",notes = "传值为：客户在竞价的时间选择的运输方式（配送：CIP，自提：EXW）->改为非必传, 直接传空SAP会以抬头进行赋值到行项目")
        private String INCO1 = "";
        @ApiModelProperty(value = "贸易条款位置1",notes = "默认传：必填项（TBD）->改为非必传, 直接传空SAP会以抬头进行赋值到行项目\n")
        private String INCO2 = "";
        @ApiModelProperty(value = "付款条件",notes = "默认传：CBD->改为非必传, 直接传空SAP会以抬头进行赋值到行项目\n")
        private String ZTERM = "";

        @ApiModelProperty(value = "价格条件类型",notes = "\"默认传：" +
                "自提：YYP0；配送：YPRO\"")
        private String KSCHL = "";

        @ApiModelProperty(value = "金额",notes = "传值为：竞价成交时的出厂价价格")
        private String KBETR;
        @ApiModelProperty(value = "定价单位",notes = "默认传：1")
        private String KPEIN = "1";
        @ApiModelProperty(value = "价格单位",notes = "默认传：TO")
        private String KMEIN = "TO";
        @ApiModelProperty(value = "价格币种",notes = "默认传：CNY")
        private String WAERS = "CNY";
        @ApiModelProperty(value = "定价日期",notes = "默认传：创建合同的日期（竞价场次成交的日期）")
        private String PRSDT;

//        @ApiModelProperty(value = "贸易版本",notes = "默认传：2024")
//        private String INCOV = "2024";
    }
}


