package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.bidding.BiddingCreateDataDTO;
import com.cnoocshell.order.dao.vo.SalesPlan;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

public interface SalesPlanMapper extends IBaseMapper<SalesPlan> {

    List<BiddingCreateDataDTO> selectCreateBidding(String salesID);

    int updateRemainSellAbleQuantity(@Param("salePlanNo")String salePlanNo, @Param("dealTotalQuantity") BigDecimal dealTotalQuantity);

    @Update("UPDATE sales_plan set del_flg=1 where id=#{id}")
    int removeSalePlanById(@Param("id")String id);

    @Update("UPDATE sales_plan set is_created_inquiry = #{createdInquiry} where plan_no = #{salePlanNo} and del_flg=0")
    int updateCreatedInquiry(@Param("salePlanNo")String salePlanNo, @Param("createdInquiry") Integer isCreatedInquiry);

    int resetCreateEnquiryFlag(@Param("salePlanNo") String salePlanNo);
}
