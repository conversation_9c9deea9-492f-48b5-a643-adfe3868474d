package com.cnoocshell.order.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.order.api.constant.XxlJobConstant;
import com.cnoocshell.order.api.enums.BiddingStatusEnum;
import com.cnoocshell.order.biz.IBiddingBiz;
import com.cnoocshell.order.biz.IBiddingBuyerBiz;
import com.cnoocshell.order.biz.IBiddingBuyerDetailBiz;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyer;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class BiddingJob {
    private final IBiddingBiz iBiddingBiz;
    private final IBiddingBuyerBiz iBiddingBuyerBiz;
    private final IXxlJobService iXxlJobService;
    private final IBiddingBuyerDetailBiz iBiddingBuyerDetailBiz;

    private static final List<String> ALLOW_STATUS = Arrays.asList(BiddingStatusEnum.ADJUSTED_PRICE.getStatus(),BiddingStatusEnum.APPROVED_STRATEGY.getStatus());

    /**
     *竞价通知
     */
    @XxlJob(value = XxlJobConstant.NOTIFY_BIDDING_START_JOB)
    public void notifyBiddingStartJob() {
        Long jobId = XxlJobHelper.getJobId();
        String no = XxlJobHelper.getJobParam();
        log.info("竞价通知自动任务开始:{} {}",no,jobId);
        XxlJobHelper.log(CharSequenceUtil.format("竞价通知自动任务开始: no：{} jobId:{}",no,jobId));
        if (CharSequenceUtil.isBlank(no)) {
            log.info("竞价通知自动任务入参竞价编号为空 jobId:{} no:{}", jobId, no);
            this.destroyJob(jobId, no);
            return;
        }

        Bidding bidding = iBiddingBiz.getByNo(no);
        XxlJobHelper.log(CharSequenceUtil.format("竞价通知开始自动任务 jobId:{} no:{} bidding:{}",jobId,no, JSONUtil.toJsonStr(bidding)));
        if (Objects.nonNull(bidding) && CollUtil.contains(ALLOW_STATUS,bidding.getStatus())) {
            List<BiddingBuyer> buyers = iBiddingBuyerBiz.getNotifyBiddingStartBuyer(Arrays.asList(bidding));
            XxlJobHelper.log(CharSequenceUtil.format("竞价通知开始自动任务  jobId:{} no:{} buyers:{}",jobId,no, JSONUtil.toJsonStr(buyers)));
            if (CollUtil.isNotEmpty(buyers)) {
                //发送短信站内信
                XxlJobHelper.log(CharSequenceUtil.format("竞价通知开始自动任务 开始通知  jobId:{} no:{} ",jobId,no));
                iBiddingBiz.notifyBuyerStartBidding(Arrays.asList(bidding),buyers);
            }
        }

        XxlJobHelper.log(CharSequenceUtil.format("竞价通知自动任务结束 no:{} jobId:{}",no,jobId));
        this.destroyJob(jobId, no);
    }


    /**
     * 竞价开始
     */
    @XxlJob(value = XxlJobConstant.BIDDING_START_JOB)
    public void biddingStartJob() {
        Long jobId = XxlJobHelper.getJobId();
        String no = XxlJobHelper.getJobParam();

        XxlJobHelper.log(CharSequenceUtil.format("竞价开始自动任务开始 no:{} jobId:{}",no,jobId));

        log.info("竞价开始自动任务开始:{} {}",no,jobId);
        if (CharSequenceUtil.isBlank(no)) {
            log.info("竞价开始自动任务入参竞价编号为空");
            this.destroyJob(jobId, no);
            return;
        }

        Bidding bidding = iBiddingBiz.getByNo(no);
        XxlJobHelper.log(CharSequenceUtil.format("竞价开始自动任务 jobId:{} no:{} bidding:{}",jobId,no, JSONUtil.toJsonStr(bidding)));
        if (Objects.nonNull(bidding)) {
            if (CollUtil.contains(ALLOW_STATUS,bidding.getStatus())) {
                log.info("竞价开始自动任务 执行竞价状态变更开始{}", no);
                XxlJobHelper.log(CharSequenceUtil.format("竞价开始自动任务 jobId:{} no:{}",jobId,no));
                iBiddingBiz.updateBiddingStatus(no, BiddingStatusEnum.BIDDING);
            }
        }

        XxlJobHelper.log(CharSequenceUtil.format("竞价开始自动任务结束 no:{} jobId:{}",no,jobId));
        this.destroyJob(jobId, no);
    }


    /**
     *竞价结束
     */
    @XxlJob(value = XxlJobConstant.BIDDING_END_JOB)
    public void biddingEndJob() {
        Long jobId = XxlJobHelper.getJobId();
        String no = XxlJobHelper.getJobParam();

        XxlJobHelper.log(CharSequenceUtil.format("竞价结束自动任务开始 no:{} jobId:{}",no,jobId));

        log.info("竞价结束自动任务开始:{} {}",no,jobId);
        if (CharSequenceUtil.isBlank(no)) {
            log.info("竞价结束自动任务入参竞价编号为空");
            this.destroyJob(jobId, no);
            return;
        }

        Bidding bidding = iBiddingBiz.getByNo(no);
        XxlJobHelper.log(CharSequenceUtil.format("竞价结束自动任务 jobId:{} no:{} bidding:{}",jobId,no, JSONUtil.toJsonStr(bidding)));
        if (Objects.nonNull(bidding)) {
            log.info("竞价结束自动任务 执行竞价状态变更开始{}", no);
            if(iBiddingBuyerDetailBiz.existsBiddingBuyerDetail(no)){
                XxlJobHelper.log(CharSequenceUtil.format("竞价结束自动任务 成交结果待确认 jobId:{} no:{}",jobId,no));
                iBiddingBiz.updateBiddingStatus(no, BiddingStatusEnum.TO_CONFIRM_RESULT);
            }else{
                XxlJobHelper.log(CharSequenceUtil.format("竞价结束自动任务 无报量作废 jobId:{} no:{}",jobId,no));
                log.info("竞价结束自动任务 无客户报量 作废竞价：{}",no);
                bidding.setStatus(BiddingStatusEnum.CANCELLED.getStatus());
                bidding.setDealTime(new Date());
                bidding.setDealOperator("system");
                iBiddingBiz.updateSelective(bidding);
            }
        }

        XxlJobHelper.log(CharSequenceUtil.format("竞价结束自动任务结束 no:{} jobId:{}",no,jobId));
        this.destroyJob(jobId, no);
    }

    private void destroyJob(Long jobId, String no) {
        XxlJobHelper.log(CharSequenceUtil.format("竞价自动任务销毁开始 jobId:{} biddingNo：{}", jobId, no));
        log.info("竞价自动任务销毁开始发送消息 jobId:{} biddingNo：{}", jobId, no);
        //屏蔽销毁逻辑
//        iXxlJobService.destroyXxlJob(jobId);
    }
}
