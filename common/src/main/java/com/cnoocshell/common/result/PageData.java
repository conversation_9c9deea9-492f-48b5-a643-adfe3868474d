package com.cnoocshell.common.result;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*
 *
 * @Description 统一page对象
 * <AUTHOR>
 * @Date Create in 17:25 01/08/2018
 **/
@Data
public class PageData<T> implements Serializable {
	private static final long serialVersionUID = 1L;
	private int pageNum;
	private int pageSize;
	private long total;
	private int pages;
	private transient List<T> list;

	public PageData(){}

	public PageData(List<T> list) {
			this.pageNum = 1;
			this.pageSize = list.size();
			this.pages = 1;
			this.list = list;
			this.total = list.size();

	}

	public PageData(Page<T> page) {
		this.pageNum = page.getPageNum();
		this.pageSize = page.getPageSize();
		this.pages = page.getPages();
		this.list = page.getResult();
		this.total = page.getTotal();

	}

	public PageData(PageInfo<T> page) {
		this.pageNum = page.getPageNum();
		this.pageSize = page.getPageSize();
		this.pages = page.getPages();
		this.list = page.getList();
		this.total = page.getTotal();

	}

}

