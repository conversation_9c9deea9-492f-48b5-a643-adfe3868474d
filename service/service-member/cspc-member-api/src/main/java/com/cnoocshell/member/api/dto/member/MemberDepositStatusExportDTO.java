package com.cnoocshell.member.api.dto.member;

import com.cnoocshell.common.excel.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberDepositStatusExportDTO implements Serializable {

    @Excel(index = 1, title = "客户名称")
    private String memberName;

    @Excel(index = 2, title = "客户编码")
    private String crmCode;

    @Excel(index = 3, title = "保证金状态")
    private String depositStatus;

    @Excel(index = 4, title = "保证金状态更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
