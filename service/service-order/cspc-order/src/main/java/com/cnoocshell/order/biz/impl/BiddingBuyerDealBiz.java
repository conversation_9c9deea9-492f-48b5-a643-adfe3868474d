package com.cnoocshell.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.MessageDTO;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.dto.WechatSendDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.IWechatSendService;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.common.utils.GeneratorCodeUtil;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.integration.enums.WechatTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountNameDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.QueryIntentionInfoDTO;
import com.cnoocshell.member.api.dto.member.QueryMemberAccountDTO;
import com.cnoocshell.member.api.enums.SubscribeWechatBusinessTypeEnum;
import com.cnoocshell.member.api.service.IAccountRelationService;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.api.constant.OrderNumberConstant;
import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.order.api.dto.AccountSubscribeWechatKeyDTO;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingDealQuantityDTO;
import com.cnoocshell.order.api.enums.BiddingMessageTemplateEnum;
import com.cnoocshell.order.biz.IBiddingBiz;
import com.cnoocshell.order.biz.IBiddingBuyerDealBiz;
import com.cnoocshell.order.biz.IBiddingBuyerDetailBiz;
import com.cnoocshell.order.dao.mapper.BiddingBuyerDealMapper;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyerDeal;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.IBiddingAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingBuyerDealBiz extends BaseBiz<BiddingBuyerDeal> implements IBiddingBuyerDealBiz {
    @Resource
    private BiddingBuyerDealMapper biddingBuyerDealMapper;
    private final GeneratorCodeUtil generatorCodeUtil;
    @Resource(name = "threadPoolExecutor")
    private Executor executor;
    @Resource
    private BiddingMapper biddingMapper;
    private final IBiddingBuyerDetailBiz iBiddingBuyerDetailBiz;
    private final IBiddingAccountService iBiddingAccountService;
    private final IEmailSendService iEmailSendService;
    private final IWechatSendService iWechatSendService;
    private final IMemberService iMemberService;
    @Resource
    private IMessageSendService iMessageSendService;
    private final ISmsSendService iSmsSendService;
    private final IAccountService iAccountService;
    private final IAccountRelationService iAccountRelationService;

    private static final String BIDDING_NO = "biddingNo";
    private static final String BIDDING_NAME = "biddingName";
    private static final String REAL_NAME = "realName";
    private static final String TEMPLATE = "{}_{}";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDealResult(List<BiddingBuyerDetail> buyerBiddingDetail, Bidding bidding, String operatorId, String operatorName) {
        if (CollUtil.isEmpty(buyerBiddingDetail))
            return;
        //先删除旧成交结果
        Condition condition = new Condition(BiddingBuyerDeal.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO, bidding.getBiddingNo());
        BiddingBuyerDeal remove = new BiddingBuyerDeal();
        remove.setDelFlg(Boolean.TRUE);
        setOperatorInfo(remove, operatorId, false);

        biddingBuyerDealMapper.updateByConditionSelective(remove, condition);
        //保存新的成交结果
        List<BiddingBuyerDeal> saveList = buyerBiddingDetail.stream().map(v -> {
            BiddingBuyerDeal target = new BiddingBuyerDeal();
            setOperatorInfo(target, operatorId, true);
            target.setCreateUserName(operatorName);

            //生成成交编号
            target.setDealNo(generatorCodeUtil.generate("BD",3));

            target.setId(super.getUuidGeneratorGain());
            target.setBiddingNo(bidding.getBiddingNo());
            target.setBiddingBuyerDetailId(v.getId());
            target.setMemberCode(v.getMemberCode());
            target.setMemberName(v.getMemberName());
            target.setCurrentStandardPrice(bidding.getLastStandardPrice());
            target.setWantQuantity(v.getWantQuantity());
            target.setDeliveryMode(v.getDeliveryMode());
            target.setPack(v.getPack());
            target.setTotalAmount(NumberUtil.mul(target.getWantQuantity(), target.getCurrentStandardPrice()));

            return target;
        }).collect(Collectors.toList());

        biddingBuyerDealMapper.insertList(saveList);
        log.info("成交结果插入成功，成交单号：{}", bidding.getBiddingNo());
    }

    @Override
    public void resetBuyerDealByBiddingNos(List<String> biddingNos) {
        if(CollUtil.isEmpty(biddingNos))
            return;
        Condition condition = new Condition(BiddingBuyerDeal.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(ColumnConstant.BIDDING_NO,biddingNos);
        if(Objects.equals(biddingBuyerDealMapper.selectCountByCondition(condition), OrderNumberConstant.ZERO))
            return;

        BiddingBuyerDeal remove = new BiddingBuyerDeal();
        remove.setDelFlg(Boolean.TRUE);

        biddingBuyerDealMapper.updateByConditionSelective(remove,condition);
    }

    @Override
    public void resetBuyerDealByBiddingList(List<Bidding> biddingList) {
        if(CollUtil.isEmpty(biddingList))
            return;
        List<String> biddingNos = biddingList.stream().map(Bidding::getBiddingNo).collect(Collectors.toList());
        resetBuyerDealByBiddingNos(biddingNos);
    }

    @Override
    public List<BiddingBuyerDeal> listByBiddingNos(List<String> biddingNos) {
        if(CollUtil.isEmpty(biddingNos))
            return Collections.emptyList();
        Condition condition = new Condition(BiddingBuyerDeal.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(ColumnConstant.BIDDING_NO,biddingNos);

        return this.findByCondition(condition);
    }

    @Override
    public List<BiddingBuyerDeal> listByBiddingList(List<Bidding> biddingList) {
        if(CollUtil.isEmpty(biddingList))
            return Collections.emptyList();
        List<String> biddingNos = biddingList.stream().map(Bidding::getBiddingNo).collect(Collectors.toList());

        return this.listByBiddingNos(biddingNos);
    }

    @Override
    public List<BiddingBuyerDeal> listByBiddingNo(String biddingNo, String memberCode) {
        Condition condition = new Condition(BiddingBuyerDeal.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO,biddingNo)
                .andEqualTo(ColumnConstant.MEMBER_CODE,memberCode);
        return this.findByCondition(condition);
    }

    @Override
    public List<BiddingDealQuantityDTO> getBiddingDealTotalQuantity(List<String> biddingList) {
        if(CollUtil.isEmpty(biddingList))
            return null;
        return biddingBuyerDealMapper.getBiddingDealTotalQuantity(biddingList);
    }

    @Override
    public void updateBiddingDealContractInfo(BiddingDealContractDTO param) {
        BiddingBuyerDeal deal = this.getByDealNo(param.getDealNo());
        if(Objects.isNull(deal))
            throw new BizException(OrderErrorCode.CUSTOM,"成交结果不存在");
        Bidding bidding = biddingMapper.getByNo(deal.getBiddingNo());
        if(Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不存在");
        BiddingBuyerDetail detail = iBiddingBuyerDetailBiz.getById(deal.getBiddingBuyerDetailId());


        if(BooleanUtil.isTrue(param.getCreateContractStatus()))
            param.setCreateContractFailReason("");
        param.setCreateContractFailReason(CharSequenceUtil.subWithLength(param.getCreateContractFailReason(),0,512));
        biddingBuyerDealMapper.updateBiddingDealContractNo(param);

        //创建SAP合同成功或失败通知相关人员
        executor.execute(()->{
            //成功 通知客户企业管理员 产品人员 销售等相关人员
            if(BooleanUtil.isTrue(param.getCreateContractStatus())){
                this.notifyByDeal(bidding,detail,param.getSapContractNo());
            }else{
                //失败通知该产品销售经理
                Map<String,List<AccountNameDTO>> accountMp = iBiddingAccountService.findApprover(BaseRoleTypeEnum.SELLER_SALES_MANAGER.getRoleCode(),
                        Arrays.asList(bidding.getGoodsCode()));
                List<AccountNameDTO> saleManagerList = CommonUtils.getByKey(accountMp,bidding.getGoodsCode());
                if(CollUtil.isNotEmpty(saleManagerList)){
                    EmailDTO saleManagerEmail = new EmailDTO();
                    saleManagerEmail.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_SALE_MANAGER_BY_CREATE_CONTRACT_FAIL.getCode());
                    for (AccountNameDTO saleManager : saleManagerList) {
                        saleManagerEmail.setTos(Arrays.asList(saleManager.getEmail()));
                        Map<String,Object> saleManagerParam = new HashMap<>();
                        saleManagerParam.put("realName",saleManager.getRealName());
                        saleManagerParam.put("memberName",detail.getMemberName());
                        saleManagerParam.put("failMessage",param.getCreateContractFailReason());
                        saleManagerParam.put("biddingNo",bidding.getBiddingNo());
                        saleManagerParam.put("biddingName",bidding.getBiddingName());
                        saleManagerEmail.setTemplateParam(saleManagerParam);
                        iEmailSendService.sendEmail(saleManagerEmail);
                    }
                }
            }
        });
    }

    @Override
    public BiddingBuyerDeal getByDealNo(String dealNo) {
        Condition condition = new Condition(BiddingBuyerDeal.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.DEAL_NO,dealNo);
        List<BiddingBuyerDeal> deals = this.findByCondition(condition);
        if(CollUtil.isEmpty(deals))
            return null;
        if(CollUtil.size(deals) > 1)
            throw new BizException(OrderErrorCode.CUSTOM,"存在多条成交数据");
        return CollUtil.getFirst(deals);
    }

    @Override
    public List<BiddingBuyerDeal> listByBiddingAndDealNos(List<Bidding> biddingList, List<String> dealNos) {
        if(CollUtil.isEmpty(biddingList))
            return Collections.emptyList();
        List<String> biddingNos = biddingList.stream().map(Bidding::getBiddingNo).collect(Collectors.toList());
        if(CollUtil.isEmpty(biddingNos))
            return Collections.emptyList();
        Condition condition = new Condition(BiddingBuyerDeal.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(ColumnConstant.BIDDING_NO,biddingNos);
        CommonUtils.andInIfNotEmpty(criteria,ColumnConstant.DEAL_NO,dealNos);

        return this.findByCondition(condition);
    }

    @Override
    public void notifyByDeal(Bidding bidding,BiddingBuyerDetail detail,String contractNo) {
        if (Objects.isNull(bidding) || Objects.isNull(detail))
            return;
        List<BiddingBuyerDetail> details = Arrays.asList(detail);
        //站内信 短信 邮件 微信服务消息
        List<String> memberCodesList = Arrays.asList(detail.getMemberCode());
        List<String> goodsCodes = Arrays.asList(bidding.getGoodsCode());
        List<String> biddingNos = Arrays.asList(bidding.getBiddingNo());

        QueryMemberAccountDTO query = new QueryMemberAccountDTO(memberCodesList,
                Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()));
        //客户企业管理员
        List<AccountSimpleDTO> accountByAdmin = iMemberService.listAccountsByMemberCodes(query);

        //微信订阅记录
        Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMapByAdmin = iBiddingAccountService
                .queryAccountSubscribeWechat(getAccountIds(accountByAdmin), biddingNos, SubscribeWechatBusinessTypeEnum.BIDDING);


        //通知客户+产品对应销售人员/客服
        QueryIntentionInfoDTO queryIntention = new QueryIntentionInfoDTO(null, goodsCodes, memberCodesList, null);
        List<MemberPurchaseGoodsIntentionDTO> intentions = iMemberService.queryMemberBySaleInfo(queryIntention);
        //K:memberCode_goodsCode
        Map<String, List<MemberPurchaseGoodsIntentionDTO>> intentionGroup = setIntentionGroup(intentions);
        List<AccountSimpleDTO> salesAccountList = getSalesAccountList(intentions);
        List<AccountRelationInfoDTO> saleRelations = getSaleRelations(intentions);
        List<AccountSimpleDTO> customerService = getCustomerService(intentions, saleRelations);

        Map<String, AccountSimpleDTO> saleMap = CommonUtils.getMap(salesAccountList, AccountSimpleDTO::getAccountId);
        Map<String, List<AccountRelationInfoDTO>> relationGroup = CommonUtils.group(saleRelations, AccountRelationInfoDTO::getAccountId);
        Map<String, AccountSimpleDTO> customerServiceMap = CommonUtils.getMap(customerService, AccountSimpleDTO::getAccountId);

        Map<String, List<MemberPurchaseGoodsIntentionDTO>> finalIntentionGroup = intentionGroup;
        //发送短信站内信通知客户产品人员
        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> permissionAccountMap = iBiddingAccountService.queryAccountDataPermission(memberCodesList, goodsCodes);
        //微信订阅记录 产品人员
        Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMapByGoods = iBiddingAccountService
                .queryAccountSubscribeWechat(getAccountIds(Objects.nonNull(permissionAccountMap) ? permissionAccountMap.values() : null),
                        biddingNos, SubscribeWechatBusinessTypeEnum.BIDDING);

        //通知买家企业管理员
        this.notifyAccountByAdmin(accountByAdmin, details, bidding, wechatMapByAdmin,contractNo);
        //通知客户产品相关人员
        List<QueryAccountDataPermissionDTO> keys = details.stream()
                .map(v1 -> v1.getMemberCode())
                .distinct()
                .map(v1 -> new QueryAccountDataPermissionDTO(v1, bidding.getGoodsCode()))
                .collect(Collectors.toList());
        keys.forEach(v1 -> {
            List<AccountSimpleDTO> goodsAccount = filterAccount(CommonUtils.getByKey(permissionAccountMap, v1), accountByAdmin);
            this.notifyByBiddingDeal(bidding, goodsAccount, wechatMapByGoods, false,contractNo);


            //通知销售/客服
            if (CollUtil.isNotEmpty(intentions)) {
                for (BiddingBuyerDetail buyer : details) {
                    String key = CharSequenceUtil.format(TEMPLATE, buyer.getMemberCode(), bidding.getGoodsCode());
                    List<MemberPurchaseGoodsIntentionDTO> buyerIntentions = CommonUtils.getByKey(finalIntentionGroup, key);
                    if (CollUtil.isEmpty(buyerIntentions))
                        continue;
                    this.notifySaleAndCustomerServiceByDeal(buyerIntentions, saleMap, buyer, bidding, relationGroup, customerServiceMap,contractNo);
                }
            }
        });
    }


    private void notifyAccountByAdmin(List<AccountSimpleDTO> accountByAdmin,
                                      List<BiddingBuyerDetail> v,
                                      Bidding k,
                                      Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMap,
                                      String contractNo) {
        //通知买家企业管理员
        if (CollUtil.isEmpty(accountByAdmin))
            return;

        List<AccountSimpleDTO> accountAdmin = accountByAdmin.stream()
                .filter(v1 -> v.stream().anyMatch(v2 -> CharSequenceUtil.equals(v1.getMemberCode(), v2.getMemberCode())))
                .collect(Collectors.toList());
        this.notifyByBiddingDeal(k, accountAdmin, wechatMap, true,contractNo);
    }

    private Map<String, List<MemberPurchaseGoodsIntentionDTO>> setIntentionGroup(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        Map<String, List<MemberPurchaseGoodsIntentionDTO>> intentionGroup = null;
        if (CollUtil.isNotEmpty(intentions)) {
            intentionGroup = intentions.stream().
                    collect(Collectors.groupingBy(v4 -> CharSequenceUtil.format(TEMPLATE,
                            v4.getMemberCode(), v4.getGoodsCode())));
        }
        return intentionGroup;
    }

    private Set<String> setMemberCodes(Map<Bidding, List<BiddingBuyerDetail>> biddingInfo) {
        Set<String> memberCodes = new HashSet<>();
        for (List<BiddingBuyerDetail> value : biddingInfo.values()) {
            Set<String> memberCodeSet = value.stream().map(BiddingBuyerDetail::getMemberCode).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(memberCodeSet))
                memberCodes.addAll(memberCodeSet);
        }
        return memberCodes;
    }

    private List<AccountSimpleDTO> getCustomerService(List<MemberPurchaseGoodsIntentionDTO> intentions, List<AccountRelationInfoDTO> saleRelations) {
        List<AccountSimpleDTO> customerService = null;
        if (CollUtil.isNotEmpty(intentions)) {
            List<String> saleUserIds = getSaleUserIds(intentions);
            if (CollUtil.isNotEmpty(saleUserIds) && CollUtil.isNotEmpty(saleRelations)) {
                List<String> linkAccountIds = saleRelations.stream()
                        .map(AccountRelationInfoDTO::getLinkAccountId)
                        .filter(CharSequenceUtil::isNotBlank)
                        .distinct().collect(Collectors.toList());
                if (CollUtil.isNotEmpty(linkAccountIds)) {
                    customerService = iAccountService.listSimpleByIds(linkAccountIds);
                }
            }
        }
        return customerService;
    }

    private List<AccountRelationInfoDTO> getSaleRelations(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        List<AccountRelationInfoDTO> saleRelations = new ArrayList<>();
        if (CollUtil.isNotEmpty(intentions)) {
            List<String> saleUserIds = getSaleUserIds(intentions);
            if (CollUtil.isNotEmpty(saleUserIds)) {
                QueryAccountRelationDTO queryRelation = new QueryAccountRelationDTO();
                queryRelation.setAccountIds(saleUserIds);
                queryRelation.setLinkAccountRoles(Collections.singletonList(BaseRoleTypeEnum.SELLER_CUSTOMER_SERVICE.getRoleCode()));
                saleRelations = iAccountRelationService.queryAccountRelation(queryRelation);
            }
        }
        return saleRelations;
    }

    private List<AccountSimpleDTO> getSalesAccountList(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        List<AccountSimpleDTO> salesAccountList = null;
        if (CollUtil.isNotEmpty(intentions)) {
            List<String> saleUserIds = getSaleUserIds(intentions);
            if (CollUtil.isNotEmpty(saleUserIds)) {
                salesAccountList = iAccountService.listSimpleByIds(saleUserIds);
                QueryAccountRelationDTO queryRelation = new QueryAccountRelationDTO();
                queryRelation.setAccountIds(saleUserIds);
                queryRelation.setLinkAccountRoles(Collections.singletonList(BaseRoleTypeEnum.SELLER_CUSTOMER_SERVICE.getRoleCode()));
            }
        }
        return salesAccountList;
    }

    private void notifyByBiddingDeal(Bidding k,
                                     List<AccountSimpleDTO> accounts,
                                     Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> wechatMap,
                                     boolean isAdmin,
                                     String contractNo) {
        log.info("竞价成交通知开始 biddingNo:{} isAdmin:{} contractNo：{} accounts:{}", k.getBiddingNo(), isAdmin,contractNo, accounts);
        if (CollUtil.isEmpty(accounts))
            return;
        List<String> mobiles = accounts.stream().map(v1 -> v1.getMobile()).distinct().collect(Collectors.toList());
        //发送短信
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.TRANSACTION_SUCCESS_CODE.getCode(), mobiles, Arrays.asList(k.getBiddingNo(), k.getBiddingName(),contractNo));
        iSmsSendService.sendSms(sms);

        accounts.forEach(v3 -> {
            //发送站内信
            MessageDTO message = new MessageDTO();
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL.getTitle());
            message.setReceiveAccountIds(Arrays.asList(v3.getAccountId()));
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL.getMsg(v3.getMemberName(), k.getBiddingNo(),contractNo));
            iMessageSendService.sendMessage(message);

            //发送邮件
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL_TEMPLATE.getCode());
            emailDTO.setTos(Arrays.asList(v3.getEmail()));
            Map<String, Object> param = new HashMap<>();
            param.put(REAL_NAME, v3.getMemberName());
            param.put(BIDDING_NO, k.getBiddingNo());
            param.put(BIDDING_NAME, k.getBiddingName());
            param.put("contractNo",contractNo);
            emailDTO.setTemplateParam(param);
            iEmailSendService.sendEmail(emailDTO);

            //微信通知
            List<AccountSubscribeWechatDTO> wechatAccounts = CommonUtils.getByKey(wechatMap,
                    new AccountSubscribeWechatKeyDTO(v3.getAccountId(), k.getBiddingNo(), SubscribeWechatBusinessTypeEnum.BIDDING.getType()));
            this.notifyWechatByDeal(k, wechatAccounts);

        });
    }

    /**
     * 竞价成交微信通知
     */
    private void notifyWechatByDeal(Bidding k, List<AccountSubscribeWechatDTO> wechatAccounts) {
        log.info("竞价成交微信通知开始 biddingNo:{} accounts:{}", k.getBiddingNo(), wechatAccounts);
        if (CollUtil.isEmpty(wechatAccounts))
            return;
        for (AccountSubscribeWechatDTO wechatAccount : wechatAccounts) {
            WechatSendDTO wechatDto = new WechatSendDTO();
            wechatDto.setTemplateCode(WechatTemplateEnum.BIDDING_RESULTS_TEMPLATE.getCode());
            wechatDto.setTemplate_id(wechatAccount.getTemplateId());
            wechatDto.setTouser(wechatAccount.getOpenId());
            wechatDto.setPage(CharSequenceUtil.format(WechatTemplateEnum.BIDDING_RESULTS_TEMPLATE.getPage(), k.getId(), k.getBiddingNo()));
            wechatDto.setData(WechatTemplateEnum.createBiddingResultDataMap(k.getBiddingNo(), k.getBiddingName(), "已成交", "恭喜您！快来查看您的详细结果吧！"));
            iWechatSendService.sendWechat(wechatDto);
        }
    }

    private static List<AccountSimpleDTO> filterAccount(List<AccountSimpleDTO> accounts1,
                                                        List<AccountSimpleDTO> accounts2) {
        if (CollUtil.isEmpty(accounts1) || CollUtil.isEmpty(accounts2))
            return accounts1;
        List<String> accountIds2 = accounts2.stream().map(v -> v.getAccountId()).collect(Collectors.toList());
        return accounts1.stream()
                .filter(v -> !CollUtil.contains(accountIds2, v.getAccountId()))
                .collect(Collectors.toList());
    }

    private static List<String> getAccountIds(List<AccountSimpleDTO> accounts) {
        if (CollUtil.isEmpty(accounts))
            return null;
        return accounts.stream()
                .filter(Objects::nonNull)
                .map(AccountSimpleDTO::getAccountId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    private static List<String> getAccountIds(Collection<List<AccountSimpleDTO>> accounts) {
        if (CollUtil.isEmpty(accounts))
            return null;
        Set<String> result = new HashSet<>();
        for (List<AccountSimpleDTO> account : accounts) {
            List<String> accountIds = getAccountIds(account);
            if (CollUtil.isNotEmpty(accountIds))
                result.addAll(accountIds);
        }
        return result.stream().collect(Collectors.toList());
    }

    private void notifySaleAndCustomerServiceByDeal(List<MemberPurchaseGoodsIntentionDTO> buyerIntentions,
                                                    Map<String, AccountSimpleDTO> saleMap,
                                                    BiddingBuyerDetail buyer,
                                                    Bidding k,
                                                    Map<String, List<AccountRelationInfoDTO>> relationGroup,
                                                    Map<String, AccountSimpleDTO> customerServiceMap,
                                                    String contractNo) {
        for (MemberPurchaseGoodsIntentionDTO v2 : buyerIntentions) {
            AccountSimpleDTO saleUser = CommonUtils.getByKey(saleMap, v2.getSaleUserId());
            if (Objects.isNull(saleUser))
                continue;
            //站内信通知销售
            MessageDTO message = new MessageDTO();
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL_TO_SALES.getTitle());
            message.setReceiveAccountIds(Arrays.asList(saleUser.getAccountId()));
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL_TO_SALES.getMsg(saleUser.getRealName(), buyer.getMemberName(), k.getBiddingNo(),contractNo));
            iMessageSendService.sendMessage(message);

            //发送邮件 通知销售
            EmailDTO email = new EmailDTO();
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_BIDDING_RESULT_BY_DEAL_TO_SALES_TEMPLATE.getCode());
            email.setTos(Arrays.asList(saleUser.getEmail()));
            Map<String, Object> param = new HashMap<>();
            param.put(REAL_NAME, saleUser.getRealName());
            param.put("memberName", buyer.getMemberName());
            param.put(BIDDING_NO, k.getBiddingNo());
            param.put(BIDDING_NAME, k.getBiddingName());
            param.put("contractNo",contractNo);
            email.setTemplateParam(param);

            iEmailSendService.sendEmail(email);

            List<AccountRelationInfoDTO> relations = CommonUtils.getByKey(relationGroup, saleUser.getAccountId());
            if (CollUtil.isNotEmpty(relations)) {
                List<String> linkUserIds = relations.stream().map(v3 -> v3.getLinkAccountId()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
                List<AccountSimpleDTO> customerServiceBySale = CommonUtils.getListByKey(customerServiceMap, linkUserIds);
                if (CollUtil.isEmpty(customerServiceBySale))
                    continue;
                //发送邮件通知客服
                notifyCustomerServiceByDeal(customerServiceBySale, email, param);
            }
        }
    }

    private void notifyCustomerServiceByDeal(List<AccountSimpleDTO> customerServiceBySale, EmailDTO email, Map<String, Object> param) {
        //发送邮件通知客服
        for (AccountSimpleDTO customerServiceUser : customerServiceBySale) {
            email.setTos(Arrays.asList(customerServiceUser.getEmail()));
            param.put(REAL_NAME, customerServiceUser.getRealName());
            email.setTemplateParam(param);
            iEmailSendService.sendEmail(email);
        }
    }

    public List<String> getSaleUserIds(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        return intentions.stream()
                .map(MemberPurchaseGoodsIntentionDTO::getSaleUserId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
    }
}
