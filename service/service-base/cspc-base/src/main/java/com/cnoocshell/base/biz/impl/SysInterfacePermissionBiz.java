package com.cnoocshell.base.biz.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.dto.InterfacePermissionDTO;
import com.cnoocshell.base.biz.ISysInterfacePermissionBiz;
import com.cnoocshell.base.dao.mapper.RolePermissionMapper;
import com.cnoocshell.base.dao.mapper.SysInterfacePermissionMapper;
import com.cnoocshell.base.dao.vo.SysInterfacePermission;
import com.cnoocshell.common.redis.InterfacePermissionKey;
import com.cnoocshell.common.service.IAuthorizeService;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysInterfacePermissionBiz extends BaseBiz<SysInterfacePermission> implements ISysInterfacePermissionBiz {
    private final SysInterfacePermissionMapper sysInterfacePermissionMapper;
    private final BizRedisService bizRedisService;
    private final RolePermissionMapper rolePermissionMapper;
    private final IAuthorizeService iAuthorizeService;

    @PostConstruct
    public void initPermissions() {
        log.info("初始化接口权限开始");
        Integer count = 0;
        try {
            List<InterfacePermissionDTO> list = sysInterfacePermissionMapper.listByPublic(null);

            if (CollUtil.isNotEmpty(list)) {
                List<InterfacePermissionDTO> privateList = list.stream().filter(v -> !BooleanUtil.isTrue(v.getIsPublic())).collect(Collectors.toList());
                List<InterfacePermissionDTO> publicList = list.stream().filter(v -> BooleanUtil.isTrue(v.getIsPublic())).collect(Collectors.toList());

                if (CollUtil.isNotEmpty(privateList))
                    bizRedisService.set(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PRIVATE, privateList);
                if (CollUtil.isNotEmpty(publicList))
                    bizRedisService.set(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC, publicList);

                count = CollUtil.size(list);
            }
        } catch (Exception e) {
            log.error("初始化接口权限异常 error:", e);
            return;
        }
        log.info("初始化接口权限结束 完成初始化缓存数据：{}", count);
    }

    @Override
    public boolean verifyPermission(String requestUrl, List<String> roleCodes) {
        //先校验是否是公共接口
        List<InterfacePermissionDTO> publicList = this.getListByPublic(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC);
        if(iAuthorizeService.verifyPublic(requestUrl,publicList))
            return true;
        //不在公共接口范围
        if (CollUtil.isEmpty(roleCodes))
            return false;

        String requestPermissionKey = iAuthorizeService.getAuthKey(requestUrl,roleCodes);
        if (bizRedisService.hasKey(requestPermissionKey))
            return bizRedisService.get(requestPermissionKey, Boolean.class);

        //校验是否受权限控制
        List<InterfacePermissionDTO> privateList = this.getListByPublic(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PRIVATE);
        if (CollUtil.isNotEmpty(privateList)) {
            //接口是否在权限范围内
            List<String> permissionCodes = privateList.stream()
                    .filter(v -> CommonUtils.equalsByUrl(requestUrl, v.getInterfaceUrl()))
                    .map(v -> v.getPermissionCode())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            //不在权限控制返范围
            if (CollUtil.isEmpty(permissionCodes))
                return false;

            //缓存 10分钟
            Boolean existPermission = rolePermissionMapper.existPermission(roleCodes, permissionCodes) > 0;
            bizRedisService.setex(requestPermissionKey, 10 * 60L, existPermission);
            return existPermission;
        }

        return false;
    }

    private List<InterfacePermissionDTO> getListByPublic(String redisKey) {
        if (CharSequenceUtil.isBlank(redisKey))
            return sysInterfacePermissionMapper.listByPublic(null);
        List<InterfacePermissionDTO> result = List.of();
        switch (redisKey) {
            case InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PRIVATE: {
                result = getInterfacePermission(redisKey, Boolean.FALSE);
            }
            break;
            case InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC: {
                result = getInterfacePermission(redisKey, Boolean.TRUE);
            }
            break;
            default:
                break;
        }
        return result;
    }

    private List<InterfacePermissionDTO> getInterfacePermission(String redisKey, Boolean isPublic) {
        List<InterfacePermissionDTO> result = List.of();
        if (bizRedisService.hasKey(redisKey))
            result = bizRedisService.get(redisKey, List.class);
        if (CollUtil.isEmpty(result)) {
            result = sysInterfacePermissionMapper.listByPublic(isPublic);
            if (CollUtil.isNotEmpty(result))
                bizRedisService.set(redisKey, result);
        }
        return result;
    }


}