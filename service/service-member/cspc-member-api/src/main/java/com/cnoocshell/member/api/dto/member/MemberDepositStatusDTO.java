package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberDepositStatusDTO implements Serializable {

    @ApiModelProperty("会员类型")
    private String memberType;

    @ApiModelProperty("CRM客户代码")
    private String crmCode;

    @ApiModelProperty("买家企业名称")
    private String memberName;

    @ApiModelProperty("保证金状态")
    private String depositStatus;

    private String accountId;

    private Integer pageNum;

    private Integer pageSize;

    private List<String> goodsCodeList;

    private String depositStatusUpdateStartTime;

    private String depositStatusUpdateEndTime;
}
