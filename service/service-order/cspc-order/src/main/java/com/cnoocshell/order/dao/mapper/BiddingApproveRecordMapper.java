package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.bidding.BiddingApproveListDTO;
import com.cnoocshell.order.dao.vo.BiddingApproveRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BiddingApproveRecordMapper extends IBaseMapper<BiddingApproveRecord> {

    List<BiddingApproveListDTO> queryApproveList(@Param("biddingId") String biddingId, @Param("status") String status);
}
