package com.cnoocshell.web.base.controller;

import com.cnoocshell.base.api.dto.permission.PermissionTreeDTO;
import com.cnoocshell.base.api.service.IPermissionService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.web.common.annotation.NoLogin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = {"Permission"}, description = "权限相关service")
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final IPermissionService permissionService;

    @ApiOperation("查询权限菜单")
    @PostMapping(value = "/findPermissionMenu")
    ItemResult<PermissionTreeDTO> findPermissionMenu(@RequestParam("roleCode") String roleCode, @RequestParam("roleType") String roleType){
        return new ItemResult<>(permissionService.findPermissionMenu(roleCode, roleType));
    }
}
