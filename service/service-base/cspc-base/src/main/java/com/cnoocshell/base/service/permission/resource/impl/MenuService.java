package com.cnoocshell.base.service.permission.resource.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.base.api.dto.AccountPermissionDTO;
import com.cnoocshell.base.api.dto.authRes.GetMenuTreeDTO;
import com.cnoocshell.base.service.permission.authenticate.IAuthenticateService;
import com.cnoocshell.base.service.permission.resource.IMenuService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MenuService implements IMenuService {

    private final IAuthenticateService authenticateService;

    @Override
    public AccountPermissionDTO getMenuTree(GetMenuTreeDTO dto) {
        log.info("======>getMenuTree:{}", dto);
        if (CollUtil.isEmpty(dto.getRoleIdList())) {
            AccountPermissionDTO accountPermissionDTO = new AccountPermissionDTO();
            accountPermissionDTO.setProjectType(dto.getPlatform());
            accountPermissionDTO.setCrumbsMap(Maps.newHashMap());
            accountPermissionDTO.setButtonList(Lists.newArrayList());
            accountPermissionDTO.setPageList(Sets.newHashSet());
            accountPermissionDTO.setMenuTreeList(Lists.newArrayList());
            accountPermissionDTO.setMenuPageCodeList(Lists.newArrayList());
            return accountPermissionDTO;
        }
        return authenticateService.findPermissionByProject(dto.getRoleIdList(), dto.getPlatform());
    }
}