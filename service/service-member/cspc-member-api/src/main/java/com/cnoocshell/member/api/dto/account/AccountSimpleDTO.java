
package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 */
@ApiModel("账号DTO")
@Data
public class AccountSimpleDTO implements Serializable {

    private static final long serialVersionUID = 6199641122262891612L;

    /**
     * 账户id
     */
    @ApiModelProperty("账户id")
    private String accountId;

    /**
     * 账户code
     */
    @ApiModelProperty("账户code")
    private String accountCode;

    /**
     * 账户名(登陆使用，唯一)
     */
    @ApiModelProperty("账户名(登陆使用，唯一)")
    private String accountName;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 账户昵称
     */
    @ApiModelProperty("账户昵称")
    private String accountNickname;

    /**
     * 0个人/1企业主账号/2企业子账号
     */
    @ApiModelProperty("账号类型0个人/1企业主账号/2企业子账号")
    private Integer accountType;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private String memberId;

    /**
     * 会员code
     */
    @ApiModelProperty("会员code")
    private String memberCode;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名称")
    private String memberName;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名称")
    private String memberShortName;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("个人会员-电话")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("openID")
    private String wechatId;

}