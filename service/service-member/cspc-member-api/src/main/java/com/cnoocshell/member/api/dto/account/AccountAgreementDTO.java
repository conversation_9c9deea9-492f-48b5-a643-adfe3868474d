package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.member.api.enums.AgreementBusinessTypeEnum;
import com.cnoocshell.member.api.enums.AgreementTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class AccountAgreementDTO {
    @ApiModelProperty("账户ID")
    private String accountId;
    /**
     * {@link AgreementTypeEnum}
     */
    @ApiModelProperty("协议类型")
    private String agreementType;

    @ApiModelProperty("协议版本号")
    private String agreementVersion = "1.0";

    @ApiModelProperty("业务单号")
    private String businessNo;

    /**
     * {@link AgreementBusinessTypeEnum}
     */
    @ApiModelProperty("业务单号类型")
    private String businessType;
}
