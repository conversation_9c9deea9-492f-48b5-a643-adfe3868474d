package com.cnoocshell.goods.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.goods.dao.vo.GoodsAttribute;

import java.util.List;

/**
 * 
 * @Author: <EMAIL>
 * @Description  IGoodsAttributeBiz
 * @date   2018年8月15日 下午4:54:20
 */
public interface IGoodsAttributeBiz extends IBaseBiz<GoodsAttribute> {

    List<GoodsAttribute> findByAttrNameAndValueType(String attrName,int valueType);

    List<GoodsAttribute> findByAttrNameLike(String attrName);
    GoodsAttribute findByAttrName(String attrName);
    GoodsAttribute findByAttrNameAndAttrType(String attrName,int attrType);
}
