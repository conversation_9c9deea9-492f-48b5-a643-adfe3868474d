package com.cnoocshell.base.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

@Getter
public enum PlatformEnum {
	PLATFORM("PLATFORM"),
	BUYER("BUYER"),
	SELLER("SELLER"),
	<PERSON><PERSON><PERSON><PERSON>("CARRIER"),
	APP("APP"),
	MINI_APPS("MINI_APPS"),
	SELLER_APP("SELLER_APP"),
	BUYER_APP("BUYER_APP"),
	DRIVER_APP("DRIVER_APP"),
	BASE("BASE"),
	EMALL("EMALL"),
	SUPPLIER("SUPPLIER");
	
	private String value;
	PlatformEnum(String value){
		this.value=value;
	}
	public static PlatformEnum convert(String value){
		if( StringUtils.isBlank(value) ){
			return null;
		}
		for (PlatformEnum platformEnum : PlatformEnum.values()) {
			if( StringUtils.equals(platformEnum.value,value) ){
				return platformEnum;
			}
		}
		return null;
	}
}
