package com.cnoocshell.integration.handler;

import cn.hutool.core.text.CharSequenceUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Configuration
@Data
@ConfigurationProperties(prefix = "oss.cucloud")
public class OssConfig {
    private String accessKey;
    private String secretKey;
    private String endpoint;
    private String bucketName;
    private int connectionTimeout;
    private int socketTimeout;
    private int maxErrorRetry;

    @Bean
    public AmazonS3Client getOssClient() {
        BasicAWSCredentials basicAwsCred = new BasicAWSCredentials(accessKey, secretKey);

        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setProtocol(Protocol.HTTP);
        clientConfiguration.setConnectionTimeout(connectionTimeout);
        clientConfiguration.setSocketTimeout(socketTimeout);
        clientConfiguration.setMaxErrorRetry(maxErrorRetry);
        AmazonS3Client s3 = new AmazonS3Client(basicAwsCred, clientConfiguration);
        S3ClientOptions options = S3ClientOptions.builder()
                .setPathStyleAccess(true)
                .setPayloadSigningEnabled(true)
                .disableChunkedEncoding().build();
        s3.setS3ClientOptions(options);
        s3.setEndpoint(endpoint);
        return s3;
    }

    public String getBucketName() {
        return this.bucketName;
    }

    public String getUrl(){
        return CharSequenceUtil.format("https://{}.{}", bucketName, endpoint);
    }
}