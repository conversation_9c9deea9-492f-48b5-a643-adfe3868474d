package com.cnoocshell.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cnoocshell.base.api.dto.AccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionAccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionGoodsCodeDTO;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.MessageDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.GeneratorCodeUtil;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.order.api.dto.salesPlan.*;
import com.cnoocshell.order.biz.ISalesPlanBiz;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.mapper.SalesPlanMapper;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.SalesPlanService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SalesPlanServiceImpl implements SalesPlanService {

    @Autowired
    private ISalesPlanBiz salesPlanBiz;

    @Autowired
    private SalesPlanMapper salesPlanMapper;
    
    @Autowired
    private IEmailSendService emailSendService;

    @Autowired
    private IAccountService accountService;
    
    @Autowired
    private IMessageSendService messageSendService;

    @Autowired
    private GeneratorCodeUtil generatorCodeUtil;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private BiddingMapper biddingMapper;


    @Override
    public PageInfo<SalesPlanPageResponseDTO> findSalePlanByCondition(SalesPlanPageRequestDTO requestDTO) {
        PageInfo<SalesPlanPageResponseDTO> resDTO = new PageInfo<>();
        List<DataPermissionDTO> goodsByCountId = getDataPermissionList(requestDTO.getMemberId(),requestDTO.getAccountId());
        if (CollectionUtils.isEmpty(goodsByCountId)){
            return resDTO;
        }
        List<String> goodsCodeList = goodsByCountId.stream().map(DataPermissionDTO::getGoodsCode).collect(Collectors.toList());
        PageInfo<SalesPlan> pageByCondition = salesPlanBiz.findSalesPlanByCondiftion(requestDTO, goodsCodeList);
        if (null != pageByCondition) {
            BeanUtils.copyProperties(pageByCondition, resDTO);
            if (CollectionUtils.isNotEmpty(pageByCondition.getList())) {
                List<SalesPlanPageResponseDTO> pageList = pageByCondition.getList().stream()
                        .map(page -> {
                            SalesPlanPageResponseDTO res = new SalesPlanPageResponseDTO();
                            BeanUtils.copyProperties(page, res);
                            res.setApplySellableQuantity(new BigDecimal(page.getApplySellableQuantity()));
                            res.setDeliveryEffectStartDate(page.getDeliveryEffectStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                            res.setDeliveryEffectEndDate(page.getDeliveryEffectEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                            res.setCreateTime(page.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                            return res;
                        }).collect(Collectors.toList());
                resDTO.setList(pageList);
            }
        }
        return resDTO;
    }

    @Override
    public String addSalesPlan(SalesPlanInfoDTO requestDTO) {
        SalesPlan salesPlan = new SalesPlan();
        String datePart = new SimpleDateFormat("yyyy年MM月dd日").format(new Date());
        BeanUtils.copyProperties(requestDTO,salesPlan);
        salesPlan.setPlanName(datePart + " " + requestDTO.getGoodsName() + " " + DuplicateString.SALES_PLAN_NAME);
        salesPlan.setExpectBiddingCount(Integer.valueOf(requestDTO.getExpectBiddingCount()));
        salesPlan.setId(uuidGenerator.gain());
        salesPlan.setRemainSellAbleQuantity(requestDTO.getApplySellableQuantity());
        salesPlan.setDelFlg(false);
        salesPlan.setCreateUser(requestDTO.getCreateUser());
        salesPlan.setCreateUserName(requestDTO.getCreateUserName());
        salesPlan.setCreateTime(new Date());
        salesPlan.setUpdateUser(requestDTO.getCreateUser());
        salesPlan.setUpdateUserName(requestDTO.getCreateUserName());
        salesPlan.setUpdateTime(new Date());
        salesPlan.setPlanNo(generatorCodeUtil.generate("SP",3));
        if (requestDTO.getStatus().equals(DuplicateString.TO_APPROVE)) {
            salesPlan.setSubmitUser(requestDTO.getCreateUser());
            salesPlan.setSubmitUserName(requestDTO.getCreateUserName());
            salesPlan.setSubmitTime(new Date());
            salesPlanMapper.insert(salesPlan);

            this.notifySalePlanToApprove(salesPlan);

        } else if (requestDTO.getStatus().equals(DuplicateString.DRAFT)) {
            salesPlanMapper.insert(salesPlan);
        }
        return salesPlan.getId();
    }

    @Override
    public int revokeSalesPlan(String id) {
        SalesPlan salesPlan = salesPlanMapper.selectByPrimaryKey(id);
        if (DuplicateString.TO_APPROVE.equals(salesPlan.getStatus())) {
            salesPlan.setStatus(DuplicateString.DRAFT);
            salesPlan.setUpdateTime(new Date());
            return salesPlanMapper.updateByPrimaryKeySelective(salesPlan);
        }
        return 0;
    }

    @Override
    public void removeDraft(RemoveSalesPlanDTO param) {
        SalesPlan salesPlan = salesPlanBiz.getByNo(param.getSalesPlanNo());
        if (Objects.isNull(salesPlan))
            throw new BizException(OrderErrorCode.CUSTOM, "销售计划不存在");
        if (!DuplicateString.DRAFT.equals(salesPlan.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不支持删除");
        salesPlanMapper.removeSalePlanById(salesPlan.getId());
    }

    @Override
    public int updateSalesPlan(SalesPlanInfoDTO requestDTO) {
        SalesPlan salesPlan = salesPlanMapper.selectByPrimaryKey(requestDTO.getId());
        if (DuplicateString.DRAFT.equals(salesPlan.getStatus())
                || DuplicateString.REJECTED.equals(salesPlan.getStatus())) {
            requestDTO.setRemainSellAbleQuantity(requestDTO.getApplySellableQuantity());
            salesPlan.setStatus(DuplicateString.DRAFT);
            BeanUtils.copyProperties(requestDTO, salesPlan);
            salesPlan.setUpdateTime(new Date());
            salesPlan.setUpdateUser(requestDTO.getUpdateUser());
            salesPlan.setUpdateUserName(requestDTO.getUpdateUserName());
            salesPlan.setUpdateTime(new Date());
            //预计竞价场次次数
            if(CharSequenceUtil.isNotBlank(requestDTO.getExpectBiddingCount())){
                if(!BooleanUtil.isTrue(NumberUtil.isNumber(requestDTO.getExpectBiddingCount())))
                    throw new BizException(OrderErrorCode.CUSTOM,"预计竞价场次次数需为正整数");
                salesPlan.setExpectBiddingCount(NumberUtil.parseInt(requestDTO.getExpectBiddingCount()));
            }
            if (requestDTO.getStatus().equals(DuplicateString.TO_APPROVE)) {
                salesPlan.setSubmitUser(requestDTO.getCreateUser());
                salesPlan.setSubmitUserName(requestDTO.getCreateUserName());
                salesPlan.setSubmitTime(new Date());
                //通知销售计划审批人
                this.notifySalePlanToApprove(salesPlan);
            }
            return salesPlanMapper.updateByPrimaryKeySelective(salesPlan);
        }
        return 0;
    }


    @Override
    public SalesDetailDTO findSalePlanById(String id,String accountId,String memberId) {
        //数据权限校验
        SalesPlanPageRequestDTO requestDTO = new SalesPlanPageRequestDTO();
        requestDTO.setAccountId(accountId);
        requestDTO.setMemberId(memberId);
        requestDTO.setId(id);
        PageInfo<SalesPlanPageResponseDTO> salePlanByCondition = findSalePlanByCondition(requestDTO);
        if (salePlanByCondition.getTotal() == 0) {
            return new SalesDetailDTO();
        }
        SalesDetailDTO resDTO = new SalesDetailDTO();
        Example example = new Example(SalesPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DuplicateString.ID, id);
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        SalesPlan salesPlan = salesPlanMapper.selectOneByExample(example);
        BeanUtils.copyProperties(salesPlan,resDTO);
        resDTO.setDeliveryEffectStartDate(salesPlan.getDeliveryEffectStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        resDTO.setDeliveryEffectEndDate(salesPlan.getDeliveryEffectEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        resDTO.setCreateTime(salesPlan.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        resDTO.setUpdateTime(salesPlan.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());

        Example example1 = new Example(Bidding.class);
        Example.Criteria criteria1 = example1.createCriteria();
        criteria1.andEqualTo("salesPlanNo", salesPlan.getPlanNo());
        criteria1.andEqualTo(DuplicateString.DEL_FLG, false);
        List<Bidding> biddings = biddingMapper.selectByCondition(example1);
        if (CollUtil.isNotEmpty(biddings)) {
            double sum = biddings.stream()
                    .filter(bidding -> bidding.getTotalDealQuantity() != null && !bidding.getTotalDealQuantity().isEmpty())
                    .mapToDouble(bidding -> Double.parseDouble(bidding.getTotalDealQuantity())).sum();
            resDTO.setTotalDealQuantity(String.valueOf(sum));
        }
        return resDTO;
    }

    public List<DataPermissionDTO> getDataPermissionList(String memberId, String accountId) {
        AccountRoleDTO dto = new AccountRoleDTO();
        dto.setAccountId(accountId);
        dto.setMemberId(memberId);
        dto.setPlatform("SELLER");
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);
        dto = roleService.getRoleByAccountId2(dto);
        if (accountSimpleDTO != null) {
            dto.setAccountId(accountId);
            dto.setAccountName(accountSimpleDTO.getAccountName());
            dto.setMemberName(accountSimpleDTO.getMemberName());
            dto.setRealName(accountSimpleDTO.getRealName());
            dto.setEmployeeId(accountSimpleDTO.getEmployeeId());
            dto.setMemberCode(accountSimpleDTO.getMemberCode());
            dto.setAccountCode(accountSimpleDTO.getAccountCode());
            dto.setMobile(accountSimpleDTO.getMobile());
            dto.setAccountType(accountSimpleDTO.getAccountType());
            dto.setDepartment(accountSimpleDTO.getDepartment());
            dto.setPosition(accountSimpleDTO.getPosition());
        }
        // 获取用户商品分类授权list
        return roleService.getDataPermissionList(dto);
    }


    /**
     *通知销售计划审批人
     */
    private void notifySalePlanToApprove(SalesPlan salesPlan) {
        DataPermissionGoodsCodeDTO permissionGoodsDTO = new DataPermissionGoodsCodeDTO();
        permissionGoodsDTO.setRoleCode(BaseRoleTypeEnum.SELLER_BUSINESS_MANAGER.getRoleCode());
        List<String> goodsCodeList = new ArrayList<>();
        goodsCodeList.add(salesPlan.getGoodsCode());
        permissionGoodsDTO.setGoodsCodeList(goodsCodeList);
        List<DataPermissionAccountInfoDTO> accountByCategoryCode = roleService.findAccountByGoodsCode(permissionGoodsDTO);
        MessageDTO messageDTO = new MessageDTO();
        if (CollectionUtils.isNotEmpty(accountByCategoryCode)) {
            List<AccountInfoDTO> accountList = accountByCategoryCode.get(0).getAccountList();
            accountList.forEach(accountInfoDTO -> {
                List<String> ids = new ArrayList<>();
                ids.add(accountInfoDTO.getAccountId());
                messageDTO.setReceiveAccountIds(ids);
                messageDTO.setTitle("销售计划待审批");
                messageDTO.setContent("尊敬的"+ accountInfoDTO.getRealName() +"，您有一条销售计划"+ salesPlan.getPlanNo() +"待审批，请及时处理，谢谢。");
                messageSendService.sendMessage(messageDTO);

                EmailDTO emailDTO = new EmailDTO();
                List<String> toEmailList = new ArrayList<>();
                toEmailList.add(accountInfoDTO.getEmail());
                emailDTO.setTos(toEmailList);
                emailDTO.setToCcs(new ArrayList<>());
                emailDTO.setEmailTemplateCode(EmailTemplateEnum.SALES_PLAN_CREATE.getCode());
                //邮件模板参数
                Map<String, Object> templateParam = new HashMap<>();
                templateParam.put("manager", accountInfoDTO.getRealName());
                templateParam.put("salesPlanNo", salesPlan.getPlanNo());
                templateParam.put("submitName", salesPlan.getSubmitUserName());
                templateParam.put("salesPlanName", salesPlan.getPlanName());
                emailDTO.setTemplateParam(templateParam);
                //发送邮件
                emailSendService.sendEmail(emailDTO);
            });
        }
    }
}
