package com.cnoocshell.web.seller.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanApprovePageDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageResponseDTO;
import com.cnoocshell.order.api.service.ISalesPlanApproveService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/salesPlanApprove")
public class SalesPlanApproveController {

    @Autowired
    private ISalesPlanApproveService salesPlanApproveService;


    @ApiOperation("分页查询销售计划审批列表")
    @PostMapping("/findSalePlanApproveByCondition")
    ItemResult<PageInfo<SalesPlanPageResponseDTO>> findSalePlanApproveByCondition(@ApiIgnore LoginInfo loginInfo,@ApiIgnore HttpServletRequest request, @RequestBody SalesPlanPageRequestDTO requestDTO){
        requestDTO.setAccountId(loginInfo.getAccountId());
        requestDTO.setMemberId(loginInfo.getMemberId());
        requestDTO.setType(request.getHeader("APPLICATION_TYPE"));
        return new ItemResult<>(salesPlanApproveService.findSalePlanApproveByCondition(requestDTO));
    }

    @ApiOperation("审批")
    @PostMapping("/approveSalesPlan")
    ItemResult<String> approveSalesPlan(@ApiIgnore LoginInfo loginInfo, @Validated @RequestBody SalesPlanInfoDTO requestDTO){
        requestDTO.setCreateUser(loginInfo.getAccountId());
        requestDTO.setCreateUserName(loginInfo.getRealName());
        return new ItemResult<>(salesPlanApproveService.approveSalesPlan(requestDTO));
    }

    @ApiOperation("查询审批数量")
    @PostMapping("/countApprove")
    ItemResult<Long> countApprove(@ApiIgnore LoginInfo loginInfo,@ApiIgnore HttpServletRequest request, @RequestBody SalesPlanPageRequestDTO requestDTO){
        requestDTO.setAccountId(loginInfo.getAccountId());
        requestDTO.setType(request.getHeader("APPLICATION_TYPE"));
        return new ItemResult<>(salesPlanApproveService.countApprove(requestDTO));
    }

    @ApiOperation("审批记录")
    @GetMapping("/approveSalesPlanList")
    ItemResult<List<SalesPlanApprovePageDTO>> approveSalesPlanList(@RequestParam("id") String id){
        return new ItemResult<>(salesPlanApproveService.approveSalesPlanList(id));
    }
}
