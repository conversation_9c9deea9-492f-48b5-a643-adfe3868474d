package com.cnoocshell.order.tasks;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.order.api.enums.EnquiryStatusEnum;
import com.cnoocshell.order.biz.impl.EnquiryBiz;
import com.cnoocshell.order.dao.mapper.EnquiryMapper;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 更新询价开始状态
 */
@Component
@Slf4j
public class EnquiryStartScheduler {

    @Resource
    private EnquiryMapper enquiryMapper;

    @Autowired
    private EnquiryBiz enquiryBiz;

    @Autowired
    private IXxlJobService iXxlJobService;

    @XxlJob("enquiryStartJob")
    public void doJob(String no) {
        XxlJobHelper.log("EnquiryStartScheduler is start");
        Long jobId = XxlJobHelper.getJobId();
        String enquiryNo = XxlJobHelper.getJobParam();
        try {
            XxlJobHelper.log("EnquiryStartScheduler  enquiryNo:{}", enquiryNo);
            if (StringUtils.isEmpty(enquiryNo)) {
                return;
            }
            Condition condition1 = enquiryBiz.newCondition();
            condition1.createCriteria().andEqualTo("enquiryNo", enquiryNo);
            List<Enquiry> enquiryList = enquiryBiz.findByCondition(condition1);
            if (CollectionUtils.isEmpty(enquiryList)) {
                XxlJobHelper.log("EnquiryStartScheduler enquiryList is empty:");
                return;
            }
            List<String> ids = enquiryList.stream().map(Enquiry::getId).collect(Collectors.toList());
            enquiryMapper.updateStatusByIds(ids, EnquiryStatusEnum.ENQUIRING.getCode());
        }catch (Exception e) {
            log.error("EnquiryStartScheduler no:{} error:",no,e);
            XxlJobHelper.log("EnquiryStartScheduler is error:"+CharSequenceUtil.sub(ExceptionUtil.stacktraceToString(e),0,1024));
        }
        XxlJobHelper.log("EnquiryStartScheduler is end");
        //屏蔽销毁逻辑
//        destroyJob(jobId, enquiryNo);
    }

    private void destroyJob(Long jobId, String no) {
        XxlJobHelper.log(CharSequenceUtil.format("询价自动任务销毁开始 jobId:{} enquiryNo：{}", jobId, no));
        iXxlJobService.destroyXxlJob(jobId);
    }

}
