package com.cnoocshell.information.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageDeleteDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageQueryDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageReadStatusUpdateDTO;
import com.cnoocshell.information.dao.vo.PrivateMessageInbox;
import com.github.pagehelper.PageInfo;

public interface IPrivateMessageInboxBiz extends IBaseBiz<PrivateMessageInbox> {

    /**
     * 查询未读消息数量
     * @param accountId
     * @return
     */
    int countByAccountId(String accountId);

    /**
     * 删除消息
     * @param internalMessageDeleteDTO
     */
    Boolean delete(InternalMessageDeleteDTO internalMessageDeleteDTO);

    /**
     * 设置状态为已读 一个会员下的一个账号的一条或者多条
     * @param internalMessageReadStatusUpdateDTO
     * @return
     */
    Boolean updateReadStatus(InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO);

    PageInfo<PrivateMessageInbox> findAll(InternalMessageQueryDTO internalMessageQueryDTO);
}
