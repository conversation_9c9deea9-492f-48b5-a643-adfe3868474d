package com.cnoocshell.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.biz.ISalesPlanBiz;
import com.cnoocshell.order.dao.mapper.SalesPlanMapper;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.exception.DuplicateString;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
@RequiredArgsConstructor
public class SalesPlanBiz extends BaseBiz<SalesPlan> implements ISalesPlanBiz {
    @Resource
    private SalesPlanMapper salesPlanMapper;

    @Override
    public PageInfo<SalesPlan> findSalesPlanByCondiftion(SalesPlanPageRequestDTO requestDTO, List<String> goodsCode) {
        Condition condition = new Condition(SalesPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        //模糊查询条件
        likeCodition(criteria,requestDTO);
        if (CollectionUtils.isNotEmpty(requestDTO.getStatus())) {
            criteria.andIn(DuplicateString.STATUS, requestDTO.getStatus());
        }
        if (StringUtils.isNotBlank(requestDTO.getId())) {
            criteria.andEqualTo(DuplicateString.ID, requestDTO.getId());
        }
        if (requestDTO.getIsCreatedInquiry() != null) {
            criteria.andEqualTo(DuplicateString.IS_CREATED_INQUIRY, requestDTO.getIsCreatedInquiry());
        }
        if (requestDTO.getIsNeedInquiry() != null) {
            criteria.andEqualTo(DuplicateString.IS_NEED_INQUIRY, requestDTO.getIsNeedInquiry());
        }
        if (requestDTO.getDeliveryEffectEndDate() != null  && requestDTO.getDeliveryEffectStartDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String startDate = sdf.format(requestDTO.getDeliveryEffectStartDate());
            String endDate = sdf.format(requestDTO.getDeliveryEffectEndDate());
            Condition condition2 = new Condition(SalesPlan.class);
            Example.Criteria criteria1 = condition2.createCriteria();
            criteria1.andLessThanOrEqualTo("deliveryEffectStartDate",endDate)
                            .andGreaterThanOrEqualTo("deliveryEffectEndDate",startDate);
            condition.and(criteria1);
        }
        if (StringUtils.isNotBlank(requestDTO.getCreateUserName())) {
            criteria.andLike(DuplicateString.CREATE_USER_NAME, "%" + requestDTO.getCreateUserName() + "%");
        }
        if (requestDTO.getCreateStartDate() != null && requestDTO.getCreateEndDate() != null) {
            LocalDateTime localDateTime = requestDTO.getCreateEndDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime endOfDay = localDateTime.withHour(23).withMinute(59).withSecond(59);
            Date newDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            criteria.andGreaterThanOrEqualTo(DuplicateString.CREATE_TIME, requestDTO.getCreateStartDate())
                    .andLessThanOrEqualTo(DuplicateString.CREATE_TIME, newDate);
        }

        criteria.andIn(DuplicateString.GOODS_CODE, goodsCode);
        condition.orderBy("createTime").desc();
        PageInfo pageInfo = new PageInfo();
        if (DuplicateString.MP.equals(requestDTO.getType())) {
            salesPlanMapper.selectByCondition(condition);
            PageInfo<SalesPlan> resDTO = new PageInfo<>();
            resDTO.setList(salesPlanMapper.selectByCondition(condition));
            resDTO.setTotal(resDTO.getList().size());
            return resDTO;
        } else {
            pageInfo.setPageSize(requestDTO.getPageSize() < 10 ? 10 : requestDTO.getPageSize());
            pageInfo.setPageNum(requestDTO.getPageNum() < 0 ? 1 : requestDTO.getPageNum());
            return super.pageInfo(condition, pageInfo);
        }
    }

    private void likeCodition(Example.Criteria criteria, SalesPlanPageRequestDTO requestDTO) {
        if (StringUtils.isNotBlank(requestDTO.getPlanNo())) {
            criteria.andLike(DuplicateString.PLAN_NO, "%" + requestDTO.getPlanNo() + "%");
        }
        if (StringUtils.isNotBlank(requestDTO.getPlanName())) {
            criteria.andLike(DuplicateString.PLAN_NAME, "%" + requestDTO.getPlanName() + "%");
        }
        if (StringUtils.isNotBlank(requestDTO.getGoodsName())) {
            criteria.andLike(DuplicateString.GOODS_NAME, "%" + requestDTO.getGoodsName() + "%");
        }
        if (StringUtils.isNotBlank(requestDTO.getCreateUserName())) {
            criteria.andLike(DuplicateString.CREATE_USER_NAME, "%" + requestDTO.getCreateUserName() + "%");
        }
    }

    @Override
    public List<SalesPlan> listByNos(List<String> nos) {
        if(CollUtil.isEmpty(nos))
            return Collections.emptyList();
        Condition condition = new Condition(SalesPlan.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(ColumnConstant.PLAN_NO,nos);
        return this.findByCondition(condition);
    }

    @Override
    public SalesPlan getByNo(String no) {
        if(CharSequenceUtil.isBlank(no))
            return null;

        Condition condition = new Condition(SalesPlan.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.PLAN_NO,no);
        return CollUtil.getFirst(this.findByCondition(condition));
    }

    @Override
    public void updateRemainSellAbleQuantity(String salePlanNo, BigDecimal dealTotalQuantity) {
        if(CharSequenceUtil.isBlank(salePlanNo) || Objects.isNull(dealTotalQuantity))
            return;
        salesPlanMapper.updateRemainSellAbleQuantity(salePlanNo,dealTotalQuantity);
    }

    @Override
    public Boolean updateCreatedInquiry(String salePlanNo, Integer isCreatedInquiry) {
        if(CharSequenceUtil.isBlank(salePlanNo))
            return false;

        return salesPlanMapper.updateCreatedInquiry(salePlanNo,isCreatedInquiry) > 0;
    }

}
