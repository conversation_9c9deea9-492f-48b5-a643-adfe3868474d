

package com.cnoocshell.base.api.dto.role;

import com.cnoocshell.base.api.dto.DataPermissionDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;
import java.util.List;

@Data
public class AccountRoleDTO {

    @NotBlank
    @ApiModelProperty("账号id")
    private String accountId;
    @NotBlank
    @ApiModelProperty("会员id")
    private String memberId;

    @ApiModelProperty("是否主账号授权")
    private Boolean masterFlg;

    @ApiModelProperty("会员类型")
    private String memberType;

    @ApiModelProperty("角色id集合")
    private List<Integer> roleIds;

    @ApiModelProperty("所属平台")
    private String platform;

    @ApiModelProperty("平台角色列表")
    private List<PlatformRoleDTO> platformRoleList;

    @ApiModelProperty("商品分类list")
    private List<DataPermissionDTO> dataPermissionDTOList;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("会员名称")
    private String memberName;

    @ApiModelProperty("真实姓名")
    private String realName;
    @ApiModelProperty("员工工号")
    private String employeeId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("移动电话")
    private String mobile;

    @ApiModelProperty("会员代码")
    private String memberCode;

    @ApiModelProperty("账号代码")
    private String accountCode;

    @ApiModelProperty("账号类型")
    private Integer accountType;

    /**
     * 是否立即生效
     */
    @ApiModelProperty("是否立即生效")
    private Integer isImmediateEffect;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private Date effectTime;

}
