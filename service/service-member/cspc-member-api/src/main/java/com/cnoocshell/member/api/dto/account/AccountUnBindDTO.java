package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *账号解绑移除通知
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountUnBindDTO {
    @ApiModelProperty("会员ID")
    private String memberId;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("账户ID")
    private String accountId;
}
