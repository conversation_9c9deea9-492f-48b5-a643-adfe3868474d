package com.cnoocshell.information.api.dto.announcement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@ApiModel("公告DTO")
@Data
public class AnnouncementDTO {
    /**
     * id
     */
    @ApiModelProperty("id")
    private String announcementId;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 生效开始时间
     */
    @ApiModelProperty("生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty("生效结束时间")
    private Date endTime;

    /**
     * 进行状态０未开始　3 进行中，4 已关闭"
     */
    @ApiModelProperty("进行状态 0未开始 3进行中 4 已关闭")
    private Integer processStatus=0;

    /**
     * 公告内容url
     */
    @ApiModelProperty("公告内容url")
    private String contentUrl;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    private String content;

    /**
     * 可用状态 0启用  1暂停
     */
    @ApiModelProperty("可用状态 0启用  1暂停")
    private Integer availableStatus;

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @ApiModelProperty("审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @ApiModelProperty("公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    /**
     * 排序序号
     */
    @ApiModelProperty("排序序号")
    private Integer weight;

    /**
     * 删除状态
     */
    @ApiModelProperty("删除状态")
    private Integer delFlg;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 附件id
     */
    @ApiModelProperty("附件id")
    private String attachmentIds;

    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    private List<AnnouncementAttachmentDTO> attachmentList;
}

