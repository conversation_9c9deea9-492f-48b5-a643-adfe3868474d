package com.cnoocshell.goods.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "go_resource_region")
public class ResourceRegion implements Serializable {
    /**
     * 资源行政区域ID
     */
    @Id
    @Column(name = "resource_region_id")
    private String resourceRegionId;

    /**
     * 资源ID
     */
    @Column(name = "resource_id")
    private String resourceId;

    /**
     * 资源版本号
     */
    @Column(name = "resource_version")
    private Integer resourceVersion;

    /**
     * 所在国家编码
     */
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 所在省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @Column(name = "area_code")
    private String areaCode;

    /**
     * 所在国家名称
     */
    @Column(name = "country_name")
    private String countryName;

    /**
     * 所在省名称
     */
    @Column(name = "province_name")
    private String provinceName;

    /**
     * 所在城市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 所在地区名称
     */
    @Column(name = "area_name")
    private String areaName;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 所在街道编码
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 所在街道名称
     */
    @Column(name = "street_name")
    private String streetName;

    private static final long serialVersionUID = 1L;

    /**
     * 获取资源行政区域ID
     *
     * @return resource_region_id - 资源行政区域ID
     */
    public String getResourceRegionId() {
        return resourceRegionId;
    }

    /**
     * 设置资源行政区域ID
     *
     * @param resourceRegionId 资源行政区域ID
     */
    public void setResourceRegionId(String resourceRegionId) {
        this.resourceRegionId = resourceRegionId == null ? null : resourceRegionId.trim();
    }

    /**
     * 获取资源ID
     *
     * @return resource_id - 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源ID
     *
     * @param resourceId 资源ID
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * 获取资源版本号
     *
     * @return resource_version - 资源版本号
     */
    public Integer getResourceVersion() {
        return resourceVersion;
    }

    /**
     * 设置资源版本号
     *
     * @param resourceVersion 资源版本号
     */
    public void setResourceVersion(Integer resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    /**
     * 获取所在国家编码
     *
     * @return country_code - 所在国家编码
     */
    public String getCountryCode() {
        return countryCode;
    }

    /**
     * 设置所在国家编码
     *
     * @param countryCode 所在国家编码
     */
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode == null ? null : countryCode.trim();
    }

    /**
     * 获取所在省编码
     *
     * @return province_code - 所在省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置所在省编码
     *
     * @param provinceCode 所在省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取所在城市编码
     *
     * @return city_code - 所在城市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置所在城市编码
     *
     * @param cityCode 所在城市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取所在地区编码
     *
     * @return area_code - 所在地区编码
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     * 设置所在地区编码
     *
     * @param areaCode 所在地区编码
     */
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode == null ? null : areaCode.trim();
    }

    /**
     * 获取所在国家名称
     *
     * @return country_name - 所在国家名称
     */
    public String getCountryName() {
        return countryName;
    }

    /**
     * 设置所在国家名称
     *
     * @param countryName 所在国家名称
     */
    public void setCountryName(String countryName) {
        this.countryName = countryName == null ? null : countryName.trim();
    }

    /**
     * 获取所在省名称
     *
     * @return province_name - 所在省名称
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * 设置所在省名称
     *
     * @param provinceName 所在省名称
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * 获取所在城市名称
     *
     * @return city_name - 所在城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 设置所在城市名称
     *
     * @param cityName 所在城市名称
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * 获取所在地区名称
     *
     * @return area_name - 所在地区名称
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * 设置所在地区名称
     *
     * @param areaName 所在地区名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取所在街道编码
     *
     * @return street_code - 所在街道编码
     */
    public String getStreetCode() {
        return streetCode;
    }

    /**
     * 设置所在街道编码
     *
     * @param streetCode 所在街道编码
     */
    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null : streetCode.trim();
    }

    /**
     * 获取所在街道名称
     *
     * @return street_name - 所在街道名称
     */
    public String getStreetName() {
        return streetName;
    }

    /**
     * 设置所在街道名称
     *
     * @param streetName 所在街道名称
     */
    public void setStreetName(String streetName) {
        this.streetName = streetName == null ? null : streetName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", resourceRegionId=").append(resourceRegionId);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", resourceVersion=").append(resourceVersion);
        sb.append(", countryCode=").append(countryCode);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", areaCode=").append(areaCode);
        sb.append(", countryName=").append(countryName);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityName=").append(cityName);
        sb.append(", areaName=").append(areaName);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createUser=").append(createUser);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", streetName=").append(streetName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}