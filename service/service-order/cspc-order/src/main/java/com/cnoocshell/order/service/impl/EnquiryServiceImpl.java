package com.cnoocshell.order.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.cron.pattern.CronPatternBuilder;
import cn.hutool.cron.pattern.Part;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.ValueSetDTO;
import com.cnoocshell.base.api.dto.ValueSetTreeDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.dto.role.RoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.dto.*;
import com.cnoocshell.common.enums.ApplicationType;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.GoodsDTO;
import com.cnoocshell.goods.api.dto.goods.GoodCodesListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.enums.SellerFlgEnum;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.order.api.constant.OrderNumberConstant;
import com.cnoocshell.order.api.constant.StringConstant;
import com.cnoocshell.order.api.constant.XxlJobConstant;
import com.cnoocshell.order.api.dto.*;
import com.cnoocshell.order.api.dto.analysis.EnquiryAnalysisInfoDTO;
import com.cnoocshell.order.api.dto.analysis.EnquiryAnalysisResultDTO;
import com.cnoocshell.order.api.dto.enquiry.*;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.api.enums.BiddingMessageTemplateEnum;
import com.cnoocshell.order.api.enums.EnquiryStatusEnum;
import com.cnoocshell.order.api.enums.ParticipationStatusEnum;
import com.cnoocshell.order.api.enums.SalesChannelEnum;
import com.cnoocshell.order.biz.*;
import com.cnoocshell.order.biz.impl.SalesPlanBiz;
import com.cnoocshell.order.common.SimpleDailyCounter;
import com.cnoocshell.order.config.XxlJobConfig;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerDetailMapper;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerMapper;
import com.cnoocshell.order.dao.mapper.EnquiryMapper;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.EnquiryService;
import com.cnoocshell.order.service.IBaseBizService;
import com.cnoocshell.order.service.IBiddingAccountService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 询价
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnquiryServiceImpl implements EnquiryService {

    @Autowired
    private IEnquiryBiz enquiryBiz;

    @Autowired
    private IEnquiryBuyerBiz enquiryBuyerBiz;

    @Autowired
    private IMemberService memberService;

    @Resource
    private EnquiryMapper enquiryMapper;

    @Resource
    private BiddingMapper biddingMapper;

    @Resource
    private EnquiryBuyerMapper enquiryBuyerMapper;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private SimpleDailyCounter simpleDailyCounter;

    @Autowired
    private IGoodsCategoryService goodsCategoryService;

    @Autowired
    private ISalesPlanBiz salesPlanBiz;

    @Autowired
    private IEnquiryBuyerDetailBiz enquiryBuyerDetailBiz;

    @Autowired
    private EnquiryBuyerServiceImpl enquiryBuyerService;

    @Autowired
    private XxlJobConfig xxlJobConfig;

    @Autowired
    private IXxlJobService iXxlJobService;

    @Autowired
    private IValueSetService iValueSetService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private IRoleService roleService;

    private final IBiddingAccountService iBiddingAccountService;
    private final IMessageSendService iMessageSendService;
    private final ISmsSendService iSmsSendService;
    private final IBiddingBiz iBiddingBiz;
    private final EnquiryBuyerDetailMapper enquiryBuyerDetailMapper;
    private final IBaseBizService iBaseBizService;

    private static final String QUERY_USER_ERROR ="查询用户异常！";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<String> createEnquiry(EnquiryDTO enquiryDTO) {
        String checkResult = checkEnquiry(enquiryDTO);
        if (StringUtils.isNotEmpty(checkResult)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), checkResult);
        }
        Enquiry enquiry = Enquiry.builder().build();
        BeanUtils.copyProperties(enquiryDTO, enquiry);
        AccountDTO accountDTO = accountService.findById(enquiryDTO.getOperator());

        SalesPlan salesPlan = salesPlanBiz.get(enquiryDTO.getSalesPlanId());
        log.info("EnquiryServiceImpl createEnquiry salesPlan:{}", salesPlan);
        if (Objects.isNull(salesPlan)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "销售计划不存在！");
        }

        //CR 销售计划的询价场次作废或者取消后，允许再创建询价 调整
        //有效询价状态
        List<String> effectStatus = Arrays.asList(EnquiryStatusEnum.DRAFT.getCode(),
                EnquiryStatusEnum.TO_START.getCode(),
                EnquiryStatusEnum.ENQUIRING.getCode(),
                EnquiryStatusEnum.END.getCode());
        if (BooleanUtil.isTrue(enquiryBiz.existByPlanAndStatus(salesPlan.getPlanNo(),effectStatus))) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "该销售计划已创建询价！");
        }

        enquiry.setId(IdUtil.simpleUUID());
        enquiry.setCreateTime(new Date());
        enquiry.setUpdateTime(new Date());
        enquiry.setUpdateUser(accountDTO.getAccountId());
        enquiry.setUpdateUserName(StringUtils.isEmpty(accountDTO.getRealName()) ? accountDTO.getAccountName() : accountDTO.getRealName());
        enquiry.setCreateUser(accountDTO.getAccountId());
        enquiry.setCreateUserName(StringUtils.isEmpty(accountDTO.getRealName()) ? accountDTO.getAccountName() : accountDTO.getRealName());
        enquiry.setDelFlg(false);
        //取消竞价初始化
        enquiry.setCancelBiddingNotifyFlag(Boolean.FALSE);
        enquiry.setEnquiryNo(getEnquiryNum());



        enquiry.setGoodsCode(salesPlan.getGoodsCode());
        enquiry.setGoodsName(salesPlan.getGoodsName());
        enquiry.setCategoryCode(salesPlan.getCategoryCode());
        enquiry.setSalesPlanNo(salesPlan.getPlanNo());
        enquiry.setSalesPlanName(salesPlan.getPlanName());
        enquiry.setEnquiryName(DateUtil.format(new Date(), "yyyy年MM月dd日") + salesPlan.getGoodsName() + "产品询价");
        //设置询价包装方式 pack
        setEnquiryPack(enquiry);
        int insertEnquiry = enquiryBiz.insert(enquiry);
        if (insertEnquiry == 0) {
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "创建失败！");
        }
        //创建enquiry_buyer
        List<MemberDataInfoDTO> memberDetailDTOS = memberService.queryMemberInfo(enquiry.getGoodsCode());
        log.info("EnquiryServiceImpl createEnquiry memberDetailDTOS:{}", memberDetailDTOS);
        List<EnquiryBuyer> enquiryBuyerList = Lists.newArrayList();
        for (MemberDataInfoDTO memberDataInfoDTO : memberDetailDTOS) {
            EnquiryBuyer enquiryBuyer = EnquiryBuyer.builder()
                    .id(IdUtil.simpleUUID())
                    .enquiryNo(enquiry.getEnquiryNo())
                    .memberName(memberDataInfoDTO.getMemberName())
                    .memberCode(memberDataInfoDTO.getMemberCode())
                    .goodsCode(enquiry.getGoodsCode())
                    .goodsName(enquiry.getGoodsName())
                    .participationStatus(ParticipationStatusEnum.TODO.getCode())
                    .createTime(new Date())
                    .createUser(accountDTO.getAccountId())
                    .delFlg(false)
                    .build();
            enquiryBuyerList.add(enquiryBuyer);
        }

        salesPlan.setIsCreatedInquiry(1);
        salesPlanBiz.save(salesPlan);

        try {
            if (enquiryDTO.getStatus().equals(EnquiryStatusEnum.TO_START.getCode())) {
                new Thread(() -> createXllJob(enquiry)).start();
            }
        }catch (Exception e) {
            log.error("createEnquiry createXllJob is error:", e);
        }
        if (CollectionUtils.isEmpty(enquiryBuyerList)) {
            return new ItemResult<>("创建成功！");
        }
        try {
            enquiryBuyerBiz.batchInsert(enquiryBuyerList);
        }catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("createEnquiry is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "创建失败！");
        }
        return new ItemResult<>("创建成功！");
    }

    private void setEnquiryPack(Enquiry enquiry) {
        try {
            String goodsCode = enquiry.getGoodsCode();
            GoodCodesListDTO dto = new GoodCodesListDTO();
            dto.setGoodsCodeList(List.of(goodsCode));
            ItemResult<List<GoodsDataListDTO>> packSalesGroupByGoodsCode = goodsService.getPackSalesGroupByGoodsCode(dto);
            List<GoodsDataListDTO> goodsCodeData = packSalesGroupByGoodsCode.getData();
            if (CollUtil.isNotEmpty(goodsCodeData)) {
                GoodsDataListDTO goodsDataListDTO = goodsCodeData.stream().filter(f -> f.getGoodsCode().equals(goodsCode)).findFirst().orElse(null);
                if (Objects.nonNull(goodsDataListDTO)) {
                    enquiry.setPack(goodsDataListDTO.getPack());
                }
            }
        } catch (Exception e) {
            log.error("设置询价包装方式失败：{}", e.getMessage());
        }
    }

    @Override
    public void createXllJob(Enquiry enquiry) {
        String notifyJobDesc = CharSequenceUtil.format("询价场次编号为：{}（{}） 的通知客户询价即将开始自动任务", enquiry.getEnquiryNo(), enquiry.getEnquiryName());
        String startJobDesc = CharSequenceUtil.format("询价场次编号为：{}（{}） 的自动开始询价任务", enquiry.getEnquiryNo(), enquiry.getEnquiryName());
        String endJobDesc = CharSequenceUtil.format("询价场次编号为：{}（{}） 的自动结束询价任务", enquiry.getEnquiryNo(), enquiry.getEnquiryName());
        log.info("EnquiryServiceImpl createXllJob enquiry:{}", enquiry);

        //通知客户自动任务
        CronPatternBuilder notifyCron = CronPatternBuilder.of();
        //询价开始前10分钟
        Date notifyTime = DateUtil.offsetMinute(enquiry.getEnquiryStartTime(),-10);
        notifyCron.set(Part.SECOND, String.valueOf(DateUtil.second(notifyTime)));
        notifyCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(notifyTime)));
        notifyCron.set(Part.HOUR, String.valueOf(DateUtil.hour(notifyTime,true)));
        notifyCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(notifyTime)));
        notifyCron.set(Part.MONTH, String.valueOf(DateUtil.month(notifyTime) + 1));
        notifyCron.set(Part.DAY_OF_WEEK,"?");
        Integer year = DateUtil.year(notifyTime);
        notifyCron.setRange(Part.YEAR, year,year);
        XxlJobDTO notifyJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                notifyJobDesc,
                notifyCron.build(),
                XxlJobConstant.NOTIFY_ENQUIRY_START_JOB,
                enquiry.getEnquiryNo());
        iXxlJobService.createXxlJob(notifyJob);
        log.info("EnquiryServiceImpl createXllJob enquiryId:{} notifyEnquiryStartJob is start", enquiry.getEnquiryNo());

        //询价开始户自动任务
        Date enquiryStartTime = enquiry.getEnquiryStartTime();
        CronPatternBuilder startCron = CronPatternBuilder.of();
        startCron.set(Part.SECOND,String.valueOf(DateUtil.second(enquiryStartTime)));
        startCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(enquiryStartTime)));
        startCron.set(Part.HOUR, String.valueOf(DateUtil.hour(enquiryStartTime,true)));
        startCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(enquiryStartTime)));
        startCron.set(Part.MONTH, String.valueOf(DateUtil.month(enquiryStartTime) + 1));
        startCron.set(Part.DAY_OF_WEEK,"?");
        Integer startYear = DateUtil.year(enquiryStartTime);
        startCron.setRange(Part.YEAR, startYear,startYear);
        XxlJobDTO startJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                startJobDesc,
                startCron.build(),
                XxlJobConstant.ENQUIRY_START_JOB,
                enquiry.getEnquiryNo());

        iXxlJobService.createXxlJob(startJob);
        log.info("EnquiryServiceImpl createXllJob enquiryId:{} enquiryStartJob is start", enquiry.getEnquiryNo());

        //竞价结束 自动任务
        Date enquiryEndTime = enquiry.getEnquiryEndTime();
        CronPatternBuilder endCron = CronPatternBuilder.of();
        endCron.set(Part.SECOND, String.valueOf(DateUtil.second(enquiryEndTime)));
        endCron.set(Part.MINUTE, String.valueOf(DateUtil.minute(enquiryEndTime)));
        endCron.set(Part.HOUR, String.valueOf(DateUtil.hour(enquiryEndTime,true)));
        endCron.set(Part.DAY_OF_MONTH, String.valueOf(DateUtil.dayOfMonth(enquiryEndTime)));
        endCron.set(Part.MONTH, String.valueOf(DateUtil.month(enquiryEndTime) + 1));
        endCron.set(Part.DAY_OF_WEEK,"?");
        Integer endYear = DateUtil.year(enquiryEndTime);
        endCron.setRange(Part.YEAR, endYear,endYear);
        XxlJobDTO endJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                endJobDesc,
                endCron.build(),
                XxlJobConstant.ENQUIRY_END_JOB,
                enquiry.getEnquiryNo());
        //竞价开始户定时任务
        iXxlJobService.createXxlJob(endJob);
        log.info("EnquiryServiceImpl createXllJob enquiryId:{} enquiryEndJob is start", enquiry.getEnquiryNo());
    }

    @Override
    public ExportExcelDTO exportEnquiryBuyerDetail(String enquiryNo) {
        String fileName = CharSequenceUtil.format("线上销售平台_询价结果列表_{}.xlsx", enquiryNo);
        String sheet1Name = "客户最新询价结果列表";
        String sheet2Name = "客户全部询价结果列表";
        ExportSheetDTO sheet1 = new ExportSheetDTO(sheet1Name, ExportEnquiryBuyerDetailDTO.class,Collections.emptyList());
        ExportSheetDTO sheet2 = new ExportSheetDTO(sheet2Name, ExportEnquiryBuyerDetailDTO.class,Collections.emptyList());

        Enquiry enquiry = enquiryBiz.getByNo(enquiryNo);

        Condition condition = enquiryBuyerDetailBiz.newCondition();
        condition.createCriteria().andEqualTo("enquiryNo", enquiryNo);
        List<EnquiryBuyerDetail> enquiryBuyerDetailList = enquiryBuyerDetailBiz.findByCondition(condition);
        List<EnquiryBuyerDetail> groupEnquiry = enquiryBuyerService.getEnquiryBuyerDetailsByEnquiryNo(enquiryNo, null);
        if(Objects.isNull(enquiry) || CollUtil.isEmpty(enquiryBuyerDetailList) || CollectionUtils.isEmpty(groupEnquiry)) {
            return new ExportExcelDTO(fileName, Arrays.asList(sheet2));
        }

        //包装方式运输方式字典
        List<ValueSetDTO> dictList = iValueSetService.getValueTreeByCodes(Arrays.asList(StringConstant.VS_DELIVERY_METHOD,StringConstant.VS_PACKAGE_METHOD));
        Map<String,ValueSetDTO> dictMap = CommonUtils.getMap(dictList,ValueSetDTO::getReferenceCode);
        //运输方式
        ValueSetDTO deliveryMethodSet = CommonUtils.getByKey(dictMap,StringConstant.VS_DELIVERY_METHOD);
        Map<String, ValueSetTreeDTO> ysfsMap = Objects.nonNull(deliveryMethodSet) ? CommonUtils.getMap(deliveryMethodSet.getOptions(),ValueSetTreeDTO::getOptionKey) : null;
        //包装方式
        ValueSetDTO packMethodSet = CommonUtils.getByKey(dictMap,StringConstant.VS_PACKAGE_METHOD);
        Map<String,ValueSetTreeDTO> bzfsMap = Objects.nonNull(packMethodSet) ? CommonUtils.getMap(packMethodSet.getOptions(),ValueSetTreeDTO::getOptionKey) : null;

        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(enquiryBuyerDetailList,EnquiryBuyerDetail::getMemberCode);

        //K: memberCode
        Map<String,MemberSimpleDTO> memberMap = CommonUtils.getMap(memberService.listSimpleMemberByCodes(memberCodes),MemberSimpleDTO::getMemberCode);

        //处理销售渠道数据
        QueryIntentionInfoDTO queryIntentionInfoDTO = new QueryIntentionInfoDTO();
        queryIntentionInfoDTO.setGoodsCodes(Arrays.asList(enquiry.getGoodsCode()));
        queryIntentionInfoDTO.setMemberCodes(memberCodes);
        //K:memberCode
        Map<String,MemberPurchaseGoodsIntentionDTO> intentionMap = CommonUtils.getMap(memberService.queryMemberBySaleInfo(queryIntentionInfoDTO),
                MemberPurchaseGoodsIntentionDTO::getMemberCode);

        Integer serialNum = 1;
        List<ExportEnquiryBuyerDetailDTO> exportBiddingBuyerDetailDTOS = new ArrayList<>();
        for (EnquiryBuyerDetail v : enquiryBuyerDetailList) {
            ExportEnquiryBuyerDetailDTO target = BeanUtil.toBean(v, ExportEnquiryBuyerDetailDTO.class);

            handleMember(v,target, memberMap);
            handleDict(v,target, ysfsMap , bzfsMap);
            handleSaleChannel(v,target, intentionMap);

            target.setSerialNum(serialNum);
            serialNum++;
            exportBiddingBuyerDetailDTOS.add(target);
        }


        Integer serialNum2 = 1;
        List<ExportEnquiryBuyerDetailDTO> exportBiddingBuyerDetailGroup = new ArrayList<>();

        for (EnquiryBuyerDetail v : groupEnquiry) {
            ExportEnquiryBuyerDetailDTO target = BeanUtil.toBean(v, ExportEnquiryBuyerDetailDTO.class);
            handleMember(v,target, memberMap);
            handleDict(v,target, ysfsMap, bzfsMap);
            handleSaleChannel(v,target, intentionMap);
            target.setSerialNum(serialNum2);

            serialNum2++;
            exportBiddingBuyerDetailGroup.add(target);
        }


        sheet1.setDataList(exportBiddingBuyerDetailGroup);
        sheet2.setDataList(exportBiddingBuyerDetailDTOS);

        return new ExportExcelDTO(fileName, Arrays.asList(sheet1, sheet2));
    }


    /**
     *处理企业数据
     */
    private static void handleMember(EnquiryBuyerDetail v,
                                     ExportEnquiryBuyerDetailDTO target,
                                     Map<String, MemberSimpleDTO> memberMap) {
        MemberSimpleDTO member = CommonUtils.getByKey(memberMap, v.getMemberCode());
        if (Objects.isNull(member))
            return;
        target.setCrmCode(member.getCrmCode());
    }


    /**
     *处理销售渠道
     */
    private static void handleSaleChannel(EnquiryBuyerDetail v,
                                          ExportEnquiryBuyerDetailDTO target,
                                          Map<String, MemberPurchaseGoodsIntentionDTO> intentionMap) {
        MemberPurchaseGoodsIntentionDTO intention = CommonUtils.getByKey(intentionMap, v.getMemberCode());
        if (Objects.isNull(intention))
            return;
        SalesChannelEnum salesChannelEnum = SalesChannelEnum.getByCode(intention.getSaleChannel());
        if (Objects.isNull(salesChannelEnum))
            return;
        target.setSaleChannel(salesChannelEnum.getDescription());
    }


    /**
     *处理字典数据
     */
    private static void handleDict(EnquiryBuyerDetail v,ExportEnquiryBuyerDetailDTO target,
                                   Map<String, ValueSetTreeDTO> ysfsMap,
                                   Map<String, ValueSetTreeDTO> bzfsMap) {
        ValueSetTreeDTO ysfsOption = CommonUtils.getByKey(ysfsMap, v.getDeliveryMode());
        if (Objects.nonNull(ysfsOption)) {
            target.setDeliveryModeName(ysfsOption.getOptionValue());
        }

        ValueSetTreeDTO bzfsOption = CommonUtils.getByKey(bzfsMap, v.getPack());
        if (Objects.nonNull(bzfsOption)) {
            target.setPackName(bzfsOption.getOptionValue());
        }
    }

    private String checkEnquiry(EnquiryDTO enquiryDTO) {
        if (StringUtils.isEmpty(enquiryDTO.getPayCondition())) {
            return "付款条件为空";
        }
        if (StringUtils.isEmpty(enquiryDTO.getPriceTradeTerm())) {
            return "价格贸易条款为空";
        }
        if (StringUtils.isEmpty(enquiryDTO.getTradeCurrency())) {
            return "交易币种为空";
        }
        if (Objects.isNull(enquiryDTO.getEnquiryStartTime())) {
            return "询价开始时间为空";
        }
        if (Objects.isNull(enquiryDTO.getEnquiryEndTime())) {
            return "询价结束时间为空";
        }
        if (enquiryDTO.getEnquiryStartTime().before(new Date())) {
            return "询价开始时间需要大于当前时间";
        }
        return null;
    }

    public ItemResult<PageInfo<EnquiryDTO>> queryEnquiryList(EnquiryDTO enquiryDTO) {
        AccountDTO accountDTO = accountService.findById(enquiryDTO.getOperator());
        MemberDetailDTO member = memberService.findMemberById(accountDTO.getMemberId());
        List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(accountDTO.getAccountId());
        List<String> roleCodes = roleDTOList.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList());

        if (Objects.isNull(member)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), QUERY_USER_ERROR);
        }
        List<String> goodsCodes = enquiryDTO.getGoodsCodes();
        //判断当前账号是否有管理员角色buyer_admin,没有走下面现有逻辑,有的则查询买家可参与的所有信息,只用member_code查询
        if (Objects.equals(member.getSellerFlg(), SellerFlgEnum.SELLER.getCode()) && CollectionUtils.isEmpty(enquiryDTO.getGoodsCodes())) {
            goodsCodes = getGoodsCode(enquiryDTO, accountDTO);
            if (CollectionUtils.isEmpty(goodsCodes)) {
                return new ItemResult<>(new PageInfo<>());
            }
        }else if (Objects.equals(member.getSellerFlg(), SellerFlgEnum.NOT_SELLER.getCode()) && CollectionUtils.isEmpty(enquiryDTO.getGoodsCodes())) {
            if (CollectionUtils.isNotEmpty(roleCodes) &&  !roleCodes.contains(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode())) {
                goodsCodes = getGoodsCode(enquiryDTO, accountDTO);
                if (CollectionUtils.isEmpty(goodsCodes)) {
                    return new ItemResult<>(new PageInfo<>());
                }
            }
            enquiryDTO.setMemberCode(member.getMemberCode());
        }

        PageInfo<Enquiry> enquiryPageList = getEnquiryList(enquiryDTO, goodsCodes, member);
        List<Enquiry> enquiryList = enquiryPageList.getList();

        List<EnquiryDTO> enquiryDTOS = BeanUtil.copyToList(enquiryList, EnquiryDTO.class);
        List<String> salesPlanNos = enquiryDTOS.stream().map(EnquiryDTO::getSalesPlanNo).collect(Collectors.toList());
        List<String> enquiryNos = enquiryDTOS.stream().map(EnquiryDTO::getEnquiryNo).collect(Collectors.toList());

        //补充销售计划id
        if (CollectionUtils.isNotEmpty(salesPlanNos)) {
            Condition condition = salesPlanBiz.newCondition();
            condition.createCriteria().andIn("planNo", salesPlanNos).andEqualTo("delFlg", false);
            List<SalesPlan> salesPlans = salesPlanBiz.findByCondition(condition);
            Map<String, List<SalesPlan>> saleMap = salesPlans.stream().collect(Collectors.groupingBy(SalesPlan::getPlanNo));
            enquiryDTOS.forEach(enquiryDTO1 -> {
                enquiryDTO1.setSalesPlanId(saleMap.get(enquiryDTO1.getSalesPlanNo()).get(0).getId());
                enquiryDTO1.setApplySellableQuantity(saleMap.get(enquiryDTO1.getSalesPlanNo()).get(0).getApplySellableQuantity());
            });
        }

        //补充是否参与字段
        setParticipationStatus(enquiryDTO, enquiryNos, enquiryDTOS, member);

        PageInfo<EnquiryDTO> enquiryDTOPageInfo = new PageInfo<>(enquiryDTOS);
        enquiryDTOPageInfo.setTotal(enquiryPageList.getTotal());
        return new ItemResult<>(enquiryDTOPageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelEnquiry(CancelEnquiryDTO param) {
        Enquiry enquiry = enquiryBiz.getByNo(param.getEnquiryNo());
        if(Objects.isNull(enquiry))
            throw new BizException(OrderErrorCode.CUSTOM,"询价不存在");
        String nowStatus = enquiry.getStatus();
        List<String> allowStatusList = Arrays.asList(EnquiryStatusEnum.TO_START.getCode(),EnquiryStatusEnum.ENQUIRING.getCode());
        if(!CollUtil.contains(allowStatusList,enquiry.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM,"不支持非询价待开始/询价中状态进行取消");

        //计算最大最小报价及总量
        EnquiryBuyerDetailDTO priceInfo = enquiryBuyerDetailMapper.selectMaxAndLowPrice(enquiry.getEnquiryNo());
        if(Objects.nonNull(priceInfo)){
            enquiry.setMaxPrice(priceInfo.getMaxPrice());
            enquiry.setMinPrice(priceInfo.getMinPrice());
            enquiry.setTotalQuantity(priceInfo.getTotalQuantity());
        }
        enquiry.setStatus(EnquiryStatusEnum.ENQUIRY_CANCEL.getCode());
        enquiry.setCancelUser(param.getOperatorBy());
        enquiry.setCancelReason(param.getCancelReason());
        enquiry.setCancelUserName(param.getOperatorName());
        enquiry.setCancelTime(ObjectUtil.defaultIfNull(param.getOperationTime(),new Date()));

        if(enquiryMapper.updateByPrimaryKeySelective(enquiry) <= 0)
            throw new BizException(OrderErrorCode.CUSTOM,"取消询价失败");

        //重置销售计划 是否已创建询价标记
        salesPlanBiz.updateCreatedInquiry(enquiry.getSalesPlanNo(), OrderNumberConstant.ZERO);

        //停止询价定时任务执行 包括通知 开始 结束任务
        iXxlJobService.stopXxlJob(new StopJobDTO(xxlJobConfig.getAppName(),Arrays.asList(XxlJobConstant.NOTIFY_ENQUIRY_START_JOB,
                XxlJobConstant.ENQUIRY_START_JOB,XxlJobConstant.ENQUIRY_END_JOB),enquiry.getEnquiryNo()));

        //发送消息通知 询价取消
        this.notifyByCancelEnquiry(enquiry, nowStatus, param.getOperatorBy());

        return Boolean.TRUE;
    }

    @Override
    public void notifyBiddingDelay(NotifyBiddingDelayDTO param) {

        Enquiry enquiry = enquiryBiz.getByNo(param.getEnquiryNo());
        if (Objects.isNull(enquiry))
            throw new BizException(OrderErrorCode.CUSTOM, "询价不存在");
        //是否推送过消息
        if (BooleanUtil.isTrue(enquiry.getCancelBiddingNotifyFlag()))
            throw new BizException(OrderErrorCode.CUSTOM, "您已推送过此取消通知，无需重复推送。");

        if (!CharSequenceUtil.equals(EnquiryStatusEnum.END.getCode(), enquiry.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "不支持非询价已结束状态进行消息推送");
        //判断询价是否有有效竞价数据
        if (BooleanUtil.isTrue(iBiddingBiz.existByEnquiryNo(enquiry.getEnquiryNo())))
            throw new BizException(OrderErrorCode.CUSTOM, "存在竞价场次不可推送消息");

        //通知客户管理员以及客户产品相关人员
        this.notifyBiddingDelay(enquiry, param.getOperatorBy());
        //更新推送消息标记
        enquiryMapper.updateCancelBiddingNotifyFlag(enquiry.getEnquiryNo(), Boolean.TRUE);
    }

    private void setParticipationStatus(EnquiryDTO enquiryDTO, List<String> enquiryNos, List<EnquiryDTO> enquiryDTOS, MemberDetailDTO member) {
        if (CollectionUtils.isNotEmpty(enquiryNos)) {
            if (CollectionUtils.isNotEmpty(enquiryDTO.getParticipationStatusArray()) && enquiryDTO.getParticipationStatusArray().size() == 1 && enquiryDTO.getParticipationStatusArray().get(0).equals("TODO")) {
                enquiryDTOS.forEach(enquiryDTO1 -> enquiryDTO1.setParticipationStatus("TODO"));
            }
            if (CollectionUtils.isNotEmpty(enquiryDTO.getParticipationStatusArray()) && enquiryDTO.getParticipationStatusArray().size() == 1 && enquiryDTO.getParticipationStatusArray().get(0).equals("DONE")) {
                enquiryDTOS.forEach(enquiryDTO1 -> enquiryDTO1.setParticipationStatus("DONE"));
            }
            if (CollectionUtils.isEmpty(enquiryDTO.getParticipationStatusArray()) || (CollectionUtils.isNotEmpty(enquiryDTO.getParticipationStatusArray()) && enquiryDTO.getParticipationStatusArray().size() == 2)) {
                List<EnquiryBuyerDTO> enquiryBuyerDTOS = enquiryBuyerMapper.selectByEnquiryMemberCode(enquiryNos, member.getMemberCode());
                Map<String, List<EnquiryBuyerDTO>> enquiryBuyerMap = enquiryBuyerDTOS.stream().collect(Collectors.groupingBy(EnquiryBuyerDTO::getEnquiryNo));
                enquiryDTOS.forEach(enquiryDTO1 ->  {
                    if (CollectionUtils.isEmpty(enquiryBuyerMap.get(enquiryDTO1.getEnquiryNo()))) {
                        enquiryDTO1.setParticipationStatus("TODO");
                        return;
                    }
                    enquiryDTO1.setParticipationStatus(enquiryBuyerMap.get(enquiryDTO1.getEnquiryNo()).get(0).getParticipationStatus());
                });
            }
        }
    }

    public PageInfo<Enquiry> getEnquiryList(EnquiryDTO enquiryDTO, List<String> goodsCodes, MemberDetailDTO member) {
        if (CollectionUtils.isEmpty(enquiryDTO.getStatusArrayList()) && Objects.equals(member.getSellerFlg(), SellerFlgEnum.NOT_SELLER.getCode())) {
            enquiryDTO.setStatusArrayList(Arrays.asList(EnquiryStatusEnum.TO_START.getCode(),
                    EnquiryStatusEnum.ENQUIRING.getCode()
                    , EnquiryStatusEnum.END.getCode()
                    , EnquiryStatusEnum.CANCELLED.getCode(),
                    EnquiryStatusEnum.ENQUIRY_CANCEL.getCode()
            ));
        }
        enquiryDTO.setGoodsCodes(goodsCodes);

        List<Enquiry> enquiryList = new ArrayList<>();
        List<String> participationStatusArray = enquiryDTO.getParticipationStatusArray();
        if (CollectionUtils.isEmpty(enquiryDTO.getParticipationStatusArray())) {
            PageMethod.startPage(enquiryDTO.getPageNum(), enquiryDTO.getPageSize());
            enquiryList = enquiryMapper.selectList(enquiryDTO);
        }
        if (CollectionUtils.isNotEmpty(participationStatusArray) && participationStatusArray.size() == 1 && participationStatusArray.get(0).equals("DONE")) {
            Condition detailCondition = enquiryBuyerDetailBiz.newCondition();
            detailCondition.createCriteria().andEqualTo("memberCode", member.getMemberCode()).andEqualTo("delFlg", false);
            List<EnquiryBuyerDetail> enquiryBuyerDetailList = enquiryBuyerDetailBiz.findByCondition(detailCondition);
            List<String> enquiryNos = enquiryBuyerDetailList.stream().map(EnquiryBuyerDetail::getEnquiryNo).collect(Collectors.toList());
            enquiryDTO.setEnquiryNos(enquiryNos);
            PageMethod.startPage(enquiryDTO.getPageNum(), enquiryDTO.getPageSize());
            enquiryList = enquiryMapper.selectList(enquiryDTO);
        }
        if (CollectionUtils.isNotEmpty(participationStatusArray) && participationStatusArray.size() == 1 && participationStatusArray.get(0).equals("TODO")) {
            List<EnquiryBuyer> enquiryBuyerList = enquiryBuyerMapper.selectTODOList(member.getMemberCode());
            List<String> enquiryNos = enquiryBuyerList.stream().map(EnquiryBuyer::getEnquiryNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(enquiryNos)) {
                enquiryDTO.setEnquiryNos(enquiryNos);
                PageMethod.startPage(enquiryDTO.getPageNum(), enquiryDTO.getPageSize());
                enquiryList = enquiryMapper.selectList(enquiryDTO);
            }
        }
        return new PageInfo<>(enquiryList);
    }


    public ItemResult<PageInfo<EnquiryDTO>> buyerQueryEnquiryList(EnquiryDTO enquiryDTO) {
        MemberDetailDTO member = memberService.findMemberById(enquiryDTO.getOperator());
        if (Objects.isNull(member)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), QUERY_USER_ERROR);
        }
        PageMethod.startPage(enquiryDTO.getPageNum(), enquiryDTO.getPageSize());
        List<Enquiry> enquiryList = enquiryMapper.buyerSelectList(enquiryDTO);
        return new ItemResult<>(new PageInfo<>(BeanUtil.copyToList(enquiryList, EnquiryDTO.class)));
    }

    public ItemResult<EnquiryDTO> getEnquiryDetail(EnquiryDTO enquiryDTO) {
        if (Objects.isNull(enquiryDTO) || StringUtils.isEmpty(enquiryDTO.getId())) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "查询详情参数为空！");
        }
        Enquiry enquiry = enquiryBiz.get(enquiryDTO.getId());
        if (Objects.isNull(enquiry)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), "询价场次不存在！");
        }

        log.info("EnquiryServiceImpl getEnquiryDetail enquiry:{}", enquiry);
        Condition salesCondition = salesPlanBiz.newCondition();
        salesCondition.createCriteria().andEqualTo("planNo", enquiry.getSalesPlanNo());
        List<SalesPlan> salesPlans = salesPlanBiz.findByCondition(salesCondition);
        if (CollectionUtils.isEmpty(salesPlans)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), "销售计划不存在！");
        }
        log.info("EnquiryServiceImpl getEnquiryDetail salesPlan:{}", salesPlans.get(0));
        EnquiryDTO result = EnquiryDTO.builder().build();
        BeanUtil.copyProperties(enquiry, result);
        SalesPlanInfoDTO salesPlanInfoDTO = SalesPlanInfoDTO.builder().build();
        BeanUtil.copyProperties(salesPlans.get(0), salesPlanInfoDTO);
        result.setSalesPlanInfoDTO(salesPlanInfoDTO);

        List<EnquiryBuyerDetail> enquiryBuyerDetailList = enquiryBuyerService.getEnquiryBuyerDetailsByEnquiryNo(enquiry.getEnquiryNo(), null);

        log.info("EnquiryServiceImpl getEnquiryDetail enquiryBuyerDetailList:{}", enquiryBuyerDetailList);
        if (CollectionUtils.isNotEmpty(enquiryBuyerDetailList)) {
            List<EnquiryBuyerDetailDTO> enquiryBuyerDetailDTOS = BeanUtil.copyToList(enquiryBuyerDetailList, EnquiryBuyerDetailDTO.class);
            List<String> memberCodes = enquiryBuyerDetailList.stream()
                    .map(v->v.getMemberCode())
                    .distinct()
                    .collect(Collectors.toList());
            //查询会员信息 K:memberCode
            Map<String, MemberSimpleDTO> memberMap = CommonUtils.getMap(memberService.listSimpleMemberByCodes(memberCodes), MemberSimpleDTO::getMemberCode);
            //处理销售渠道数据
            QueryIntentionInfoDTO queryIntentionInfoDTO = new QueryIntentionInfoDTO();
            queryIntentionInfoDTO.setGoodsCodes(Arrays.asList(enquiry.getGoodsCode()));
            queryIntentionInfoDTO.setMemberCodes(memberCodes);
            List<MemberPurchaseGoodsIntentionDTO> intentions = memberService.queryMemberBySaleInfo(queryIntentionInfoDTO);
            //K:memberCode_goodsCode
            Map<String, MemberPurchaseGoodsIntentionDTO> intentionMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(intentions)) {
                intentionMap = intentions.stream()
                        .collect(Collectors.toMap(v -> v.getMemberCode() + "_" + v.getGoodsCode(), Function.identity(), (v1, v2) -> v1));
            }
            for (EnquiryBuyerDetailDTO v : enquiryBuyerDetailDTOS) {
                MemberSimpleDTO member = CommonUtils.getByKey(memberMap, v.getMemberCode());
                String key = v.getMemberCode() + "_" + enquiry.getGoodsCode();
                if (Objects.nonNull(member))
                    v.setCrmCode(member.getCrmCode());
                MemberPurchaseGoodsIntentionDTO intention = CommonUtils.getByKey(intentionMap,key);
                if(Objects.nonNull(intention))
                    v.setSaleChannel(intention.getSaleChannel());
            }
            result.setEnquiryBuyerDetailDTOS(enquiryBuyerDetailDTOS);
        }

        //询价分析图
        result.setEnquiryAnalysis(this.enquiryAnalysis(enquiryDTO.getTerminal(),enquiry.getEnquiryNo(),CollUtil.getFirst(salesPlans)));

        //处理图标数据
        EnquiryDataDetailDTO enquiryDataDetail = getEnquiryDataDetail(enquiry, enquiryBuyerDetailList);
        result.setEnquiryDataDetailDTO(enquiryDataDetail);
        //剩余量
        result.setCheckEnquirySurplus(new BigDecimal(salesPlanInfoDTO.getRemainSellAbleQuantity()).compareTo(new BigDecimal(0)) > 0);
        //是否存在竞价
        result.setExistBidding(iBiddingBiz.existByEnquiryNo(enquiry.getEnquiryNo()));

        ItemResult<GoodsDTO> goodsInfoByGoodsCode = goodsService.getGoodsInfoByGoodsCode(enquiry.getGoodsCode());
        if (Objects.nonNull(goodsInfoByGoodsCode) && Objects.nonNull(goodsInfoByGoodsCode.getData())) {
            GoodsDTO goodsDTO = goodsInfoByGoodsCode.getData();
            result.setDeliveryCostStandard(goodsDTO.getDeliverCostStandard());
            result.setSelfPickupCarrier(goodsDTO.getSelfPickupCarrier());
            result.setSelfPickupGuide(goodsDTO.getSelfPickupGuide());
        }

        return new ItemResult<>(result);
    }

    public ItemResult<EnquiryDTO> updateEnquiry(EnquiryDTO enquiryDTO) {
        if (Objects.isNull(enquiryDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "查询详情参数为空！");
        }
        AccountDTO accountDTO = accountService.findById(enquiryDTO.getOperator());
        if (Objects.isNull(accountDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "操作人不存在！");
        }
        List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(accountDTO.getAccountId());
        List<String> roleCodes = roleDTOList.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList());
        Enquiry enquiry = enquiryBiz.get(enquiryDTO.getId());
        if (Objects.isNull(enquiry)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "询价不存在！");
        }
        if (StringUtils.isNotEmpty(enquiry.getPayCondition())) {
            enquiry.setPayCondition(enquiryDTO.getPayCondition());
        }
        if (StringUtils.isNotEmpty(enquiry.getPriceTradeTerm())) {
            enquiry.setPriceTradeTerm(enquiryDTO.getPriceTradeTerm());
        }
        if (StringUtils.isNotEmpty(enquiry.getTradeCurrency())) {
            enquiry.setTradeCurrency(enquiryDTO.getTradeCurrency());
        }
        if (Objects.nonNull(enquiry.getEnquiryStartTime())) {
            enquiry.setEnquiryStartTime(enquiryDTO.getEnquiryStartTime());
        }
        if (Objects.nonNull(enquiry.getEnquiryEndTime())) {
            enquiry.setEnquiryEndTime(enquiryDTO.getEnquiryEndTime());
        }
        enquiry.setStatus(enquiryDTO.getStatus());
        enquiry.setUpdateUser(enquiryDTO.getOperator());
        enquiry.setUpdateUserName(accountDTO.getRealName());
        enquiry.setUpdateTime(new Date());
        enquiryMapper.updateByPrimaryKey(enquiry);
        try {
            if (enquiryDTO.getStatus().equals(EnquiryStatusEnum.TO_START.getCode())) {
                new Thread(() -> createXllJob(enquiry)).start();
            }
        }catch (Exception e) {
            log.error("updateEnquiry createXllJob is error:", e);
        }
        return new ItemResult<>(null);
    }

    public ItemResult<List<GoodsSimpleDTO>> getEnquiryGoods(String accountId) {
        AccountDTO accountDTO = accountService.findById(accountId);
        MemberDetailDTO member = memberService.findMemberById(accountDTO.getMemberId());
        if (Objects.isNull(member)) {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getCode(), QUERY_USER_ERROR);
        }
        //如果是买家
        String memberCode = "";
        if (Objects.equals(member.getSellerFlg(), SellerFlgEnum.NOT_SELLER.getCode())) {
            AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
            accountRoleDTO.setAccountId(accountDTO.getAccountId());
            List<DataPermissionDTO> goodsByCategoryAccountId = roleService.getDataPermissionList(accountRoleDTO);
            log.info("EnquiryServiceImpl getEnquiryGoods accountId:{} goodsByCategoryAccountId {}", accountDTO.getAccountId(), goodsByCategoryAccountId.size());
            if (CollectionUtils.isEmpty(goodsByCategoryAccountId)) {
                return new ItemResult<>(Collections.emptyList());
            }
            return new ItemResult<>(BeanUtil.copyToList(goodsByCategoryAccountId, GoodsSimpleDTO.class));
        }
        List<GoodsSimpleDTO> goodsSimpleDTOS = enquiryMapper.selectGoodsSimpleList(memberCode);
        return new ItemResult<>(goodsSimpleDTOS);
    }

    public String getEnquiryNum() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String day = currentDate.format(formatter);
        Long num = simpleDailyCounter.getNextValue();
        return "E"+day+String.format("%03d", num);
    }

    private EnquiryDataDetailDTO getEnquiryDataDetail(Enquiry enquiry, List<EnquiryBuyerDetail> enquiryBuyerDetailList) {
        List<String> allowStatus = Arrays.asList(EnquiryStatusEnum.END.getCode(),EnquiryStatusEnum.ENQUIRY_CANCEL.getCode());
        if (!CollUtil.contains(allowStatus, enquiry.getStatus()) || CollectionUtils.isEmpty(enquiryBuyerDetailList)) {
            log.info("getEnquiryDataDetail return");
            return new EnquiryDataDetailDTO();
        }
        if (Objects.isNull(enquiry.getMaxPrice()) || Objects.isNull(enquiry.getMinPrice())) {
            log.info("getEnquiryDataDetail return");
            return new EnquiryDataDetailDTO();
        }
        BigDecimal maxPrice = enquiry.getMaxPrice();
        BigDecimal minPrice = enquiry.getMinPrice();
        BigDecimal pjs = maxPrice.add(minPrice).divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);
        BigDecimal medianOne = minPrice.add(pjs).divide(new BigDecimal(2),2, RoundingMode.HALF_UP);
        BigDecimal medianTwo = pjs.add(maxPrice).divide(new BigDecimal(2),2, RoundingMode.HALF_UP);
        List<BigDecimal> qty = enquiryBuyerDetailList.stream().map(EnquiryBuyerDetail::getQuantity).collect(Collectors.toList());
        BigDecimal totalQty = BigDecimal.ZERO;
        for (BigDecimal qty1 : qty) {
            totalQty = totalQty.add(qty1);
        }

        Long distributionOne = enquiryBuyerDetailList.stream()
                .filter(v -> Objects.nonNull(v.getPrice()))
                .filter(v -> v.getPrice().compareTo(medianOne) <= 0)
                .count();
        Long distributionTwoLeft = enquiryBuyerDetailList.stream()
                .filter(v -> Objects.nonNull(v.getPrice()))
                .filter(v -> v.getPrice().compareTo(medianOne) > 0
                        && v.getPrice().compareTo(pjs) <= 0)
                .count();
        Long distributionTwoRight = enquiryBuyerDetailList.stream()
                .filter(v -> Objects.nonNull(v.getPrice()))
                .filter(v -> v.getPrice().compareTo(pjs) > 0
                        && v.getPrice().compareTo(medianTwo) <= 0)
                .count();
        Long distributionThree = enquiryBuyerDetailList.stream()
                .filter(v -> Objects.nonNull(v.getPrice()))
                .filter(v -> v.getPrice().compareTo(medianTwo) > 0)
                .count();

        return EnquiryDataDetailDTO.builder()
                .medianOne(medianOne)
                .medianTwo(medianTwo)
                .average(pjs)
                .maxPrice(enquiry.getMaxPrice())
                .minPrice(enquiry.getMinPrice())
                .distributionOne(distributionOne)
                .distributionTwoLeft(distributionTwoLeft)
                .distributionTwoRight(distributionTwoRight)
                .distributionThree(distributionThree)
                .totalQty(totalQty)
                .build();
    }

    public ItemResult<EnquiryBiddingTimesDTO> getEnquiryBiddingTimes(String accountId) {
        AccountDTO accountDTO = accountService.findById(accountId);

        List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(accountDTO.getAccountId());
        List<String> roleCodes = roleDTOList.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList());
        List<String> goodsCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleCodes) && !roleCodes.contains(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode())) {
            AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
            accountRoleDTO.setAccountId(accountDTO.getAccountId());
            List<DataPermissionDTO> goodsByCategoryAccountId = roleService.getDataPermissionList(accountRoleDTO);
            goodsCodes = goodsByCategoryAccountId.stream().map(DataPermissionDTO::getGoodsCode).collect(Collectors.toList());
        }

        Integer enquiryTimes = enquiryMapper.selectEnquiryTimesByMemberCode(accountDTO.getMemberCode(), goodsCodes);
        Integer biddingTimes = biddingMapper.selectBiddingTimesByMemberCode(accountDTO.getMemberCode(), goodsCodes);
        EnquiryBiddingTimesDTO enquiryBiddingTimesDTO = EnquiryBiddingTimesDTO.builder()
                .enquiryTimes(Optional.ofNullable(enquiryTimes).orElse(0))
                .biddingTimes(Optional.ofNullable(biddingTimes).orElse(0))
                .build();
        log.info("getEnquiryBiddingTimes enquiryBiddingTimesDTO:{}", enquiryBiddingTimesDTO);
        return new ItemResult<>(enquiryBiddingTimesDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDraft(RemoveEnquiryDTO param) {
        Enquiry enquiry = enquiryBiz.getByNo(param.getEnquiryNo());
        if (Objects.isNull(enquiry))
            throw new BizException(OrderErrorCode.CUSTOM, "询价不存在");
        if (!CharSequenceUtil.equals(enquiry.getStatus(), EnquiryStatusEnum.DRAFT.getCode()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不允许删除");

        //重置销售计划 是否已创建询价标记
        salesPlanBiz.updateCreatedInquiry(enquiry.getSalesPlanNo(), OrderNumberConstant.ZERO);

        enquiryMapper.removeEnquiryById(enquiry.getId());
    }

    @Override
    public List<EnquiryAnalysisResultDTO> enquiryAnalysis(String terminal,String enquiryNo, SalesPlan salesPlan) {
        if (Objects.isNull(salesPlan)) {
            Enquiry enquiry = enquiryBiz.getByNo(enquiryNo);
            if (Objects.isNull(enquiry))
                throw new BizException(OrderErrorCode.CUSTOM, "询价不存在");
            salesPlan = salesPlanBiz.getByNo(enquiry.getSalesPlanNo());
        }
        List<EnquiryAnalysisInfoDTO> enquiryInfo = enquiryMapper.queryEnquiryInfo(enquiryNo);
        if (CollUtil.isEmpty(enquiryInfo))
            return Collections.emptyList();

        QueryMemberIntentionInfoDTO param = new QueryMemberIntentionInfoDTO();
        param.setMemberCodes(CommonUtils.getListValueByDistinctAndFilterBank(enquiryInfo, EnquiryAnalysisInfoDTO::getMemberCode));
        //产品只有一个
        param.setGoodsCodes(CommonUtils.getListValueByDistinctAndFilterBank(enquiryInfo, EnquiryAnalysisInfoDTO::getGoodsCode));
        List<MemberIntentionInfoDTO> intentions = memberService.queryMemberIntentionInfo(param);
        //K:saleChannel
        Map<String, List<MemberIntentionInfoDTO>> intentionGroup = CommonUtils.allowNullGroupByKey(intentions,
                MemberIntentionInfoDTO::getSaleChannel);
        //销售渠道字典
        Map<String, ValueSetTreeDTO> saleChannelDict = iBaseBizService.getDict(StringConstant.VS_SALES_CHANNEL);

        List<EnquiryAnalysisResultDTO> result = new ArrayList<>();
        //排序 1 SALE_PLAN：销售计划 2 VO_SALES_CHANNEL_EU:终端客户 3 VO_SALES_CHANNEL_RS:贸易商 4 VO_SALES_CHANNEL_DS:分销商 5 OTHER:其它
        if (Objects.nonNull(salesPlan)) {
            EnquiryAnalysisResultDTO salePlanAnalysis = new EnquiryAnalysisResultDTO();
            salePlanAnalysis.setType("SALE_PLAN");
            String salePlanTypeName = "申请可售量：{}吨\n提货期：{}-{}";

            salePlanAnalysis.setTypeName(CharSequenceUtil.format(salePlanTypeName,
                    salesPlan.getApplySellableQuantity(),
                    DateUtil.format(salesPlan.getDeliveryEffectStartDate(), StringConstant.DATE_FORMAT),
                    DateUtil.format(salesPlan.getDeliveryEffectEndDate(),StringConstant.DATE_FORMAT)));
            salePlanAnalysis.setQuantity(new BigDecimal(salesPlan.getApplySellableQuantity()));
            result.add(salePlanAnalysis);
        }
        //终端
        EnquiryAnalysisResultDTO euAnalysis = new EnquiryAnalysisResultDTO();
        euAnalysis.setType(StringConstant.VO_SALES_CHANNEL_EU);
        ValueSetTreeDTO euDictValue = CommonUtils.getByKey(saleChannelDict, StringConstant.VO_SALES_CHANNEL_EU);
        if (Objects.nonNull(euDictValue))
            euAnalysis.setTypeName(euDictValue.getOptionValue());
        List<MemberIntentionInfoDTO> euIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_EU);
        euAnalysis.setCount(CollUtil.size(euIntentions));
        euAnalysis.setQuantity(getTotalQuantity(enquiryInfo, euIntentions));
        result.add(euAnalysis);

        //贸易商
        EnquiryAnalysisResultDTO rsAnalysis = new EnquiryAnalysisResultDTO();
        rsAnalysis.setType(StringConstant.VO_SALES_CHANNEL_RS);
        ValueSetTreeDTO rsDictValue = CommonUtils.getByKey(saleChannelDict, StringConstant.VO_SALES_CHANNEL_RS);
        if (Objects.nonNull(rsDictValue))
            rsAnalysis.setTypeName(rsDictValue.getOptionValue());
        List<MemberIntentionInfoDTO> rsIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_RS);
        rsAnalysis.setCount(CollUtil.size(rsIntentions));
        rsAnalysis.setQuantity(getTotalQuantity(enquiryInfo, rsIntentions));
        result.add(rsAnalysis);

        //分销商
        EnquiryAnalysisResultDTO dsAnalysis = new EnquiryAnalysisResultDTO();
        dsAnalysis.setType(StringConstant.VO_SALES_CHANNEL_DS);
        ValueSetTreeDTO dsDictValue = CommonUtils.getByKey(saleChannelDict, StringConstant.VO_SALES_CHANNEL_DS);
        if (Objects.nonNull(dsDictValue))
            dsAnalysis.setTypeName(dsDictValue.getOptionValue());
        List<MemberIntentionInfoDTO> dsIntentions = CommonUtils.getByKey(intentionGroup, StringConstant.VO_SALES_CHANNEL_DS);
        dsAnalysis.setCount(CollUtil.size(dsIntentions));
        dsAnalysis.setQuantity(getTotalQuantity(enquiryInfo, dsIntentions));
        result.add(dsAnalysis);

        //其它
        EnquiryAnalysisResultDTO otherAnalysis = new EnquiryAnalysisResultDTO();
        otherAnalysis.setType("OTHER");
        otherAnalysis.setTypeName("其他");
        List<MemberIntentionInfoDTO> otherIntentions = CommonUtils.allowNullGetByKey(intentionGroup, null);
        otherAnalysis.setCount(CollUtil.size(otherIntentions));
        otherAnalysis.setQuantity(getTotalQuantity(enquiryInfo, otherIntentions));
        result.add(otherAnalysis);

        return result;
    }

    public List<String> getGoodsCode(EnquiryDTO enquiryDTO, AccountDTO accountDTO) {
        List<String> goodsCodes = enquiryDTO.getGoodsCodes();
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(accountDTO.getAccountId());
        List<DataPermissionDTO> goodsByCategoryAccountId = roleService.getDataPermissionList(accountRoleDTO);
        log.info("EnquiryServiceImpl queryEnquiryList accountId:{} goodsByCategoryAccountId {}", accountDTO.getAccountId(), goodsByCategoryAccountId.size());
        goodsCodes = goodsByCategoryAccountId.stream().map(DataPermissionDTO::getGoodsCode).collect(Collectors.toList());
        log.info("EnquiryServiceImpl queryEnquiryList accountId:{} goodsCodes {}", accountDTO.getAccountId(), goodsCodes);
        return goodsCodes;
    }

    /**
     * 询价场次为待开始、询价中状态时，销售经理提交取消询价场次时
     * 若是询价中：已参与询价的客户的企业管理员+负责该产品的员工
     * 若是待开始：则是所有可参与询价的客户的企业管理员+负责该产品的员工
     * 站内信+短信
     *
     * @param enquiry 询价信息
     * @param enquiryStatus 询价取消时的状态
     * @param operatorBy 操作人
     *
     */
    private void notifyByCancelEnquiry(Enquiry enquiry,String enquiryStatus,String operatorBy) {
        if (Objects.isNull(enquiry))
            return;
        List<String> memberCodes = null;
        //待开始
        if(EnquiryStatusEnum.TO_START.getCode().equals(enquiryStatus)){
            memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(enquiryBuyerBiz.listByNo(enquiry),EnquiryBuyer::getMemberCode);
        }
        //询价中
        if(EnquiryStatusEnum.ENQUIRING.getCode().equals(enquiryStatus)){
            memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(enquiryBuyerDetailBiz.listByNo(enquiry), EnquiryBuyerDetail::getMemberCode);
        }
        if (CollUtil.isEmpty(memberCodes)) {
            log.info("notifyByCancelEnquiry 取消询价通知 会员编码为空");
            return;
        }
        //企业管理员
        Map<String, List<AccountSimpleDTO>> adminGroup = CommonUtils.group(iBiddingAccountService.queryMemberAccountByRoleCodes(memberCodes,
                Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()), null), AccountSimpleDTO::getMemberCode);

        //企业产品人员
        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> memberGoodsAccounts = iBiddingAccountService
                .queryAccountDataPermission(memberCodes, Arrays.asList(enquiry.getGoodsCode()));

        for (String memberCode : memberCodes) {
            List<AccountSimpleDTO> adminAccounts = CommonUtils.getByKey(adminGroup, memberCode);
            List<AccountSimpleDTO> goodsAccounts = CommonUtils.getByKey(memberGoodsAccounts, new QueryAccountDataPermissionDTO(memberCode, enquiry.getGoodsCode()));
            //去重
            List<AccountSimpleDTO> distinctAccounts = CommonUtils.unionDistinct(adminAccounts,goodsAccounts, AccountSimpleDTO::getAccountId);
            if(CollUtil.isEmpty(distinctAccounts))
                continue;
            String memberName = CollUtil.getFirst(distinctAccounts).getMemberName();
            List<String> accountIds = CommonUtils.getListValueByDistinctAndFilterBank(distinctAccounts, AccountSimpleDTO::getAccountId);
            List<String> mobiles = CommonUtils.getListValueByDistinctAndFilterBank(distinctAccounts, AccountSimpleDTO::getMobile);

            //发送站内信
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(accountIds);
            siteMsg.setOperator(operatorBy);
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_ENQUIRY.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_ENQUIRY.getMsg(memberName, enquiry.getEnquiryNo(), enquiry.getGoodsName()));
            iMessageSendService.sendMessage(siteMsg);

            //发送短信
            SmsDTO smsDTO = new SmsDTO(SmsTemplateEnum.NOTIFY_CANCEL_ENQUIRY.getCode(), mobiles, Arrays.asList(enquiry.getEnquiryNo(), enquiry.getGoodsName()));
            iSmsSendService.sendSms(smsDTO);
        }
    }


    /**
     * 竞价场次推迟通知
     * 询价已正常结束，第二天的竞价无法正常开始，销售经理手动通过询价触发通知
     * 站内信+短信
     */
    private void notifyBiddingDelay(Enquiry enquiry, String operatorBy) {
        if (Objects.isNull(enquiry))
            return;
        List<EnquiryBuyerDetail> buyerDetails = enquiryBuyerDetailBiz.listByNo(enquiry);
        if (CollUtil.isEmpty(buyerDetails))
            return;
        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(buyerDetails, EnquiryBuyerDetail::getMemberCode);
        if (CollUtil.isEmpty(memberCodes))
            return;
        //企业管理员
        Map<String, List<AccountSimpleDTO>> adminGroup = CommonUtils.group(iBiddingAccountService.queryMemberAccountByRoleCodes(memberCodes,
                Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()), null), AccountSimpleDTO::getMemberCode);

        //企业产品人员
        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> memberGoodsAccounts = iBiddingAccountService
                .queryAccountDataPermission(memberCodes, Arrays.asList(enquiry.getGoodsCode()));

        for (String memberCode : memberCodes) {
            List<AccountSimpleDTO> adminAccounts = CommonUtils.getByKey(adminGroup, memberCode);
            List<AccountSimpleDTO> goodsAccounts = CommonUtils.getByKey(memberGoodsAccounts, new QueryAccountDataPermissionDTO(memberCode, enquiry.getGoodsCode()));
            //去重
            List<AccountSimpleDTO> distinctAccounts = CommonUtils.unionDistinct(adminAccounts,goodsAccounts, AccountSimpleDTO::getAccountId);
            if(CollUtil.isEmpty(distinctAccounts))
                continue;

            String memberName = CollUtil.getFirst(distinctAccounts).getMemberName();
            List<String> accountIds = CommonUtils.getListValueByDistinctAndFilterBank(distinctAccounts, AccountSimpleDTO::getAccountId);
            List<String> mobiles = CommonUtils.getListValueByDistinctAndFilterBank(distinctAccounts, AccountSimpleDTO::getMobile);

            //发送站内信
            MessageDTO siteMsg = new MessageDTO();
            siteMsg.setReceiveAccountIds(accountIds);
            siteMsg.setOperator(operatorBy);
            siteMsg.setTitle(BiddingMessageTemplateEnum.NOTIFY_BIDDING_DELAY.getTitle());
            siteMsg.setContent(BiddingMessageTemplateEnum.NOTIFY_BIDDING_DELAY.getMsg(memberName,enquiry.getGoodsName()));
            iMessageSendService.sendMessage(siteMsg);

            //发送短信
            SmsDTO smsDTO = new SmsDTO(SmsTemplateEnum.NOTIFY_BIDDING_DELAY.getCode(), mobiles, Arrays.asList(enquiry.getGoodsName()));
            iSmsSendService.sendSms(smsDTO);
        }
    }


    /**
     * 获取同一销售渠道下企业询价报量总量
     */
    private static BigDecimal getTotalQuantity(List<EnquiryAnalysisInfoDTO> enquiryList,
                                               List<MemberIntentionInfoDTO> intentions) {
        if (CollUtil.isEmpty(enquiryList) || CollUtil.isEmpty(intentions))
            return null;
        return enquiryList.stream()
                .filter(enquiry -> intentions.stream()
                        .anyMatch(intention ->
                                CharSequenceUtil.equals(enquiry.getMemberCode(), intention.getMemberCode())))
                .map(EnquiryAnalysisInfoDTO::getQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
