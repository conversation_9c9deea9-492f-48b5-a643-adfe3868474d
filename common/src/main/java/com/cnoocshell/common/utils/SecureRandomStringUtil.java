package com.cnoocshell.common.utils;

import java.security.SecureRandom;

public class SecureRandomStringUtil {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final String LETTER_STR = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String NUMBER_STR = "0123456789";

    public static String random(int count, boolean letters, boolean numbers) {
        if (count < 0) {
            throw new IllegalArgumentException("Count must be non-negative");
        }

        String chars = "";
        if (letters) {
            chars += LETTER_STR;
        }
        if (numbers) {
            chars += NUMBER_STR;
        }

        if (chars.isEmpty()) {
            throw new IllegalArgumentException("At least one of letters or numbers must be true");
        }

        StringBuilder sb = new StringBuilder(count);
        for (int i = 0; i < count; i++) {
            int index = SECURE_RANDOM.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }

}
