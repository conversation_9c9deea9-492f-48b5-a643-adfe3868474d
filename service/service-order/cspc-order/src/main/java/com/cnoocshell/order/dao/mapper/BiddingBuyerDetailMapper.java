package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface BiddingBuyerDetailMapper extends IBaseMapper<BiddingBuyerDetail> {
    @Select("select * from bidding_buyer_detail where id=#{id} and del_flg=0 ")
    BiddingBuyerDetail getById(@Param("id") String id);

    @Select("select * from bidding_buyer_detail where bidding_no=#{biddingNo} and member_code=#{memberCode}  and del_flg=0 order by current_round desc limit 1")
    BiddingBuyerDetail getLastBuyerDetail(@Param("biddingNo") String biddingNo,@Param("memberCode")String memberCode);

}
