package com.cnoocshell.base.api.dto.authRes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class VUEPageDTO implements Serializable {
    private static final long serialVersionUID = 5671469763827690145L;
    /**
     * 页面代码
     */
    @ApiModelProperty("页面代码")
    private String pageCode;

    /**
     * 页面名称
     */
    @ApiModelProperty("页面名称")
    private String pageName;

    /**
     * 页面url
     */
    @ApiModelProperty("页面url")
    private String pageUrl;

    /**
     * 页面文件名称
     */
    @ApiModelProperty("页面文件名称")
    private String pageFileName;


}
