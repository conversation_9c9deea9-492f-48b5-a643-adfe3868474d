package com.cnoocshell.member.aop;

/**
 * @Author: <EMAIL>
 * @Date: 09/11/2018 14:42
 * @DESCRIPTION:记录登陆日志
 */

import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.biz.IAccountSessionBiz;
import com.cnoocshell.member.dao.mapper.AccountLoginInfoMapper;
import com.cnoocshell.member.dao.vo.AccountLoginInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.Executor;

@Slf4j
@Component
@Aspect
@RequiredArgsConstructor
public class AccountLoginInfoAop {

    private final AccountLoginInfoMapper accountLoginInfoMapper;
    private final IAccountSessionBiz accountSessionBiz;

    private final UUIDGenerator uuidGenerator;
    @Qualifier("myTaskAsyncPool")
    private final Executor executor;
    /**
     * 切入点
     */
    @Pointcut("@annotation(com.cnoocshell.member.annotation.LoginLog)")
    public void entryPoint() {
        //do nothing
    }

    @Around("entryPoint()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable{
        AccountLoginInfo accountLoginInfo = new AccountLoginInfo();
        try{
            before(proceedingJoinPoint.getArgs(),accountLoginInfo);
            Object obj = proceedingJoinPoint.proceed();
            after(obj,accountLoginInfo);
            return  obj;
        }catch (MemberBizException e){
            accountLoginInfo.setLoginComment(e.getOnlyMessage());
            throw e;
        }catch (Throwable e){
            accountLoginInfo.setLoginComment(e.getMessage() != null ? e.getMessage() : "未知异常");
            throw e;
        }finally{
            saveLog(accountLoginInfo);
        }
    }

    private void before(Object[] arguments,AccountLoginInfo accountLoginInfo) {
        try {
            Date now = new Date();
            accountLoginInfo.setId(uuidGenerator.gain());
            accountLoginInfo.setCreateTime(now);
            accountLoginInfo.setLoginTime(now);
            accountLoginInfo.setDelFlg(false);


            if(arguments != null && arguments.length > 0 ){
                Object obj = arguments[0];
                log.debug("login dto::{}",obj);
                if( obj instanceof PhoneCodeLoginDTO){
                    setLoginInfo(accountLoginInfo, (PhoneCodeLoginDTO) obj);
                }else if (obj instanceof AccountLoginDTO){
                    setLoginInfo(accountLoginInfo, (AccountLoginDTO) obj);
                }else if (obj instanceof AccountWeChatLoginAndBindingDTO){
                    setLoginInfo(accountLoginInfo, (AccountWeChatLoginAndBindingDTO) obj);
                }else if (obj instanceof AccountWeChatLoginDTO){
                    setLoginInfo(accountLoginInfo, (AccountWeChatLoginDTO) obj);
                }
            }
        } catch (Exception e) {
            log.warn(LOG_RECORD_ERROR_MSG,e.getMessage(),e);
        }

    }

    private void setLoginInfo(AccountLoginInfo accountLoginInfo, AccountWeChatLoginDTO obj) {
        AccountWeChatLoginDTO dto = obj;
        accountLoginInfo.setIp(dto.getIp());
        accountLoginInfo.setMac(dto.getMac());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setSessionId(dto.getSessionId());
        accountLoginInfo.setLoginName(dto.getWeChatCode());
        accountLoginInfo.setTerminal(dto.getTerminal());
        accountLoginInfo.setOs(dto.getOs());
        accountLoginInfo.setOsVersion(dto.getOsVersion());
        accountLoginInfo.setDeviceBrand(dto.getDeviceBrand());
        accountLoginInfo.setImei(dto.getImei());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setLoginType(StringUtils.isBlank(dto.getLoginType()) ? "AccountWeChatLoginDTO" : dto.getLoginType());
    }

    private void setLoginInfo(AccountLoginInfo accountLoginInfo, AccountWeChatLoginAndBindingDTO obj) {
        AccountWeChatLoginAndBindingDTO dto = obj;
        accountLoginInfo.setIp(dto.getIp());
        accountLoginInfo.setMac(dto.getMac());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setSessionId(dto.getSessionId());
        accountLoginInfo.setLoginName(dto.getLoginName());
        accountLoginInfo.setTerminal(dto.getTerminal());
        accountLoginInfo.setOs(dto.getOs());
        accountLoginInfo.setOsVersion(dto.getOsVersion());
        accountLoginInfo.setDeviceBrand(dto.getDeviceBrand());
        accountLoginInfo.setImei(dto.getImei());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setLoginType(StringUtils.isBlank(dto.getLoginType()) ? "AccountWeChatLoginAndBindingDTO" : dto.getLoginType());
    }

    private void setLoginInfo(AccountLoginInfo accountLoginInfo, AccountLoginDTO obj) {
        AccountLoginDTO dto = obj;
        accountLoginInfo.setIp(dto.getIp());
        accountLoginInfo.setMac(dto.getMac());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setSessionId(dto.getSessionId());
        accountLoginInfo.setLoginName(dto.getLoginName());
        accountLoginInfo.setTerminal(dto.getTerminal());
        accountLoginInfo.setOs(StringUtils.isBlank(dto.getOs()) ? dto.getPlatformType() : dto.getOs());
        accountLoginInfo.setOsVersion(dto.getOsVersion());
        accountLoginInfo.setDeviceBrand(dto.getDeviceBrand());
        accountLoginInfo.setImei(dto.getImei());
        accountLoginInfo.setDriverToken(dto.getDriverToken());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setLoginType(StringUtils.isBlank(dto.getLoginType()) ? "AccountLoginDTO" : dto.getLoginType());
    }

    private void setLoginInfo(AccountLoginInfo accountLoginInfo, PhoneCodeLoginDTO obj) {
        PhoneCodeLoginDTO dto = obj;
        accountLoginInfo.setIp(dto.getIp());
        accountLoginInfo.setMac(dto.getMac());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setSessionId(dto.getSessionId());
        accountLoginInfo.setLoginName(dto.getPhone());
        accountLoginInfo.setTerminal(dto.getTerminal());
        accountLoginInfo.setOs(StringUtils.isBlank(dto.getOs()) ? dto.getPlatformType() : dto.getOs());
        accountLoginInfo.setOsVersion(dto.getOsVersion());
        accountLoginInfo.setDeviceBrand(dto.getDeviceBrand());
        accountLoginInfo.setImei(dto.getImei());
        accountLoginInfo.setDriverToken(dto.getDriverToken());
        accountLoginInfo.setUserAgent(dto.getUserAgent());
        accountLoginInfo.setLoginType(StringUtils.isBlank(dto.getLoginType()) ? "PhoneCodeLoginDTO" : dto.getLoginType());
    }

    private void after(Object ret,AccountLoginInfo accountLoginInfo) {
        try {
            if (ret instanceof AccountDTO) {
                AccountDTO accountDTO = (AccountDTO) ret;
                if( StringUtils.isNotBlank(accountDTO.getDriverToken())){
                    if( StringUtils.isBlank(accountLoginInfo.getDriverToken())){
                        accountLoginInfo.setDriverToken(accountDTO.getDriverToken());
                    }
                }
                accountLoginInfo.setAccountId(accountDTO.getAccountId());
                accountLoginInfo.setLoginComment("登录成功");
                accountSessionBiz.forceOffLineSyncBySameTerminal(accountLoginInfo.getAccountId(),
                        accountLoginInfo.getTerminal(),
                        accountLoginInfo.getSessionId(),"账号在"+accountLoginInfo.getIp()+"登录",
                        null);
            }
        }catch (Exception e) {
            log.warn(LOG_RECORD_ERROR_MSG,e.getMessage(),e);
        }
    }

    private void saveLog(AccountLoginInfo accountLoginInfo){
        executor.execute(()->{
            try{
                accountLoginInfoMapper.insert(accountLoginInfo);
            }catch(Exception e){
                log.error("saveLog error:{}",e.getMessage(),e);
            }
        });
    }

    private static final String LOG_RECORD_ERROR_MSG ="登录日志记录出错: {}";
}
