package com.cnoocshell.order.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.EnquiryBuyerDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListQueryDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListViewDTO;
import com.cnoocshell.order.api.dto.EnquiryDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingCustomerInfoDTO;
import com.cnoocshell.order.api.dto.bidding.BiddingCustomerListDTO;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface EnquiryBuyerService {

    ItemResult<String> createEnquiryBuyer(EnquiryBuyerDTO enquiryBuyerDTO);

    ItemResult<EnquiryDTO> getEnquiryBuyDetail(EnquiryBuyerDTO enquiryBuyerDTO);

    List<EnquiryBuyerDetail> getEnquiryBuyerDetailsByEnquiryNo(String EnquiryNo, List<String> memberIds);

    List<BiddingCustomerListDTO> queryBuyerUserInfo(BiddingCustomerInfoDTO dto, String status);

    List<BiddingBuyerDTO> selectBuyerList(List<String> idList, String enquiryStatus);

    List<BiddingBuyerDTO> queryCheckBuyerUserInfo(List<String> buyerList, String enquiryTODOStatus, List<String> ids);

    PageInfo<EnquiryBuyerListViewDTO> buyerQueryEnquiryList(EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO);
}
