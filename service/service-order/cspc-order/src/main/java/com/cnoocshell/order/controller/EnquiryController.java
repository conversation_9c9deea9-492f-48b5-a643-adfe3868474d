package com.cnoocshell.order.controller;


import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.EnquiryDTO;
import com.cnoocshell.order.api.dto.GoodsSimpleDTO;
import com.cnoocshell.order.api.dto.enquiry.CancelEnquiryDTO;
import com.cnoocshell.order.api.dto.enquiry.EnquiryBiddingTimesDTO;
import com.cnoocshell.order.api.dto.enquiry.NotifyBiddingDelayDTO;
import com.cnoocshell.order.api.dto.enquiry.RemoveEnquiryDTO;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.EnquiryService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/enquiry")
@Slf4j
@RequiredArgsConstructor
public class EnquiryController {

    private final EnquiryService enquiryService;

    private static final String QUERY_ENQUIRY_FAILURE_MESSAGE = "查询询价失败！";

    /**
     * 创建新的询价记录。
     * @param enquiryDTO 询价数据传输对象，包含创建询价所需的信息
     * @return 返回ItemResult对象，包含操作结果信息。成功时返回成功消息，失败时返回错误代码和描述
     */
    @ApiOperation("创建新的询价记录")
    @PostMapping("/createEnquiry")
    public ItemResult<String> createEnquiry(@RequestBody EnquiryDTO enquiryDTO) {
        log.info("EnquiryController createEnquiry request:{}", enquiryDTO);
        if (Objects.isNull(enquiryDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "询价为空！");
        }
        try {
            return enquiryService.createEnquiry(enquiryDTO);
        }catch (Exception e) {
            log.error("EnquiryController createEnquiry is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "创建询价失败！");
        }
    }

    /**
     * 卖家查询询价列表
     * @param enquiryDTO 询价数据传输对象，包含查询条件
     * @return 返回ItemResult对象，包含操作结果信息。成功时返回成功消息，失败时返回错误代码和描述
     */
    @ApiOperation("查询询价列表")
    @PostMapping("/queryEnquiryList")
    public ItemResult<PageInfo<EnquiryDTO>> queryEnquiryList(@RequestBody EnquiryDTO enquiryDTO) {
        log.info("EnquiryController queryEnquiryList request:{}", enquiryDTO);
        try {
            return enquiryService.queryEnquiryList(enquiryDTO);
        }catch (Exception e) {
            log.error("EnquiryController queryEnquiryList is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), QUERY_ENQUIRY_FAILURE_MESSAGE);
        }
    }




    /**
     * 获取询价详情。
     *
     * @param enquiryDTO 询价信息数据传输对象，包含查询所需的参数
     * @return 返回一个ItemResult对象，包含处理结果和对应的EnquiryDTO数据。如果发生错误，则返回错误代码和消息
     */
    @PostMapping("/getEnquiryDetail")
    @ApiOperation("获取询价详情")
    public ItemResult<EnquiryDTO> getEnquiryDetail(@RequestBody EnquiryDTO enquiryDTO) {
        log.info("EnquiryController getEnquiryDetail request:{}", enquiryDTO);

        enquiryDTO.setPageSize(1);
        enquiryDTO.setPageNum(1);
        ItemResult<PageInfo<EnquiryDTO>> pageInfoItemResult = enquiryService.queryEnquiryList(enquiryDTO);
        if (Objects.isNull(pageInfoItemResult) || CollectionUtils.isEmpty(pageInfoItemResult.getData().getList())) {
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "无权限查看详情！");
        }

        try {
            return enquiryService.getEnquiryDetail(enquiryDTO);
        } catch (Exception e) {
            log.error("EnquiryController getEnquiryDetail is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价详情失败！");
        }
    }

    /**
     * 更新询价记录。
     *
     * @param enquiryDTO 询价信息数据传输对象，包含需要更新的询价记录信息。
     * @return 包含操作结果的ItemResult对象。如果更新成功，则返回成功信息及更新后的询价记录；若发生错误，则返回相应的错误代码和消息。
     */
    @PostMapping("/updateEnquiry")
    @ApiOperation("更新询价记录")
    public ItemResult<EnquiryDTO> updateEnquiry(@RequestBody EnquiryDTO enquiryDTO) {
        try {
            return enquiryService.updateEnquiry(enquiryDTO);
        } catch (Exception e) {
            log.error("EnquiryController updateEnquiry is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "修改询价失败！");
        }
    }

    /**
     * 获取指定账户的询价产品列表。
     *
     * @param accountId 账户ID，用于标识查询的用户账户
     * @return 包含GoodsSimpleDTO对象列表的ItemResult，表示该账户下的询价商品信息。如果发生错误，则返回带有错误码和消息的ItemResult。
     */
    @ApiOperation("获取询价产品")
    @GetMapping("/getEnquiryGoods")
    public ItemResult<List<GoodsSimpleDTO>> getEnquiryGoods(@RequestParam("accountId") String accountId) {
        try {
            return enquiryService.getEnquiryGoods(accountId);
        }catch (Exception e) {
            log.error("EnquiryController getEnquiryGoods is error:", e);
            return new ItemResult<>(BasicCode.UNKNOWN_ERROR.getCode(), "查询询价商品失败！");
        }
    }

    /**
     * 导出询价买家详细信息。
     *
     * @param enquiryNo 询价单号，用于标识特定的询价请求。
     * @return 包含导出结果的ItemResult对象。如果操作成功，返回包含ExportExcelDTO的对象；如果失败，则返回错误代码和消息。
     */
    @ApiOperation(value = "下载询价接口")
    @GetMapping("/exportEnquiryBuyerDetail")
    public ItemResult<ExportExcelDTO> exportEnquiryBuyerDetail(@RequestParam("enquiryNo") String enquiryNo) {
        try {
            return ItemResult.success(enquiryService.exportEnquiryBuyerDetail(enquiryNo));
        } catch (Exception e) {
            log.error("导出询价结果失败 error:", e);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "导出询价结果失败");
        }
    }

    /**
     * 获取指定会员的询价竞价次数。
     *
     * @param accountId 会员ID，用于查询该会员的询价竞价记录。
     * @return 包含询价竞价次数信息的结果对象。
     */
    @ApiOperation("获取询价竞价的次数")
    @GetMapping("/getEnquiryBiddingTimes")
    public ItemResult<EnquiryBiddingTimesDTO> getEnquiryBiddingTimes(@RequestParam("accountId") String accountId){
        return enquiryService.getEnquiryBiddingTimes(accountId);
    }

    @ApiOperation("取消询价")
    @PostMapping("/cancelEnquiry")
    public ItemResult<Boolean> cancelEnquiry(@RequestBody CancelEnquiryDTO param){
        return ItemResult.success(enquiryService.cancelEnquiry(param));
    }

    @ApiOperation("竞价场次推迟通知")
    @PostMapping("/notifyBiddingDelay")
    public ItemResult<Void> notifyBiddingDelay(@RequestBody NotifyBiddingDelayDTO param){
        enquiryService.notifyBiddingDelay(param);
        return ItemResult.success();
    }

    @ApiOperation("删除草稿")
    @PostMapping("/removeDraft")
    public ItemResult<Void> removeDraft(@RequestBody RemoveEnquiryDTO param){
        enquiryService.removeDraft(param);
        return ItemResult.success();
    }
}
