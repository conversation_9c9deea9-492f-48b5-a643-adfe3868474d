
package com.cnoocshell.member.api.dto.account.checkCode;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.enums.RegexPatterns;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import javax.validation.constraints.NotBlank;

import java.util.regex.Pattern;

/**
 * @DESCRIPTION:
 */
@NoArgsConstructor
@Data
public class GetEmailCodeDTO {


    /**
     * 用途
     */
    @NotBlank
    @ApiModelProperty("用途")
    private String use;

    /**
     * 电子邮箱
     */
    @NotBlank
    @ApiModelProperty("要收验证码的电子邮件")
    private String email;

    /**
     * 是否校验手机号
     */
    @ApiModelProperty("是否需要校验手机号在数据库中，如果在才发送验证码")
    private Boolean checkEmail = Boolean.TRUE;

    /**
     * 会话id
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的前端sessionId")
    private String sessionId="sessionId";

    /**
     * 是否需要校验图形验证码
     */
    @ApiModelProperty("发送短信验证码是否需要校验图形验证码，默认true")
    private Boolean checkCaptcha = Boolean.FALSE;

    /**
     * 图形验证码
     */
    @ApiModelProperty("图形验证码")
    private String captcha;

    /**
     * 是否需要清除该图形验证码
     */
    @ApiModelProperty("是否需要清除该图形验证码")
    private Boolean deleteCaptcha = Boolean.FALSE;


    @ApiModelProperty("账户id")
    private String accountId;

    public GetEmailCodeDTO(String use,String email,String sessionId){
        this(use,email,sessionId,true);
    }

    public GetEmailCodeDTO(String use,String email,String sessionId,Boolean checkEmail){
        this(use,email,sessionId,checkEmail,null);
    }

    public GetEmailCodeDTO(String use,String email,String sessionId,Boolean checkEmail,String accountId){
        if( StringUtils.isBlank(email) || !Pattern.matches(RegexPatterns.EMAIL_REGEX,email)){
            throw new BizException(MemberCode.VALIDATION_ERROR,"邮件地址不正确");
        }
        this.use = use;
        this.email = email;
        this.checkEmail = checkEmail == null || checkEmail;
        this.sessionId = sessionId;
        this.accountId = accountId;
    }

}
