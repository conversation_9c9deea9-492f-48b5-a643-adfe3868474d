package com.cnoocshell.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * 算术工具类
 * @Author: <EMAIL>
 * @Description  ArithUtils
 * @date   2018年9月11日 下午3:16:09
 */
public class ArithUtils {

    public static final Integer ZERO = Integer.valueOf(0);

    private ArithUtils() {
        throw new IllegalStateException("Utility class");
    }
	/**
	 * BigDecimal的加法运算封装
	 * @Author: <EMAIL>
	 * 2017年3月23日下午4:53:21
	 * @param b1
	 * @param bn
	 * @return
	 */
   public static BigDecimal add(BigDecimal b1, BigDecimal... bn) {
       if (null == b1) {
           b1 = BigDecimal.ZERO;
       }
       if (null != bn) {
           for (BigDecimal b : bn) {
               b1 = b1.add(null == b ? BigDecimal.ZERO : b);
           }
       }
       return b1;
   }
 
   /**
    * Integer加法运算的封装
    * @Author: <EMAIL>
    * 2017年3月23日下午4:54:08
    * @param b1   第一个数
    * @param bn   需要加的加法数组
    * @注 ： Optional  是属于com.google.common.base.Optional<T> 下面的class
    * @return
    */
   public static Integer add(Integer b1, Integer... bn) {
       if (null == b1) {
           b1 = 0;
       }
       Integer r = b1;
       if (null != bn) {
           for (Integer b : bn) {
               r += Optional.ofNullable(b).orElse(0);
           }
       }
       return r > 0 ? r : 0;
   }
 
   /**
    * 计算金额方法,为负数时返回0
    * @Author: <EMAIL>
    * 2017年3月23日下午4:53:00
    * @param b1
    * @param bn
    * @return
    */
   public static BigDecimal subtract(BigDecimal b1, BigDecimal... bn) {
       return subtract(true, b1, bn);
   }
 
   /**
    * BigDecimal的安全减法运算
    * @Author: <EMAIL>
    * 2017年3月23日下午4:50:45
    * @param isZero  减法结果为负数时是否返回0，true是返回0（金额计算时使用），false是返回负数结果
    * @param b1		   被减数
    * @param bn        需要减的减数数组
    * @return
    */
   public static BigDecimal subtract(Boolean isZero, BigDecimal b1, BigDecimal... bn) {
       if (null == b1) {
           b1 = BigDecimal.ZERO;
       }
       BigDecimal r = b1;
       if (null != bn) {
           for (BigDecimal b : bn) {
               r = r.subtract((null == b ? BigDecimal.ZERO : b));
           }
       }
       if (isZero && r.compareTo(BigDecimal.ZERO) < 0) {
           return BigDecimal.ZERO;
       } else {
           return r;
       }
   }
 
   /**
    * 整型的减法运算，小于0时返回0
    * @Author: <EMAIL>
    * 2017年3月23日下午4:58:16
    * @param b1
    * @param bn
    * @return
    */
   public static Integer subtract(Integer b1, Integer... bn) {
       if (null == b1) {
           b1 = 0;
       }
       Integer r = b1;
       if (null != bn) {
           for (Integer b : bn) {
               r -= Optional.ofNullable(b).orElse(0);
           }
       }
       return null != r && r > 0 ? r : 0;
   }
 
   /**
    * 金额除法计算，返回2位小数（具体的返回多少位大家自己看着改吧）
    * @Author: <EMAIL>
    * 2017年3月23日下午5:02:17
    * @param b1
    * @param b2
    * @return
    */
   public static <T extends Number> BigDecimal divide(T b1, T b2){
       return divide(b1, b2, BigDecimal.ZERO);
   }
 
   /**
    * BigDecimal的除法运算封装，如果除数或者被除数为0，返回默认值
    * 默认返回小数位后2位，用于金额计算
    * @Author: <EMAIL>
    * 2017年3月23日下午4:59:29
    * @param b1
    * @param b2
    * @param defaultValue
    * @return
    */
   public static <T extends Number> BigDecimal divide(T b1, T b2, BigDecimal defaultValue) {
       if (null == b1 || null == b2) {
           return defaultValue;
       }
       try {
           BigDecimal dividend = BigDecimal.valueOf(b1.doubleValue());
           BigDecimal divisor = BigDecimal.valueOf(b2.doubleValue());
           return dividend.divide(divisor, 2, RoundingMode.HALF_UP);
       } catch (Exception e) {
           return defaultValue;
       }
   }
 
   /**
    * BigDecimal的乘法运算封装
    * @Author: <EMAIL>
    * 2017年3月23日下午5:01:57
    * @param b1
    * @param b2
    * @return
    */
   public static <T extends Number> BigDecimal multiply(T b1, T b2) {
       if (null == b1 || null == b2) {
           return BigDecimal.ZERO;
       }
       return BigDecimal.valueOf(b1.doubleValue())
               .multiply(BigDecimal.valueOf(b2.doubleValue()))
               .setScale(2, RoundingMode.HALF_UP);
   }

    /**
     * 为空或小于0时返回0,否则返回原值
     */
    public static BigDecimal nullAsZero(BigDecimal b1){
        return b1 == null || b1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : b1;
    }
}
