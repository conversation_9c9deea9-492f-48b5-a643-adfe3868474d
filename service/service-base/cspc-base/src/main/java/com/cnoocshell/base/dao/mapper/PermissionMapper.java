package com.cnoocshell.base.dao.mapper;

import com.cnoocshell.base.dao.vo.Permission;
import com.cnoocshell.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

@Mapper
public interface PermissionMapper extends IBaseMapper<Permission> {

    @Select("select * from sys_permission where del_flg = 0 and role_type =#{roleType}")
    List<Permission> findPermissionByRoleType(@Param("roleType") String roleType);

    List<Permission> queryRolePermissions(@Param("permissionCodes") Collection<String> permissionCodes, @Param("appType") String appType);
}