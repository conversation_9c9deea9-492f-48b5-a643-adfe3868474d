package com.cnoocshell.member.biz.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.base.api.dto.account.RemoveAccountRoleDTO;
import com.cnoocshell.base.api.enums.AccountPlatformEnum;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BasicRuntimeException;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.MD5;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.constant.MemberNumberConstant;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberDTO;
import com.cnoocshell.member.api.dto.member.enums.*;
import com.cnoocshell.member.api.enums.AgreementBusinessTypeEnum;
import com.cnoocshell.member.api.enums.AgreementTypeEnum;
import com.cnoocshell.member.api.enums.RegisterTypeEnum;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.biz.*;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.common.PassWordGeneratorUtil;
import com.cnoocshell.member.common.RedisKey;
import com.cnoocshell.member.common.RequestUtils;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.mapper.MemberMapper;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.dao.vo.MemberApproveRequest;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountChangeHistoryService;
import com.cnoocshell.member.service.IPasswordService;
import com.cnoocshell.mq.core.MQMessage;
import com.cnoocshell.mq.core.service.IMQProducer;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class AccountBiz extends BaseCacheBiz<Account> implements IAccountBiz {

    private final AccountMapper accountMapper;
    private final BizRedisService bizRedisService;
    private final IPasswordService passwordService;
    private final MemberMapper memberMapper;
    private final UUIDGenerator uuidGenerator;
    private final IAccountChangePasswordHistoryBiz accountChangePasswordHistoryBiz;
    private final MemberCodeIncrementIdGenerator memberCodeIncrementIdGenerator;
    private final IMemberApproveRequestBiz iMemberApproveRequestBiz;
    private final IRoleService iRoleService;
    private final IAccountChangeHistoryService iAccountChangeHistoryService;
    private final IAccountSessionBiz iAccountSessionBiz;
    private final ISmsSendService iSmsSendService;
    @Autowired
    @Lazy
    private IMemberBiz memberBiz;
    private final IMQProducer mqProducer;


    @Value(value = "${user.password.maxRetryCount:10}")
    private int maxRetryCount = 10;
    @Value(value = "${user.lock.second:86400}")
    private int userLoginSecond = 86400;

    private static final String ACCOUNT_NOT_EXISTS = "账号不存在";
    private static final String ACCOUNTID_NOT_EXISTS = "账号不存在,accountId:";
    private static final String MOBILE = "mobile";
    public static final String DEFAULT_MOBLE_NUMBER = "***********";

    private static final String NULL = "null";

    @PostConstruct
    public void init() {
        initCache(null, DuplicateString.ACCOUNT_ID, Lists.newArrayList(DuplicateString.ACCOUNT_NAME, DuplicateString.ACCOUNT_CODE));
        this.cleanRedisCache(null);
    }

    @Override
    public void cleanRedisCache(String operator) {
        cacheClear();
        log.info("{} clear account cache", operator);
    }

    /**
     * 根据用户名或手机号和密码，本地数据库登录校验
     *
     * @param accountLoginDTO 登录名信息
     * @return 校验通过则返回true
     */
    @Override
    public AccountDTO loginVerification(AccountLoginDTO accountLoginDTO) {
        log.info("==>logDTO : {}", JSON.toJSONString(accountLoginDTO));
        //1、校验登录失败次数，如果达到登录失败累计最大次数，则锁定24小时，
        String key = RedisKey.USER_LOGIN_RETRY_COUNT + accountLoginDTO.getLoginName();
        Integer count = checkLoginCount(0, key, null);

        String driverToken = null;
        String platformType = null;
        String ip = accountLoginDTO.getIp();
        if (StringUtils.isNotBlank(accountLoginDTO.getDriverToken())) {
            driverToken = accountLoginDTO.getDriverToken();
            platformType = getPlatformType(accountLoginDTO);
        }

        if (StringUtils.isBlank(accountLoginDTO.getLoginName()) || StringUtils.isBlank(accountLoginDTO.getPassword())) {
            bizRedisService.setex(key, userLoginSecond, ++count);
            throw new MemberBizException(MemberCode.LOGIN_ERROR, DuplicateString.ACCOUNT_NAME_OR_PD_INCORRECT);
        }
        //手机号+密码登录
        return loginByMobileAndPassword(accountLoginDTO, key, count, driverToken, platformType, ip);
    }

    @Override
    public Account findById(String accountId) {
        Account account = get(accountId);
        if (account == null || BooleanUtil.isTrue(account.getDelFlg())) {
            return null;
        }
        return account;
    }

    /**
     * 根据账号类型判断手机号是否存在
     *
     * @param newMobilePhone 新用户名
     * @param accountType
     * @return
     */
    @Override
    public boolean checkMobilePhoneExists(String newMobilePhone, Integer... accountType) {
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.MOBILE,newMobilePhone);
        if(Objects.nonNull(accountType)){
            criteria.andIn(DuplicateString.ACCOUNT_TYPE, Arrays.asList(accountType));
        }

        return mapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public AccountDTO loginByPhoneVerificationCode(PhoneCodeLoginDTO phoneCodeLoginDTO) {
        List<Account> list = findByMobilePhone(phoneCodeLoginDTO.getPhone(), null);
        if (CollectionUtils.isEmpty(list)) {
            throw new MemberBizException(MemberCode.LOGIN_ERROR, ACCOUNT_NOT_EXISTS);
        }
        Account account = null;
        if (list.size() > 1) {
            if (StringUtils.isNotBlank(phoneCodeLoginDTO.getAccountCode())) {
                account = list.stream().filter(item -> StringUtils.equals(item.getAccountCode(), phoneCodeLoginDTO.getAccountCode())).findFirst().orElse(null);
            }
            if (account == null) {
                throw new MemberBizException(MemberCode.LOGIN_ERROR, "当前手机号已关联多个账户，不能使用短信验证码登录");
            }
        }

        account = list.get(0);

        //1、校验登录失败次数，如果达到登录失败累计最大次数，则锁定24小时，
        Integer count = 0;
        String key1 = RedisKey.USER_LOGIN_RETRY_COUNT + account.getAccountName();
        String key2 = RedisKey.USER_LOGIN_RETRY_COUNT + account.getMobile();
        count = checkLoginCount(count, key1, key2);
        //判断是否被禁用
        this.haveDisable(account, null, count);

        String driverToken = null;
        String os = null;
        String ip = phoneCodeLoginDTO.getIp();
        if (StringUtils.isNotBlank(phoneCodeLoginDTO.getDriverToken())) {
            driverToken = phoneCodeLoginDTO.getDriverToken();
            os = phoneCodeLoginDTO.getOs();
            if (StringUtils.isBlank(os)) {
                ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (servletRequestAttributes != null) {
                    //获取请求的来源
                    os = RequestUtils.getPlatformType(servletRequestAttributes.getRequest());
                }
            }
        }
        account.setLastLoginIp(ip);
        account.setDriverToken(driverToken);
        account.setMobileOs(os);

        return getResult(account, null);
    }

    @Override
    public List<Account> findByMobilePhone(String mobilePhoneNumber, Integer accountType) {
        return findByMobilePhone(mobilePhoneNumber, null, accountType);
    }

    @Override
    public Account findByEmail(String email) {
        Condition condition = new Condition(Account.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo("email", email);


        return CollUtil.getFirst(findByCondition(condition));
    }

    /**
     * 校验是否重名的问题 不区分用户名大小写
     *
     * @param newAccountName 新用户名
     * @return
     */
    @Override
    public boolean checkAccountNameExists(String newAccountName) {
        Condition condition = new Condition(Account.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.ACCOUNT_NAME, newAccountName)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        return mapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public Account registerAccount(Account account) {
        account.setNeedUpdatePassword(Boolean.FALSE);
        account.setLastLoginDate(new Date());
        account.setAccountType(AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType());
        account = this.register(account,  null);
        return account;
    }

    @Override
    public void updateMobilePhone(String accountId, String newMobilePhone, String operator) {
        Account account = get(accountId);
        if (account == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNTID_NOT_EXISTS + accountId);
        }
        account.setMobile(newMobilePhone);
        save(account, operator);
    }

    @SneakyThrows
    @Override
    public Account updateBaseInfo(Account account, String operator) {
        Account account1 = get(account.getAccountId());
        if (account1 == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNTID_NOT_EXISTS + account.getAccountId());
        }
        if (account.getBirthDay() != null) {
            account1.setBirthDay(account.getBirthDay());
        }
        if (StringUtils.isNotBlank(account.getHeadPic())) {
            account1.setHeadPic(account.getHeadPic());
        }
        if (account.getSex() != null) {
            account1.setSex(account.getSex());
        }
        if (StringUtils.isNotBlank(account.getWechatId())) {
            account1.setWechatId(account.getWechatId());
        }
        if (StringUtils.isNotBlank(account.getAccountNickname())) {
            account1.setAccountNickname(account.getAccountNickname());
        }
        if ("null".equals(account.getWechatId())) {
            account1.setWechatId(null);
        }
        if (CharSequenceUtil.isNotBlank(account.getEmail())) {
            account1.setEmail(account.getEmail());
        }
        if(CharSequenceUtil.isNotBlank(account.getRealName())){
            account1.setRealName(account.getRealName());
        }
        account1.setUpdateTime(new Date());
        account1.setUpdateUser(operator);
        String newMemberName = getMemberNameByAccount(account1);
        if (Objects.equals(account1.getAccountType(), AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType())) {
            Member member = memberMapper.findById(account1.getMemberId());
            if (member != null && !StringUtils.equals(newMemberName, member.getMemberName())) {
                member.setMemberName(account.getAccountNickname());
                memberBiz.updateSelective(member);
            }
        }

        //发送账户信息变更消息
        MQMessage<AccountSimpleDTO> accountChange = new MQMessage<>();
        accountChange.setExchange(MqTopicConstant.ACCOUNT_CHANGE_TOPIC);
        accountChange.setData(BeanUtil.toBean(account1,AccountSimpleDTO.class));
        mqProducer.send(accountChange);

        return updateById(account1);
    }

    @Override
    public Account updateById(Account account) {
        updateByPrimaryKey(account);
        return account;
    }

    @Override
    public void updateAccountName(String accountId, String newAccountName, String operator) {
        if (checkAccountNameExists(newAccountName)) {
            throw new MemberBizException(BasicCode.UNDEFINED_ERROR, "账户名已存在");
        }
        Account account = get(accountId);
        if (account == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNTID_NOT_EXISTS + accountId);
        }
        if (Objects.equals(account.getAccountType(), AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType())) {
            Member member = memberBiz.get(account.getMemberId());
            if (StringUtils.equals(account.getAccountName(), member.getMemberName())) {
                account.setMemberName(getMemberNameByAccount(account));
                member.setMemberName(account.getMemberName());
            }
            if (StringUtils.equals(account.getAccountName(), member.getMemberShortName())) {
                account.setMemberShortName(newAccountName);
                member.setMemberShortName(member.getMemberName());
            }
            memberBiz.updateSelective(member);
        }
        account.setAccountName(newAccountName);
        save(account, operator);
    }

    @Override
    public int updateMemberName(String memberId, String memberName, String memberShortName) {
        Condition condition = new Condition(Account.class);
        condition.createCriteria().andEqualTo(DuplicateString.MEMBER_ID, memberId);
        Account account = new Account();
        account.setMemberShortName(memberShortName);
        account.setMemberName(memberName);
        int resultCount = mapper.updateByConditionSelective(account, condition);
        //更新缓存
        super.cacheClear();
        return resultCount;
    }

    @Override
    public Account registerSubAccount(SubAccountRegisterDTO dto) {
        if (StringUtils.isBlank(dto.getMemberId())) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, "会员id不可为空");
        }
        List<Account> accounts = this.listByMobile(dto.getMobile());
        if (CollUtil.isNotEmpty(accounts)) {
            List<Integer> notAllowType = Arrays.asList(AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType(),
                    AccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getAccountType(),
                    AccountTypeEnum.PLATFORM_ACCOUNT.getAccountType());
            if (accounts.stream().anyMatch(v -> CollUtil.contains(notAllowType, v.getAccountType())))
                throw new BizException(BasicCode.CUSTOM_ERROR, "手机号已注册");
        }

        Member member = memberBiz.get(dto.getMemberId());
        if (member == null) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, "会员[" + dto.getMemberId() + "]不存在");
        }
        //存在自己注册的个人账户
        Account existPersonal = accounts.stream()
                .filter(v->Objects.equals(v.getAccountType(),AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType()))
                .findFirst().orElse(null);

        Account account = new Account();
        if(Objects.nonNull(existPersonal)){
            account = existPersonal;
            //判断改账户是否在申请企业类型中
            MemberApproveRequest request = iMemberApproveRequestBiz.findLastChangeRequest(existPersonal.getMemberId(),ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER,AdvertStatusEnum.NEW_REQUEST);
            if(Objects.nonNull(request))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"该手机号处于注册企业审批中");
        }else {
            BeanUtils.copyProperties(dto, account);
        }
        //处理创建子账户时 手机已存在的账户信息并更新
        handleSubAccountInfo(dto, account);
        //判断是否平台创建
        if(BooleanUtil.isTrue(dto.getIsPlatform())){
            account.setAccountType(AccountTypeEnum.PLATFORM_ACCOUNT.getAccountType());
        }else {
            account.setAccountType(AccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getAccountType());
        }
        //注册类型 管理员创建
        account.setRegisterType(RegisterTypeEnum.ADMIN_CREATE.getType());
        account.setMemberId(member.getMemberId());
        account.setMemberName(member.getMemberName());
        account.setMemberCode(member.getMemberCode());
        account.setMemberShortName(member.getMemberShortName());
        account.setNeedUpdatePassword(false);

        return this.registerBySub(account, member, dto.getOperatorId());
    }

    private static void handleSubAccountInfo(SubAccountRegisterDTO dto, Account account) {
        account.setRealName(dto.getRealName());
        if(CharSequenceUtil.isNotBlank(dto.getEmail()))
            account.setEmail(dto.getEmail());
        if(CharSequenceUtil.isNotBlank(dto.getAccountName()))
            account.setAccountName(dto.getAccountName());
        if(CharSequenceUtil.isNotBlank(dto.getEmployeeId()))
            account.setEmployeeId(dto.getEmployeeId());
        if(CharSequenceUtil.isNotBlank(dto.getDepartment()))
            account.setDepartment(dto.getDepartment());
        if(CharSequenceUtil.isNotBlank(dto.getPosition()))
            account.setPosition(dto.getPosition());
    }

    @Override
    public Page<Account> findAll(AccountSearchDTO accountSearchDTO) {
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria().andEqualTo(DuplicateString.DEL_FLG, false);

        CommonUtils.andInIfNotEmpty(criteria, DuplicateString.ACCOUNT_TYPE, accountSearchDTO.getAccountTypeList());
        CommonUtils.andEqualToIfNotNull(criteria, DuplicateString.STATUS, accountSearchDTO.getDisabled());
        CommonUtils.andEqualToIfNotNull(criteria, DuplicateString.SEX, accountSearchDTO.getSex());
        CommonUtils.andEqualToIfNotNBank(criteria, DuplicateString.MEMBER_ID, accountSearchDTO.getMemberId());
        CommonUtils.andInIfNotEmpty(criteria, DuplicateString.ACCOUNT_ID, accountSearchDTO.getAccountIds());
        getLikeCondition(accountSearchDTO, criteria);
        condition.orderBy(DuplicateString.CREATE_TIME).desc();
        PageInfo<Account> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(accountSearchDTO.getPageNum());
        pageInfo.setPageSize(accountSearchDTO.getPageSize());

        return this.page(condition, pageInfo);
    }

    @Override
    public void disabled(String accountId, String reason, String operator) {
        Account account = get(accountId);
        Account account2 = get(operator);
        if (account == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNT_NOT_EXISTS);
        }
        account.setStatus(true);
        updateSelective(account);

        //禁用账号成功后添加历史记录
        AccountChangeHistoryDTO accountChangeHistoryDTO = new AccountChangeHistoryDTO();
        accountChangeHistoryDTO.setAccountId(accountId);
        accountChangeHistoryDTO.setRealName(account.getRealName());
        accountChangeHistoryDTO.setOperatroName(account2.getRealName());
        accountChangeHistoryDTO.setWay(AccountChangeWayEnum.DISABLE.getCode());
        accountChangeHistoryDTO.setReason(reason);
        accountChangeHistoryDTO.setIsEffect(1);
        accountChangeHistoryDTO.setIsImmediateEffect(1);
        accountChangeHistoryDTO.setEffectTime(new Date());
        iAccountChangeHistoryService.add(accountChangeHistoryDTO, operator);
        //todo 发送短信
        //smsMessageProducer.sendDisableAccount(account.getAccountId(), account.getAccountName());
    }

    @Override
    public void enabled(String accountId, String operator) {
        Account account = get(accountId);
        Account account2 = get(operator);
        if (account == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNT_NOT_EXISTS);
        }
        account.setStatus(false);
        updateSelective(account);

        //启用账号成功后添加历史记录
        AccountChangeHistoryDTO accountChangeHistoryDTO = new AccountChangeHistoryDTO();
        accountChangeHistoryDTO.setAccountId(accountId);
        accountChangeHistoryDTO.setRealName(account.getRealName());
        accountChangeHistoryDTO.setOperatroName(account2.getRealName());
        accountChangeHistoryDTO.setWay(AccountChangeWayEnum.ENABLE.getCode());
        accountChangeHistoryDTO.setReason("启用菜单");
        accountChangeHistoryDTO.setIsEffect(1);
        accountChangeHistoryDTO.setIsImmediateEffect(1);
        accountChangeHistoryDTO.setEffectTime(new Date());
        iAccountChangeHistoryService.add(accountChangeHistoryDTO, operator);
        //todo 发送短信
//        smsMessageProducer.sendEnableAccount(account.getMemberId(), account.getAccountId(), account.getAccountName());
    }

    @Override
    public Account findByAccountName(String accountName) {
        Account account = findByCacheField(DuplicateString.ACCOUNT_NAME, accountName);
        if (account != null && !BooleanUtil.isTrue(account.getDelFlg())) {
            return account;
        }
        account = accountMapper.findByName(accountName);
        if (account != null) {
            cachePut(account);
        }
        return account;
    }

    @Override
    public Account findById(String accountId, String memberId) {
        Account account = findById(accountId);

        if (StringUtils.isBlank(memberId)) {
            return account;
        }

        if (account != null && StringUtils.equals(memberId, account.getMemberId())) {
            return account;
        }
        return null;
    }

    @SneakyThrows
    @Override
    public Account updateSubAccountInfo(SubAccountUpdateDTO dto) {
        Account account1 = get(dto.getAccountId());
        if (account1 == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, ACCOUNTID_NOT_EXISTS + dto.getAccountId());
        }

        account1.setAccountName(dto.getAccountName());
        account1.setEmail(dto.getEmail());
        account1.setMobile(dto.getMobile());
        account1.setRealName(dto.getRealName());
        if (dto.getBirthDay() != null) {
            account1.setBirthDay(dto.getBirthDay());
        }
        account1.setEmployeeId(dto.getEmployeeId());
        account1.setDepartment(CharSequenceUtil.isEmpty(dto.getDepartment()) ? null : dto.getDepartment());
        account1.setPosition(CharSequenceUtil.isEmpty(dto.getPosition()) ? null : dto.getPosition());
        //发送账户信息变更消息
        MQMessage<AccountSimpleDTO> accountChange = new MQMessage<>();
        accountChange.setExchange(MqTopicConstant.ACCOUNT_CHANGE_TOPIC);
        accountChange.setData(BeanUtil.toBean(account1,AccountSimpleDTO.class));
        mqProducer.send(accountChange);

        return save(account1, dto.getOperatorId());
    }

    @Override
    public List<Account> findByMemberIdAndAccountType(String memberId, Integer accountType) {
        if (StringUtils.isBlank(memberId)) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, ":memberId不可为空");
        }
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.MEMBER_ID, memberId);

        if (accountType != null) {
            criteria.andEqualTo(DuplicateString.ACCOUNT_TYPE, accountType);
        }
        return accountMapper.selectByCondition(condition);
    }

    @SneakyThrows
    @Override
    public void unBindingAccount(String currUserAccountId, String unBindingAccountId) {
        Account currUserAccount = get(currUserAccountId);
        if (currUserAccount == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, "当前账号不存在");
        }
        Account account = get(unBindingAccountId);
        if (account == null) {
            throw new MemberBizException(MemberCode.MEMBER_DATA_NOT_EXIST, "要解绑的账号不存");
        }
        if(!CharSequenceUtil.equals(currUserAccount.getMemberId(),account.getMemberId())){
            throw new MemberBizException(BasicCode.INVALID_PARAM, "要解绑的账号不是你的员工账号");
        }
        account.setDelFlg(Boolean.TRUE);
        setOperatorInfo(account,currUserAccountId,false);
        updateById(account);

        //发送账户解绑消息
        MQMessage<AccountUnBindDTO> mq = new MQMessage<>();
        mq.setExchange(MqTopicConstant.ACCOUNT_UNBIND_TOPIC);
        mq.setData(new AccountUnBindDTO(account.getMemberId(),account.getMemberCode(),unBindingAccountId));
        mqProducer.send(mq);

        //解绑的账号强制退出
        iAccountSessionBiz.forceOffLineSync(unBindingAccountId,null,null,"启用账户解绑员工账号强制退出",currUserAccountId);
    }

    @Override
    public List<Account> listByMobile(String mobile) {
        Condition condition = this.newCondition();
        condition.and()
                .andEqualTo(DuplicateString.MOBILE, mobile)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        return this.findByCondition(condition);
    }

    @Override
    public boolean existById(String accountId) {
        Condition condition = this.newCondition();
        condition.and()
                .andEqualTo(DuplicateString.ACCOUNT_ID, accountId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);

        return accountMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public boolean existsAccountByMobile(String mobile, String exceptAccountId) {
        if (CharSequenceUtil.isBlank(mobile))
            return false;
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.MOBILE, mobile);
        if (CharSequenceUtil.isNotBlank(exceptAccountId)) {
            criteria.andNotEqualTo(DuplicateString.ACCOUNT_ID, exceptAccountId);
        }
        return accountMapper.selectCountByCondition(condition) > 0;
    }

    private void getLikeCondition(AccountSearchDTO accountSearchDTO, Example.Criteria criteria) {
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.EMPLOYEE_ID, accountSearchDTO.getEmployeeId());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.DEPARTMENT, accountSearchDTO.getDepartment());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.POSITION, accountSearchDTO.getPosition());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.ACCOUNT_ID, accountSearchDTO.getAccountId());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.ACCOUNT_NAME, accountSearchDTO.getAccountName());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.MEMBER_NAME, accountSearchDTO.getMemberName());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.MOBILE, accountSearchDTO.getMobile());
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.REAL_NAME, accountSearchDTO.getRealName());
    }

    public Account register(Account account, String createUserId) {
        //手机号不可重复
        if(this.checkMobilePhoneExists(account.getMobile(),null)){
            throw new MemberBizException(MemberCode.REGISTER_ERROR, "手机号已存在");
        }

        try {
            account.setAccountId(null);
            updateAccountForRegister(account, createUserId);

            accountMapper.insert(account);

            //同时注册会员
            this.createMember(account, MemberTypeEnum.PERSON_BUYER.getCode(), ApproveRequestTypeEnum.REGISTER_PERSON_BUYER.getCode());
            //保存用户、隐私协议记录
            this.saveAgreement(account);

            //创建个人买家角色 卖家创建子账号不添加个人买家角色
            if (CharSequenceUtil.isBlank(createUserId))
                createUserId = account.getAccountId();
            //设置初始化角色 个人买家
            iRoleService.setBuyerPersonal(account.getMemberId(), account.getAccountId(), createUserId);

            if (StringUtils.isBlank(account.getCreateUser())) {
                account.setCreateUser(account.getAccountId());
            }
            updateSelective(account);

            //更新缓存
            cachePut(account);

            return account;

        } catch (Exception e) {
            log.error("账户注册失败 error:",e);
            throw new MemberBizException(MemberCode.REGISTER_ERROR,"账户注册失败");
        }
    }

    public Account registerBySub(Account account, Member member, String createUserId) {
        try {
            updateAccountForRegister(account, createUserId);
            //创建子账号，设置账号代码
            updateSubAccountForRegister(account, member.getMemberCode());
            if (this.existById(account.getAccountId())) {
                this.updateById(account);
            } else {
                this.insert(account);
            }
            //更新缓存数据
            cachePut(account);
            //移除个人账号下所有角色 角色分配由企业处理
            RemoveAccountRoleDTO removeRole = new RemoveAccountRoleDTO(account.getAccountId(), null, null, true);
            iRoleService.removeAccountRole(removeRole);

            return account;

        } catch (Exception e) {
            log.error("创建子账户失败 error：", e);
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "创建子账户失败");
        }
    }

    private void updateAccountForRegister(Account account, String createUserId) {
        account.setStatus(false);
        account.setLockStatus(false);
        account.setDelFlg(false);
        account.setAccountId(StringUtils.isBlank(account.getAccountId()) ? uuidGenerator.gain() : account.getAccountId());
        account.setCreateUser(StringUtils.isBlank(account.getCreateUser()) ? createUserId : account.getCreateUser());
        account.setCreateTime(new Date());
        String name = account.getAccountName();
        if (StringUtils.isBlank(name)) {
            account.setAccountName(null);
        }
        if (account.getSex() == null) {
            //保密
            account.setSex(2);
        }
    }


    private void updateSubAccountForRegister(Account account,String memberCode) {
        if (StringUtils.isBlank(memberCode)) {
            throw new MemberBizException(MemberCode.REGISTER_ERROR, "会员代码不可为空");
        }
        while (true) {
            String accountCode = getAccountCode(memberCode);
            if ((memberCode + "-001").equals(accountCode) || findByAccountCode(accountCode) == null) {
                account.setAccountCode(accountCode);
                break;
            }
            log.info("discard accountCode: {},reason:already occupied", accountCode);
        }
    }

    private String getAccountCode(String memberCode) {
        String key = MemberRedisKeys.MEMBER_INCR_CODE_SUBACCOUNT + memberCode;
        //获取账户代码
        long code = bizRedisService.incr(key, 1);
        //key设置了过期时间，如果过期后再来新建，则需要执行if中的代码，以提高性能
        if (code == 1) {
            String maxAccountCode = null;
            try {
                //检查数据库是否存在
                maxAccountCode = accountMapper.findMaxAccountCode(memberCode);
                if (StringUtils.isNotBlank(maxAccountCode) && Long.parseLong(maxAccountCode.trim().split("-")[1]) >= 1) {
                    long maxAccountCodeLong = Long.parseLong(maxAccountCode.trim().split("-")[1]);
                    //如果存在，则直接新增到最大值
                    bizRedisService.set(key, maxAccountCodeLong);
                    code = bizRedisService.incr(key, 1);
                }
            } catch (NumberFormatException e) {
                log.warn("accountCode error:{}", maxAccountCode);
            }
        }
        bizRedisService.expire(key, 86400);

        //大于等于3位，则直接返回
        if (code > 99) {
            return memberCode + "-" + code;
        }
        //少于3位则补0
        if (code > 9) {
            return memberCode + "-0" + code;
        }
        return memberCode + "-00" + code;
    }

    private void createMember(Account account, String memberType, String requestType) {
        Member member = new Member();
        member.setMemberId(uuidGenerator.gain());
        member.setMainAccountId(account.getAccountId());
        member.setMemberName(this.getMemberNameByAccount(account));
        member.setMemberShortName(member.getMemberName());
        member.setContactPhone(account.getMobile());
        member.setContactName(account.getAccountName());
        member.setMemberType(memberType);
        member.setSellerFlg(MemberDTO.FLG_FALSE);
        String memberCode = memberCodeIncrementIdGenerator.getNewMemberCode();
        member.setMemberCode(memberCode);
        account.setMemberCode(memberCode);
        account.setAccountCode(memberCode + "-001");
        member.setStatus(MemberStatusEnum.NORMAL.getCode());
        setOperInfo(member, account.getAccountId(), true);
        memberMapper.insertSelective(member);
        account.setMemberId(member.getMemberId());
        // 同步变更
        iMemberApproveRequestBiz.newRequestByContactName(
                member.getMemberId(), member.getMemberCode(),
                requestType,
                member.getMemberType(),
                member.getMemberName(),
                account.getAccountName(),
                account.getMobile(),
                "会员注册",
                false,
                account.getAccountId());
    }

    private String getMemberNameByAccount(Account account) {
        if (StringUtils.isNotBlank(account.getRealName())) {
            //真实姓名
            return account.getRealName();
        }
        if (StringUtils.isNotBlank(account.getAccountNickname())) {
            // 昵称
            return account.getAccountNickname();
        } else if (StringUtils.isNotBlank(account.getMobile()) && !DEFAULT_MOBLE_NUMBER.equals(account.getMobile())) {
            // 手机号
            return account.getMobile();
        }
        return account.getAccountName();
    }

    private void handleRegisterException(Exception e) {
        log.error(e.getMessage(), e);
        String msg = e.getMessage();
        if (msg.contains("account_idx_unique_open_id")) {
            // open_id被绑定
            msg = "微信号已被绑定";
        } else if (msg.contains("account_idx_unique_name")) {
            msg = "用户已存在";
        } else {
            // 未知错误
            msg = "未知错误";
        }
        throw new MemberBizException(MemberCode.REGISTER_ERROR, msg);
    }

    public List<Account> findByMobilePhone(String mobilePhoneNumber, String memberId, Integer accountType) {
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria();
        if (memberId != null) {
            criteria.andEqualTo(DuplicateString.MEMBER_ID, memberId);
        }
        if (accountType != null) {
            criteria.andEqualTo(DuplicateString.ACCOUNT_TYPE, accountType);
        }
        criteria.andEqualTo(MOBILE, mobilePhoneNumber).andEqualTo(DuplicateString.DEL_FLG, false);
        return findByCondition(condition);
    }

    public List<Account> findByMobile(String mobile) {
        return findByMobilePhone(mobile, null, null);
    }

    public Account findByAccountCode(String accountCode) {
        Account account = findByCacheField(DuplicateString.ACCOUNT_CODE, accountCode);
        if (account != null && !BooleanUtil.isTrue(account.getDelFlg()) && BooleanUtil.isTrue(account.getStatus())) {
            return account;
        }
        Condition condition = new Condition(Account.class);
        condition.createCriteria().andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE).andEqualTo(DuplicateString.STATUS, 0).andEqualTo(DuplicateString.ACCOUNT_CODE, accountCode);
        List<Account> list = mapper.selectByCondition(condition);
        account = list == null || list.isEmpty() ? null : list.get(0);
        if (account != null) {
            cachePut(account);
        }
        return account;
    }

    private AccountDTO loginSingleResultCheck(AccountLoginDTO accountLoginDTO, String key, Integer count, String driverToken, String platformType, String ip, Account account) {
        //判断是否被禁用
        this.haveDisable(account, key, count);
        //解码前端传过来的密码，得到一个新的密码字符串
        String md5pw = passwordService.buildPassword(accountLoginDTO.getPassword(), account.getPasswordSalt());
        if (CharSequenceUtil.equals(account.getPassword(),md5pw)) {
            if (count > 0) {
                bizRedisService.del(key);
            }
            if (StringUtils.isNotBlank(driverToken)) {
                account.setDriverToken(driverToken);
                log.info("set driver token3 : {}/{}", account.getAccountId(), driverToken);
                account.setMobileOs(platformType);
            }
            return getResult(account, ip);
        }
        bizRedisService.setex(key, userLoginSecond, ++count);
        log.info("手机号:{}登录，密码不正确", accountLoginDTO.getLoginName());
        throw new MemberBizException(MemberCode.LOGIN_ERROR, DuplicateString.ACCOUNT_NAME_OR_PD_INCORRECT);
    }

    private AccountDTO getResult(Account account, String ip) {
        if (account == null) {
            return null;
        }
        account.setLastLoginDate(new Date());
        account.setLastLoginIp(ip);
        //如果是第一次登陆
        if ((account.getNeedUpdatePassword() != null && account.getNeedUpdatePassword().booleanValue()) || account.getLastLoginDate() == null) {
            account.setNeedUpdatePassword(true);
        }

        updateSelective(account);

        AccountDTO accountDTO = new AccountDTO();
        BeanUtils.copyProperties(account, accountDTO);
        return accountDTO;
    }

    private void haveDisable(Account account, String key, int failCount) {
        if (account == null) {
            throw new MemberBizException(MemberCode.LOGIN_ERROR, ACCOUNT_NOT_EXISTS);
        }
        if (BooleanUtil.isTrue(account.getDelFlg())) {
            if (key != null) {
                bizRedisService.setex(key, userLoginSecond, ++failCount);
            }
            throw new MemberBizException(MemberCode.LOGIN_ERROR, ACCOUNT_NOT_EXISTS);
        }
        if ((null != account.getStatus() && account.getStatus()) || (null != account.getLockStatus() && account.getLockStatus())) {
            if (key != null) {
                bizRedisService.setex(key, userLoginSecond, ++failCount);
            }
            //该账户已被禁用
            throw new MemberBizException(MemberCode.LOGIN_DISABLE_ERROR, "您账户已被禁用,请联系管理员");
        }

        memberHasDisable(account, key, failCount);
    }

    private void memberHasDisable(Account account, String key, int failCount) {
        if (StringUtils.isNotBlank(account.getMemberId())) {
            Member member = memberMapper.findById(account.getMemberId());
            //判断会员是否被禁用
            if (null != member && member.getStatus() != null
                    && (MemberStatusEnum.CANCELLATION.getCode().equals(member.getStatus()))) {
                if (key != null) {
                    bizRedisService.setex(key, userLoginSecond, ++failCount);
                }
                //该账户已被禁用
                throw new MemberBizException(MemberCode.LOGIN_DISABLE_ERROR, "您的会员已被禁用,请联系管理员");
            }
        }
    }

    private AccountDTO loginByMobileAndPassword(AccountLoginDTO accountLoginDTO, String key, Integer count, String driverToken, String platformType, String ip) {
        List<Account> accountList = findByMobile(accountLoginDTO.getLoginName());
        if (CollUtil.isEmpty(accountList)) {
            log.info("{}账户不存在", accountLoginDTO.getLoginName());
            bizRedisService.setex(key, userLoginSecond, ++count);
            throw new MemberBizException(MemberCode.LOGIN_ERROR, DuplicateString.ACCOUNT_NAME_OR_PD_INCORRECT);
        }
        //如果手机号查询结果不只有一个账户信息
        if (!Objects.equals(MemberNumberConstant.ONE,CollUtil.size(accountList)))
            throw new BasicRuntimeException("账户异常，请联系管理员");

        return loginSingleResultCheck(accountLoginDTO, key, count, driverToken, platformType, ip, accountList.get(0));
    }


    private Integer checkLoginCount(Integer count, String key1, String key2) {
        if (key1 != null && bizRedisService.hasKey(key1)) {
            count = bizRedisService.get(key1);
            if (count >= maxRetryCount) {
                throw new MemberBizException(MemberCode.LOGIN_LOCKED, DuplicateString.TRY_AGAIN_IN_24_HOURS);
            }
        }
        if (key2 != null && bizRedisService.hasKey(key2)) {
            count = bizRedisService.get(key2);
            if (count >= maxRetryCount) {
                throw new MemberBizException(MemberCode.LOGIN_LOCKED, DuplicateString.TRY_AGAIN_IN_24_HOURS);
            }
        }
        return count;
    }

    private String getPlatformType(AccountLoginDTO accountLoginDTO) {
        String platformType;
        platformType = accountLoginDTO.getPlatformType();
        if (StringUtils.isBlank(platformType)) {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (servletRequestAttributes != null) {
                //获取请求的来源
                platformType = RequestUtils.getPlatformType(servletRequestAttributes.getRequest());
            }
        }
        return platformType;
    }

    @Override
    public List<Account> findByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<Account> result = Lists.newArrayListWithCapacity(ids.size());
        ids.forEach(item -> {
            Account account = findById(item);
            if (account != null) {
                result.add(account);
            }
        });
        return result;
    }

    @Override
    public List<String> listAccountIdByMemberCodes(List<String> memberCodes) {
        if(CollUtil.isEmpty(memberCodes))
            return Collections.emptyList();
        return accountMapper.listAccountIdByMemberCodes(memberCodes);
    }

    @Override
    public boolean existsAccountByEmail(String email, String exceptAccountId) {
        if(CharSequenceUtil.isBlank(email))
            return false;
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.EMAIL,email);
        if(CharSequenceUtil.isNotBlank(exceptAccountId)){
            criteria.andNotEqualTo(DuplicateString.ACCOUNT_ID,exceptAccountId);
        }
        return accountMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public List<Account> listByIds(List<String> ids) {
        if(CollUtil.isEmpty(ids))
            return null;
        Condition condition = new Condition(Account.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(DuplicateString.ACCOUNT_ID,ids);

        return this.findByCondition(condition);
    }

    private void saveAgreement(Account account) throws Exception {
        MQMessage<List<AccountAgreementDTO>> agreement = new MQMessage<>();
        agreement.setExchange(MqTopicConstant.SAVE_AGREEMENT_TOPIC);
        //用户协议
        AccountAgreementDTO userAgreement = new AccountAgreementDTO();
        userAgreement.setAccountId(account.getAccountId());
        userAgreement.setAgreementType(AgreementTypeEnum.USER_AGREEMENT.getType());
        userAgreement.setBusinessNo(account.getAccountId());
        userAgreement.setBusinessType(AgreementBusinessTypeEnum.ACCOUNT_ID.getType());
        //隐私协议
        AccountAgreementDTO privacyStatement = new AccountAgreementDTO();
        privacyStatement.setAccountId(account.getAccountId());
        privacyStatement.setAgreementType(AgreementTypeEnum.PRIVACY_STATEMENT.getType());
        privacyStatement.setBusinessNo(account.getAccountId());
        privacyStatement.setBusinessType(AgreementBusinessTypeEnum.ACCOUNT_ID.getType());
        agreement.setData(Arrays.asList(userAgreement,privacyStatement));

        mqProducer.send(agreement);
    }
}
