package com.cnoocshell.web.common.interceptor;

import com.cnoocshell.member.api.session.LoginInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestAttributes;

@Slf4j
@Component
public class LoginInfoArgumentResolver implements WebArgumentResolver {

    @Override
    public Object resolveArgument(MethodParameter methodParameter,
                                  NativeWebRequest webRequest) throws Exception {
        //方法参数的第一个参数
        if (methodParameter.getParameterIndex() == 0 && methodParameter.getGenericParameterType() == LoginInfo.class) {
            Object object = webRequest.getAttribute(LoginInfo.SESSION_NAME, RequestAttributes.SCOPE_SESSION);
            if(object == null){
                log.error("LoginInfo 获取为空,sessionId:{}",webRequest.getSessionId());
                return new LoginInfo();
            }
            return object;
        } else {
            return UNRESOLVED;
        }
    }
}
