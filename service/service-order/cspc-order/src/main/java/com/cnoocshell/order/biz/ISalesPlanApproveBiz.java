package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanPageApproveRequestDTO;
import com.cnoocshell.order.dao.vo.SalesPlanApproveRecord;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface ISalesPlanApproveBiz extends IBaseBiz<SalesPlanApproveRecord> {

    public PageInfo<SalesPlanApproveRecord> findSalesPlanApprovelByCondiftion(SalesPlanPageApproveRequestDTO requestDTO, List<String> categoryCode);
}
