package com.cnoocshell.integration.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.integration.dto.email.EmailSendDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name ="cspc-service-interface")
public interface IEmailService {

    @PostMapping(path = "/message/email/send", consumes = "application/json")
    ItemResult<Boolean> sendEmailCode(@RequestBody EmailSendDTO param);
}
