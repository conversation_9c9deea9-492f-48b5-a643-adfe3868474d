package com.cnoocshell.goods.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.goods.dao.vo.GoodsAttributeValue;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 
 * @Author: <EMAIL>
 * @Description  IGoodsAttributeValueBiz
 * @date   2018年8月15日 下午4:54:20
 */
public interface IGoodsAttributeValueBiz extends IBaseBiz<GoodsAttributeValue> {

    List<GoodsAttributeValue> findByGoodsIdAndAttrId(Collection<String> goodsIds,Collection<String> goodsAttriIds);

    List<GoodsAttributeValue> findByGoodsId(String goodsId);
    void copy(String fromGoodsId, String toGoodsId, String operator, Date time);

    void deleteByGoodsIdAndGoodsAttriId(String goodsId,Collection<String> notIn);
}
