package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel("提交会员购买商品意向信息-维护销售信息")
public class SubmitMemberPurchaseGoodsIntentionDTO {
    @ApiModelProperty("会员ID")
    @NotBlank(message = "会员信息不能为空")
    private String memberId;

    @ApiModelProperty("会员编码")
    private String memberCode;

    @ApiModelProperty("请求ID")
    private String requestId;

    @ApiModelProperty("CRM code")
    private String crmCode;

    @ApiModelProperty("商品意向数据")
    private List<MemberPurchaseGoodsIntentionDTO> intentions;

    @ApiModelProperty(value = "操作人ID",hidden = true)
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名",hidden = true)
    private String operatorName;
}
