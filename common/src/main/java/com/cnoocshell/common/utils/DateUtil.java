package com.cnoocshell.common.utils;

import org.apache.commons.lang.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


public class DateUtil {
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

	
	public static String getCurrentDateStr(String format) {
		SimpleDateFormat dateFormat=new SimpleDateFormat(format);
		return dateFormat.format(new Date());
	}
	
	
	public static String getDefaultDateStr() {
		SimpleDateFormat dateFormat = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
		return dateFormat.format(new Date());
	}
	
	
	public static String getDefaultDateTimeStr() {
		SimpleDateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm");
		return dateFormat.format(new Date());
	}
	public static String convertDateToString(Date aDate) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
		return dateFormat.format(aDate);
	}
	
	public static String format(Date date,String pattern) {
		SimpleDateFormat dateFormat=new SimpleDateFormat(pattern);
		return dateFormat.format(date);
	}
	/**
	 * 将Timestamp 转换成指定格式的字符串
	 *
	 * @param format 格式字符串
	 */
	public static String format(Object d, String format) {
		if (d == null) {
			return null;
		}
		if (StringUtils.isEmpty(format)) {
			return d.toString();
		}
		DateFormat sdf = new SimpleDateFormat(format);
		return sdf.format(d);
	}
	
	public static Date parse(String dateStr,String pattern) {
		if(StringUtils.isBlank(dateStr)){
			return null;
		}
		SimpleDateFormat dateFormat=new SimpleDateFormat(pattern);
		try {
			return dateFormat.parse(dateStr);
		} catch (ParseException e) {
			throw new RuntimeException("SimpleDateFormat.parse ParseException dateStr="+dateStr,e);
		}
	}
	
	
	public static int getLastDayInMonth(int year,int month) {
		Calendar cal=Calendar.getInstance();
		cal.set(Calendar.DATE, 1);
		cal.set(Calendar.MONTH, month-1);
		cal.set(Calendar.YEAR, year);
		return cal.getActualMaximum(Calendar.DATE);
	}
	
	
	public static final int daysBetween(Date early, Date late) { 
	     
        Calendar calst = Calendar.getInstance();
        Calendar caled = Calendar.getInstance();
        calst.setTime(early);
         caled.setTime(late);
         calst.set(Calendar.HOUR_OF_DAY, 0);
         calst.set(Calendar.MINUTE, 0);
         calst.set(Calendar.SECOND, 0);
         caled.set(Calendar.HOUR_OF_DAY, 0);
         caled.set(Calendar.MINUTE, 0);
         caled.set(Calendar.SECOND, 0);
         int days = ((int) (caled.getTime().getTime() / 1000) - (int) (calst   
                .getTime().getTime() / 1000)) / 3600 / 24;   
         
        return days;   
   }  
	
	
	public static final Date getFirstDayCurrentMonth() { 
	    Calendar cal = Calendar.getInstance();    
	    cal.add(Calendar.MONTH, 0);
	    cal.set(Calendar.DAY_OF_MONTH,1);
        Date firstDate = cal.getTime();
        return firstDate;
	}  
	
	public static final Date getLastDayCurrentMonth() { 
		Calendar cal = Calendar.getInstance();    
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));   
        Date lastDate = cal.getTime();
        return lastDate;
	}  
	
	/**
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static List<Date> getBetweenDates(Date startDate, Date endDate) {
		Calendar p_start = Calendar.getInstance();
		p_start.setTime(startDate);
		Calendar p_end = Calendar.getInstance();
		p_end.setTime(endDate);

		List<Date> result = new ArrayList<Date>();
		Calendar temp = (Calendar) p_start.clone();
		while (temp.before(p_end) || temp.compareTo(p_end) == 0) {
			result.add(temp.getTime());
			temp.add(Calendar.DAY_OF_YEAR, 1);
		}
		return result;
	}

	/**
	 * @param date
	 * @return
	 */
	public static String getFirstDayOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);

		cal.add(Calendar.MONTH, 0);
		cal.set(Calendar.DAY_OF_MONTH,1);
		Date firstDate = cal.getTime();

		SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
		String firstDay = sdf.format(firstDate);
		return firstDay;
	}

	/**
	 * @param date
	 * @return
	 */
	public static String getLastDaOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();    
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));   
		Date lastDate = cal.getTime();

		SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
		String lastDay = sdf.format(lastDate);
		return lastDay;
	}

	public static long getLocalTime(long timestamp,LocalTime localTime){
		return Instant.ofEpochMilli(timestamp)
				.atZone(ZoneId.systemDefault())
				.toLocalDate()
				.atTime(localTime)
				.atZone(ZoneId.systemDefault())
				.toEpochSecond()*1000;
	}

	public static LocalDateTime getLocalDateTime(long timestamp,LocalTime localTime){
		return Instant.ofEpochMilli(timestamp)
				.atZone(ZoneId.systemDefault())
				.toLocalDate()
				.atTime(localTime);
	}

	public static Date getBeginDate(Date date){
		date = date == null ? new Date() : date;
		return new Date(getLocalTime(date.getTime(),LocalTime.MIN));
	}

	public static Date getEndDate(Date date){
		date = date == null ? new Date() : date;
		return new Date(getLocalTime(date.getTime(),LocalTime.MAX));
	}
}