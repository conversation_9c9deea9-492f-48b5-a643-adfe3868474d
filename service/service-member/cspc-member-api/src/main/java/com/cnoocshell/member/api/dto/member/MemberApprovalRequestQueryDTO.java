package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 变更请求记录查询
 */
@Data
@ApiModel("会员变更请求")
public class MemberApprovalRequestQueryDTO implements Serializable {

    private static final long serialVersionUID = 7687880447783405080L;

    @ApiModelProperty("来自哪个应用(取值AppNames.platform)")
    private String appName;

    @ApiModelProperty("当前操作会员ID")
    private String queryMemberId;
    /**
     * 申请人
     */
    @ApiModelProperty("会员ID")
    private String memberId;

    /**
     * 会员code
     */
    @ApiModelProperty("会员code")
    private String memberCode;

    /**
     * 申请ID
     */
    @ApiModelProperty("申请ID")
    private String requestId;

    /**
     * 变更请求id（方便前端查询使用）
     */
    @ApiModelProperty("变更请求id（方便前端查询使用）")
    private String requestNum;

    /**
     * 会员名
     */
    @ApiModelProperty("会员名")
    private String memberName;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactName;

    /**
     * 手机号
     */
    @ApiModelProperty("联系方式")
    private String contactPhone;

    /**
     * 变更类型
     */
    @ApiModelProperty("变更类型")
    private String requestType;

    /**
     * 变更状态
     */
    @ApiModelProperty("变更状态")
    private String status;

    /**
     * 申请提交时间-起
     */
    @ApiModelProperty("申请时间-起")
    private Date requestTimeLeft;

    /**
     * 申请提交时间-止
     */
    @ApiModelProperty("申请时间-止")
    private Date requestTimeRight;

    /**
     * 是否申请企业类型
     */
    @ApiModelProperty("是否申请企业类型")
    private Boolean registerFlg;

    /**
     * 是否已处理
     */
    @ApiModelProperty("是否已处理")
    private Boolean isDeal;

    /**
     * 代注册
     */
    @ApiModelProperty("代注册 0-否，1-是 2卖家代注册")
    private Integer proxyStatus;

    @ApiModelProperty("代注册会员id")
    private String proxyMemberId;
    @ApiModelProperty("代注册会员操作的账户id")
    private String proxyAccountId;
    /**
     * 真实名称
     */
    @ApiModelProperty("实名认证-真实名称")
    private String realName;


    @ApiModelProperty("是否企业买家")
    private Integer buyerFlg;

    @ApiModelProperty("是否卖家")
    private Integer sellerFlg;

    @ApiModelProperty("是否承运商")
    private Integer carrierFlg;

    @ApiModelProperty("是否供应商")
    private Integer supplierFlg;
    /**
     * 2020.11.2 新增承运商注册字段 业务范围（或者叫业务类型），
     */
    @ApiModelProperty("承运商业务范围")
    private String businessScope;

    @ApiModelProperty("会员类型")
    private String memberType;
}
