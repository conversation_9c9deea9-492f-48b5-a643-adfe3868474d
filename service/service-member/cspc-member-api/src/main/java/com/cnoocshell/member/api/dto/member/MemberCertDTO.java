package com.cnoocshell.member.api.dto.member;

import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.session.Operator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: <EMAIL>
 * @Description
 * @Date 10/08/2018 15:41
 */
@Data
public class MemberCertDTO implements Serializable {

    private static final long serialVersionUID = -6761417192204515110L;

    /**
     * 资质ID
     */
    @ApiModelProperty("资质历史记录表主键ID")
    private String id;


    /**
     * 资质ID
     */
    @ApiModelProperty("资质ID")
    private String certId;

    /**
     * 会员id
     */
    @ApiModelProperty("会员ID，一般资质时，该字段不可为空")
    private String memberId;

    /**
     * 账户id
     */
    @ApiModelProperty("账户ID，身份证时，该字段不可为空")
    private String accountId;

    /**
     * 附件ID
     */
    @ApiModelProperty("附件ID, 不可为空")
    private String attachmentId;

    /**
     * 是否默认资质
     */
    @ApiModelProperty("是否默认资质")
    private Integer isDefault;

    /**
     * 资质名称
     */
    @ApiModelProperty("资质名称")
    private String certName;

    /**
     * 资质类型
     */
    @ApiModelProperty("资质类型")
    private String certType;

    /**
     * 资质的状态
     */
    @ApiModelProperty("资质状态")
    private String status;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名，如果是身份证，该字段不可为空")
    private String realName;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idNumber;

    /**
     * 民族
     */
    @ApiModelProperty("民族")
    private String ethnicity;

    /**
     * 籍贯
     */
    @ApiModelProperty("籍贯")
    private String nativePlace;

    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty("紧急联系人姓名")
    private String contactName;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty("紧急联系人电话")
    private String contactPhone;

    /**
     * 开始时间
     */
    @ApiModelProperty("有效开始时间")
    private Date effectiveStartTime;
    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Date effectiveTime;

    /**
     * 资质文件
     */
    @ApiModelProperty("资质文件，更新/创建资质时，不用传此参数")
    private List<MemberCertAttachmentDTO> memberCertAttachmentDTOS;

    /**
     * 实名认证时返回旧的身份证信息
     */
    @ApiModelProperty("实名认证时返回旧的身份证信息")
    private MemberCertDTO oldInfo;

    /**
     * 实名认证时返回旧的身份证信息-变更的字段集合
     */
    @ApiModelProperty("实名认证时返回旧的身份证信息-变更的字段集合")
    private Set<String> changeFields;

    /**
     * 是否新增资质 getMemberApprovalCertDetails 使用
     */
    @ApiModelProperty("是否新增资质 getMemberApprovalCertDetails 使用")
    private Boolean newCert;

    /**
     * 操作者id
     */
    @ApiModelProperty("操作者id")
    private String operatorId;

    /**
     * 操作者
     */
    @ApiModelProperty("操作者")
    private Operator operator;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("会员注册时间")
    private Date memberRegTime;

    @ApiModelProperty("实名认证资料是否需要审批")
    private Boolean realNameCertNoApproval;

    @ApiModelProperty("实名认证类型（个人 or 司机 默认为个人）")
    private ApproveRequestTypeEnum requestTypeEnum;

    /**
     * 车辆载重大于4.5吨则需要道路运输经营许可证
     */
    @ApiModelProperty("车辆载重大于4.5吨则需要道路运输经营许可证(0-不需要上传，1-需要上传)")
    private Integer needCert;

    /**
     * 道路运输经营许可证号码
     */
    @ApiModelProperty("道路运输经营许可证号码")
    private String roadLicenseNo;

    /**
     * 道路运输经营许可证 注册地址
     */
    @ApiModelProperty("道路运输经营许可证 注册地址")
    private String roadAddress;

    /**
     * 道路运输经营许可证 经营范围
     */
    @ApiModelProperty("道路运输经营许可证 经营范围")
    private String roadBusinessScope;

    /**
     * 道路运输经营许可证 业务名称
     */
    @ApiModelProperty("道路运输经营许可证 业务名称")
    private String roadBusinessName;

    /**
     * 证件类型(驾驶证,从业资格证等)
     */
    @ApiModelProperty("证件类型(驾驶证,从业资格证等)")
    private String subCertType;

    /**
     * 发证机关
     */
    @ApiModelProperty("发证机关")
    private String issuingAuthority;

    @ApiModelProperty("排序码")
    private Integer orderNum;
}
