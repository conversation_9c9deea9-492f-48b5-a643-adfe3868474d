package com.cnoocshell.base.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.LinkedList;
import java.util.List;

/**
 * @description: 值集树
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ValueSetTreeDTO extends ValueSetOptionDTO{

    /**
     * 值集树
     */
    @ApiModelProperty("值集树")
    private List<ValueSetTreeDTO> treeList = new LinkedList<ValueSetTreeDTO>();
}
