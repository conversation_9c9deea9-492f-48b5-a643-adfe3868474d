
package com.cnoocshell.web.common.interceptor;

import com.cnoocshell.common.utils.CommonConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

@Slf4j
public class RestTemplateInterceptor implements RequestInterceptor {

    @Override
    public void apply(final RequestTemplate requestTemplate) {
        try{
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if(null != attributes) {
                HttpServletRequest request = attributes.getRequest();

//            log.info("sessionId:"+request.getHeader(CommonConstants.X_AUTH_TOKEN));
                requestTemplate.header(CommonConstants.X_AUTH_TOKEN,
                        ReadCookieMap(request, "SESSION"));
            }
        }catch (Exception e){
            log.error("添加session异常：",e);
        }

    }

    private String ReadCookieMap(HttpServletRequest request,String key) {
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                if(key.equals(cookie.getName())){
                    return cookie.getValue();
                }
            }
        }
        return "";
    }

}
