package com.cnoocshell.web.common.cros;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.DefaultCorsProcessor;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

@Slf4j
public class CustomCorsProcessor extends DefaultCorsProcessor {

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
    private final Map<String, Pattern> PATTERN_MAP = new ConcurrentHashMap<>(1);

    /**
     * 跨域请求，会通过此方法检测请求源是否被允许
     *
     * @param config        CORS 配置
     * @param requestOrigin 请求源
     * @return 如果请求源被允许，返回请求源；否则返回 null
     */
    @Override
    protected String checkOrigin(CorsConfiguration config, String requestOrigin) {
        // 先调用父类的 checkOrigin 方法，保证原来的方式继续支持
        String result = super.checkOrigin(config, requestOrigin);
        if (result != null) {
            return result;
        }

        // 获取配置的 origins
        List<String> allowedOrigins = config.getAllowedOrigins();
        if(CollUtil.isEmpty(allowedOrigins)){
            allowedOrigins = config.getAllowedOriginPatterns();
        }
        if (CollectionUtils.isEmpty(allowedOrigins)) {
            log.info("allowedOrigins is null,requestOrigin:{}",requestOrigin);
            return null;
        }

        result = checkOriginWithRegex(allowedOrigins, requestOrigin);
        if (result != null) {
            return result;
        }
        return  checkOriginWithAntPathMatcher(allowedOrigins,requestOrigin);
    }

    /**
     * 用正则的方式来校验 requestOrigin
     */
    private String checkOriginWithRegex(List<String> allowedOrigins, String requestOrigin) {
        for (String allowedOrigin : allowedOrigins) {
            if(ReUtil.isMatch(allowedOrigin,requestOrigin)){
                return requestOrigin;
            }
        }
        log.info("checkOriginWithRegex result is null,requestOrigin:{}",requestOrigin);
        return null;
    }

    /**
     * Ant 风格的路径匹配
     */
    private String checkOriginWithAntPathMatcher(List<String> allowedOrigins, String requestOrigin) {
        for (String allowedOrigin : allowedOrigins) {
            if (antPathMatcher.match(allowedOrigin,requestOrigin)) {
                return requestOrigin;
            }
        }
        log.info("checkOriginWithAntPathMatcher result is null,requestOrigin:{}",requestOrigin);
        return null;
    }
}
