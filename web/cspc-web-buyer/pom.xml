<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>web</artifactId>
		<groupId>com.cnoocshell</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>cspc-web-buyer</artifactId>

	<packaging>jar</packaging>
	<name>cspc-web-buyer</name>
	<description>Web Buyer Microservice</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>
    
	<dependencies>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-web-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-order-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-goods-api</artifactId>
		</dependency>


		<dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>kafka-spring-boot-starter</artifactId>
        </dependency>
		<!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.cuisongliu</groupId>
			<artifactId>kaptcha-spring-boot-autoconfigure</artifactId>
			<version>1.3</version>
		</dependency>
		<!--poi-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>cspc-web-buyer</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<!-- spring热部署-->
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.8.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
