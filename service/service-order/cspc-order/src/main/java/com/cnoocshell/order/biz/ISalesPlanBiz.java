package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;

public interface ISalesPlanBiz extends IBaseBiz<SalesPlan> {

    PageInfo<SalesPlan> findSalesPlanByCondiftion(SalesPlanPageRequestDTO requestDTO, List<String> categoryCode);

    List<SalesPlan> listByNos(List<String> nos);

    SalesPlan getByNo(String no);

    void updateRemainSellAbleQuantity(String salePlanNo, BigDecimal dealTotalQuantity);

    /**
     * 更新销售计划是否已创建询价标记
     *
     * @param salePlanNo       销售计划编号
     * @param isCreatedInquiry 1:已创建询价 0:未创建询价
     * @return true:更新成功 false:更新失败
     */
    Boolean updateCreatedInquiry(String salePlanNo, Integer isCreatedInquiry);
}
