package com.cnoocshell.mq.core.handler;

import com.cnoocshell.mq.core.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.OrderComparator;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Slf4j
@Component
public class MessageHandlerFactory implements ApplicationContextAware {
    private List<MessageHandler> messageHandlers = new ArrayList<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (ClassUtils.isPresent(
                "com.cnoocshell.mq.core.handler.MessageHandler",
                null)) {
            Map<String, MessageHandler> map = applicationContext.getBeansOfType(MessageHandler.class);
            if (map != null && map.size() > 0) {
                List<MessageHandler> list = new ArrayList(map.values());
                OrderComparator.sort(list);
                this.messageHandlers =  list;
            }
        }
    }

    /**
     * 找出对应的操作方法
     * @param mqMessage
     */
    public boolean messageHandle(MQMessage mqMessage){
        for(MessageHandler handler:messageHandlers){
            if(handler.handleType().equals(mqMessage.getMessageType())){
                return handler.handle(mqMessage);
            }
        }
        return false;
    }

    public MessageHandler getMessageHandler(String queueName) throws Exception {
        for(MessageHandler handler:messageHandlers){
            if(handler.handleType().equals(queueName)){
                return handler;
            }
        }

        throw new Exception("未找到对应message handler["  + queueName + "]");
    }

}
