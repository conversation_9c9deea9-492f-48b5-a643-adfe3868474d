package com.cnoocshell.integration.dto.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

@Data
public class SMSRespDTO {
    @ApiModelProperty("请求状态码。返回200代表请求成功")
    private String code;

    @ApiModelProperty("状态码的描述。")
    private String message;

    @ApiModelProperty("问题排查时，提供该字段值给短信平台")
    private String requestId;

    @ApiModelProperty("返回数据")
    private SMSRespDataDTO data;

    @Data
    public static class SMSRespDataDTO{
        @ApiModelProperty("每⼀批短信编号。该值不为空，说明平台\n" +
                "已经接收提交的短信任务\n")
        private String msgId;

        @ApiModelProperty(value = "提交失败的⼿机号",notes = "示例\n" +
                "1813757832089,1813759932088,18737\n" +
                "57932087")
        private String failPhones;

        @ApiModelProperty("状态描述")
        private String desc;
    }
}
