package com.cnoocshell.web.base.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.cnoocshell.base.api.dto.role.RoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.enums.RoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.enums.ApplicationType;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonConstants;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.common.utils.MD5;
import com.cnoocshell.common.utils.SecureRandomStringUtil;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.integration.service.IWechatService;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.api.dto.member.MemberSimpleDTO;
import com.cnoocshell.member.api.enums.RegisterTypeEnum;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.api.service.IAccountLoginInfoService;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.base.dto.account.AccountRestPasswordDTO;
import com.cnoocshell.web.base.dto.account.LoginDTO;
import com.cnoocshell.web.base.util.ExceptionUtils;
import com.cnoocshell.web.base.util.IPUtils;
import com.cnoocshell.common.constant.HeaderConstant;
import com.google.code.kaptcha.Producer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * web端控制器，账户相关功能对外服务接口
 */

@Api(tags = {"AccountAnonV2Controller"}, description = "账户注册、登录、忘记密码,该接口可匿名访问(各接口请使用对应的验证码接口)")
@Slf4j
@RestController
@RequestMapping("/anon/account/v2")
@RequiredArgsConstructor
public class AccountAnonV2Controller {
    private static final String REDIS_KEY_PREFIX = "web_base:";

    /**
     * 验证码种类
     */
    private static final String SMS_CODE_REGISTER_PC = "2";
    //登录使用
    private static final String SMS_CODE_LOGIN = "4";
    private static final String SMS_CODE_LOGIN_PHONE_CODE = "6";

    private static final String DESCRIPTION_LOGIN_ERROR = "用户名或密码错误";
    private static final String DESCRIPTION_PHONE_ERROR = "手机号错误";
    private static final String DESCRIPTION_PHONE_OR_CODE_ERROR = "手机号或验证码错误";
    private static final String DESCRIPTION_CODE_ERROR = "验证码错误/已过期";
    private static final String DESCRIPTION_PHONE_EXIST = "手机号已被注册";
    private static final String LOG_MOBILE_CAPTCHA_CODE = "mobile: {},captchaCode: {}";
    private static final String DESCRIPTION_ACCOUNT_NOT_EXIST = "手机号对应的账号不存在";
    private static final String DESCRIPTION_ID_ERROR = "身份证号错误";


    private static final String ACCOUNT_NAME = "accountName";
    private static final String DRIVER_APP = "driverApp";
    private static final String SELLER_APP = "sellerApp";
    private static final String PHONE_NUMBER_REQUEST_SESSION_CAPTCHA_NEED = "captchaNeed";
    private static final String LOGIN_FAIL_COUNT = "login_fail_count";
    private static final String PHONE_NUMBER_REQUEST_SESSION_CAPTCHA = "captchaKey";
    private static final String CHECK_REAL_NAME_CERTIFICATION = "checkRealNameCertification";
    /**
     * 宽松校验，可能以后会新增号段
     */
    private static final String PHONE_NUMBER_REG = "^1[3-9]\\d{9}$";
    private static final String USERNAME_PATTERN = "^$|^[a-zA-Z][a-zA-Z0-9\\w]{5,19}$";
    private static final String ID_NUMBER_REGEX = "/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/";
    private static final String EMAIL_REG = "^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$";


    /**
     * 手机验证码过期时间 3分钟 ms
     */
    private static final long PHONE_SMSCODE_TIME = 3 * 60 * 1000L;
    private static final String PHONE_NUMBER_REQUEST_SESSION_SMSCODE = "smscodeKey";
    private static final String PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME = "smscodeKeyTime";
    private static final String PHONE_NUMBER_REQUEST_SESSION_PHONENUM = "phoneNum";

    @Value("${account.login.captcha.disable:false}")
    private boolean disableCaptcha = false;
    @Value("${account.register.disable:false}")
    private boolean registerDisable = false;
    @Value("${account.login.smscode.disable:false}")
    private boolean disableSMSCode = false;

    //图形验证码服务
    @Autowired
    @Qualifier("kaptcha")
    private Producer producer;

    private final BizRedisService bizRedisService;
    private final CaptchaService captchaService;
    private final IMemberService memberService;
    private final IRoleService roleService;
    private final IAccountService accountService;
    private final ISmsSendService iSmsSendService;
    private final IWechatService wechatService;
    private final IAccountLoginInfoService iAccountLoginInfoService;

    @ApiOperation("用户名密码登录")
    @PostMapping("/login")
    public ItemResult<LoginInfo> login(@Valid @RequestBody LoginDTO loginDTO,
                                       @ApiIgnore HttpServletRequest request) throws Exception {
        log.info("AccountAnonV2Controller login loginDTO:{}", loginDTO);
        loginDTO.setRemoteIp(IPUtils.getIpAddr(request));

        log.info("loginDTO:{}", JSON.toJSONString(loginDTO));
        ItemResult<LoginInfo> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode(MemberCode.LOGIN_ERROR_CAPTCHA_NEED.getCode());
        result.setDescription(DESCRIPTION_LOGIN_ERROR);
        result.setData(null);

        String loginType = loginDTO.getLoginType();
        loginDTO.setLoginType("web".equals(loginType) ? null : loginType);

        //登录失败次数 redis key
        String loginFailCountKey = CharSequenceUtil.format("{}_{}_{}", LOGIN_FAIL_COUNT, SMS_CODE_LOGIN, loginDTO.getLoginName());
        //登录失败需要图形验证码 redis key
        String captchaNeedKey = CharSequenceUtil.format("{}_{}_{}", PHONE_NUMBER_REQUEST_SESSION_CAPTCHA_NEED, SMS_CODE_LOGIN, loginDTO.getLoginName());


        AccountLoginDTO accountLoginDTO = new AccountLoginDTO();
        BeanUtils.copyProperties(loginDTO, accountLoginDTO);
        accountLoginDTO.setPassword(loginDTO.getPassword());
        accountLoginDTO.setLoginName(loginDTO.getLoginName());
        accountLoginDTO.setDriverToken(loginDTO.getDriverToken());
        Integer captchaNeed = bizRedisService.get(captchaNeedKey);
        if (captchaNeed != null && captchaNeed == 1) {
            if (StringUtils.isBlank(loginDTO.getCaptcha())) {
                result.setDescription("验证码不可为空");
                return result;
            }

            CaptchaVO vo = new CaptchaVO();
            vo.setCaptchaVerification(loginDTO.getCaptcha());
            ResponseModel verification = captchaService.verification(vo);
            boolean success = verification.isSuccess();
            if (!success) {
                throw new BizException(MemberCode.VALIDATION_ERROR, DESCRIPTION_CODE_ERROR);
            }
        }
        AccountDTO dto = null;

        try {
            updateLoginDTOInfo(accountLoginDTO, request);
            log.info("=======> session Id:" + request.getSession().getId());
            //登陆
            dto = accountService.loginVerification(accountLoginDTO);
        } catch (Exception e) {
            log.error("登录异常", e);
            if (handleLockedAccount(e,result,captchaNeedKey)) {
                return result;
            }
            if (handleLoginFailLock(e,captchaNeedKey)){
                result.setDescription("账户已被锁定，请24小时后再试");
                return result;
            }
            //表示被禁用, 或者表示只用用户名登录
            handleDisabledAccount(e);

            handleLoginDisableError(e,loginFailCountKey,captchaNeedKey);

            if (handleLoginFailCount(captchaNeedKey,loginFailCountKey)){
                return result;
            }
            throw e;
        }

        //登录成功，清除redis中的失败次数和验证码需要标志
        bizRedisService.del(loginFailCountKey);
        bizRedisService.del(captchaNeedKey);

        validateLogin(dto);

        LoginInfo loginInfo = getLoginInfo(dto, loginDTO.getDriverToken(), request);

        //获取openid
        getOpenId(dto.getAccountId(), dto.getWechatId(), loginDTO.getWxCode());

        return new ItemResult<>(loginInfo);
    }

    private void validateLogin(AccountDTO dto){
        if (dto == null || dto.getAccountId() == null) {
            throw new BizException(MemberCode.LOGIN_ERROR, DESCRIPTION_LOGIN_ERROR);
        }
    }

    private boolean handleLockedAccount(Exception e, ItemResult<LoginInfo> result,String captchaNeedKey) {
        if (e.getMessage() != null && e.getMessage().contains("账户已被锁定")) {
            bizRedisService.setex(captchaNeedKey, 60, 1);
            result.setDescription("账户已被锁定，请24小时后再试");
            return true;
        }
        return false;
    }

    private void handleDisabledAccount(Exception e) throws Exception {
        if (e.getMessage() != null && e.getMessage().contains("您账户已被禁用,请联系管理员")) {
            throw e;
        }
        if (e.getCause() != null && e.getCause().getMessage() != null && e.getMessage().contains("您账户已被禁用,请联系管理员")) {
            throw e;
        }
    }

    private void handleLoginDisableError(Exception e,String loginFailCountKey,String captchaNeedKey) throws Exception {
        if (e.getMessage() != null && (e.getMessage().contains("LOGIN_DISABLE_ERROR") || e.getMessage().contains("该手机号关联多个账号，请使用用户名登录"))) {
            bizRedisService.del(loginFailCountKey);
            bizRedisService.del(captchaNeedKey);
            throw e;
        }
        if (e.getCause() != null && e.getCause().getMessage() != null && (e.getCause().getMessage().contains("LOGIN_DISABLE_ERROR") || e.getCause().getMessage().contains("该手机号关联多个账号，请使用用户名登录"))) {
            bizRedisService.del(loginFailCountKey);
            bizRedisService.del(captchaNeedKey);
            throw e;
        }
    }

    private boolean handleLoginFailCount(String captchaNeedKey,String loginFailCountKey) {
        Integer loginFailCount = bizRedisService.get(loginFailCountKey);
        if (null != loginFailCount && loginFailCount >= 3) {
            bizRedisService.setex(captchaNeedKey, 60, 1);
            return true;
        } else {
            int count = loginFailCount == null ? 1 : loginFailCount + 1;
            bizRedisService.setex(loginFailCountKey, 60, count);
        }
        return false;
    }

    private boolean handleLoginFailLock(Exception e,String captchaNeedKey){
        Exception e2 = ExceptionUtils.convert(e);
        if (e2 instanceof BizException &&
                e2.getMessage() != null &&
                e2.getMessage().contains("账户已被锁定")) {
            bizRedisService.setex(captchaNeedKey, 60, 1);
            return true;
        }
        return false;
    }

    @ApiOperation("根据手机号和验证码登录")
    @PostMapping(value = "/loginByPhoneVerificationCode")
    public ItemResult<LoginInfo> loginByPhoneVerificationCode(@Valid @RequestBody PhoneCodeLoginDTO phoneCodeLoginDTO,
                                                              @ApiIgnore HttpServletRequest request) {
        log.info("AccountAnonV2Controller loginByPhoneVerificationCode phoneCodeLoginDTO:{}", phoneCodeLoginDTO);
        log.info("loginByPhoneVerificationCode x-auth-token:{} sessionId:{}",
                request.getHeader(CommonConstants.X_AUTH_TOKEN),request.getSession().getId());
        ItemResult<LoginInfo> result = new ItemResult<>(null);

        if (!ReUtil.isMatch(PHONE_NUMBER_REG, phoneCodeLoginDTO.getPhone())) {
            result.setSuccess(false);
            result.setCode(MemberCode.VALIDATION_ERROR.getCode());
            result.setDescription(DESCRIPTION_PHONE_OR_CODE_ERROR);
            result.setData(null);
            return result;
        }
        if(!BooleanUtil.isTrue(accountService.checkMobilePhoneExists(phoneCodeLoginDTO.getPhone())))
            throw new BizException(BasicCode.CUSTOM_ERROR,DESCRIPTION_PHONE_ERROR);

        checkSMSCode(SMS_CODE_LOGIN_PHONE_CODE, phoneCodeLoginDTO.getPhone(), phoneCodeLoginDTO.getVerificationCode(), request);

        updateLoginDTOInfo(phoneCodeLoginDTO, request);
        AccountDTO dto = accountService.loginByPhoneVerificationCode(phoneCodeLoginDTO);
        log.info("AccountAnonV2Controller loginByPhoneVerificationCode accountDTO:{}", dto);
        if (dto == null || dto.getAccountId() == null) {
            result.setSuccess(false);
            result.setCode(MemberCode.LOGIN_ERROR.getCode());
            result.setDescription("登录失败");
            result.setData(null);
            return result;
        }
        //获取openid
        getOpenId(dto.getAccountId(), dto.getWechatId(), phoneCodeLoginDTO.getWxCode());
        return new ItemResult<>(getLoginInfo(dto, phoneCodeLoginDTO.getDriverToken(), request));
    }

    @ApiOperation("微信登录并绑定账号-获取手机验证码(先校验图形验证码，验证通过则发送手机验证码)")
    @GetMapping("/loginByPhoneVerificationCode/getSMSCode")
    public ItemResult<Boolean> loginByPhoneVerificationCodeGetSMSCode(String mobile, @ApiIgnore HttpServletRequest request) {
        log.info("loginByPhoneVerificationCodeGetSMSCode mobile:{}",mobile);
        log.info("loginByPhoneVerificationCodeGetSMSCode x-auth-token:{} sessionId:{}",
                request.getHeader(CommonConstants.X_AUTH_TOKEN),request.getSession().getId());
        if(!BooleanUtil.isTrue(accountService.checkMobilePhoneExists(mobile)))
            throw new BizException(BasicCode.CUSTOM_ERROR,DESCRIPTION_PHONE_ERROR);

        return sendSMSCode(SMS_CODE_LOGIN_PHONE_CODE, mobile, request);
    }

    @ApiOperation("微信登录并绑定账号-校验手机验证码")
    @GetMapping("/loginByPhoneVerificationCode/checkSMSCode")
    public ItemResult<Boolean> loginByPhoneVerificationCodeCheckSMSCode(String mobile, String smsCode, @ApiIgnore HttpServletRequest request) {
        log.info("loginByPhoneVerificationCodeCheckSMSCode x-auth-token:{} sessionId:{}",
                request.getHeader(CommonConstants.X_AUTH_TOKEN),request.getSession().getId());
        return checkSMSCode(SMS_CODE_LOGIN_PHONE_CODE, mobile, smsCode, request);
    }


    @ApiOperation("获取一个sessionid")
    @GetMapping("/getSessionId")
    public ItemResult<String> getSessionId(@ApiIgnore HttpServletRequest request) {
        log.info("getSessionId x-auth-token:{} sessionId:{}",
                request.getHeader(CommonConstants.X_AUTH_TOKEN),request.getSession().getId());
        return new ItemResult<>(request.getSession().getId());
    }

    @ApiOperation("检查手机号是否存在账户中")
    @GetMapping("/checkMobileByMasterAndPersonal")
    public ItemResult<Boolean> checkMobileByMasterAndPersonal(String mobile) {
        ItemResult<Boolean> result = new ItemResult<>(null);
        result.setSuccess(true);
        result.setData(true);
        if (StringUtils.isBlank(mobile) || !ReUtil.isMatch(PHONE_NUMBER_REG, mobile)) {
            result.setDescription("请输入正确手机号");
            return result;
        }
        boolean existsAccount = accountService.checkMobilePhoneExists(mobile);
        if (BooleanUtil.isTrue(existsAccount)) {
            result.setDescription("用户已存在");
            return result;
        }
        result.setData(false);
        result.setDescription("可以注册");
        return result;
    }

    @ApiOperation("PC注册-获取手机验证码(先校验图形验证码，验证通过则发送手机验证码)")
    @GetMapping("/register/pc/getSMSCode")
    public ItemResult<Boolean> registerAccountByPCGetSMSCode(String mobile, @ApiIgnore HttpServletRequest request) {
        if (registerDisable) {
            return registerDisableReturn();
        }
        log.info("注册账号 mobile:{}", mobile);
        return sendSMSCode(SMS_CODE_REGISTER_PC, mobile, request);
    }

    @ApiOperation("PC注册,手机唯一,账户唯一，必须是手机未注册过个人账户")
    @PostMapping("/register/pc")
    public ItemResult<AccountDTO> registerAccountByPC(@Valid @RequestBody AccountPCRegisterDTO dto, @ApiIgnore HttpServletRequest request) {
        if (registerDisable) {
            return registerDisableReturn();
        }
        ItemResult<AccountDTO> result = new ItemResult<>();
        if (StringUtils.isBlank(dto.getMobile()) || !ReUtil.isMatch(PHONE_NUMBER_REG, dto.getMobile()) || StringUtils.isBlank(dto.getSmsCode())) {
            result.setSuccess(false);
            result.setCode(MemberCode.VALIDATION_ERROR.getCode());
            result.setDescription(DESCRIPTION_PHONE_OR_CODE_ERROR);
            result.setData(null);
            return result;
        }

        checkSMSCode(SMS_CODE_REGISTER_PC, dto.getMobile(), dto.getSmsCode(), request);

        if (accountService.checkMobilePhoneExistsByPersonl(dto.getMobile())) {
            result.setSuccess(false);
            result.setCode(MemberCode.REGISTER_ERROR.getCode());
            result.setDescription(DESCRIPTION_PHONE_EXIST);
            result.setData(null);
            return result;
        }
        //小程序来源
        if(ApplicationType.MP.getType().equals(request.getHeader(HeaderConstant.APPLICATION_TYPE))){
            dto.setRegisterType(RegisterTypeEnum.MINI_APP.getType());
        }

        ItemResult<AccountDTO> resp = accountService.registerAccountByPC(dto);
        //PC端注册返回登录信息
        if(Objects.nonNull(resp) && Objects.nonNull(resp.getData())  && !ApplicationType.MP.getType().equals(request.getHeader(HeaderConstant.APPLICATION_TYPE))){
            AccountDTO account = resp.getData();
            //返回登录信息
            resp.getData().setLoginInfo(this.getLoginInfo(resp.getData(),null,request));
            //手动增加一条登录记录
            AccountLoginInfoDTO param = new AccountLoginInfoDTO();
            param.setSessionId(request.getSession().getId());
            param.setAccountId(account.getAccountId());
            param.setLoginName(account.getMobile());
            param.setLoginType(PhoneCodeLoginDTO.class.getSimpleName());
            param.setLoginTime(new Date());
            param.setLoginComment("用户注册登录成功");
            param.setIp(IPUtils.getIpAddr(request));
            param.setMac(IPUtils.getMac(request));
            param.setUserAgent(IPUtils.getHeaderUserAgent(request));
            param.setTerminal(ApplicationType.PC.getType());

            iAccountLoginInfoService.insertLoginInfo(param);
        }
        return resp;
    }

    @ApiOperation("判断账户名，如果存在或则不合法则返回true")
    @GetMapping("/checkName")
    public ItemResult<Boolean> checkAccountNameExists(String accountName, @ApiIgnore HttpServletRequest request) {
        log.info("判断账户名: {}", accountName);

        if (StringUtils.isBlank(accountName)) {
            return new ItemResult<>(Boolean.FALSE);
        }
        if (StringUtils.isBlank(accountName) || accountName.length() < 5 || !ReUtil.isMatch(USERNAME_PATTERN, accountName)) {
            log.info("用户名不合法");
            throw new BizException(BasicCode.INVALID_PARAM, "【用户名不合法】");
        }
        return new ItemResult<>(accountService.checkAccountNameExists(accountName));
    }

    @ApiOperation("判断手机号是否存在，手机号不唯一")
    @GetMapping(value = "/checkMobilePhoneExistsForWeb")
    public ItemResult<Boolean> checkMobilePhoneExistsForWeb(String mobile, @ApiIgnore HttpServletRequest request) {
        log.info("checkMobilePhoneExistsForWeb:{}", mobile);
        return new ItemResult<>(accountService.checkMobilePhoneExists(mobile));
    }

    @ApiOperation("判断当前token是否已登陆")
    @GetMapping("/isLogin")
    public ItemResult<Boolean> isLogin(@ApiIgnore LoginInfo loginInfo) {
        if (loginInfo == null || loginInfo.getAccountId() == null) {
            return new ItemResult<>(false);
        }
        return new ItemResult<>(true);
    }

    private ItemResult<Boolean> checkSMSCode(String key, String mobile, String smsCode, HttpServletRequest request) {
        if (disableSMSCode) {
            return new ItemResult<>(true);
        }
        key = StringUtils.isBlank(key) ? "" : MD5.getMD5(key);
        Object smsCode_ = request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE + key);
        if (smsCode_ == null) {
            log.info("验证码不存在,sessionId: {}", request.getSession().getId());
            throw new BizException(MemberCode.MEMBER_OTHER_ERROR, DESCRIPTION_CODE_ERROR);
        }

        long smsCodeTime = (Long) request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME + key);
        if (System.currentTimeMillis() - smsCodeTime > PHONE_SMSCODE_TIME) {
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE + key);
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME + key);
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM + key);
            log.info("验证码已失效,请重新获取,sessionId: {}", request.getSession().getId());
            throw new BizException(MemberCode.MEMBER_OTHER_ERROR, DESCRIPTION_CODE_ERROR);
        }

        log.info("验证码剩余有效时间:{}ms,sessionId: {}", (PHONE_SMSCODE_TIME - System.currentTimeMillis() + smsCodeTime), request.getSession().getId());


        if (!StringUtils.equalsIgnoreCase(smsCode_.toString(), smsCode)) {
            log.info("smsCode验证码错误,smsCode1: {},smsCode2: {},sessionId: {}", smsCode_.toString(), smsCode, request.getSession().getId());
            throw new BizException(MemberCode.MEMBER_OTHER_ERROR, DESCRIPTION_CODE_ERROR);
        }
        if (StringUtils.isNotBlank(mobile)) {
            String phoneNum = (String) request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM + key);
            if (!StringUtils.equals(mobile, phoneNum)) {
                log.info("验证码和手机号不匹配,mobile: {},phoneNum: {},sessionId: {}", mobile, phoneNum, request.getSession().getId());
                throw new BizException(MemberCode.MEMBER_OTHER_ERROR, DESCRIPTION_CODE_ERROR);
            }
        }
        return new ItemResult<>(true);
    }


    private void updateLoginDTOInfo(Object obj, HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        String ip = IPUtils.getIpAddr(request);
        String mac = IPUtils.getMac(request);
        String userAgent = IPUtils.getHeaderUserAgent(request);
        if (obj instanceof PhoneCodeLoginDTO) {
            PhoneCodeLoginDTO dto = (PhoneCodeLoginDTO) obj;
            dto.setSessionId(sessionId);
            dto.setIp(ip);
            if (StringUtils.isNotBlank(dto.getMac())) {
                dto.setMac(mac);
            }
            dto.setTerminal(getHeaderValueOrderDefault(HeaderConstant.APPLICATION_TYPE, ApplicationType.PC.getType(),request));
            dto.setUserAgent(userAgent);
        } else if (obj instanceof AccountLoginDTO) {
            AccountLoginDTO dto = (AccountLoginDTO) obj;
            dto.setSessionId(sessionId);
            dto.setIp(ip);
            if (StringUtils.isNotBlank(dto.getMac())) {
                dto.setMac(mac);
            }
            dto.setTerminal(getHeaderValueOrderDefault(HeaderConstant.APPLICATION_TYPE, ApplicationType.PC.getType(),request));
            dto.setUserAgent(userAgent);
        } else if (obj instanceof AccountWeChatLoginAndBindingDTO) {
            AccountWeChatLoginAndBindingDTO dto = (AccountWeChatLoginAndBindingDTO) obj;
            dto.setSessionId(sessionId);
            dto.setIp(ip);
            if (StringUtils.isNotBlank(dto.getMac())) {
                dto.setMac(mac);
            }
            dto.setTerminal(getHeaderValueOrderDefault(HeaderConstant.APPLICATION_TYPE, ApplicationType.PC.getType(),request));
            dto.setUserAgent(userAgent);
        } else if (obj instanceof AccountWeChatLoginDTO) {
            AccountWeChatLoginDTO dto = (AccountWeChatLoginDTO) obj;
            dto.setSessionId(sessionId);
            dto.setIp(ip);
            if (StringUtils.isNotBlank(dto.getMac())) {
                dto.setMac(mac);
            }
            dto.setTerminal(getHeaderValueOrderDefault(HeaderConstant.APPLICATION_TYPE, ApplicationType.PC.getType(),request));
            dto.setUserAgent(userAgent);
        }
        log.info("updateLoginDTOInfo: {}", JSON.toJSONString(obj));
    }

    private LoginInfo getLoginInfo(AccountDTO accountDTO, String driverToken, HttpServletRequest request) {

        LoginInfo loginInfo = new LoginInfo(accountDTO);
        loginInfo.setSessionId(request.getSession().getId());
        loginInfo.setClientIP(IPUtils.getIpAddr(request));
        loginInfo.setUserAgent(IPUtils.getHeaderUserAgent(request));
        loginInfo.setDriverToken(driverToken);

        //默认buyerFlg为0
        loginInfo.setBuyerFlg(0);

        //默认为个人买家
        loginInfo.setRoleList(Lists.newArrayList(BaseRoleTypeEnum.BUYER_PERSONAL.getRoleCode()));
        loginInfo.setRoleNameList(Lists.newArrayList(BaseRoleTypeEnum.BUYER_PERSONAL.getRoleName()));

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(accountDTO.getMemberId());
        log.info("memberSimpleDTO:{}", memberSimpleDTO);
        loginInfo.setHasErp(false);
        if (memberSimpleDTO != null) {
            loginInfo.setMemberType(memberSimpleDTO.getMemberType());
            loginInfo.setMemberCode(memberSimpleDTO.getMemberCode());
            loginInfo.setSellerFlg(memberSimpleDTO.getSellerFlg());
            loginInfo.setCarrierFlg(memberSimpleDTO.getCarrierFlg());
            loginInfo.setSupplierFlg(memberSimpleDTO.getSupplierFlg());

            loginInfo.setSellerType(memberSimpleDTO.getSellerType());
            loginInfo.setCarrierType(memberSimpleDTO.getCarrierType());
            loginInfo.setSupplierType(memberSimpleDTO.getSupplierType());
            loginInfo.setBuyerType(memberSimpleDTO.getBuyerType());
            loginInfo.setMemberShortName(memberSimpleDTO.getMemberShortName());
        }

        try {
            List<RoleDTO> accountRole = roleService.getRoleByAccountId(accountDTO.getAccountId());
            if (CollectionUtils.isNotEmpty(accountRole)) {
                loginInfo.setRoleList(accountRole.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList()));
                loginInfo.setRoleNameList(accountRole.stream().map(RoleDTO::getRoleName).collect(Collectors.toList()));
                loginInfo.setRoleTypeList(accountRole.stream().map(RoleDTO::getRoleType).distinct().collect(Collectors.toList()));
                for (RoleDTO roleDTO : accountRole) {
                    if (CharSequenceUtil.equals(RoleTypeEnum.BUYER.getRoleType(),roleDTO.getRoleType())) {
                        loginInfo.setBuyerFlg(1);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取账号角色错误 {},", e);
        }
        //保存session  Spring HttpSession 自动保存session到redis
        request.getSession().setAttribute(LoginInfo.SESSION_NAME, loginInfo);
        request.getSession().setAttribute("_accountMobile", loginInfo.getMobile());
        log.info("sessionId : {}", loginInfo.getSessionId());

        return loginInfo;
    }

    private <T> ItemResult<T> registerDisableReturn() {
        log.info("registerDisable is true");
        ItemResult<T> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode(MemberCode.VALIDATION_ERROR.getCode());
        result.setDescription("注册功能暂未对外开放");
        result.setData(null);
        return result;
    }

    private ItemResult<Boolean> sendSMSCode(String key, String mobile, HttpServletRequest request) {
        checkMobile(mobile);
        checkSendMobile(mobile, StringUtils.isBlank(key) ? GetSMSCodeDTO.USE_DEFAULT : key);
        String smsCode = SecureRandomStringUtil.random(6, false, true);

        log.info("sendSMSCode mobile:{} smsCode:{} x-auth-token:{} sessionId:{}", mobile, smsCode,
                request.getHeader(CommonConstants.X_AUTH_TOKEN),request.getSession().getId());
        SmsDTO sms = new SmsDTO();
        sms.setTemplateParams(Arrays.asList(smsCode));
        sms.setMobiles(Arrays.asList(mobile));
        SmsTemplateEnum templateEnum = null;
        switch (key){
            case SMS_CODE_REGISTER_PC:
                templateEnum = SmsTemplateEnum.USER_REGISTER_PROCESS_CODE;
                break;
            case SMS_CODE_LOGIN_PHONE_CODE:
                templateEnum = SmsTemplateEnum.USER_PHONE_VERIFICATION_LOGIN_PROCESS_CODE;
                break;
            default:
                break;
        }
        if(Objects.isNull(templateEnum)){
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"发送验证码失败");
        }
        sms.setTemplateCode(templateEnum.getCode());
        iSmsSendService.sendSms(sms);

        key = StringUtils.isBlank(key) ? "" : MD5.getMD5(key);
        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE + key, smsCode);
        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME + key, System.currentTimeMillis());
        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM + key, mobile);

        if (BooleanUtil.isTrue(disableSMSCode)) {
            log.info("短信验证码发送被禁用 ：{}：{},disableSMSCode:{}", mobile, smsCode, disableSMSCode);
            return new ItemResult<>(true);
        }

        bizRedisService.setex(MemberRedisKeys.SMSCODE + mobile, 60, System.currentTimeMillis());

        return new ItemResult<>(true);
    }


    private void checkMobile(String mobile) {
        if (StringUtils.isBlank(mobile) || !ReUtil.isMatch(PHONE_NUMBER_REG, mobile)) {
            throw new BizException(MemberCode.VALIDATION_ERROR, DESCRIPTION_PHONE_ERROR);
        }
    }

    private void checkSendMobile(String mobile, String use) {
        //校验手机号发送频率 1分钟1个手机只能发送1条
        String key1 = MemberRedisKeys.SMSCODE + mobile;
        if (bizRedisService.hasKey(key1)) {
            long time = bizRedisService.get(key1, Long.class);
            long now = System.currentTimeMillis();
            log.info("checkSendMobile key: [{}],nowTime:{} - time:{} < 60000 = {} ", key1, now, time, (now - time < 60000));
            if (now - time < 60000) {
                log.info("每个手机号对应用途60秒内只能发送1次验证码短信，当前请求发送短信的手机:{},use:{}", mobile, use);
                throw new MemberBizException(MemberCode.SMSCODE_SEND_FREQUENCY_LIMIT, "");
            }
        } else {
            log.info("checkSendMobile key: [{}],在redis中不存在", key1);
        }
    }

    public static void checkEmail(String email) {
        if (StringUtils.isBlank(email) || !ReUtil.isMatch(EMAIL_REG, email)) {
            throw new BizException(MemberCode.VALIDATION_ERROR, "请输入正确的邮箱");
        }
    }

    private static String getHeaderValueOrderDefault(String headerName,String defaultValue,HttpServletRequest request){
        String headerValue = request.getHeader(headerName);
        return CharSequenceUtil.isBlank(headerValue) ? defaultValue : headerValue;
    }

    public void getOpenId(String accountId, String wechatId, String wxCode) {
        if (StringUtils.isEmpty(wechatId) && StringUtils.isNotEmpty(wxCode)) {
            try {
                new Thread(()-> {
                    log.info("AccountAnonV2Controller loginByPhoneVerificationCode start get openId:{}", wechatId);
                    Boolean openId = wechatService.getOpenId(accountId, wxCode);
                    log.info("AccountAnonV2Controller loginByPhoneVerificationCode start get openId result:{}", openId);
                }).start();
            }catch (Exception e) {
                log.error("AccountAnonV2Controller loginByPhoneVerificationCode start get openId error", e);
            }
        }
    }

}
