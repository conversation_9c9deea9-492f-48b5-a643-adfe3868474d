package com.cnoocshell.goods.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Table(name = "go_goods_shipping_fee")
@EqualsAndHashCode(callSuper = true)
public class GoodsShippingFee extends BaseEntity {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "goods_code")
    private String goodsCode;

    @Column(name = "goods_name")
    private String goodsName;

    @Column(name = "sap_material_code")
    private String sapMaterialCode;

    @Column(name = "province_code")
    private String provinceCode;

    @Column(name = "province_name")
    private String provinceName;

    @Column(name = "city_code")
    private String cityCode;

    @Column(name = "city_name")
    private String cityName;

    @Column(name = "district_code")
    private String districtCode;

    @Column(name = "district_name")
    private String districtName;

    @Column(name = "pack")
    private String pack;

    @Column(name = "pack_fee")
    private BigDecimal packFee;

    @Column(name = "shipping_fee")
    private BigDecimal shippingFee;

    @Column(name = "effect_start_date")
    private Date effectStartDate;

    @Column(name = "effect_end_date")
    private Date effectEndDate;

    @Column(name = "create_user_name")
    private String createUserName;

    @ApiModelProperty(name = "update_user_name")
    private String updateUserName;
}
