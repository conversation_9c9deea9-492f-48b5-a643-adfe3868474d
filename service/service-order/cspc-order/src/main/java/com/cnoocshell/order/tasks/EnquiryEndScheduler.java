package com.cnoocshell.order.tasks;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.order.api.dto.EnquiryBuyerDetailDTO;
import com.cnoocshell.order.api.enums.EnquiryStatusEnum;
import com.cnoocshell.order.biz.impl.EnquiryBiz;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerDetailMapper;
import com.cnoocshell.order.dao.mapper.EnquiryMapper;
import com.cnoocshell.order.dao.mapper.SalesPlanMapper;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 更新询价结束状态
 */
@Component
@Slf4j
public class EnquiryEndScheduler {

    @Resource
    private EnquiryMapper enquiryMapper;

    @Resource
    private EnquiryBuyerDetailMapper enquiryBuyerDetailMapper;

    @Autowired
    private EnquiryBiz enquiryBiz;

    @Autowired
    private IXxlJobService iXxlJobService;
    @Resource
    private SalesPlanMapper salesPlanMapper;

    @XxlJob("enquiryEndJob")
    public void doJob() {
        XxlJobHelper.log("EnquiryEndScheduler is start");
        Long jobId = XxlJobHelper.getJobId();
        String no = XxlJobHelper.getJobParam();
        try {
            XxlJobHelper.log("EnquiryEndScheduler  enquiryNo:{}", no);
            if (StringUtils.isEmpty(no)) {
                return;
            }
            Condition condition1 = enquiryBiz.newCondition();
            condition1.createCriteria().andEqualTo("enquiryNo", no);
            List<Enquiry> enquiryList = enquiryBiz.findByCondition(condition1);
            if (CollectionUtils.isEmpty(enquiryList)) {
                XxlJobHelper.log("EnquiryEndScheduler enquiryList is empty");
                return;
            }
            List<String> ids = enquiryList.stream().map(Enquiry::getId).collect(Collectors.toList());
            Enquiry enquiry = enquiryList.get(0);
            EnquiryBuyerDetailDTO enquiryBuyerDetailDTO = enquiryBuyerDetailMapper.selectMaxAndLowPrice(no);
            if (Objects.isNull(enquiryBuyerDetailDTO)) {
                XxlJobHelper.log("EnquiryEndScheduler enquiryNoList:{} enquiryBuyerDetailDTO is empty", no);
                enquiryMapper.updateStatusByIds(ids, EnquiryStatusEnum.CANCELLED.getCode());
                //刷新销售计划创建询价标记
                salesPlanMapper.resetCreateEnquiryFlag(enquiry.getSalesPlanNo());
                return;
            }
            enquiry.setMaxPrice(enquiryBuyerDetailDTO.getMaxPrice());
            enquiry.setMinPrice(enquiryBuyerDetailDTO.getMinPrice());
            enquiry.setTotalQuantity(enquiryBuyerDetailDTO.getTotalQuantity());
            enquiryBiz.save(enquiry);
            enquiryMapper.updateStatusByIds(ids, EnquiryStatusEnum.END.getCode());
        }catch (Exception e) {
            log.error("EnquiryEndScheduler no:{} error:",no,e);
            XxlJobHelper.log("EnquiryEndScheduler is error:"+
                    CharSequenceUtil.sub(ExceptionUtil.stacktraceToString(e),0,1024));
        }
        XxlJobHelper.log("EnquiryEndScheduler is end");
        //屏蔽销毁逻辑
        //this.destroyJob(jobId, no);
    }

    private void destroyJob(Long jobId, String no) {
        XxlJobHelper.log(CharSequenceUtil.format("询价自动任务销毁开始 jobId:{} enquiryNo：{}", jobId, no));
        iXxlJobService.destroyXxlJob(jobId);
    }


}
