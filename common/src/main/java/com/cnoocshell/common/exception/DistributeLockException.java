package com.cnoocshell.common.exception;



/**
 * 分布式锁异常
 * @Author: <EMAIL>
 */
public class DistributeLockException extends RuntimeException {

	private static final long serialVersionUID = -4757581999998896855L;

	public DistributeLockException() {
		super();
	}

	public DistributeLockException(String message, Throwable cause) {
		super(message, cause);
	}

	public DistributeLockException(String message) {
		super(message);
	}

	public DistributeLockException(Throwable cause) {
		super(cause);
	}

	public String getMessage() {
		String msg = super.getMessage();
		return msg;
	}

}
