package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.common.dto.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import jdk.dynalink.linker.LinkerServices;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @DESCRIPTION:查询条件对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountSearchDTO extends BasePageDTO {
    /**
     * 账号状态 eq
     */
    @ApiModelProperty("账号状态 eq")
    private Boolean accountStatus;
    /**
     * 个人/企业主账号/企业子账号 eq
     */
    @ApiModelProperty("个人/企业主账号/企业子账号 eq")
    private Integer accountType;
    /**
     * 性别 eq
     */
    @ApiModelProperty("性别 eq")
    private Integer sex;

    /**
     * 账户id like
     */
    @ApiModelProperty("账户id like")
    private String accountId;
    /**
     * 用户名称 like
     */
    @ApiModelProperty("用户名称 like")
    private String accountName;
    /**
     * 会员id like
     */
    @ApiModelProperty("会员id like")
    private String memberId;

    /**
     * 会员名称 like
     */
    @ApiModelProperty("会员名称 like")
    private String memberName;
    /**
     * 手机号 like
     */
    @ApiModelProperty("手机号 like")
    private String mobile;
    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;
    /**
     * 在线状态 eq
     */
    @ApiModelProperty("在线状态 eq")
    private Boolean onlineStatus;
    /**
     * 工号 employeeId
     */
    @ApiModelProperty("工号 employeeId")
    private String employeeId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("只查询账号信息？")
    private Boolean onlyAccountInfo;

    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String roleInfo;

    /**
     * 是否禁用
     */
    @ApiModelProperty("是否禁用")
    private Boolean disabled;

    @ApiModelProperty(value = "账户ID",hidden = true,notes = "根据账户角色查询账户数据时 先查询角色下账户ID 根据ID过滤数据")
    private List<String> accountIds;


    /**
     *{@link com.cnoocshell.member.api.dto.member.enums.AccountTypeEnum}
     */
    @ApiModelProperty("账号类型")
    private List<Integer> accountTypeList;
}
