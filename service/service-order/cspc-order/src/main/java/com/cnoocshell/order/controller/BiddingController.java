package com.cnoocshell.order.controller;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.BiddingBuyerDetailDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingEnquiryDataDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.BiddingReverseDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.RemoveBiddingDTO;
import com.cnoocshell.order.biz.IBiddingBiz;
import com.github.pagehelper.PageInfo;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.IBiddingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/bidding")
@Api(tags = {"BiddingController"}, description = "竞价场次")
@RequiredArgsConstructor
public class BiddingController {

    @Autowired
    private IBiddingBiz biddingService;

    private final IBiddingService iBiddingService;

    @ApiOperation("卖家竞价场次列表")
    @PostMapping(value = "/sales/list")
    public ItemResult<PageInfo<BiddingSalesListViewDTO>> salesBiddingList(@RequestBody BiddingSalesListDTO biddingSalesListDTO) {
        try {
            PageInfo<BiddingSalesListViewDTO> viewDTOPage = biddingService.salesBiddingList(biddingSalesListDTO, 1);
            return ItemResult.success(viewDTOPage);
        } catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "竞价场次列表查询失败");
        }
    }

    @ApiOperation("批量创建竞价场次")
    @PostMapping(value = "/batch/creation")
    public ItemResult<BiddingCreateReturnDTO> batchCreateBidding(@RequestBody BiddingCreateBiddingDTO biddingCreateBiddingDTO) {
        try {
            BiddingCreateReturnDTO biddingCreateReturnDTO = biddingService.batchCreateBidding(biddingCreateBiddingDTO, 1);
            return ItemResult.success(biddingCreateReturnDTO);
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        } catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "批量创建竞价场次失败");
        }
    }

    @ApiOperation("销售计划-创建竞价场次信息查询")
    @GetMapping(value = "/query/create/data")
    public ItemResult<BiddingQueryCreateDataDTO> queryCreateBiddingData(@RequestParam("saleId") String saleId) {
        BiddingQueryCreateDataDTO biddingQueryCreateDataDTO = biddingService.queryCreateBiddingData(saleId);
        return ItemResult.success(biddingQueryCreateDataDTO);
    }

    @ApiOperation("销售计划-创建竞价场次")
    @PostMapping(value = "/single/creation")
    public ItemResult<BiddingCreateReturnDTO> singleCreateBidding(@RequestBody BiddingCreateBiddingDTO biddingCreateBiddingDTO) {
        try {
            BiddingCreateReturnDTO biddingCreateReturnDTO = biddingService.singleCreateBidding(biddingCreateBiddingDTO, 2);
            return ItemResult.success(biddingCreateReturnDTO);
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        } catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "创建竞价场次失败");
        }

    }

    @ApiOperation("添加客户列表信息查询")
    @PostMapping(value = "/query/customer/list")
    public ItemResult<List<BiddingCustomerListDTO>> queryCustomerInfoList(@RequestBody BiddingCustomerInfoDTO dto) {
        List<BiddingCustomerListDTO> biddingCustomerListDTO = biddingService.queryCustomerInfoList(dto);
        return new ItemResult<>(biddingCustomerListDTO);
    }

    @ApiOperation("竞价场次已添加买家查询")
    @GetMapping(value = "/query/customer/info")
    public ItemResult<List<BiddingCustomerListDTO>> queryCustomerData(@RequestParam("biddingNo") String biddingNo) {
        List<BiddingCustomerListDTO> list = biddingService.queryEditQueryData(biddingNo);
        return ItemResult.success(list);
    }

    @ApiOperation("草稿-提交/保存竞价场次")
    @PostMapping(value = "/submit/single")
    public ItemResult<Boolean> submitSingleData(@RequestBody BiddingCreateBiddingSaveDTO dto) {
        try {
            Boolean createReturnDTO = biddingService.submitSingleData(dto);
            return ItemResult.success(createReturnDTO);
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "提交/保存竞价场次失败");
        }
    }

    @ApiOperation("提交竞价策略")
    @PostMapping(value = "/submit/strategy")
    public ItemResult<Boolean> submitStrategyData(@RequestBody BiddingSubmitStrategySaveDTO dto) {
        try {
            Boolean strategyData = biddingService.submitStrategyData(dto);
            return ItemResult.success(strategyData);
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        } catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "提交竞价策略失败");
        }
    }

    @ApiOperation("竞价场次审批列表")
    @PostMapping(value = "/approve/list")
    public ItemResult<PageInfo<BiddingSalesListViewDTO>> queryApproveList(@RequestBody BiddingSalesListDTO dto) {
        PageInfo<BiddingSalesListViewDTO> returnListDTO = biddingService.queryApproveList(dto);
        return ItemResult.success(returnListDTO);
    }

    @ApiOperation("竞价场次审批列表数量查询")
    @PostMapping(value = "/approve/number")
    public ItemResult<Integer> queryApproveNumberList(@RequestBody BiddingSalesListDTO dto) {
        Integer number = biddingService.queryApproveNumberList(dto);
        return ItemResult.success(number);
    }

    @ApiOperation("竞价场次批量审批通过驳回")
    @PostMapping(value = "/batch/approval/rejected")
    public ItemResult<Boolean> batchApprovalOrRejected(@RequestBody BiddingApprovalDTO dto) {
        return biddingService.batchApprovalOrRejected(dto);
    }

    @ApiOperation("竞价场次撤回")
    @GetMapping(value = "/single/withdraw")
    public ItemResult<Boolean> singleWithdrawData(@RequestParam("biddingId") String biddingId) {
        try {
            Boolean batchWithdrawData = biddingService.singleWithdrawData(biddingId);
            return ItemResult.success(batchWithdrawData);
        } catch (BizException e) {
            log.error("竞价策略审批撤回失败 error:",e);
            return ItemResult.fail(e.getErrorCode().getCode(), e.getOnlyMessage());
        } catch (Exception e) {
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "竞价场次撤回失败");
        }
    }

    @ApiOperation("竞价策略查看")
    @GetMapping(value = "/query/strategy")
    public ItemResult<BiddingStrategyViewDTO> queryStrategy(@RequestParam("biddingId") String biddingId) {
        BiddingStrategyViewDTO biddingStrategyViewDTO = biddingService.queryStrategy(biddingId);
        return ItemResult.success(biddingStrategyViewDTO);
    }

    @ApiOperation("买家报量")
    @PostMapping("/joinBiddingByBuyer")
    public ItemResult<Boolean> joinBiddingByBuyer(@RequestBody BiddingBuyerDetailDTO param) {
        try {
            return ItemResult.success(iBiddingService.joinBiddingByBuyer(param));
        } catch (BizException e) {
            log.error("报量业务校验错误 ", e);
            return ItemResult.fail(e.getErrorCode().getCode(), e.getOnlyMessage());
        } catch (Exception e) {
            log.error("报量异常 ", e);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "报量失败");
        }
    }

    @ApiOperation("导出调价记录")
    @GetMapping("/exportAdjustRecord")
    public ItemResult<ExportExcelDTO> exportAdjustRecord(@RequestParam("biddingNo") String biddingNo) {
        try {
            return ItemResult.success(iBiddingService.exportAdjustRecord(biddingNo));
        } catch (Exception e) {
            log.error("导出调剂记录失败 error:", e);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "导出调价记录失败");
        }
    }

    @ApiOperation(value = "导出竞价结果")
    @GetMapping("/exportBiddingBuyerDetail")
    public ItemResult<ExportExcelDTO> exportBiddingBuyerDetail(@RequestParam("biddingNo") String biddingNo) {
        try {
            return ItemResult.success(iBiddingService.exportBiddingBuyerDetail(biddingNo));
        } catch (Exception e) {
            log.error("导出竞价结果失败 error:", e);
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "导出竞价结果失败");
        }
    }

    @ApiOperation(value = "价格调整")
    @PostMapping("/biddingAdjust")
    public ItemResult<Boolean> biddingAdjust(@RequestBody BiddingAdjustDTO param) {
        return iBiddingService.biddingAdjust(param);
    }

    @ApiOperation(value = "竞价成交结果不成交")
    @PostMapping("/biddingNotDeal")
    public ItemResult<Boolean> biddingNotDeal(@RequestBody BiddingDealConfirmDTO param) {
        return iBiddingService.biddingNotDeal(param);
    }

    @ApiOperation(value = "竞价确认成交")
    @PostMapping("/biddingDeal")
    public ItemResult<Boolean> biddingDeal(@RequestBody BiddingDealConfirmDTO param) {
        return iBiddingService.biddingDeal(param);
    }

    @ApiOperation(value = "竞价成交结果审批撤回")
    @PostMapping("/withdrawDealResult")
    public ItemResult<Boolean> withdrawDealResult(@RequestBody WithdrawDealResultDTO param) {
        return iBiddingService.withdrawDealResult(param);
    }

    @ApiOperation(value = "竞价详情")
    @PostMapping("/biddingDetail")
    public ItemResult<BiddingDetailDTO> biddingDetail(@RequestBody QueryBiddingDetailDTO param) {
        return iBiddingService.biddingDetail(param);
    }

    @ApiOperation("买家竞价场次列表")
    @PostMapping(value = "/buyer/list")
    public ItemResult<PageInfo<BiddingBuyerListViewDTO>> buyerBiddingList(@RequestBody BiddingBuyerQueryDTO biddingBuyerListDTO) {
        PageInfo<BiddingBuyerListViewDTO> viewDTOPage = biddingService.buyerBiddingList(biddingBuyerListDTO);
        return ItemResult.success(viewDTOPage);
    }

    @ApiOperation("买家竞价场次列表-竞价产品选择")
    @PostMapping(value = "/buyer/goods/list")
    public ItemResult<List<String>> buyerGoodsBiddingList(@RequestBody BiddingBuyerGoodsDTO dto) {
        List<String> list = biddingService.buyerGoodsBiddingList(dto);
        return ItemResult.success(list);
    }

    @ApiOperation("卖家竞价场次列表-竞价产品选择")
    @PostMapping(value = "/seller/goods/list")
    public ItemResult<List<String>> sellerGoodsBiddingList(@RequestBody BiddingBuyerGoodsDTO dto) {
        List<String> list = biddingService.buyerGoodsBiddingList(dto);
        return ItemResult.success(list);
    }

    @ApiOperation("竞价审批列表导出接口")
    @PostMapping(value = "/export/check/excel")
    public List<BiddingStrategyDetailExportExcelDTO> exportExcel(@RequestBody BiddingExportDataDTO dto) {
        return biddingService.exportExcel(dto);
    }

    @ApiOperation("竞价策略导入接口")
    @PostMapping(value = "/import/excel")
    public ItemResult<String> importExcel(@RequestBody BiddingImportExcelInfoDTO dto) {
        try {
            return biddingService.importExcel(dto);
        } catch (Exception e) {
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), "竞价策略导入失败");
        }
    }

    @ApiOperation("判断客户是否存在竞价场次状态=竞价中且参与状态=已参与的竞价场次")
    @GetMapping(value = "/judge/bool")
    public Boolean judgeBool(@RequestParam("memberCode") String memberCode) {
        return biddingService.judgeBool(memberCode);
    }

    @ApiOperation("客户询价/竞价活跃度报表接口")
    @PostMapping(value = "/queryBiddingEnquiryDataList")
    public ItemResult<PageInfo<ExportBiddingEnquiryDataDTO>> queryBiddingEnquiryDataList(@RequestBody ExportBiddingEnquiryDataDTO queryCondition) {
        return biddingService.queryBiddingEnquiryDataList(queryCondition);
    }

    @ApiOperation("导出--客户询价/竞价活跃度报表接口")
    @PostMapping(value = "/exportBiddingEnquiryDataList")
    public ItemResult<ExportExcelDTO> exportBiddingEnquiryDataList(@RequestBody ExportBiddingEnquiryDataDTO queryCondition) {
        return biddingService.exportBiddingEnquiryDataList(queryCondition);
    }

    @ApiOperation("查询可售量变更报表接口")
    @PostMapping(value = "/querySellNumDataList")
    public ItemResult<PageInfo<ExportSellNumDataDTO>> querySellNumDataList(@RequestBody ExportSellNumDataDTO exportSellNumDataDTO) {
        return biddingService.querySellNumDataList(exportSellNumDataDTO);
    }

    @ApiOperation("导出--查询可售量变更报表接口")
    @PostMapping(value = "/exportSellNumDataList")
    public ItemResult<ExportExcelDTO> exportSellNumDataList(@RequestBody ExportSellNumDataDTO exportSellNumDataDTO) {
        return biddingService.exportSellNumDataList(exportSellNumDataDTO);
    }

    @ApiOperation("查询竞价信息")
    @GetMapping(value = "/queryBiddingInfo")
    public ItemResult<BiddingInfoDTO> queryBiddingInfo(@RequestParam("id") String id) {
        BiddingInfoDTO biddingInfoDTO = biddingService.queryBiddingInfo(id);
        return ItemResult.success(biddingInfoDTO);
    }

    @ApiOperation("手动创建SAP合同")
    @GetMapping(value = "/createSapContractBySeller")
    public ItemResult<Boolean> createSapContractBySeller(@RequestParam("dealNo")String dealNo){
        return iBiddingService.createSapContractBySeller(dealNo);
    }

    @ApiOperation("竞价列表勾选数据查询接口")
    @PostMapping(value = "/query/check/data")
    public List<BiddingQueryCheckDataExcelDTO> queryBiddingCheckData(@RequestBody BiddingListCheckDataDTO dto) {
        return biddingService.queryBiddingCheckData(dto);
    }

    @ApiOperation(value = "竞价策略待提交、竞价策略已驳回 撤回草稿",notes = "CR 2025.4.22")
    @GetMapping(value = "/biddingStrategyWithdrawDraft")
    public ItemResult<Boolean> biddingStrategyWithdrawDraft(@RequestParam("biddingNo") String biddingNo,
                                                            @RequestParam(value = "operatorId",required = false)String operatorId) {
        return ItemResult.success(iBiddingService.biddingStrategyWithdrawDraft(biddingNo,operatorId));
    }

    @ApiOperation("撤回竞价策略")
    @PostMapping("/withDrawStrategy")
    public ItemResult<Void> withDrawStrategy(@RequestBody BiddingReverseDTO param) {
        iBiddingService.withDrawStrategy(param);
        return ItemResult.success();
    }

    @ApiOperation("取消竞价场次")
    @PostMapping("/cancelBidding")
    public ItemResult<Void> cancelBidding(@RequestBody BiddingReverseDTO param) {
        iBiddingService.cancelBidding(param);
        return ItemResult.success();
    }

    @ApiOperation("删除草稿")
    @PostMapping("/removeDraft")
    public ItemResult<Void> removeDraft(@RequestBody RemoveBiddingDTO param){
        iBiddingService.removeDraft(param);
        return ItemResult.success();
    }

    @ApiOperation("周经营会汇报报表 竞价报表")
    @PostMapping("/biddingReport")
    public PageInfo<BiddingReportResultDTO> biddingReport(@RequestBody BiddingReportQueryDTO param) {
        return iBiddingService.biddingReport(param);
    }

    @ApiOperation("活跃度报表")
    @PostMapping("/memberActivityReport")
    public PageInfo<MemberActivityReportResultDTO> memberActivityReport(@RequestBody MemberActivityReportQueryDTO param){
        return iBiddingService.memberActivityReport(param);
    }
}
