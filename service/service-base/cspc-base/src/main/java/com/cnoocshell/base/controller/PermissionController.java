package com.cnoocshell.base.controller;

import com.cnoocshell.base.api.dto.permission.PermissionTreeDTO;
import com.cnoocshell.base.api.dto.permission.QueryRolePermissionDTO;
import com.cnoocshell.base.api.dto.permission.RoleMenuPermissionDTO;
import com.cnoocshell.base.api.dto.permission.VerifyPermissionDTO;
import com.cnoocshell.base.api.dto.role.RoleDTO;
import com.cnoocshell.base.service.IInterfacePermissionService;
import com.cnoocshell.base.service.IPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = {"Permission"}, description = "权限相关service")
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final IPermissionService permissionService;
    private final IInterfacePermissionService iInterfacePermissionService;

    @ApiOperation("查询权限菜单")
    @PostMapping(value = "/findPermissionMenu")
    public PermissionTreeDTO findPermissionMenu(@RequestParam("roleCode") String roleCode, @RequestParam("roleType") String roleType){
        return permissionService.findPermissionMenu(roleCode, roleType);
    }

    @ApiOperation(value = "查询角色菜单权限，元素标签权限 平铺展示",notes = "根据数据排序字段进行排序")
    @PostMapping("/queryRolePermissions")
    List<RoleMenuPermissionDTO> queryRolePermissions(@RequestBody QueryRolePermissionDTO param){
        return permissionService.queryRolePermissions(param);
    }

    @ApiOperation("校验接口权限")
    @PostMapping("/verifyInterfacePermission")
    Boolean verifyInterfacePermission(@RequestBody VerifyPermissionDTO param){
        return iInterfacePermissionService.verifyInterfacePermission(param);
    }
}
