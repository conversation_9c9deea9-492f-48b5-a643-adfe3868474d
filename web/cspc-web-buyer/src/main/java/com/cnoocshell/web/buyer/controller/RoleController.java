package com.cnoocshell.web.buyer.controller;

import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.goods.GoodCodesListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsListDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberDTO;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.member.api.session.LoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:角色相关service
 */

@Api(tags = {"RoleController"}, description = "角色相关service")
@RestController
@RequiredArgsConstructor
@RequestMapping("/role")
@Slf4j
public class RoleController {

    private final IRoleService iRoleService;
    private final IAccountService accountService;
    private final IMemberService iMemberService;
    private final IGoodsService iGoodsService;

    private static final String BUYER = "BUYER";

    @ApiOperation("根据账户id查询已分配的角色")
    @PostMapping(value = "/getRoleByAccountId")
    public ItemResult<AccountRoleDTO> getRoleByAccountId(@ApiIgnore LoginInfo loginInfo,
                                                         @ApiParam(name = "accountId", value = "账号id")
                                                         @RequestParam("accountId") String accountId) {
        AccountRoleDTO dto = new AccountRoleDTO();
        dto.setAccountId(accountId);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setPlatform(BUYER);
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);
        dto = iRoleService.getRoleByAccountId2(dto);
        if (accountSimpleDTO != null) {
            dto.setAccountId(accountId);
            dto.setAccountName(accountSimpleDTO.getAccountName());
            dto.setMemberName(accountSimpleDTO.getMemberName());
            dto.setRealName(accountSimpleDTO.getRealName());
            dto.setEmployeeId(accountSimpleDTO.getEmployeeId());
            dto.setMemberCode(accountSimpleDTO.getMemberCode());
            dto.setAccountCode(accountSimpleDTO.getAccountCode());
            dto.setMobile(accountSimpleDTO.getMobile());
            dto.setAccountType(accountSimpleDTO.getAccountType());
            dto.setDepartment(accountSimpleDTO.getDepartment());
            dto.setPosition(accountSimpleDTO.getPosition());
        }
        return new ItemResult<>(dto);
    }

    @ApiOperation("更新账户角色")
    @PostMapping(value = "/updateRoleByAccountId")
    public ItemResult<Object> updateRoleByAccountId(@ApiIgnore LoginInfo loginInfo,
                                                    @ApiParam(name = "role", value = "账号角色DTO") @RequestBody AccountRoleDTO dto) {
        checkLogin(loginInfo);
        isCurrMemberSubAccount(loginInfo,dto.getAccountId(),null);

        dto.setOperatorId(loginInfo.getAccountId());
        dto.setMemberId(loginInfo.getMemberId());
        iRoleService.updateAccountRole(dto);
        return new ItemResult<>(new Object());
    }

    private void isCurrMemberSubAccount(LoginInfo loginInfo, String accountId, String accountName) {
        if (StringUtils.isBlank(accountId) && StringUtils.isBlank(accountName)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "账号id或账号名不可为空");
        }
        AccountDTO accountDTO = StringUtils.isBlank(accountName) ? accountService.findById(accountId) : accountService.findByAccountName(accountName);

        if (!StringUtils.equals(accountDTO.getMemberId(), loginInfo.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "该账户不是你的企业子账户");
        }
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    @ApiOperation("企业下可配商品范围接口（根据买家主次意向）")
    @PostMapping(value = "/getGoodIntentionList")
    public ItemResult<GoodsListDTO> getGoodIntentionList(@ApiIgnore LoginInfo loginInfo) {
        checkLogin(loginInfo);
        List<MemberPurchaseGoodsIntentionDTO> intentionList
                = iMemberService.getIntentionsByMemberId(loginInfo.getMemberId());
        List<String> goodsCodeList = intentionList.stream().map(MemberPurchaseGoodsIntentionDTO::getGoodsCode)
                .distinct().collect(Collectors.toList());
        GoodsListDTO dto = new GoodsListDTO();
        dto.setGoodsCodeList(goodsCodeList);
        return iGoodsService.getGoodsListByGoodsCode(dto);
    }

    @PostMapping(value= "/getPackSalesGroupByGoodsCode")
    ItemResult<List<GoodsDataListDTO>> getPackSalesGroupByGoodsCode(@RequestBody GoodCodesListDTO dto){
        return iGoodsService.getPackSalesGroupByGoodsCode(dto);
    }

    @ApiOperation("同企业下其他账号已配商品接口")
    @PostMapping(value = "/configureProductScope")
    public ItemResult<List<DataPermissionDTO>> configureProductScope(@ApiIgnore LoginInfo loginInfo) {
        checkLogin(loginInfo);
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setMemberId(loginInfo.getMemberId());
        return iRoleService.configureProductScope(accountRoleDTO);
    }

    @ApiOperation("员工账号已配商品接口")
    @PostMapping(value = "/configureProductScopeByAccountId")
    public ItemResult<List<DataPermissionDTO>> configureProductScopeByAccountId(@ApiIgnore LoginInfo loginInfo,
                                                                                @RequestBody AccountRoleDTO dto) {
        checkLogin(loginInfo);
        log.info("configureProductScopeByAccountId AccountRoleDTO {}",dto);
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(dto.getAccountId());
        return iRoleService.configureProductScope(accountRoleDTO);
    }

    @ApiOperation("查看登录人已配商品接口")
    @PostMapping(value = "/configureProductScopeByLoginInfo")
    public ItemResult<List<DataPermissionDTO>> configureProductScopeByLoginInfo(@ApiIgnore LoginInfo loginInfo) {
        checkLogin(loginInfo);
        log.info("configureProductScopeByAccountId AccountRoleDTO {}",loginInfo);
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(loginInfo.getAccountId());
        return iRoleService.configureProductScope(accountRoleDTO);
    }




}