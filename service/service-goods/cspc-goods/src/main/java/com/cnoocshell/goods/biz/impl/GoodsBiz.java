package com.cnoocshell.goods.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.base.api.dto.DataPermissionGoodsNameDTO;
import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.api.service.IAttachmentService;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.BeanConvertUtils;
import com.cnoocshell.common.utils.CommonConstants;
import com.cnoocshell.common.utils.CsStringUtils;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.enums.*;
import com.cnoocshell.goods.biz.*;
import com.cnoocshell.goods.cache.ICodeNumCacheService;
import com.cnoocshell.goods.cache.IGoodsAttrCacheService;
import com.cnoocshell.goods.dao.mapper.*;
import com.cnoocshell.goods.dao.vo.*;
import com.cnoocshell.goods.exception.GoodsCode;
import com.cnoocshell.goods.exception.ResourceCode;
import com.cnoocshell.information.api.dto.announcement.AnnouncementAttachmentDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: <EMAIL>
 * @Description GoodsBiz
 * @date 2018年8月15日 下午4:54:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsBiz extends BaseBiz<Goods> implements IGoodsBiz {

    private static final String COMMODITY_CODE = "commodityCode";
    private static final String SELLER_ID = "sellerId";
    private static final String DEL_FLG = "delFlg";
    private static final String GOODS_ID = "goodsId";
    private static final String GOOD_ID = "goodId";
    private static final String CATEGORY_TYPE = "categoryType";
    private static final String CATEGORY_CODE = "categoryCode";
    private static final String GOODS_NAME = "goodsName";
    private static final String UPDATE_TIME = "updateTime";
    private static final String GOODS_ATTRI_ID = "goodsAttriId";
    private static final String SPU_ID = "spuId";
    private static final String CATEGORY1 = "category1";
    private static final String ATTRI_NAME = "attriName";
    private static final String BRAND = "brand";
    private static final String GOODS_STATUS = "goodsStatus";
    private static final String SEARCH_KEYWORDS = "searchKeywords";
    private static final String GOODS_CODE = "goodsCode";
    private static final String GOODS_TYPE = "goodsType";
    private static final String BASE_GOODS = "该标准商品[";
    private static final String ALREADY_USE = "]使用中，不能删除";
    private static final String NOT_DISABLE = "]未禁用，不能删除！";
    private static final String GOODS_BIZ_TYPE = "商品业务类型";
    private static final String BASE_GOODS_ID = "基本商品ID";
    private static final String PAGE_NUM_BEGIN_FROM_1 = "页码，从1开始";
    private static final String PAGE_SIZE_MUST_MORE_THAN_0 = "页面大小，必须大于0";
    private static final String GOODS_IDS = "商品IDS";
    private static final String BASE_GOODS_IDS = "基本商品IDS";
    private static final String GOODS_CATEGORY = "商品分类-";
    private static final String DATA_PERMISSION_KEY = "permission:data:";

    @Autowired
    private UUIDGenerator uuidGenerator;
    @Lazy
    @Autowired
    private IGoodsAttributeBiz goodsAttributeBiz;
    @javax.annotation.Resource
    private GoodsAttributeLinkMapper goodsAttributeLinkMapper;
    @javax.annotation.Resource
    private GoodsAttributeValueMapper goodsAttributeValueMapper;
    @javax.annotation.Resource
    private GoodsCategoryMapper goodsCategoryMapper;
    @javax.annotation.Resource
    private GoodCategoryRelationMapper goodCategoryRelationMapper;
    @Autowired
    private ICodeNumCacheService codeNumCacheService;
    @Autowired
    private IGoodsAttrCacheService goodsAttrCacheService;
    @Lazy
    @Autowired
    private IGoodsCategoryAttrBiz goodsCategoryAttrBiz;
    @javax.annotation.Resource
    private GoodsMapper goodsMapper;
    @javax.annotation.Resource
    private ResourceMapper resourceMapper;
    @Lazy
    @Autowired
    private IGoodsAttributeValueBiz goodsAttributeValueBiz;

    @javax.annotation.Resource
    private GoodsAuditAttributeValueMapper goodsAuditAttributeValueMapper;

    @javax.annotation.Resource
    private GoodsCategoryAttrValueBiz goodsCategoryAttrValueBiz;

    @Autowired
    private IResourceBiz resourceBiz;

    @Autowired
    private IAttachmentService attachmentService;

    private final KafkaTemplate<String,String> kafkaTemplate;

    private final BizRedisService bizRedisService;


//    @Autowired
//    protected IMemberService memberService;

    public Boolean ifCanGoodsUnShelve(String goodsId) {
        // 参数检查
        if (StringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        Boolean result = Boolean.TRUE;
        try {
            Example example = new Example(Resource.class);
            Criteria criteria = example.createCriteria();
            criteria.andEqualTo(DEL_FLG, false);
            criteria.andEqualTo(GOODS_ID, goodsId);
            criteria.andEqualTo("status", ResourceStatusEnum.RES_STATUS100.getCode());//已上架
            List<Resource> resources = resourceMapper.selectByExample(example);
            if (resources != null && resources.size() > 0) {
                log.info("ifCanGoodsUnShelve resourceIds:{}", resources.stream().map(item -> item.getResourceId()).collect(Collectors.joining(",")));
                return Boolean.FALSE;
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("ifCanGoodsUnShelve Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return result;
    }


    private String queryOptimizationByGoodName(String keyWords) {
        log.info("关键词优化入参keyWords：{}", keyWords);
        if (StringUtils.isEmpty(keyWords)) {
            return keyWords;
        }

        String regex0 = "(PO)(.*?)";
        List<String> list0 = new ArrayList<String>();
        Pattern pattern0 = Pattern.compile(regex0, Pattern.CASE_INSENSITIVE);
        Matcher m0 = pattern0.matcher(keyWords);
        // 得到所有匹配正则表达式的字段
        while (m0.find()) {
            int i = 1;
            list0.add(m0.group(i));
            i++;
        }
        keyWords = getKeyWords(keyWords, list0);
        log.info("PO优化结果：{}", keyWords);

        String regex1 = "(PC)(.*?)";
        List<String> list1 = new ArrayList<String>();
        Pattern pattern1 = Pattern.compile(regex1, Pattern.CASE_INSENSITIVE);
        Matcher m1 = pattern1.matcher(keyWords);
        // 得到所有匹配正则表达式的字段
        while (m1.find()) {
            int i = 1;
            list1.add(m1.group(i));
            i++;
        }
        keyWords = getWords(keyWords, list1);
        log.info("PC优化结果：{}", keyWords);

        String regex2 = "(PII)(.*?)";
        List<String> list2 = new ArrayList<String>();
        Pattern pattern2 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE);
        Matcher m2 = pattern2.matcher(keyWords);
        // 得到所有匹配正则表达式的字段
        while (m2.find()) {
            int i = 1;
            list2.add(m2.group(i));
            i++;
        }
        keyWords = getKeyWords1(keyWords, list2);
        log.info("PII优化结果：{}", keyWords);

        String regex3 = "(PP)(.*?)";
        List<String> list3 = new ArrayList<String>();
        Pattern pattern3 = Pattern.compile(regex3, Pattern.CASE_INSENSITIVE);
        Matcher m3 = pattern3.matcher(keyWords);
        // 得到所有匹配正则表达式的字段
        while (m3.find()) {
            int i = 1;
            list3.add(m3.group(i));
            i++;
        }
        for (String str : list3) {
            String pstr = null;
            if (keyWords.length() > 2) {
                pstr = "P·P ";
            } else {
                pstr = "P·P";
            }
            Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
            Matcher matcher = p.matcher(keyWords);
            keyWords = matcher.replaceAll(pstr);
        }
        log.info("PP优化结果：{}", keyWords);

        String regex4 = "(M)(.*?)";
        List<String> list4 = new ArrayList<String>();
        Pattern pattern4 = Pattern.compile(regex4, Pattern.CASE_INSENSITIVE);
        Matcher m4 = pattern4.matcher(keyWords);
        // 得到所有匹配正则表达式的字段
        while (m4.find()) {
            int i = 1;
            list3.add(m4.group(i));
            i++;
        }
        for (String str : list4) {
            String pstr = null;
            if (keyWords.length() > 1) {
                pstr = "M ";
            } else {
                pstr = "M";
            }
            Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
            Matcher matcher = p.matcher(keyWords);
            keyWords = matcher.replaceAll(pstr);
        }
        log.info("M优化结果：{}", keyWords);
        return keyWords;
    }

    private static String getKeyWords1(String keyWords, List<String> list2) {
        for (String str : list2) {
            String pstr = null;
            if (keyWords.length() > 3) {
                pstr = "P·II ";
            } else {
                pstr = "P·II";
            }
            Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
            Matcher matcher = p.matcher(keyWords);
            keyWords = matcher.replaceAll(pstr);
        }
        return keyWords;
    }

    private static String getWords(String keyWords, List<String> list1) {
        for (String str : list1) {
            String pstr = null;
            if (keyWords.length() > 2) {
                pstr = "P·C ";
            } else {
                pstr = "P·C";
            }
            Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
            Matcher matcher = p.matcher(keyWords);
            keyWords = matcher.replaceAll(pstr);
        }
        return keyWords;
    }

    private static String getKeyWords(String keyWords, List<String> list0) {
        for (String str : list0) {
            String pstr = null;
            if (keyWords.length() > 2) {
                pstr = "P·O ";
            } else {
                pstr = "P·O";
            }
            Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
            Matcher matcher = p.matcher(keyWords);
            keyWords = matcher.replaceAll(pstr);
        }
        return keyWords;
    }


    @Override
    public GoodsDTO getGoodsInfo(String goodsId) {
        GoodsDTO goodsDTO = null;
        if (StringUtils.isNotBlank(goodsId)) {
            Goods goods = this.get(goodsId);
            if (goods != null) {
//                List<GoodsAttributeDTO> attrList = getGoodsAttrLinkById(goodsId);
                goodsDTO = convertGoodsDTO(goods);
//                goodsDTO.setAttrList(attrList);
            }
        }
        return goodsDTO;
    }

    @Override
    public void deleteGoods(String goodsIds, String operator) {
        if (StringUtils.isNotBlank(goodsIds)) {
            String[] goodIdArray = goodsIds.split(",");
            List<String> nmList = Lists.newArrayList();
            for (String goodsId : goodIdArray) {
                boolean onShelve = this.ifCanGoodsUnShelve(goodsId);
                nmListAddItem(goodsId, onShelve, nmList);
            }

            if (CollectionUtils.isNotEmpty(nmList)) {
                String msg = "已挂牌商品【" + nmList.stream().filter(Objects::nonNull).collect(Collectors.joining(",")) + "】";
                throw new BizException(BasicCode.UNDEFINED_ERROR, msg + "不能删除");
            }

            for (String goodsId : goodIdArray) {
                Goods updGoods = new Goods();
                updGoods.setGoodsId(goodsId);
                updGoods.setDelFlg(true);
                updGoods.setUpdateUser(operator);
                updGoods.setUpdateTime(new Date());
                this.updateSelective(updGoods);
                // 清理sku与商品分类关系
                Example example = new Example(GoodCategoryRelation.class);
                example.createCriteria().andEqualTo(GOOD_ID, goodsId);
                goodCategoryRelationMapper.deleteByExample(example);
            }
        } else {
            throw new BizException(BasicCode.PARAM_NULL, "goodsIds");
        }
    }

    private void nmListAddItem(String goodsId, boolean onShelve, List<String> nmList) {
        if (!onShelve) {
            Goods goods = this.get(goodsId);
            if (goods != null) {
                nmList.add(goods.getGoodsName());
            } else {
                nmList.add(goodsId);
            }
        }
    }


    /**
     * 检查销售单位--销售单位支持多选，多选必须包含计量单位
     *
     * @param categoryAttributeDTOS
     */
    private void checkSaleUnit(List<CategoryAttributeDTO> categoryAttributeDTOS) {
        Optional<CategoryAttributeDTO> saleUnitsOptional = categoryAttributeDTOS.stream().filter(categoryAttributeDTO -> "0101".equals(categoryAttributeDTO.getGoodsAttriId())).findFirst();
        Optional<CategoryAttributeDTO> measureUnitsOptional = categoryAttributeDTOS.stream().filter(categoryAttributeDTO -> "0102".equals(categoryAttributeDTO.getGoodsAttriId())).findFirst();
        if (saleUnitsOptional.isPresent() && measureUnitsOptional.isPresent()) {
            List<CategoryAttributeValueDTO> saleUnits = saleUnitsOptional.get().getCategoryAttributeValueDTOs();
            List<CategoryAttributeValueDTO> measureUnits = measureUnitsOptional.get().getCategoryAttributeValueDTOs();
            List<String> saleUnitValues = saleUnits.stream().map(CategoryAttributeValueDTO::getAttributeValue).collect(Collectors.toList());
            String measureUnitValue = measureUnits.stream().map(CategoryAttributeValueDTO::getAttributeValue).collect(Collectors.toList()).get(0);
            if (saleUnitValues.size() > 1 && !saleUnitValues.contains(measureUnitValue)) {
                throw new BizException(GoodsCode.SALE_UNIT_NOT_RIGHT, "销售单位必须包含计量单位");
            }
        }
    }

    @Override
    public PageInfo<BaseGoodsDTO> pageBaseGoods(PageBaseGoodsDTO pageBaseGoodsDTO) {
        // 参数检测
        checkParams(pageBaseGoodsDTO);
        // 初始化结果
        PageInfo<BaseGoodsDTO> pageData = new PageInfo<>();
        try {
            // 入参封装
            Example example = new Example(Goods.class);
            Criteria criteria = example.createCriteria();
            if (pageBaseGoodsDTO.getCategoryType() != null) {
                // fixme 查询分类
                criteria.andEqualTo(CATEGORY_TYPE, pageBaseGoodsDTO.getCategoryType());
            }
            if (pageBaseGoodsDTO.getGoodsStatus() != null) {
                criteria.andEqualTo(GOODS_STATUS, pageBaseGoodsDTO.getGoodsStatus());
            }
            if (!StringUtils.isBlank(pageBaseGoodsDTO.getGoodsName())) {
                criteria.andLike(GOODS_NAME, "%" + pageBaseGoodsDTO.getGoodsName() + "%");
            }
            if (!StringUtils.isBlank(pageBaseGoodsDTO.getSearchKeywords())) {
                criteria.andLike(SEARCH_KEYWORDS, "%" + pageBaseGoodsDTO.getSearchKeywords() + "%");
            }
            if (!StringUtils.isBlank(pageBaseGoodsDTO.getGoodsCode())) {
                criteria.andEqualTo(GOODS_CODE, pageBaseGoodsDTO.getGoodsCode());
            }
            String categoryId = pageBaseGoodsDTO.getCategoryCode();
            if (!StringUtils.isBlank(categoryId)) {
                GoodsCategory category = goodsCategoryMapper.selectByPrimaryKey(categoryId);
                if (Objects.isNull(category) || category.getDelFlg()) {
                    return new PageInfo<>(Collections.emptyList());
                }
                Condition condition = new Condition(GoodCategoryRelation.class);
                condition.createCriteria().andLike("categoryString", "%" + categoryId + "%");
                List<GoodCategoryRelation> relations = goodCategoryRelationMapper.selectByCondition(condition);
                if (CollectionUtils.isEmpty(relations)) {
                    return new PageInfo<>(Collections.emptyList());
                }
                List<String> goodIds = relations.stream().map(GoodCategoryRelation::getGoodId).collect(Collectors.toList());
                criteria.andIn(GOODS_ID, goodIds);
            }
            if (!StringUtils.isBlank(pageBaseGoodsDTO.getSellerId())) {
                criteria.andEqualTo(SELLER_ID, pageBaseGoodsDTO.getSellerId());
            }
            criteria.andEqualTo(GOODS_TYPE, GoodsTypeEnum.BASE.getCode());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            example.orderBy(UPDATE_TIME).desc();
            // 分页查询数据库
            Page<Goods> page = PageMethod.startPage(pageBaseGoodsDTO.getPageNum(), pageBaseGoodsDTO.getPageSize(), true).doSelectPage(() -> goodsMapper.selectByExample(example));
            // 结果封装
            if (page.size() < 1) {
                pageData.setPages(0);
                pageData.setTotal(0);
                return pageData;
            }
            pageData.setPages(page.getPages());
            pageData.setTotal(page.getTotal());
            List<Goods> result = page.getResult();

            List<BaseGoodsDTO> list = result.stream().map(goods -> {
                BaseGoodsDTO dto = new BaseGoodsDTO();
                BeanUtils.copyProperties(goods, dto);
                Example relation = new Example(GoodCategoryRelation.class);
                relation.createCriteria().andEqualTo(GOOD_ID, goods.getGoodsId());
                List<GoodCategoryRelationDto> goodCategoryRelationDtoList = goodCategoryRelationMapper.selectByExample(relation).stream().map(r -> {
                    GoodCategoryRelationDto relationDto = new GoodCategoryRelationDto();
                    BeanUtils.copyProperties(r, relationDto);
                    return relationDto;
                }).collect(Collectors.toList());
                dto.setRelationDtos(goodCategoryRelationDtoList);
                return dto;

            }).collect(Collectors.toList());
            pageData.setList(list);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("pageBaseGoods Exception Message:", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    private static void checkParams(PageBaseGoodsDTO pageBaseGoodsDTO) {
        if (pageBaseGoodsDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (pageBaseGoodsDTO.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (pageBaseGoodsDTO.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (pageBaseGoodsDTO.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (pageBaseGoodsDTO.getPageSize() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
    }

    private Boolean ifCanDeleteGoods(String goodsId) {
        // 参数检查
        if (StringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        Boolean result = Boolean.TRUE;

        Example example = new Example(Resource.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(GOODS_ID, goodsId);
        List<Resource> resources = resourceMapper.selectByExample(example);
        if (resources != null && resources.size() > 0) {
            return Boolean.FALSE;
        }
        return result;
    }


    /**
     * 检查卖家商品唯一性
     *
     * @param goods
     */
    private void checkSellerGoodsUnique(Goods goods) {
        Condition condition = this.newCondition();
        Criteria criteria = condition.createCriteria();
        criteriaSetValue1(goods, criteria);
        criteria.andEqualTo(DEL_FLG, false);
        List<Goods> checkGoods = this.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(checkGoods)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "已存在相同的商品");
        }
    }

    private static void criteriaSetValue1(Goods goods, Criteria criteria) {
        if (StringUtils.isNotBlank(goods.getGoodsId())) {//修改
            criteria.andNotEqualTo(GOODS_ID, goods.getGoodsId());
        }
        if (StringUtils.isNotBlank(goods.getPack())) {//包装
            criteria.andEqualTo("pack", goods.getPack());
        }
        if (StringUtils.isNotBlank(goods.getSpecs())) {//规格
            criteria.andEqualTo("specs", goods.getSpecs());
        }
        if (StringUtils.isNotBlank(goods.getBrand())) {//品牌
            criteria.andEqualTo(BRAND, goods.getBrand());
        }
        if (StringUtils.isNotBlank(goods.getUnit())) {//单位
            criteria.andEqualTo("unit", goods.getUnit());
        }
        if (StringUtils.isNotBlank(goods.getSellerId())) {//卖家ID
            criteria.andEqualTo(SELLER_ID, goods.getSellerId());
        }
        if (StringUtils.isNotBlank(goods.getColor())) {//颜色
            criteria.andEqualTo("color", goods.getColor());
        }
        if (StringUtils.isNotBlank(goods.getSize())) {//尺寸
            criteria.andEqualTo("size", goods.getSize());
        }
        if (StringUtils.isNotBlank(goods.getGoodsName())) {//商品名称
            criteria.andEqualTo(GOODS_NAME, goods.getGoodsName());
        }
    }


    /**
     * 保存卖家商品属性
     */
    private void saveSellerGoodsAttributeValue(Goods goods, List<CategoryAttributeDTO> categoryAttributeDTOS, String operator) {
        List<String> attributeIds = Lists.newArrayList();
        Condition categoryAttrCondition = new Condition(GoodsCategoryAttribute.class);
        Criteria categoryAttrConditionCriteria = categoryAttrCondition.createCriteria();
        categoryAttrConditionCriteria.andEqualTo(DEL_FLG, false);
        categoryAttrConditionCriteria.andEqualTo("spu", false);
        categoryAttrCondition.orderBy("sort").asc();
        List<GoodsCategoryAttribute> categoryAttributes = goodsCategoryAttrBiz.findByCondition(categoryAttrCondition);
        if (CollectionUtils.isNotEmpty(categoryAttributes)) {
            categoryAttributes.forEach(categoryAttribute -> attributeIds.add(categoryAttribute.getGoodsAttriId()));
        }
        Example delExample = new Example(GoodsAttributeValue.class);
        Criteria delExampleCriteria = delExample.createCriteria();
        delExampleCriteria.andEqualTo(GOODS_ID, goods.getGoodsId());
        if (CollectionUtils.isNotEmpty(attributeIds)) {
            delExampleCriteria.andIn(GOODS_ATTRI_ID, attributeIds);
        }
        goodsAttributeValueMapper.deleteByExample(delExample);
        if (CollectionUtils.isEmpty(categoryAttributeDTOS)) {
            return;
        }
        for (CategoryAttributeDTO categoryAttributeDTO : categoryAttributeDTOS) {
            List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = categoryAttributeDTO.getCategoryAttributeValueDTOs();
            if (CollectionUtils.isNotEmpty(categoryAttributeValueDTOs)) {
                for (CategoryAttributeValueDTO categoryAttributeValueDTO : categoryAttributeValueDTOs) {
                    GoodsAttributeValue goodsAttributeValue = new GoodsAttributeValue();
                    goodsAttributeValue.setValueId(uuidGenerator.gain());
                    goodsAttributeValue.setGoodsId(goods.getGoodsId());
                    goodsAttributeValue.setGoodsAttriId(categoryAttributeDTO.getGoodsAttriId());
                    goodsAttributeValue.setAttriName(categoryAttributeDTO.getAttriName());
                    goodsAttributeValue.setAttriValueCode(StringUtils.isNotEmpty(categoryAttributeValueDTO.getAttributeValueCode()) ?
                            categoryAttributeValueDTO.getAttributeValueCode() : categoryAttributeValueDTO.getAttributeValue());
                    goodsAttributeValue.setAttriValue(categoryAttributeValueDTO.getAttributeValue());
                    goodsAttributeValue.setDelFlg(false);
                    goodsAttributeValue.setCreateUser(operator);
                    goodsAttributeValue.setCreateTime(new Date());
                    goodsAttributeValue.setUpdateUser(operator);
                    goodsAttributeValue.setUpdateTime(new Date());
                    goodsAttributeValueMapper.insert(goodsAttributeValue);
                }
            }
        }
    }

    /**
     * 保存卖家商品扩展属性
     */
    private void saveSellerGoodsAttributeValueExtend(Goods goods, List<GoodsAttributeDTO> goodsAttributeDTOS, String operator) {
        List<String> attributeIds = Lists.newArrayList();
        List<GoodsCategoryAttribute> categoryAttributes = goodsCategoryAttrBiz.findByCategoryTypeAndSpu(null, false);
        if (CollectionUtils.isNotEmpty(categoryAttributes)) {
            categoryAttributes.forEach(categoryAttribute -> attributeIds.add(categoryAttribute.getGoodsAttriId()));
        }
        if (StringUtils.isNotEmpty(goods.getGoodsId()) && CollectionUtils.isNotEmpty(attributeIds)) {
            goodsAttributeValueBiz.deleteByGoodsIdAndGoodsAttriId(goods.getGoodsId(), attributeIds);
        }

        if (CollectionUtils.isNotEmpty(goodsAttributeDTOS)) {
            goodsAttributeDTOS.forEach(goodsAttributeDTO -> {
                if (StringUtils.isBlank(goodsAttributeDTO.getAttriName()) || StringUtils.isBlank(goodsAttributeDTO.getAttriValue())) {
                    throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "属性名称、属性值");
                }
                //保存扩展属性,如果不存在新增
                String goodsAttriId = null;
                List<GoodsAttribute> goodsAttributes = goodsAttributeBiz.findByAttrNameAndValueType(goodsAttributeDTO.getAttriName(), GoodsAttrTypeEnum.EXTEND.getCode());
                goodsAttriId = getGoodsAttriId(operator, goodsAttributeDTO, goodsAttributes);
                //保存扩展属性,如果不存在新增，否则更新
                List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueBiz.findByGoodsIdAndAttrId(Lists.newArrayList(goods.getGoodsId()), Lists.newArrayList(goodsAttriId));
                if (CollectionUtils.isNotEmpty(goodsAttributeValues)) {
                    GoodsAttributeValue goodsAttributeValue = goodsAttributeValues.get(0);
                    goodsAttributeValue.setAttriValue(goodsAttributeDTO.getAttriValue());
                    goodsAttributeValue.setUpdateTime(new Date());
                    goodsAttributeValue.setUpdateUser(operator);
                    goodsAttributeValueBiz.updateSelective(goodsAttributeValue);
                } else {
                    GoodsAttributeValue goodsAttributeValue = new GoodsAttributeValue();
                    goodsAttributeValue.setGoodsAttriId(goodsAttriId);
                    goodsAttributeValue.setGoodsId(goods.getGoodsId());
                    goodsAttributeValue.setValueId(uuidGenerator.gain());
                    String valueCode = generateValueCode(goodsAttriId);
                    goodsAttributeValue.setAttriValueCode(valueCode);
                    goodsAttributeValue.setAttriName(goodsAttributeDTO.getAttriName());
                    goodsAttributeValue.setAttriValue(goodsAttributeDTO.getAttriValue());
                    goodsAttributeValue.setCreateTime(new Date());
                    goodsAttributeValue.setCreateUser(operator);
                    goodsAttributeValue.setUpdateTime(new Date());
                    goodsAttributeValue.setUpdateUser(operator);
                    goodsAttributeValue.setDelFlg(false);
                    goodsAttributeValueBiz.insert(goodsAttributeValue);
                }
            });
        }
    }

    private String getGoodsAttriId(String operator, GoodsAttributeDTO goodsAttributeDTO, List<GoodsAttribute> goodsAttributes) {
        String goodsAttriId;
        if (!CollectionUtils.isNotEmpty(goodsAttributes)) {
            GoodsAttribute goodsAttribute = new GoodsAttribute();
            goodsAttribute.setGoodsAttriId(uuidGenerator.gain());
            goodsAttribute.setAttriName(goodsAttributeDTO.getAttriName());
            goodsAttribute.setAttriType(GoodsAttrTypeEnum.EXTEND.getCode());
            if (goodsAttributeDTO.getValueType() == null) {
                goodsAttribute.setValueType(GoodsAttrValTypeEnum.INPUT.getCode());
            } else {
                goodsAttribute.setValueType(goodsAttributeDTO.getValueType());
            }
            goodsAttribute.setDefalut(goodsAttributeDTO.getDefalut());
            goodsAttribute.setIsEdit(goodsAttributeDTO.getIsEdit());
            goodsAttribute.setIsShow(goodsAttributeDTO.getIsShow());
            goodsAttribute.setCreateTime(new Date());
            goodsAttribute.setCreateUser(operator);
            goodsAttribute.setUpdateTime(new Date());
            goodsAttribute.setUpdateUser(operator);
            goodsAttribute.setDelFlg(false);
            goodsAttributeBiz.insert(goodsAttribute);
            goodsAttriId = goodsAttribute.getGoodsAttriId();
        } else {
            goodsAttriId = goodsAttributes.get(0).getGoodsAttriId();
        }
        return goodsAttriId;
    }

    /**
     * 更新卖家商品关键字
     */
    private void updateSellerGoodsKeywords(Goods goods, List<CategoryAttributeDTO> categoryAttributeDTOS, List<GoodsAttributeDTO> goodsAttributeDTOS) {
        StringBuilder keywords = new StringBuilder();
        keywords.append(goods.getGoodsName());
        //分类
        //基本属性
        if (CollectionUtils.isNotEmpty(categoryAttributeDTOS)) {
            categoryAttributeDTOS.forEach(categoryAttributeDTO -> {
                List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = categoryAttributeDTO.getCategoryAttributeValueDTOs();
                categoryAttributeValueDTOs.forEach(categoryAttributeValueDTO -> keywords.append("，").append(categoryAttributeValueDTO.getAttributeValue()));
            });
        }
        //扩展属性
        if (CollectionUtils.isNotEmpty(goodsAttributeDTOS)) {
            goodsAttributeDTOS.forEach(goodsAttributeDTO -> keywords.append("，").append(goodsAttributeDTO.getAttriValue()));
        }
        goods.setSearchKeywords(keywords.toString());
        goods.setUpdateTime(new Date());
        log.info("updateKeywords:{}", goods);
        updateSelective(goods);
    }


    @Override
    @Transactional
    public void createSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator) {
        //参数检测
        if (CsStringUtils.isNullOrBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        if (CsStringUtils.isNullOrBlank(sellerGoodsDTO.getGoodsName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品名称");
        }
        if (CsStringUtils.isNullOrBlank(sellerGoodsDTO.getGoodsType())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_BIZ_TYPE);
        }
        if (GoodsTypeEnum.PURCHASE.getCode() != sellerGoodsDTO.getGoodsType() && GoodsTypeEnum.SELLER.getCode() != sellerGoodsDTO.getGoodsType()) {
            throw new BizException(GoodsCode.VALUE_ERROR, GOODS_BIZ_TYPE);
        }
        Date now = new Date();
        //如果是引用(背靠背)商品
        //查询标准商品
//        Goods spuGoods = get(sellerGoodsDTO.getSpuId());
        //保存商品
        Goods goods = new Goods();
        BeanUtils.copyProperties(sellerGoodsDTO, goods);
        goods.setGoodsId(uuidGenerator.gain());
        goods.setSpuId(sellerGoodsDTO.getSpuId());
        String code = "G" + CsStringUtils.getDateRandomStr();
        goods.setCommodityCode(sellerGoodsDTO.getCommodityCode());
        goods.setGoodsCode(code);
        goods.setImgs(sellerGoodsDTO.getImgs());
        goods.setGoodsStatus(GoodsStatusEnum.ENABLE.getCode());
        goods.setDelFlg(false);
        goods.setCreateUser(operator);
        goods.setCreateTime(now);
        goods.setUpdateUser(operator);
        goods.setUpdateTime(now);
        //映射商品属性到商品表
        List<CategoryAttributeDTO> categoryAttributeDTOS = sellerGoodsDTO.getCategoryAttributeDTOS();
        //销售单位支持多选，必须包含计量单位
//        checkSaleUnit(categoryAttributeDTOS);
        //设置换算率
        //设置TableField
//        goods = setSellerGoodsTableField(goods, categoryAttributeDTOS);
        //卖家商品唯一性检查
        checkSellerGoodsUnique(goods);
        //保存基本商品属性及值
//        saveBaseGoodsAttributeValue(goods, categoryAttributeDTOS, operator);
        setGoodsTableField(goods, categoryAttributeDTOS);
        //插入商品
        goodsMapper.insert(goods);
        List<GoodCategoryRelationDto> goodCategoryRelationDTOS = sellerGoodsDTO.getGoodCategoryRelationDTOS();
        for (GoodCategoryRelationDto goodCategoryRelationDTO : goodCategoryRelationDTOS) {
            goodCategoryRelationDTO.setGoodId(goods.getGoodsId());
        }
        //保存商品分类关系
        boolean saveGoodCategoryRelation = saveGoodCategoryRelation(goodCategoryRelationDTOS);
        if (!saveGoodCategoryRelation) {
            throw new BizException(GoodsCode.SALE_CATEGORY_FAIL, "保存商品分类失败！");
        }
        //保存商品属性
        saveSellerGoodsAttributeValue(goods, categoryAttributeDTOS, operator);
        //保存扩展属性
        List<GoodsAttributeDTO> goodsAttributeDTOS = sellerGoodsDTO.getGoodsAttributeDTOS();
        saveSellerGoodsAttributeValueExtend(goods, goodsAttributeDTOS, operator);
        //更新关键字
        updateSellerGoodsKeywords(goods, categoryAttributeDTOS, goodsAttributeDTOS);
    }

    private void changeGoodsName(String goodsName, String goodsNameForData, String goodsCode) {
        try {
            if (!goodsName.equals(goodsNameForData)) {
                DataPermissionGoodsNameDTO dataPermissionGoodsNameDTO = new DataPermissionGoodsNameDTO();
                dataPermissionGoodsNameDTO.setGoodsCode(goodsCode);
                dataPermissionGoodsNameDTO.setGoodsName(goodsName);
                //更新权限表相关商品名称
                log.info("权限列表商品名称变更开始 goodsCode:{} goodsName:{}", goodsCode, goodsName);
                kafkaTemplate.send(MqTopicConstant.GOODS_NAME_CHANGE_TOPIC, JSONUtil.toJsonStr(dataPermissionGoodsNameDTO));
                log.info("权限列表商品名称变更结束 goodsCode:{} goodsName:{}", goodsCode, goodsName);
                //删除缓存
                log.info("权限列表商品名称变更 删除缓存开始");
                bizRedisService.deleteKeysByPrefix(DATA_PERMISSION_KEY);
                log.info("权限列表商品名称变更 删除缓存成功");
            }
        } catch (Exception e) {
            log.error("权限列表商品名称变更失败：{}", e.getMessage());
        }
    }


    @Override
    @Transactional
    public void updateSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator) {
        //参数检测
        Goods goods = getGoods(sellerGoodsDTO, operator);
        String goodsName = goods.getGoodsName();
        Date now = new Date();
        //用于背靠背的商品只能修改商品名称和价格，其余信息从关联的厂家的商品带出来
        goods.setGoodsName(sellerGoodsDTO.getGoodsName());
        GoodsSetValue(sellerGoodsDTO, goods);
        //设置属性
        goods.setDelFlg(false);
        goods.setUpdateUser(operator);
        goods.setUpdateTime(now);
        goods.setAppApplicationArea(sellerGoodsDTO.getAppApplicationArea());
        goods.setPcApplicationArea(sellerGoodsDTO.getPcApplicationArea());
        goods.setDeliverCostStandard(sellerGoodsDTO.getDeliverCostStandard());
        goods.setSelfPickupGuide(sellerGoodsDTO.getSelfPickupGuide());
        goods.setSelfPickupCarrier(sellerGoodsDTO.getSelfPickupCarrier());
        goods.setDocumentIds(sellerGoodsDTO.getDocumentIds());
        //映射商品属性到商品表
        List<CategoryAttributeDTO> categoryAttributeDTOS = sellerGoodsDTO.getCategoryAttributeDTOS();
        //销售单位支持多选，必须包含计量单位
//        checkSaleUnit(categoryAttributeDTOS);
        //映射商品属性到卖家商品表
//        setSellerGoodsTableField(goods, categoryAttributeDTOS);
        setGoodsTableField(goods, categoryAttributeDTOS);
        goodsMapper.updateByPrimaryKey(goods);
        //更新权限表相关商品名称
        changeGoodsName(sellerGoodsDTO.getGoodsName(), goodsName, goods.getGoodsCode());
        //保存商品属性
        GoodsAttributeValue deleteAttribute = new GoodsAttributeValue();
        deleteAttribute.setGoodsId(goods.getGoodsId());
        goodsAttributeValueMapper.delete(deleteAttribute);
        saveSellerGoodsAttributeValue(goods, categoryAttributeDTOS, operator);
        //保存分类关系
        GoodCategoryRelation deleteGoodCategory = new GoodCategoryRelation();
        deleteGoodCategory.setGoodId(goods.getGoodsId());
        goodCategoryRelationMapper.delete(deleteGoodCategory);
        List<GoodCategoryRelationDto> goodCategoryRelationDTOS = sellerGoodsDTO.getGoodCategoryRelationDTOS();
        for (GoodCategoryRelationDto goodCategoryRelationDTO : goodCategoryRelationDTOS) {
            goodCategoryRelationDTO.setGoodId(goods.getGoodsId());
        }
        saveGoodCategoryRelation(goodCategoryRelationDTOS);
        //保存扩展属性
        List<GoodsAttributeDTO> goodsAttributeDTOS = sellerGoodsDTO.getGoodsAttributeDTOS();
        saveSellerGoodsAttributeValueExtend(goods, goodsAttributeDTOS, operator);
        //更新关键字
        updateSellerGoodsKeywords(goods, categoryAttributeDTOS, goodsAttributeDTOS);
    }

    private void GoodsSetValue(SellerGoodsDTO sellerGoodsDTO, Goods goods) {
        if (!CsStringUtils.isNullOrBlank(sellerGoodsDTO.getCommodityCode())) {
            goods.setCommodityCode(sellerGoodsDTO.getCommodityCode());
        }
        if (!CsStringUtils.isNullOrBlank(sellerGoodsDTO.getCartageRule())) {
            goods.setCartageRule(sellerGoodsDTO.getCartageRule());
        }
        if (!CsStringUtils.isNullOrBlank(sellerGoodsDTO.getDeliveryMode())) {
            goods.setDeliveryMode(sellerGoodsDTO.getDeliveryMode());
        }
        if (!CsStringUtils.isNullOrBlank(sellerGoodsDTO.getPcApplicationArea())) {
            goods.setPcApplicationArea(sellerGoodsDTO.getPcApplicationArea());
        }
        if (!CsStringUtils.isNullOrBlank(sellerGoodsDTO.getAppApplicationArea())) {
            goods.setAppApplicationArea(sellerGoodsDTO.getAppApplicationArea());
        }
    }

    @NotNull
    private Goods getGoods(SellerGoodsDTO sellerGoodsDTO, String operator) {
        if (CsStringUtils.isNullOrBlank(sellerGoodsDTO.getGoodsId())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "卖家商品ID");
        }
        if (CsStringUtils.isNullOrBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        if (CsStringUtils.isNullOrBlank(sellerGoodsDTO.getGoodsName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "卖家商品名称");
        }
        Goods goods = goodsMapper.selectByPrimaryKey(sellerGoodsDTO.getGoodsId());
        if (goods == null || (goods.getDelFlg() != null && goods.getDelFlg())) {
            throw new BizException(GoodsCode.DATA_NOT_FOUND, "卖家商品");
        }
        if (goods.getGoodsStatus().intValue() == GoodsStatusEnum.ENABLE.getCode()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "已启用的商品不能修改");
        }
        return goods;
    }

    @Override
    public SellerGoodsDTO getSellerGoodsDetail(String goodsId) {
        long s1 = System.currentTimeMillis();
        long s = System.currentTimeMillis();
        //参数检测
        if (CsStringUtils.isNullOrBlank(goodsId)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品ID");
        }
        SellerGoodsDTO sellerGoodsDTO = new SellerGoodsDTO();
        Goods goods = this.get(goodsId);
//        Goods goods = goodsMapper.selectGoodsById(goodsId);
        log.info("getSellerGoodsDetail get cost time:{}", System.currentTimeMillis() - s);
        s = System.currentTimeMillis();
        BeanUtils.copyProperties(goods, sellerGoodsDTO);
        //分类属性
//        String categoryTypeStr = "";
//        GoodsCategory goodsCategory1 = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory1());
//        log.info("getSellerGoodsDetail goodsCategory1 cost time:{}", System.currentTimeMillis() - s);
//        s = System.currentTimeMillis();
//        if (StringUtils.isNotBlank(goods.getCategory2())) {
//            GoodsCategory goodsCategory2 = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory2());
//            log.info("getSellerGoodsDetail goodsCategory2 cost time:{}", System.currentTimeMillis() - s);
//            s = System.currentTimeMillis();
//            categoryTypeStr = categoryTypeStr + goodsCategory2.getCategoryName() + "-";
//        }
//        if (goodsCategory1 != null) {
//            categoryTypeStr = categoryTypeStr + goodsCategory1.getCategoryName();
//            sellerGoodsDTO.setCategoryTypeStr(categoryTypeStr);
//        }
        //查询商品属性
        //查询商品分类基本属性
        Condition categoryAttributeCondition = new Condition(GoodsCategoryAttribute.class);
        Criteria categoryAttributeCriteria = categoryAttributeCondition.createCriteria();
        categoryAttributeCriteria.andEqualTo(DEL_FLG, false);
        categoryAttributeCriteria.andEqualTo("spu", false);
        List<GoodsCategoryAttribute> goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(categoryAttributeCondition);
        log.info("getSellerGoodsDetail goodsCategoryAttr cost time:{}", System.currentTimeMillis() - s);
        s = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(goodsCategoryAttributeList)) {
            categoryAttributeCondition.clear();
            Criteria criteriaAll = categoryAttributeCondition.createCriteria();
            criteriaAll.andEqualTo(DEL_FLG, false);
//            criteriaAll.andEqualTo("spu", false);
            categoryAttributeCondition.orderBy("sort").asc();
            goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(categoryAttributeCondition);
            log.info("getSellerGoodsDetail goodsCategoryAttr2 cost time:{}", System.currentTimeMillis() - s);
        }
        List<CategoryAttributeDTO> categoryAttributeDTOS = Lists.newArrayList();
        List<String> goodsAttriIds = Lists.newArrayList();
        goodsCategoryAttributeList.forEach(goodsCategoryAttribute -> {
            goodsAttriIds.add(goodsCategoryAttribute.getGoodsAttriId());
            CategoryAttributeDTO categoryAttributeDTO = new CategoryAttributeDTO();
            BeanUtils.copyProperties(goodsCategoryAttribute, categoryAttributeDTO);
            Condition condition = new Condition(GoodsAttributeValue.class);
            Criteria criteria = condition.createCriteria();
            criteria.andEqualTo(DEL_FLG, false);
            criteria.andEqualTo(GOODS_ATTRI_ID, goodsCategoryAttribute.getGoodsAttriId());
            criteria.andEqualTo(GOODS_ID, goodsId);
            long ss = System.currentTimeMillis();
            List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueMapper.selectByCondition(condition);
            log.info("getSellerGoodsDetail goodsAttributeValue cost time:{}", System.currentTimeMillis() - ss);
            if (!goodsAttributeValues.isEmpty()) {
                List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = goodsAttributeValues.stream().map(goodsAttributeValue -> {
                    CategoryAttributeValueDTO categoryAttributeValueDTO = new CategoryAttributeValueDTO();
                    BeanUtils.copyProperties(goodsAttributeValue, categoryAttributeValueDTO);
                    categoryAttributeValueDTO.setAttributeValue(goodsAttributeValue.getAttriValue());
                    categoryAttributeValueDTO.setAttributeValueCode(goodsAttributeValue.getAttriValueCode());
                    return categoryAttributeValueDTO;
                }).collect(Collectors.toList());
                categoryAttributeDTO.setCategoryAttributeValueDTOs(categoryAttributeValueDTOs);
                categoryAttributeDTOS.add(categoryAttributeDTO);
            }
        });
        sellerGoodsDTO.setCategoryAttributeDTOS(categoryAttributeDTOS);
        //查询商品扩展属性
        Condition condition = new Condition(GoodsAttributeValue.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(GOODS_ID, goodsId);
        if (!goodsAttriIds.isEmpty()) {
            criteria.andNotIn(GOODS_ATTRI_ID, goodsAttriIds);
        }
        s = System.currentTimeMillis();
        List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueMapper.selectByCondition(condition);
        log.info("getSellerGoodsDetail goodsAttributeValue2 cost time:{}", System.currentTimeMillis() - s);
        List<GoodsAttributeDTO> goodsAttributeDTOS = goodsAttributeValues.stream().map(goodsAttributeValue -> {
            GoodsAttributeDTO goodsAttributeDTO = new GoodsAttributeDTO();
            BeanUtils.copyProperties(goodsAttributeValue, goodsAttributeDTO);
            return goodsAttributeDTO;
        }).collect(Collectors.toList());
        sellerGoodsDTO.setGoodsAttributeDTOS(goodsAttributeDTOS);
        log.info("getSellerGoodsDetail total cost time:{}", System.currentTimeMillis() - s1);
        //返回商品分类
        Example example = new Example(GoodCategoryRelation.class);
        example.createCriteria().andEqualTo(GOOD_ID, goodsId);
        List<GoodCategoryRelation> byCondition = goodCategoryRelationMapper.selectByCondition(example);
        List<GoodCategoryRelationDto> relationDTOS = byCondition.stream()
                .map(relation -> {
                    GoodCategoryRelationDto dto = new GoodCategoryRelationDto();
                    BeanUtils.copyProperties(relation, dto);
                    return dto;
                })
                .collect(Collectors.toList());
        sellerGoodsDTO.setGoodCategoryRelationDTOS(relationDTOS);

        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(sellerGoodsDTO.getDocumentIds())){
            List<AnnouncementAttachmentDTO> attachmentDTOList = new ArrayList<>();
            List<AttachmentinfoDTO> attachmentInfoDTOList = attachmentService.getByIds(sellerGoodsDTO.getDocumentIds());
            attachmentInfoDTOList.forEach(attachment -> {
                AnnouncementAttachmentDTO attachmentDTO = new AnnouncementAttachmentDTO();
                attachmentDTO.setAttachmentId(attachment.getAttachmentId());
                attachmentDTO.setAttachmentName(attachment.getAttcName());
                attachmentDTOList.add(attachmentDTO);
            });
            sellerGoodsDTO.setAttachmentList(attachmentDTOList);
        }
        return sellerGoodsDTO;
    }

    @Override
    public PageInfo<SellerGoodsDTO> pageSellerGoods(PageSellerGoodsDTO pageSellerGoodsDTO) {
        // 参数检测
        if (pageSellerGoodsDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (pageSellerGoodsDTO.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (pageSellerGoodsDTO.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (pageSellerGoodsDTO.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (pageSellerGoodsDTO.getPageSize() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
        // 初始化结果
        PageInfo<SellerGoodsDTO> pageData = PageMethod.startPage(pageSellerGoodsDTO.getPageNum(), pageSellerGoodsDTO.getPageSize(), true)
                .doSelectPageInfo(() -> goodsMapper.pageSellerGoods(pageSellerGoodsDTO));
        return pageData;
    }


    @Override
    public List<GoodsCategoryAttrDTO> getCategoryAttrByGoodsId(String goodsId) {
        List<GoodsCategoryAttrDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsAttributeValue.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(GOODS_ID, goodsId);
        criteria.andEqualTo(DEL_FLG, false);
        List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(goodsAttributeValues)) {
            goodsAttributeValues.forEach(goodsAttributeValue -> {
                GoodsCategoryAttrDTO dto = new GoodsCategoryAttrDTO();
                BeanUtils.copyProperties(goodsAttributeValue, dto);
                Condition condition1 = new Condition(GoodsCategoryAttribute.class);
                Criteria criteria1 = condition1.createCriteria();
                criteria1.andEqualTo(GOODS_ATTRI_ID, goodsAttributeValue.getGoodsAttriId());
                List<GoodsCategoryAttribute> categoryAttributes = goodsCategoryAttrBiz.findByCondition(condition1);
                if (CollectionUtils.isNotEmpty(categoryAttributes)) {
                    GoodsCategoryAttribute goodsCategoryAttribute = categoryAttributes.get(0);
                    BeanUtils.copyProperties(goodsCategoryAttribute, dto);
                    dto.setGoodsAttriId(goodsAttributeValue.getGoodsAttriId());
                    dto.setValueCode(goodsAttributeValue.getAttriValueCode());
                }
                list.add(dto);
            });
        }
        return list;
    }

    /**
     * 获取商品扩展表属性
     *
     * @param goodsId
     * @return
     */
    public List<GoodsAttributeDTO> getGoodsAttrLinkById(String goodsId) {
        if (StringUtils.isBlank(goodsId)) {
            return null;
        }
        Goods good = get(goodsId);
        List<GoodsAttributeDTO> dtoList = null;
        GoodsAttributeLink queryLink = new GoodsAttributeLink();
        queryLink.setGoodsId(goodsId);
        queryLink.setDelFlg(false);
        List<GoodsAttributeLink> attrLinks = goodsAttributeLinkMapper.select(queryLink);
        if (CollectionUtils.isNotEmpty(attrLinks)) {
            dtoList = new ArrayList<>();
            for (GoodsAttributeLink alink : attrLinks) {
                GoodsAttribute attribute = goodsAttributeBiz.get(alink.getGoodsAttriId());//商品属性
                if (attribute != null) {
                    GoodsAttributeDTO attrDTO = BeanConvertUtils.convert(attribute, GoodsAttributeDTO.class);
                    String val = alink.getValue();//属性值
                    attrDTO.setAttriValue(val);
                    attrDTO.setIsEdit(Boolean.toString(toBoolean(attrDTO.getIsEdit())));
                    attrDTO.setIsShow(Boolean.toString(toBoolean(attrDTO.getIsShow())));
                    dtoList.add(attrDTO);
                }
            }
        }

        return dtoList;
    }


    /**
     * 获取属性值ByAttrName
     *
     * @param attrName
     * @param categoryType 商品类型 1 2
     * @return
     */
    @Override
    public List<GoodsAttributeDTO> getAttrValListByAttrName(String attrName, Integer categoryType) {
        List<GoodsAttributeDTO> valList = null;
        if (StringUtils.isNotBlank(attrName)) {
            GoodsAttribute goodsAttr = goodsAttributeBiz.findByAttrName(attrName);
            if (goodsAttr != null) {
                valList = new ArrayList<>();
                String goodsAttriId = goodsAttr.getGoodsAttriId();
                if (GoodsAttrValTypeEnum.SPECIAL.getCode() == goodsAttr.getValueType()) {//特殊值
                    valListAddValue(categoryType, goodsAttriId, goodsAttr, valList);
                } else {
                    valListSetValue(categoryType, goodsAttriId, goodsAttr, valList);
                }
            }
        }
        return valList;
    }

    private void valListAddValue(Integer categoryType, String goodsAttriId, GoodsAttribute goodsAttr, List<GoodsAttributeDTO> valList) {
        if (GoodsBaseAttrEnum.CATEGORY.getId().equals(goodsAttriId)) {//分类属性
            GoodsCategory queryCategory = new GoodsCategory();
            queryCategory.setDelFlg(false);
            List<GoodsCategory> categorys = goodsCategoryMapper.select(queryCategory);
            if (CollectionUtils.isNotEmpty(categorys)) {
                for (GoodsCategory cate : categorys) {
                    GoodsAttributeDTO attrDTO = new GoodsAttributeDTO();
                    attrDTO.setGoodsAttriId(goodsAttriId);
                    attrDTO.setParentId(cate.getParentId());
                    attrDTO.setValueId(cate.getCategoryId());
                    attrDTO.setAttriName(goodsAttr.getAttriName());
                    attrDTO.setAttriValue(cate.getCategoryName());
                    attrDTO.setAttriType(goodsAttr.getAttriType());
                    attrDTO.setValueType(goodsAttr.getValueType());
                    valList.add(attrDTO);
                }
            }
        }
    }

    private void valListSetValue(Integer categoryType, String goodsAttriId, GoodsAttribute goodsAttr, List<GoodsAttributeDTO> valList) {
        Condition condition = new Condition(GoodsAttributeValue.class);
        Criteria criteria = condition.createCriteria();
        if (!StringUtils.isBlank(goodsAttriId)) {
            criteria.andEqualTo(GOODS_ATTRI_ID, goodsAttriId);
        }
        criteria.andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        condition.orderBy("attriValue").asc();
        List<GoodsAttributeValue> attrValues = goodsAttributeValueMapper.selectByCondition(condition);
        for (GoodsAttributeValue attrVal : attrValues) {
            if (categoryType == null) {
                GoodsAttributeDTO attrDTO = BeanConvertUtils.convert(attrVal, GoodsAttributeDTO.class);
                attrDTO.setAttriName(goodsAttr.getAttriName());
                attrDTO.setAttriType(goodsAttr.getAttriType());
                attrDTO.setValueType(goodsAttr.getValueType());
                valList.add(attrDTO);
            }
        }
    }

    /**
     * 初始化分类属性缓存
     */
    @Override
    public Table<String, String, Multimap<String, GoodsAttributeDTO>> initCateAttrTableCache() {
        Table<String, String, Multimap<String, GoodsAttributeDTO>> cateAttrTable = HashBasedTable.create();
        GoodsAttributeValue queryAttr = new GoodsAttributeValue();
        queryAttr.setDelFlg(false);
        List<GoodsAttributeValue> allAttrVal = goodsAttributeValueMapper.select(queryAttr);
        if (CollectionUtils.isEmpty(allAttrVal)) {
            return cateAttrTable;
        }
        for (GoodsAttributeValue attrVal : allAttrVal) {
            GoodsAttributeDTO attrValDTO = BeanConvertUtils.convert(attrVal, GoodsAttributeDTO.class);
            String categorys = StringUtils.defaultIfEmpty(attrValDTO.getCategory(), "");
            String[] categoryList = categorys.split(",");
            String attrValCateTypes = StringUtils.defaultIfEmpty(attrValDTO.getCategoryType(), "");
            String[] cateTypesList = attrValCateTypes.split(",");
            String attriName = attrValDTO.getAttriName();
            cateAttrTableSetValue(categoryList, cateTypesList, cateAttrTable, attriName, attrValDTO);
        }
        goodsAttrCacheService.clearCateAttrTable();
        goodsAttrCacheService.setCateAttrTable(cateAttrTable);
        return cateAttrTable;
    }

    private void cateAttrTableSetValue(String[] categoryList, String[] cateTypesList, Table<String, String, Multimap<String, GoodsAttributeDTO>> cateAttrTable, String attriName, GoodsAttributeDTO attrValDTO) {
        for (String category : categoryList) {//分类
            for (String attrValCateType : cateTypesList) {//支持商品属性
                Multimap<String, GoodsAttributeDTO> attrMap = cateAttrTable.get(category, attrValCateType);
                if (attrMap == null) {
                    attrMap = ArrayListMultimap.create();
                    attrMap.put(attriName, attrValDTO);

                } else {
                    attrMap.put(attriName, attrValDTO);
                    //去重
                    Map<String, GoodsAttributeDTO> valMap = new HashMap<>();
                    for (GoodsAttributeDTO attr : attrMap.get(attriName)) {
                        valMap.put(attr.getAttriName(), attr);
                    }
                    attrMap.putAll(attriName, Lists.newArrayList(valMap.values()));
                }
                cateAttrTable.put(category, attrValCateType, attrMap);
            }
        }
    }


    private static void dealValList(List<GoodsAttributeDTO> valList, GoodsBaseAttrEnum baseAttrEnum, List<GoodsCategoryAttribute> categoryAttr, Integer valueType, boolean isEdit, boolean isShow, GoodsDTO goods) {
        if (CollectionUtils.isNotEmpty(categoryAttr)) {
            valueType = categoryAttr.get(0).getValueType();
        }

        GoodsAttributeDTO attrDTO = new GoodsAttributeDTO();
        String field = baseAttrEnum.getField();
        String fieldDesc = baseAttrEnum.getDescription();
        if (GoodsAttrValTypeEnum.SPECIAL.getCode() != valueType) {//非特殊值
            valListAddItem(field, goods, attrDTO, fieldDesc, valueType, isEdit, isShow, valList);
        } else {
            valListAddItem2(baseAttrEnum, goods, attrDTO, fieldDesc, valueType, isEdit, isShow, valList);
        }
    }

    private static void valListAddItem2(GoodsBaseAttrEnum baseAttrEnum, GoodsDTO goods, GoodsAttributeDTO attrDTO, String fieldDesc, Integer valueType, boolean isEdit, boolean isShow, List<GoodsAttributeDTO> valList) {
        if (GoodsBaseAttrEnum.CATEGORY == baseAttrEnum) {
            Map<String, String> cateMap = Maps.newHashMap();
            cateMapPutValue(goods, cateMap);

            if (!cateMap.isEmpty()) {
                attrDTO.setAttriName(fieldDesc);
                attrDTO.setAttriType(GoodsAttrTypeEnum.BASE.getCode());
                if (cateMap.values() != null) {
                    attrDTO.setAttriValue(String.join(",", cateMap.values()));
                }
                if (cateMap.keySet() != null) {
                    attrDTO.setValueId(String.join(",", cateMap.keySet()));
                }
                attrDTO.setValueType(valueType);
                attrDTO.setCategoryType(CsStringUtils.emptyIfNull(goods.getCategoryType()));
                attrDTO.setField("category");
                attrDTO.setIsEdit(Boolean.toString(isEdit));
                attrDTO.setIsShow(Boolean.toString(isShow));
                valList.add(attrDTO);
            }
        }
    }

    private static void cateMapPutValue(GoodsDTO goods, Map<String, String> cateMap) {
        if (StringUtils.isNotBlank(goods.getCategory1())) {
            cateMap.put(goods.getCategory1(), goods.getCategoryNm1());
        }
        if (StringUtils.isNotBlank(goods.getCategory2())) {
            cateMap.put(goods.getCategory2(), goods.getCategoryNm2());
        }
        if (StringUtils.isNotBlank(goods.getCategory3())) {
            cateMap.put(goods.getCategory3(), goods.getCategoryNm3());
        }
        if (StringUtils.isNotBlank(goods.getCategory4())) {
            cateMap.put(goods.getCategory4(), goods.getCategoryNm4());
        }
    }

    private static void valListAddItem(String field, GoodsDTO goods, GoodsAttributeDTO attrDTO, String fieldDesc, Integer valueType, boolean isEdit, boolean isShow, List<GoodsAttributeDTO> valList) {
        if (StringUtils.isNotBlank(field)) {
            String getMothod = "get" + field.substring(0, 1).toUpperCase() + field.substring(1);
            Object value = ReflectionUtils.invokeMethod(ReflectionUtils.findMethod(GoodsDTO.class, getMothod), goods);
            if (value != null) {
                if (value instanceof String || value instanceof Integer) {
                    attrDTO.setAttriValue(value.toString());
                } else if (value instanceof String[]) {
                    attrDTO.setAttriValue(String.join(",", (String[]) value));
                }
                attrDTO.setAttriName(fieldDesc);
                attrDTO.setAttriType(GoodsAttrTypeEnum.BASE.getCode());
                attrDTO.setValueType(valueType);
                attrDTO.setCategoryType(CsStringUtils.emptyIfNull(goods.getCategoryType()));
                attrDTO.setField(field);
                attrDTO.setIsEdit(Boolean.toString(isEdit));
                attrDTO.setIsShow(Boolean.toString(isShow));
                valList.add(attrDTO);
            }
        }
    }


    private void insertBaseAttr(GoodsDTO goods, String operator, GoodsBaseAttrEnum baseAttrEnum, Object value) {
        if (value != null) {
            if (value instanceof String) {
                for (String val : value.toString().split(",")) {
                    insertBaseAttrVal(goods, val, baseAttrEnum, operator);
                }
            } else if (value instanceof Integer) {
                insertBaseAttrVal(goods, value.toString(), baseAttrEnum, operator);
            } else {
                for (String val : (String[]) value) {
                    insertBaseAttrVal(goods, val, baseAttrEnum, operator);
                }
            }
        }
    }


    @Override
    public ItemResult<String> updateStatus(String goodsId, Integer goodsStatus, String operator) {
        if (StringUtils.isBlank(goodsId)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_ID);
        }

        String[] goodsIds = goodsId.split(",");

        Condition condition = resourceBiz.newCondition();
        condition.createCriteria().andIn(GOODS_ID, Lists.newArrayList(goodsIds))
                .andEqualTo(DEL_FLG, false);
        List<Resource> byCondition = resourceBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(byCondition) && byCondition.get(0).getStatus().equals(ResourceStatusEnum.RES_STATUS100.getCode())) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "商品已上架，请先下架后禁用！");
        }

        if (goodsStatus == null || GoodsStatusEnum.getByCode(goodsStatus) == null) {
            throw new BizException(GoodsCode.VALUE_ERROR, "商品状态");
        }

        for (String id : goodsId.split(",")) {
            if (StringUtils.isNotBlank(id)) {
                Goods updGoods = new Goods();
                updGoods.setGoodsId(id);
                updGoods.setGoodsStatus(goodsStatus);
                setOperInfo(updGoods, operator, false);
                updateSelective(updGoods);
            }
        }
        return new ItemResult<>("修改成功！");
    }

    public Goods convertGoods(GoodsDTO goodsDTO) {
        Goods goods = BeanConvertUtils.convert(goodsDTO, Goods.class);
        if (goodsDTO.getImgs() != null) {//图片
            goods.setImgs(String.join(",", goodsDTO.getImgs()));
        }
        if (StringUtils.isBlank(goods.getSpuId())) {
            goods.setSpuId(goodsDTO.getBaseGoodsId());
        }
        return goods;
    }

    public GoodsDTO convertGoodsDTO(Goods goods) {
        if (goods == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品不存在");
        }

        GoodsDTO goodsDTO = BeanConvertUtils.convert(goods, GoodsDTO.class);
        if (StringUtils.isNotBlank(goods.getImgs())) {//图片
            goodsDTO.setImgs(goods.getImgs().split(","));
        }
//        if (StringUtils.isNotBlank(goods.getCategory1())) {//分类
//            GoodsCategory category = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory1());
//            goodsDTO.setCategoryNm1(category == null ? null : category.getCategoryName());
//        }
//        if (StringUtils.isNotBlank(goods.getCategory2())) {//分类
//            GoodsCategory category = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory2());
//            goodsDTO.setCategoryNm2(category == null ? null : category.getCategoryName());
//        }
//        if (StringUtils.isNotBlank(goods.getCategory3())) {//分类
//            GoodsCategory category = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory3());
//            goodsDTO.setCategoryNm3(category == null ? null : category.getCategoryName());
//        }
//        if (StringUtils.isNotBlank(goods.getCategory4())) {//分类
//            GoodsCategory category = goodsCategoryMapper.selectByPrimaryKey(goods.getCategory4());
//            goodsDTO.setCategoryNm4(category == null ? null : category.getCategoryName());
//        }
        return goodsDTO;
    }

    public void addAttrValue(GoodsAttributeDTO goodsAttrDTO, String operator) {
        String attrName = goodsAttrDTO.getAttriName();
        if (StringUtils.isBlank(attrName)) {
            throw new BizException(BasicCode.PARAM_NULL, "attrName");
        }

        String attrValue = goodsAttrDTO.getAttriValue();
        if (StringUtils.isBlank(attrValue)) {
            throw new BizException(BasicCode.PARAM_NULL, "attrValue");
        }

        String categoryIds = goodsAttrDTO.getCategory();
        String categoryType = goodsAttrDTO.getCategoryType();//CategoryTypeEnum
        //商品属性
        GoodsAttribute goodsAttr = goodsAttributeBiz.findByAttrName(attrName);
        if (goodsAttr != null) {
            //商品属性值
            if (goodsAttr.getValueType() == GoodsAttrValTypeEnum.INPUT.getCode() || goodsAttr.getValueType() == GoodsAttrValTypeEnum.OPTION.getCode()) {

                GoodsAttributeValue queryVal = new GoodsAttributeValue();
                queryVal.setAttriValue(attrValue);
                queryVal.setGoodsAttriId(goodsAttr.getGoodsAttriId());
                GoodsAttributeValue goodsAttrValue = goodsAttributeValueMapper.selectOne(queryVal);
                if (goodsAttrValue == null) {
                    //新增属性值
                    goodsAttrValue = new GoodsAttributeValue();
                    goodsAttrValue.setValueId(uuidGenerator.gain());
                    goodsAttrValue.setCategory(categoryIds);
                    String code = generateValueCode(goodsAttr.getGoodsAttriId());
                    goodsAttrValue.setAttriValueCode(code);
                    goodsAttrValue.setAttriValue(attrValue);
                    goodsAttrValue.setAttriName(goodsAttr.getAttriName());
                    goodsAttrValue.setGoodsAttriId(goodsAttr.getGoodsAttriId());
                    setOperInfo(goodsAttrValue, operator, true);
                    goodsAttributeValueMapper.insert(goodsAttrValue);
                } else {
                    //更新属性值
                    updateAttrValueCategory(goodsAttrValue, categoryIds, Integer.valueOf(categoryType), goodsAttr.getAttriName());
                }
            }
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请先增加商品属性：" + attrName);
        }
    }

    /**
     * 生成编码
     *
     * @return
     */
    public String generateValueCode(String goodsAttriId) {
        int baseLeng = 4;
        Long codeNum = codeNumCacheService.getIncrCodeNum(goodsAttriId);
        if (codeNum == null) {
            /**
             * 取得当前最大的code
             */
            Condition condition = new Condition(GoodsAttributeValue.class);
            Criteria criteria = condition.createCriteria();
            criteria.andEqualTo(GOODS_ATTRI_ID, goodsAttriId);
            condition.orderBy("attriValueCode").desc();
            List<GoodsAttributeValue> valueList = goodsAttributeValueMapper.selectByCondition(condition);
            if (CollectionUtils.isNotEmpty(valueList)) {
                String maxCode = valueList.get(0).getAttriValueCode();
                codeNumCacheService.initCodeNum(goodsAttriId, Long.parseLong(maxCode));
            } else {
                codeNumCacheService.initCodeNum(goodsAttriId, 0);
            }
            codeNum = codeNumCacheService.getIncrCodeNum(goodsAttriId);
        }

        return StringUtils.leftPad(codeNum + "", baseLeng, "0");
    }

    private void insertBaseAttrVal(GoodsDTO goods, String attriValue, GoodsBaseAttrEnum baseAttrEnum, String operator) {
        if (StringUtils.isBlank(attriValue)) {
            return;
        }

        Integer categoryType = goods.getCategoryType();//商品类型
        String categoryIds = Stream.of(goods.getCategory1(), goods.getCategory2(), goods.getCategory3(), goods.getCategory4()).filter(i -> !StringUtils.isBlank(i)).collect(Collectors.joining(","));

        //商品属性
        GoodsAttribute baseAttr = goodsAttributeBiz.get(baseAttrEnum.getId());
        if (baseAttr == null) {
            baseAttr = new GoodsAttribute();
            baseAttr.setGoodsAttriId(baseAttrEnum.getId());
            baseAttr.setAttriName(baseAttrEnum.getDescription());
            baseAttr.setValueType(baseAttrEnum.getValType().getCode());
            baseAttr.setAttriType(GoodsAttrTypeEnum.BASE.getCode());
            setOperInfo(baseAttr, operator, true);
            goodsAttributeBiz.insert(baseAttr);
        }

        //商品属性值
        if (baseAttr.getValueType() == GoodsAttrValTypeEnum.INPUT.getCode() || baseAttr.getValueType() == GoodsAttrValTypeEnum.OPTION.getCode()) {

            GoodsAttributeValue query = new GoodsAttributeValue();
            query.setAttriValue(attriValue);
            query.setGoodsAttriId(baseAttr.getGoodsAttriId());
            GoodsAttributeValue attrValue = goodsAttributeValueMapper.selectOne(query);
            if (attrValue == null) {
                attrValue = new GoodsAttributeValue();
                attrValue.setValueId(uuidGenerator.gain());
                attrValue.setCategory(categoryIds);
                String code = generateValueCode(baseAttr.getGoodsAttriId());
                attrValue.setAttriValueCode(code);
                attrValue.setAttriValue(attriValue);
                attrValue.setAttriName(baseAttr.getAttriName());
                attrValue.setGoodsAttriId(baseAttr.getGoodsAttriId());
                setOperInfo(attrValue, operator, true);
                goodsAttributeValueMapper.insert(attrValue);
            } else {
                //更新信息
                updateAttrValueCategory(attrValue, categoryIds, categoryType, baseAttr.getAttriName());
            }
        }
    }

    private void updateAttrValueCategory(GoodsAttributeValue attrValue, String categoryIds, Integer categoryType, String attriName) {
        boolean isUpdate = false;
        //更新分类信息
        String orgCategory = attrValue.getCategory();
        if (StringUtils.isNotBlank(categoryIds)) {
            if (StringUtils.isBlank(orgCategory)) {
                attrValue.setCategory(categoryIds);
                isUpdate = true;
            } else {
                Set<String> cSet = Sets.newHashSet(categoryIds.split(","));
                Set<String> oSet = Sets.newHashSet(orgCategory.split(","));
                int osize = oSet.size();
                oSet.addAll(cSet);
                int nsize = oSet.size();
                if (osize != nsize) {
                    attrValue.setCategory(String.join(",", oSet));
                    isUpdate = true;
                }
            }
        }

        //更新支持商品类型
        if (StringUtils.isNotBlank(attriName) && !attriName.equals(attrValue.getAttriName())) {
            attrValue.setAttriName(attriName);
            isUpdate = true;
        }

        if (isUpdate) {
            goodsAttributeValueMapper.updateByPrimaryKeySelective(attrValue);
        }
    }

    private static boolean isIsUpdate(GoodsAttributeValue attrValue, Integer categoryType, String orgCategoryType, boolean isUpdate) {
        if (categoryType != null) {
            if (StringUtils.isBlank(orgCategoryType)) {
                isUpdate = true;
            } else {
                Set<String> oSet = Sets.newHashSet(orgCategoryType.split(","));
                int osize = oSet.size();
                oSet.add(categoryType.toString());
                int nsize = oSet.size();
                if (osize != nsize) {
                    isUpdate = true;
                }
            }
        }
        return isUpdate;
    }

    private void converListAddValue2(String goodsId, Table<String, String, Double> converRatioTable, String defUnit, List<UnitConverDTO> converList, Double mountkg) {
        if (mountkg != null) {
            //1袋=?吨
            double ratio = converRatioTable.get(GoodsUnitEnum.KG.getDescription(), GoodsUnitEnum.T.getDescription());
            double r = mountkg * ratio;
            UnitConverDTO unitConverDTO = new UnitConverDTO(GoodsUnitEnum.PACK.getDescription(), GoodsUnitEnum.T.getDescription(), BigDecimal.valueOf(r));
            if (defUnit.equals(unitConverDTO.getUnit1())) {
                unitConverDTO.setDefault(true);//是否商品默认单位(计量单位)
            }
            unitConverDTO.setUnitId1(getUnitId(unitConverDTO.getUnit1(), goodsId));
            unitConverDTO.setUnitId2(getUnitId(unitConverDTO.getUnit2(), goodsId));
            converList.add(unitConverDTO);

            //1吨=?袋
            double ratio2 = converRatioTable.get(GoodsUnitEnum.T.getDescription(), GoodsUnitEnum.KG.getDescription());
            double r2 = 1 * ratio2 / mountkg;
            UnitConverDTO unitConverDTO2 = new UnitConverDTO(GoodsUnitEnum.T.getDescription(), GoodsUnitEnum.PACK.getDescription(), BigDecimal.valueOf(r2));
            if (defUnit.equals(unitConverDTO2.getUnit1())) {
                unitConverDTO2.setDefault(true);///是否商品默认单位(计量单位)
            }
            unitConverDTO2.setUnitId1(getUnitId(unitConverDTO2.getUnit1(), goodsId));
            unitConverDTO2.setUnitId2(getUnitId(unitConverDTO2.getUnit2(), goodsId));
            converList.add(unitConverDTO2);
        }
    }

    private String getUnitId(String unitNm, String goodsId) {
        GoodsAttributeValue unitValue = getUnitAttr(unitNm, goodsId);
        if (unitValue != null) {
            return unitValue.getValueId();
        }
        return "";
    }

    private GoodsAttributeValue getUnitAttr(String unitNm, String goodsId) {
        GoodsAttributeValue query = new GoodsAttributeValue();
        query.setGoodsAttriId(GoodsBaseAttrEnum.UNIT.getId());
        query.setAttriValue(unitNm);
        query.setDelFlg(false);
        query.setGoodsId(goodsId);
        GoodsAttributeValue unitValue = goodsAttributeValueMapper.selectOne(query);
        return unitValue;
    }

    @PostConstruct
    private void init() {
        initCateAttrTableCache();
        initSpuId();
        initCategoryCode();
        initResourceCategoryCode();
    }

    private boolean toBoolean(String isFlg) {
        if ("1".equals(isFlg)) {
            return false;
        }
        return true;
    }

    private void initResourceCategoryCode() {
        Example example = new Example(Resource.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        List<Resource> resourceList = resourceMapper.selectByExample(example);
    }

    private void initSpuId() {
        Goods query = new Goods();
        query.setGoodsType(1);
        List<Goods> spuGoods = this.find(query);
        if (CollectionUtils.isNotEmpty(spuGoods)) {
            Map<String, String> spuMap = Maps.newHashMap();
            spuGoods.forEach(action -> spuMap.put(action.getGoodsName(), action.getGoodsId()));

            Condition condition = this.newCondition();
            condition.createCriteria().andEqualTo(GOODS_TYPE, 2).andIsNull(SPU_ID);
            List<Goods> skuGoods = this.findByCondition(condition);
            if (CollectionUtils.isNotEmpty(skuGoods)) {
                for (Goods sku : skuGoods) {
                    for (Map.Entry<String, String> entry : spuMap.entrySet()) {
                        if (sku.getGoodsName().contains(entry.getKey())) {
                            sku.setSpuId(entry.getValue());
                            log.info("upd_sku_spu:{}-{}", sku.getGoodsId(), sku.getSpuId());
                            Goods updGoods = new Goods();
                            updGoods.setGoodsId(sku.getGoodsId());
                            updGoods.setSpuId(sku.getSpuId());
                            this.updateSelective(updGoods);
                        }
                    }
                }
            }
        }
    }

    private void initCategoryCode() {
        Example example = new Example(Goods.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        List<Goods> goodsList = goodsMapper.selectByExample(example);
    }


    @Override
    public List<CategoryAttributeDTO> findBaseGoodsAttribute(String goodsId) {
        //参数检测
        if (CsStringUtils.isNullOrBlank(goodsId)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, BASE_GOODS_ID);
        }
        List<CategoryAttributeDTO> list = Lists.newArrayList();
        List<GoodsCategoryAttribute> categoryAttributes = goodsCategoryAttrBiz.findByCategoryTypeAndSpu(null, false);
        if (CollectionUtils.isNotEmpty(categoryAttributes)) {
            //获取商品属性值
            Set<String> attrIds = categoryAttributes.stream().map(item -> item.getGoodsAttriId()).collect(Collectors.toSet());
            List<GoodsAttributeValue> goodsAttributeValueList = goodsAttributeValueBiz.findByGoodsIdAndAttrId(Lists.newArrayList(goodsId), attrIds);
            Map<String, List<GoodsAttributeValue>> goodsAttributeValueGroup = goodsAttributeValueList.stream().collect(Collectors.groupingBy(GoodsAttributeValue::getGoodsAttriId));

            categoryAttributes.forEach(categoryAttribute -> {
                CategoryAttributeDTO categoryAttributeDTO = new CategoryAttributeDTO();

                BeanUtils.copyProperties(categoryAttribute, categoryAttributeDTO);

                List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueGroup.get(categoryAttribute.getGoodsAttriId());
                if (CollectionUtils.isNotEmpty(goodsAttributeValues)) {
                    List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = goodsAttributeValues.stream().map(goodsAttributeValue -> {
                        CategoryAttributeValueDTO categoryAttributeValueDTO = new CategoryAttributeValueDTO();
                        BeanUtils.copyProperties(goodsAttributeValue, categoryAttributeValueDTO);
                        categoryAttributeValueDTO.setAttributeValueCode(goodsAttributeValue.getAttriValueCode());
                        categoryAttributeValueDTO.setAttributeValue(goodsAttributeValue.getAttriValue());
                        return categoryAttributeValueDTO;
                    }).collect(Collectors.toList());

                    categoryAttributeDTO.setCategoryAttributeValueDTOs(categoryAttributeValueDTOs);
                    list.add(categoryAttributeDTO);
                }
            });
        }
        return list;
    }


    @Override
    public PageData<GoodsDTO> goodsCommonQuery(PageQuery<GoodsQueryCondDTO> pageQuery) {
        int pageNum = pageQuery.getPageNum() == null ? 1 : pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize() == null ? 50 : pageQuery.getPageSize();
        GoodsQueryCondDTO goodsQueryCondDTO = pageQuery.getQueryDTO() == null ? new GoodsQueryCondDTO() : pageQuery.getQueryDTO();
        goodsQueryCondDTO.setGoodsNameLike(queryOptimizationByGoodName(goodsQueryCondDTO.getGoodsNameLike()));

        PageInfo<Goods> pageInfo = PageMethod.startPage(pageNum, pageSize).doSelectPageInfo(() -> goodsMapper.goodsCommonQuery(goodsQueryCondDTO));

        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new PageInfo<>());
        }

        List<GoodsDTO> dtoList = Lists.newArrayList();

        PageInfo<GoodsDTO> dtoPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, dtoPageInfo, "list");

        for (Goods goods : pageInfo.getList()) {
            GoodsDTO goodsDTO = new GoodsDTO();
            BeanUtils.copyProperties(goods, goodsDTO);
            String imgs = goods.getImgs();
            if (StringUtils.isNotBlank(imgs)) {
                goodsDTO.setImgs(imgs.trim().split(","));
            }
            if (goods.getGoodsType() == GoodsTypeEnum.BASE.getCode()) {
                goodsDTO.setBaseGoodsId(goods.getGoodsId());
            }


            dtoList.add(goodsDTO);
        }

        dtoPageInfo.setList(dtoList);

        return new PageData<>(dtoPageInfo);
    }

    @Override
    public GoodsDTO getGoodsInfoByCommodityCode(String commodityCode, String sellerId) {
        Condition condition = new Condition(Goods.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(SELLER_ID, sellerId);
        criteria.andEqualTo(COMMODITY_CODE, commodityCode);
        criteria.andEqualTo(DEL_FLG, 0);
        List<Goods> goodsDTOList = goodsMapper.selectByCondition(condition);
        GoodsDTO goodsDTO = null;
        if (goodsDTOList != null && goodsDTOList.size() > 0) {
            Goods goods = goodsDTOList.get(0);
            goodsDTO = convertGoodsDTO(goods);
        }
        return goodsDTO;
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByIds(List<String> goodsIds) {
        if(CollUtil.isEmpty(goodsIds))
            return Collections.emptyList();

        return goodsMapper.findGoodsSimpleByIds(goodsIds);
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByCodes(List<String> goodsCodes) {
        if (CollUtil.isEmpty(goodsCodes))
            return Collections.emptyList();
        Condition condition = new Condition(Goods.class);
        condition.createCriteria()
                .andEqualTo(DEL_FLG, 0)
                .andIn(GOODS_CODE, goodsCodes);
        return BeanUtil.copyToList(this.findByCondition(condition), GoodsSimpleDTO.class);
    }

    @Override
    public List<GoodsAndCategoryInfoResultDTO> queryGoodsAndCategoryInfoByGoodsCodes(List<String> goodsCodes) {
        if(CollUtil.isEmpty(goodsCodes))
            return Collections.emptyList();

        return goodsMapper.queryGoodsAndCategoryInfoByGoodsCodes(goodsCodes);
    }

    public boolean saveGoodCategoryRelation(List<GoodCategoryRelationDto> relations) {
        List<GoodCategoryRelation> from = GoodCategoryRelation.from(relations);

        Map<String, List<GoodCategoryRelation>> map = from.stream().collect(Collectors.groupingBy(GoodCategoryRelation::getGoodId));

        map.forEach((goodId, rs) -> {
            Goods goods = get(goodId);
            if (goods == null || goods.getDelFlg()) {
                return;
            }

            Example example = new Example(GoodCategoryRelation.class);
            example.createCriteria().andEqualTo(GOOD_ID, goodId);
            List<GoodCategoryRelation> origin = goodCategoryRelationMapper.selectByExample(example);

            Set<String> originCategories = origin.stream().map(GoodCategoryRelation::getCategoryId).collect(Collectors.toSet());
            Set<String> newCategories = rs.stream().map(GoodCategoryRelation::getCategoryId).collect(Collectors.toSet());

            if (originCategories.equals(newCategories)) {
                return;
            }

            goodCategoryRelationMapper.deleteByExample(example);
            goodCategoryRelationMapper.insertList(rs);
        });

        return true;
    }

    /**
     * 保存标准商品属性值
     *
     * @param goods
     * @param categoryAttributeDTOS
     * @param operator
     */
    private void saveBaseGoodsAttributeValue(Goods goods, List<CategoryAttributeDTO> categoryAttributeDTOS, String operator) {
        if(CollectionUtils.isEmpty(categoryAttributeDTOS)){
            return;
        }
        for (CategoryAttributeDTO categoryAttributeDTO : categoryAttributeDTOS) {
            if (CsStringUtils.isNullOrBlank(categoryAttributeDTO.getCategoryAttributeId())) {
                throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "分类属性主键");
            }
            GoodsCategoryAttribute goodsCategoryAttribute = goodsCategoryAttrBiz.get(categoryAttributeDTO.getCategoryAttributeId());
            GoodsAuditAttributeValue goodsAttributeValue = new GoodsAuditAttributeValue();
            goodsAttributeValue.setGoodsId(goods.getGoodsId());
            goodsAttributeValue.setGoodsAttriId(goodsCategoryAttribute.getGoodsAttriId());
            goodsAttributeValue.setAttriName(goodsCategoryAttribute.getAttriName());
            goodsAttributeValue.setDelFlg(false);
            goodsAttributeValue.setCreateUser(operator);
            goodsAttributeValue.setCreateTime(new Date());
            goodsAttributeValue.setUpdateUser(operator);
            goodsAttributeValue.setUpdateTime(new Date());
            List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = categoryAttributeDTO.getCategoryAttributeValueDTOs();
            if (CollectionUtils.isNotEmpty(categoryAttributeValueDTOs)) {
                categoryAttributeValueDTOs.forEach(categoryAttributeValueDTO -> {
                    if (CsStringUtils.isNullOrBlank(categoryAttributeValueDTO.getAttributeValueId())) {
                        goodsAttributeValue.setAttriValueCode("0001");
                        goodsAttributeValue.setAttriValue(categoryAttributeValueDTO.getAttributeValue());
                    } else {
                        GoodsCategoryAttributeValue goodsCategoryAttributeValue = goodsCategoryAttrValueBiz.get(categoryAttributeValueDTO.getAttributeValueId());
                        goodsAttributeValue.setAttriValueCode(goodsCategoryAttributeValue.getAttributeValueCode());
                        goodsAttributeValue.setAttriValue(goodsCategoryAttributeValue.getAttributeValue());
                    }
                    goodsAttributeValue.setId(null);
                    goodsAuditAttributeValueMapper.insert(goodsAttributeValue);
                });
            } else {
                goodsAttributeValue.setId(null);
                goodsAttributeValue.setAttriValueCode("0001");
                goodsAuditAttributeValueMapper.insert(goodsAttributeValue);
            }
        }
    }


    private void setGoodsTableField(Goods goods, List<CategoryAttributeDTO> categoryAttributeDTOS) {
        if (CollectionUtils.isNotEmpty(categoryAttributeDTOS)) {
            for (CategoryAttributeDTO categoryAttributeDTO : categoryAttributeDTOS) {
                Field field = ReflectionUtils.findField(Goods.class, categoryAttributeDTO.getTableField());
                Optional.ofNullable(field)
                        .ifPresent(valueField -> {
                            valueField.setAccessible(true);
                            String value = categoryAttributeDTO.getCategoryAttributeValueDTOs()
                                    .stream()
                                    .map(CategoryAttributeValueDTO::getAttributeValueCode)
                                    .collect(Collectors.joining(","));

                            if (StringUtils.isEmpty(value)) {
                                value = categoryAttributeDTO.getCategoryAttributeValueDTOs()
                                        .stream()
                                        .map(CategoryAttributeValueDTO::getAttributeValue)
                                        .collect(Collectors.joining(","));
                            }

                            ReflectionUtils.setField(valueField, goods, value);
                        });
            }
        }
    }

}
