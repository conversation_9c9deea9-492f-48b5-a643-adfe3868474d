<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.ValueSetMapper">

    <resultMap id="ListResultMap" type="com.cnoocshell.base.api.dto.ValueSetDTO">
        <result column="member_id" jdbcType="VARCHAR" property="memberId" />
        <result column="reference_code" jdbcType="VARCHAR" property="referenceCode" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="attribute" jdbcType="VARCHAR" property="attribute" />
    </resultMap>

    <select id="findPage" parameterType="com.cnoocshell.base.api.dto.ValueQueryDTO" resultMap="ListResultMap">
        select * from ba_valueset where del_flg=0
        and member_id = #{memberId,jdbcType=VARCHAR}
        <!-- if -->
        <if test="referenceCode != null and referenceCode!=''">
            and reference_code like concat('%',#{referenceCode},'%')
        </if>
        <if test="name != null and name!=''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="attribute != null and attribute!=''">
            and attribute like concat('%',#{attribute},'%')
        </if>
        ORDER by update_time desc
    </select>

</mapper>