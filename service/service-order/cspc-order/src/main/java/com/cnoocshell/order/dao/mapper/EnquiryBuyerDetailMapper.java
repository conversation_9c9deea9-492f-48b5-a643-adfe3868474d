package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.EnquiryBuyerDetailDTO;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EnquiryBuyerDetailMapper extends IBaseMapper<EnquiryBuyerDetail> {

    EnquiryBuyerDetailDTO selectMaxAndLowPrice(String enquiryNo);

    List<EnquiryBuyerDetail> selectByEnquiryAndMemberIds(String enquiryNo, @Param("memberCodes") List<String> memberCodes);

}
