package com.cnoocshell.base.api.exception;

import com.cnoocshell.common.exception.CodeMeta;

/**
 * @Author: <EMAIL>
 * @Date: 20/10/2018 14:35
 * @DESCRIPTION:
 */
public class BaseCode {
    public static final CodeMeta CONFIG_ERROR = new CodeMeta("010001", "CONFIG_ERROR", "配置错误: {}", " config error: {}");
    public static final CodeMeta ATTACHMENT_CATEGORY_NOT_EXISTS = new CodeMeta("010002", "ATTACHMENT_CATEGORY_NOT_EXISTS", "应用场景{}不存在", " apply: {} not exists!");
    public static final CodeMeta ATTACHMENT_CATEGORY_REPEAT = new CodeMeta("010003", "ATTACHMENT_CATEGORY_REPEAT", "应用场景{}重复了", " apply: {} has repeat!");

    public static final CodeMeta ATTACHMENT_CATEGORY_DISABLED = new CodeMeta("010004", "ATTACHMENT_CATEGORY_DISABLED", "应用场景{}已停用", " apply: {} has disabled!");

    public static final CodeMeta IMAGE_OCR_ERROR = new CodeMeta("010005", "IMAGE_OCR_ERROR", "图片信息识别错误", "get img info error:{}");

    public static final CodeMeta METHOD_NOT_FOUND = new CodeMeta("010006", "METHOD_NOT_FOUND", "找不到对应的方法:{}", "can not find method error:{}");

    public static final CodeMeta SERVICE_NOT_FOUND = new CodeMeta("010007", "SERVICE_NOT_FOUND", "找不到对应的服务:{}", "can not find service error:{}");

    public static final CodeMeta SERIALIZE_ERROR = new CodeMeta("010008", "SERIALIZE_ERROR", "序列化出错", "serialize error");

    public static final CodeMeta FILE_NAME_IS_NOT_NULL = new CodeMeta("010009", "FILE_NAME_IS_NOT_NULL", "文件名不能为空", "The file name cannot be empty");
    public static final CodeMeta FILE_IS_NOT_NULL = new CodeMeta("010010", "FILE_IS_NOT_NULL", "文件不能为空", "The file cannot be empty");
    public static final CodeMeta FILE_PATH_IS_NOT_NULL = new CodeMeta("010011", "FILE_PATH_IS_NOT_NULL", "文件路径不能为空", "The file path cannot be empty");
    public static final CodeMeta FILE_TYPE_ERROR = new CodeMeta("010012", "FILE_TYPE_ERROR", "不支持的上传文件类型", "Unsupported file type");
    public static final CodeMeta FILE_TOO_LARGE = new CodeMeta("010013", "FILE_TOO_LARGE", "上传的文件过大", "The file is too large");
    public static final CodeMeta FILE_UPLOAD_ERROR = new CodeMeta("010014", "FILE_UPLOAD_ERROR", "上传文件失败", "File upload failed");
    public static final CodeMeta FILE_NOT_FOUND = new CodeMeta("010015", "FILE_NOT_FOUND", "文件不存在", "The file was not found");
    public static final CodeMeta FILE_DOWNLOAD_ERROR = new CodeMeta("010016", "FILE_DOWNLOAD_ERROR", "下载文件失败", "File download failed");

}
