package com.cnoocshell.web.base.dto.member;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 */
@Data
public class MemberRegisterCertDTO {

    /**
     * 附件ID
     */
    @NotNull
    private String attachmentId;

    /**
     * 是否默认资质
     */
    private Integer isDefault;

    private String certId;

    /**
     * 资质名称
     */
    @NotNull
    private String certName;

    /**
     * 资质类型
     */
    @NotNull
    private String certType;

    /**
     * 资质的状态
     */
    private String status;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号码
     */
    private String idNumber;

    /**
     * 紧急联系人姓名
     */
    private String contactName;

    /**
     * 紧急联系人电话
     */
    private String contactPhone;

    /**
     * 过期时间
     */
    @NotNull
    private Date effectiveTime;

    /**
     * 车辆载重大于4.5吨则需要道路运输经营许可证,0-不需要，1-需要
     */
    private Integer needCert;

    /**
     * 道路运输经营许可证号码
     */
    private String roadLicenseNo;

    /**
     * 道路运输经营许可证 注册地址
     */
    private String roadAddress;

    /**
     * 道路运输经营许可证 经营范围
     */
    private String roadBusinessScope;

}
