package com.cnoocshell.base.dao.mapper;

import com.cnoocshell.base.api.dto.DataPermissionAccountQueryDTO;
import com.cnoocshell.base.api.dto.DataPermissionGoodsCodeDTO;
import com.cnoocshell.base.api.dto.account.QueryAccountRoleDTO;
import com.cnoocshell.base.api.dto.account.SimpleAccountRoleDTO;
import com.cnoocshell.base.api.dto.report.AccountReportRoleResultDTO;
import com.cnoocshell.base.dao.vo.AccountRole;
import com.cnoocshell.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountRoleMapper extends IBaseMapper<AccountRole> {

    List<DataPermissionAccountQueryDTO> findAccountByGoodsCode(@Param("accountInfoDTO") DataPermissionGoodsCodeDTO accountInfoDTO);

    List<SimpleAccountRoleDTO> queryAccountRole(@Param("param") QueryAccountRoleDTO param);

    List<AccountReportRoleResultDTO> querySimpleAccountRole(@Param("accountIds") List<String> accountIds);
}