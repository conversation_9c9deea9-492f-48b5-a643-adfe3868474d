package com.cnoocshell.member.aop;

import cn.hutool.core.bean.BeanUtil;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.dao.vo.Account;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 20/12/2018 14:23
 * @DESCRIPTION:
 */
@Component
@Aspect
public class AccountCacheAspect extends BaseCacheAspect {
    public AccountCacheAspect() {
        setCacheName(MemberRedisKeys.CACHE_ACCOUNT);
    }
    private String idKeyPrefix = "id-";
    private String accountNamePrefix = "accountName-";
    private String openIdKeyPrefix = "openid-";
    private String accountCodeKeyPrefix = "accountCode-";

    @Value("${member.cache.account.enable:true}")
    private boolean accountCacheEnable=true;

    ////////////////////////////////////////////////////////////////////////////////
    ////切入点
    ////////////////////////////////////////////////////////////////////////////////

    @Pointcut("@annotation(com.cnoocshell.member.annotation.AccountCacheable)")
    public void cacheable() {
        //do nothing
    }

    @Pointcut("@annotation(com.cnoocshell.member.annotation.AccountCacheEvict)")
    public void cacheEvict() {
        //do nothing
    }

    @Pointcut("@annotation(com.cnoocshell.member.annotation.AccountCachePut)")
    public void cachePut() {
        //do nothing
    }
    @Pointcut("@annotation(com.cnoocshell.member.annotation.AccountCacheClear)")
    public void cacheClear() {
        //do nothing
    }

    ////////////////////////////////////////////////////////////////////////////////
    ////增强实现
    ////////////////////////////////////////////////////////////////////////////////

    @AfterReturning(value = "cachePut() ", returning = "account")
    public void cachePutAdvice(Object account) {
        if( accountCacheEnable ) {
            //put cache
            if (account instanceof Account) {
                put((Account) account);
            } else if (account instanceof List) {
                for (Object obj : (List) account) {
                    if (obj instanceof Account) {
                        put(BeanUtil.toBean(account,Account.class));
                    }
                }
            }
        }
    }

    @After(value = "cacheEvict() && args(arg)", argNames = "arg")
    public void cacheEvictAdvice(Object arg) {
        if( accountCacheEnable ) {
            if (arg == null) {
                return;
            }
            if (arg instanceof String) {
                //only evict id
                evictId(String.valueOf(arg));
            }
            if (arg instanceof String[]) {
                for (String id : (String[]) arg) {
                    //only evict id
                    evictId(id);
                }
            }

            if (arg instanceof Account) {
                //evict account
                evict((Account) arg);
            }
        }
    }

    @Around(value = "cacheable()")
    public Object cacheableAdvice(ProceedingJoinPoint pjp) throws Throwable {
        if( !accountCacheEnable ) {
            return pjp.proceed();
        }

        String methodName = pjp.getSignature().getName();
        Object arg = pjp.getArgs().length >= 1 ? pjp.getArgs()[0] : null;

        String key = "";
        boolean isIdKey = false;
        if ("findByAccountId".equals(methodName) || "get".equals(methodName)) {
            key = idKey(String.valueOf(arg));
            isIdKey = true;
        } else if ("findByAccountName".equals(methodName)) {
            key = accountNameKey((String) arg);
        } else if ("findByOpenId".equals(methodName)) {
            key = openIdKey((String) arg);
        }else if ("findByAccountCode".equals(methodName)){
            key = accountCodeKey((String)arg);
        }

        Account account = null;
        if (isIdKey) {
            account = get(key);
        } else {
            String id = get(key);
            if (id != null) {
                key = idKey(String.valueOf(id));
                account = get(key);
            }
        }
        //cache hit
        if (account != null) {
            log.debug("cacheName:{}, hit key:{}", cacheName, key);
            return account;
        }
        log.debug("cacheName:{}, miss key:{}", cacheName, key);

        //cache miss
        account = (Account) pjp.proceed();

        //put cache
        put(account);
        return account;
    }

    @AfterReturning(value = "cacheClear()")
    public void cacheClearAdvice() {
        if( accountCacheEnable ) {
            clear();
        }
    }

    private String idKey(String id) {
        return MemberRedisKeys.CACHE_ACCOUNT+idKeyPrefix + id;
    }

    private String accountNameKey(String accountName) {
        return MemberRedisKeys.CACHE_ACCOUNT+accountNamePrefix + accountName;
    }

    private String openIdKey(String openId) {
        return MemberRedisKeys.CACHE_ACCOUNT+openIdKeyPrefix + openId;
    }

    private String accountCodeKey(String accountCode) {
        return MemberRedisKeys.CACHE_ACCOUNT+accountCodeKeyPrefix + accountCode;
    }


    ////////////////////////////////////////////////////////////////////////////////
    ////cache 抽象实现
    ////////////////////////////////////////////////////////////////////////////////
    public void put(Account account) {
        if (account == null) {
            return;
        }
        String accountId = account.getAccountId();
        //先删除，再put
        evict((Account)get(idKey(accountId)));
        // id ---> account
        put(idKey(accountId), account);

        //accountName---> id
        put(accountNameKey(account.getAccountName()), accountId);
        //openId ---> id
        //memberCode+accountCode ---> id
        if( StringUtils.isNotBlank(account.getAccountCode()) ){
            put(accountCodeKey(account.getAccountCode()),accountId);
        }
    }


    public void evictId(String id) {
        evict(idKey(id));
    }

    public void evict(Account account) {
        if (account == null) {
            return;
        }
        String accountId = account.getAccountId();

        evict(accountNameKey(account.getAccountName()));

        if( StringUtils.isNotBlank(account.getAccountCode()) ) {
            put(accountCodeKey(account.getAccountCode()), accountId);
        }

        evict(idKey(accountId));
    }
}
