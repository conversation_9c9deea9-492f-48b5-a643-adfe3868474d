package com.cnoocshell.member.biz.impl;

import com.cnoocshell.base.api.dto.role.RolePageReponseDTO;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.biz.IAccountRelationBiz;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.vo.AccountRelation;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountRelationService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class AccountRelationBiz extends BaseBiz<AccountRelation> implements IAccountRelationBiz {

    private static final String ACCOUNT_REAL_NAME = "accountRealName";

    private static final String LINK_ACCOUNT_REAL_NAME = "linkAccountRealName";

    @Override
    public PageInfo<AccountRelation> findAccountRelationByCondition(AccountRelationPageRequestDTO requestDTO) {
        Condition condition = new Condition(AccountRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        if (StringUtils.isNotBlank(requestDTO.getAccountRealName())) {
            criteria.andLike(ACCOUNT_REAL_NAME, "%" + requestDTO.getAccountRealName() + "%");
        }
        if (StringUtils.isNotBlank(requestDTO.getLinkAccountRealName())) {
            criteria.andLike(LINK_ACCOUNT_REAL_NAME, "%" + requestDTO.getLinkAccountRealName() + "%");
        }
        condition.orderBy(DuplicateString.UPDATE_TIME).desc();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(requestDTO.getPageSize() < 10 ? 10 : requestDTO.getPageSize());
        pageInfo.setPageNum(requestDTO.getPageNum() < 0 ? 1 : requestDTO.getPageNum());
        return super.pageInfo(condition, pageInfo);
    }
}
