package com.cnoocshell.base.api.dto.authRes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author:   jacoyang
 * Date:     05/03/2019
 */
@Data
public class ButtonDTO implements Serializable {

    private static final long serialVersionUID = 5973897660508085568L;
    /**
     * 按钮id
     */
    @ApiModelProperty("按钮id")
    private String buttonId;

    /**
     * 按钮code
     */
    @ApiModelProperty("按钮code")
    private String buttonCode;

    /**
     * 按钮名称
     */
    @ApiModelProperty("按钮名称")
    private String butttonName;

    /**
     * 页面id
     */
    @ApiModelProperty("页面Code")
    private String pageCode;

    /**
     * 页面名称
     */
    @ApiModelProperty("页面名称")
    private String pageName;

    /**
     *
     */
    @ApiModelProperty("")
    private String buttonRemark;

    /**
     * URL/按钮/新页面
     */
    @ApiModelProperty("URL/按钮/新页面")
    private String urlType;

    /**
     * 页面url
     */
    @ApiModelProperty("页面url")
    private String pageUrl;

    /**
     * 按钮是否加入权限管理,不加入无法授权
     */
    @ApiModelProperty("按钮是否加入权限管理,不加入无法授权")
    private String needPriv;

    @ApiModelProperty("页面下的按钮，如果授权被选中，则为true")
    private Boolean selected = Boolean.FALSE;


    /**
     * 按钮在页面中的cssId
     */
    @ApiModelProperty("按钮在页面中的cssId")
    private String htmlId;
    /**
     * 删除标识
     */
    @ApiModelProperty("删除标识")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @ApiModelProperty("更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 数据版本
     */
    @ApiModelProperty("数据版本")
    private Long version;

    @ApiModelProperty("翻页页码")
    private Integer pageNum = 1;

    @ApiModelProperty("翻页每页数据量")
    private Integer pageSize = 10;
}
