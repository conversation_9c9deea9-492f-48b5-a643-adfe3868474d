package com.cnoocshell.base.api.dto.report;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AccountReportQueryDTO extends OperatorDTO {
    @ApiModelProperty(value = "分页参数 是否分页",notes = "控制分页列表和导出查询是否分页处理",hidden = true)
    private Boolean needPage = Boolean.TRUE;
    @ApiModelProperty(value = "分页参数 页码")
    private Integer pageNum = 1;
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "角色",notes = "模糊查询")
    private String role;

    @ApiModelProperty(value = "会员ID")
    private List<String> memberIds;

    @ApiModelProperty(value = "账号ID")
    private List<String> accountIds;

    @ApiModelProperty(value = "产品分类", notes = "多选匹配")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "产品名称", notes = "模糊匹配")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码模糊匹配到的产品编码")
    private List<String> goodsCodeBySapMaterialCode;

    @ApiModelProperty(value = "会员ID_商品编码",notes = "根据销售渠道匹配到的会员ID拼接商品编码数据")
    private List<String> memberIdConcatGoodsCodeBySaleChannel;
}
