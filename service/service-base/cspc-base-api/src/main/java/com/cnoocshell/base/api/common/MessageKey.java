package com.cnoocshell.base.api.common;

import io.swagger.annotations.ApiModelProperty;

public final class MessageKey {

    @ApiModelProperty("接收手机号id")
    public static final String RECEIVER_MOBILE = "receiver_mobile";
    @ApiModelProperty("接收人账号id")
    public static final String RECEIVER_ACCOUNT_ID="receiver_account_id";
    @ApiModelProperty("接收人账号登录名")
    public static final String RECEIVER_ACCOUNT_NAME="receiver_account_name";
    @ApiModelProperty("接收人账号id集合")
    public static final String RECEIVER_ACCOUNT_ID_LIST="receiver_account_id_list";
    @ApiModelProperty("接收人会员id")
    public static final String RECEIVER_MEMBER_ID="receiver_member_id";

    @ApiModelProperty("个人买家账号id")
    public static final String INDIVIDUAL_BUYER_ACCOUNT_ID="individual_buyer_account_id";
    @ApiModelProperty("买家会员id")
    public static final String BUYER_ID="buyer_member_id";
    @ApiModelProperty("买家采购员账号id")
    public static final String BUYER_PURCHASER_ACCOUNT_ID="buyer_purchaser_account_id";
    @ApiModelProperty("会员id集合")
    public static final String MEMBER_ID_LIST="member_id_list";
    @ApiModelProperty("会员主账号id集合")
    public static final String MEMBER_MAIN_ACCOUNT_ID_LIST="member_main_account_id_list";

    @ApiModelProperty("卖家会员id")
    public static final String SELLER_ID="seller_member_id";
    @ApiModelProperty("卖家财务人员账号id")
    public static final String SELLER_FINANCIAL_STAFF_ID="seller_financial_staff_id";
    @ApiModelProperty("卖家销售经理账号id")
    public static final String SELLER_SALES_MANAGER_ID="seller_sales_manager_id";
    @ApiModelProperty("卖家销售员账号id")
    public static final String SELLER_SALES_PERSON_ID="seller_sales_person_id";
    @ApiModelProperty("卖家的合同绑定销售员账号id")
    public static final String SELLER_CONTRACT_SALES_PERSON_ID="seller_contract_sales_person_id";
    @ApiModelProperty("卖家的合同业务员id或销售人员id")
    public static final String SELLER_CONTRACT_SALES_MAN_ID="seller_contract_sales_man_id";

    @ApiModelProperty("承运商会员id")
    public static final String CARRIER_ID="carrier_member_id";
    @ApiModelProperty("承运商管理员账号id")
    public static final String CARRIER_ADMIN_ACCOUNT_ID="carrier_admin_account_id";
    @ApiModelProperty("承运商调度员账号id")
    public static final String CARRIER_DISPATCHER_ACCOUNT_ID="carrier_dispatcher_account_id";
    @ApiModelProperty("承运商司机账号id")
    public static final String CARRIER_DRIVER_ACCOUNT_ID="carrier_driver_account_id";
    @ApiModelProperty("承运商司机账号idList")
    public static final String CARRIER_DRIVER_ACCOUNT_ID_LIST="carrier_driver_account_id_list";

    @ApiModelProperty("供应商会员id")
    public static final String SUPPLIER_ID="supplier_member_id";

    @ApiModelProperty("行政区域")
    public static final String REGION_ADCODE="region_adcode";
    @ApiModelProperty("销售区域id集合")
    public static final String SALE_REGION_ID_LIST="sale_region_id_list";
    @ApiModelProperty("销售区域id")
    public static final String SALE_REGION_ID="sale_region_id";
    @ApiModelProperty("仓库id")
    public static final String WAREHOUSE_ID="warehouse_id";

    @ApiModelProperty("订单id")
    public static final String ORDER_ID="order_id";
    @ApiModelProperty("合同id")
    public static final String CONTRACT_ID="contract_id";
    @ApiModelProperty("发货单id")
    public static final String TAKE_ID="take_id";
    @ApiModelProperty("发货单id")
    public static final String DELIVERY_NOTE_ID="delivery_note_id";
    @ApiModelProperty("pay支付单id")
    public static final String PAYMENT_BILL_ID="payment_bill_id";
    @ApiModelProperty("order支付单id")
    public static final String PAYINFO_ID="payinfo_id";

}
