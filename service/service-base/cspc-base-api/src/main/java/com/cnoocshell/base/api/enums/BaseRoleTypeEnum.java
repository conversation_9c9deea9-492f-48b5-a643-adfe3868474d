package com.cnoocshell.base.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BaseRoleTypeEnum {
    BUYER_ADMIN("BUYER_ADMIN", RoleTypeEnum.BUYER.getRoleType(), "企业管理员", "买家企业管理员"),
    BUYER_PURCHASER("BUYER_PURCHASER", RoleTypeEnum.BUYER.getRoleType(), "采购员", "买家采购员"),
    BUYER_PERSONAL("BUYER_PERSONAL", RoleTypeEnum.BUYER.getRoleType(), "个人用户", "个人买家"),

    SYSTEM_ADMIN("SYSTEM_ADMIN", RoleTypeEnum.SELLER.getRoleType(), "企业管理员", "卖家企业管理员"),
    SELLER_CM("SELLER_CM", RoleTypeEnum.SELLER.getRoleType(), "CM", "卖家CM"),
    SELLER_BUSINESS_MANAGER("SELLER_BUSINESS_MANAGER", RoleTypeEnum.SELLER.getRoleType(), "商务经理", "卖家商务经理" ),
    SELLER_SALES_MANAGER("SELLER_SALES_MANAGER",RoleTypeEnum.SELLER.getRoleType(),"销售经理","卖家销售经理"),
    SELLER_SALES_PERSON("SELLER_SALES_PERSON",RoleTypeEnum.SELLER.getRoleType(),"销售人员","卖家销售员"),
    SELLER_CUSTOMER_SERVICE("SELLER_CUSTOMER_SERVICE",RoleTypeEnum.SELLER.getRoleType(),"客服人员","卖家客服人员"),
    SELLER_CMMS("SELLER_CMMS",RoleTypeEnum.SELLER.getRoleType(),"CMMS","卖家CMMS"),
    SELLER_BUSINESS_ANALYSIS("SELLER_BUSINESS_ANALYSIS",RoleTypeEnum.SELLER.getRoleType(),"商务分析","卖家商务分析"),

    SUPPER_ADMIN("SUPPER_ADMIN",RoleTypeEnum.PLATFORM.getRoleType(),"超级管理员","平台超级管理员");

    private String roleCode;
    private String roleType;
    private String info;
    private String roleName;

    }
