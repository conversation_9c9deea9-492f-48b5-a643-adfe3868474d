package com.cnoocshell.web.base.controller;

import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.*;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.common.annotation.NoLogin;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Slf4j
@Api(tags = {"Role"}, description = "角色相关service")
@RestController
@RequestMapping("/role")
@RequiredArgsConstructor
public class RoleController {

    @Autowired
    private IAccountService accountService;

    private final IRoleService iRoleService;

    @ApiOperation("根据账户id查询已分配的角色")
    @PostMapping(value = "/getRoleByAccountId")
    public ItemResult<AccountRoleDTO> getRoleByAccountId(@RequestParam("accountId") String arg0){
        AccountRoleDTO dto = new AccountRoleDTO();
        dto.setAccountId(arg0);
        dto.setMasterFlg(true);
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(arg0);
        dto.setMemberId(accountSimpleDTO == null ? null : accountSimpleDTO.getMemberId());
        dto = iRoleService.getRoleByAccountId2(dto);
        if( accountSimpleDTO != null ){
            dto.setAccountId(arg0);
            dto.setAccountName( accountSimpleDTO.getAccountName());
            dto.setMemberName( accountSimpleDTO.getMemberName() );
            dto.setRealName ( accountSimpleDTO.getRealName() );
            dto.setEmployeeId( accountSimpleDTO.getEmployeeId() );
            dto.setMemberCode( accountSimpleDTO.getMemberCode() );
            dto.setAccountCode( accountSimpleDTO.getAccountCode() );
            dto.setMobile( accountSimpleDTO.getMobile() );
            dto.setAccountType(accountSimpleDTO.getAccountType());
        }
        // 获取用户商品分类授权list
        List<DataPermissionDTO> dataPermissionList = iRoleService.getDataPermissionList(dto);
        if(CollectionUtils.isNotEmpty(dataPermissionList)){
            dto.setDataPermissionDTOList(dataPermissionList);
        }
        return new ItemResult<>(dto);
    }

    @ApiOperation("分页查询角色列表")
    @PostMapping(value = "/findRoleByCondiftion")
    public ItemResult<PageInfo<RolePageReponseDTO>> findRoleByCondiftion(@RequestBody RolePageRequestDTO requestDTO){
        return new ItemResult<>(iRoleService.findRoleByCondiftion(requestDTO));
    }

    @ApiOperation("查询角色详情")
    @PostMapping(value = "/findDetailByRoleCode")
    @NoLogin
    public ItemResult<RoleInfoDTO> findDetailByRoleCode(@RequestParam String roleCode){
        return new ItemResult<>(iRoleService.findDetailByRoleCode(roleCode));
    }

    @ApiOperation("修改角色状态（启用，停用）")
    @PostMapping(value = "/updateStatus")
    public ItemResult<String> updateStatus(@RequestParam("id") Long id, @RequestParam("status") int status){
        return new ItemResult<>(iRoleService.updateStatus(id,status));
    }

    @ApiOperation("删除角色")
    @PostMapping(value = "/deleteRole")
    public ItemResult<String> deleteRole(@RequestParam("id") Long id){
        return new ItemResult<>(iRoleService.deleteRole(id));
    }

    @ApiOperation("新增角色")
    @PostMapping(value = "/addRole")
    public ItemResult<String> addRole(@ApiIgnore LoginInfo loginInfo,@RequestBody @Validated RoleInfoDTO role){
        role.setAccountId(loginInfo.getAccountId());
        return new ItemResult<>(iRoleService.addRole(role));
    }

    @ApiOperation("编辑角色")
    @PostMapping(value = "/updateRole")
    public ItemResult<String> updateRole(@ApiIgnore LoginInfo loginInfo,@RequestBody @Validated RoleInfoDTO role){
        role.setAccountId(loginInfo.getAccountId());
        return new ItemResult<>(iRoleService.updateRole(role));
    }

    @ApiOperation("编辑角色")
    @PostMapping(value = "/updateRoleByAccountId")
    public ItemResult<String> updateRoleByAccountId(@ApiIgnore LoginInfo loginInfo,  @RequestBody AccountRoleDTO arg0){
        arg0.setOperatorId(loginInfo.getAccountId());
        return new ItemResult<>(iRoleService.updateAccountRole(arg0));
    }
}
