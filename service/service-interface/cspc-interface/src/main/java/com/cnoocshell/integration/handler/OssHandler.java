package com.cnoocshell.integration.handler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ByteUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class OssHandler {
    private final AmazonS3 ossClient;
    private final OssConfig ossConfig;
    private final UUIDGenerator uuidGenerator;


    /**
     * 上传文件到联通云
     * 文件永久有效 文件私有访问
     *
     * @param fileByte
     * @param fileName
     */
    public String uploadFile(byte[] fileByte, String businessId, String fileName) {
        try {
            InputStream input = new ByteArrayInputStream(fileByte);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(fileByte.length);
            //文件对象(等同于存储子路径)  yyyyMMdd/businessId 或者 uuid/fileName
            String groupKey = CharSequenceUtil.isBlank(businessId) ? uuidGenerator.gain() : businessId;
            String objectKey = CharSequenceUtil.format("{}/{}/{}_{}",
                    DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT), groupKey, uuidGenerator.gain(), fileName);
            PutObjectRequest objectRequest = new PutObjectRequest(ossConfig.getBucketName(), objectKey, input, metadata)
                    .withCannedAcl(CannedAccessControlList.Private);
            ossClient.putObject(objectRequest);
            return objectKey;
        } catch (Exception e) {
            log.error("文件上传OSS异常", e);
        }
        return null;
    }

    /**
     * 从联通云下载文件
     *
     * @param objectKey 文件在OSS中的唯一标识
     * @return 文件的字节数组
     */
    @SneakyThrows
    public byte[] downloadFile(String objectKey) {
        try {
            S3Object s3Object = ossClient.getObject(new GetObjectRequest(ossConfig.getBucketName(), objectKey));
            S3ObjectInputStream inputStream = s3Object.getObjectContent();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("文件下载OSS异常", e);
        }
        return new byte[0];
    }


    public String getUrl(String objectKey) {
        if (CharSequenceUtil.startWith(objectKey, "/"))
            return ossConfig.getUrl() + objectKey;
        return ossConfig.getUrl() + "/" + objectKey;
    }
}
