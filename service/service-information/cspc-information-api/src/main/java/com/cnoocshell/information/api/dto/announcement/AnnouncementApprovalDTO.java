package com.cnoocshell.information.api.dto.announcement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * @Author: <EMAIL>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Data
public class AnnouncementApprovalDTO {

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @NotBlank
    @ApiModelProperty("审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 主键
     */
    @NotBlank
    @ApiModelProperty("主键")
    private String announcementId;

    /**
     * 审批注释
     */
    @NotBlank
    @ApiModelProperty("审批注释")
    private String comment;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     *
     */
    @ApiModelProperty("")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;


}
