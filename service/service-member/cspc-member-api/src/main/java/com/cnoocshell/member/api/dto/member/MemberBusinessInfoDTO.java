package com.cnoocshell.member.api.dto.member;

import com.cnoocshell.member.api.session.Operator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberBusinessInfoDTO implements Serializable {

    private static final long serialVersionUID = -8006982386153706662L;
    /**
     * 变更请求ID *草稿使用*
     */
    @ApiModelProperty("请求ID，*草稿状态时使用*")
    private String requestId;

    /**
     * 会员id
     */
    @ApiModelProperty("会员ID")
    private String memberId;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名")
    private String memberName;

    /**
     * 会员简称
     */
    @ApiModelProperty("会员简称")
    private String memberShortName;

    /**
     * 会员code
     */
    @ApiModelProperty("会员code")
    private String memberCode;

    /**
     * 商标URL
     */
    @ApiModelProperty("商标URL")
    private String trademarkUrl;

    /**
     * 经营信息状态
     */
    @ApiModelProperty("经营信息状态")
    private Integer businessStatus;

    /**
     * 是否三证合一
     */
    @ApiModelProperty("是否三证合一")
    private Integer isSyncretic;

    /**
     * 组织机构代码
     */
    @ApiModelProperty("组织机构代码")
    private String organizationCode;

    /**
     * 工商执照号码
     */
    @ApiModelProperty("工商执照号码")
    private String businessLicenseCode;

    /**
     * 税务登记号码
     */
    @ApiModelProperty("税务登记号码")
    private String taxCode;

    /**
     * 企业信用号
     */
    @ApiModelProperty("企业信用号")
    private String creditCode;

    /**
     * 所在国家编码
     */
    @ApiModelProperty("所在国家编码")
    private String rigistCountryCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty("所在省编码")
    private String rigistProvinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty("所在城市编码")
    private String rigistCityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty("所在地区编码")
    private String rigistAreaCode;

    /**
     * 所在街道编码
     */
    @ApiModelProperty("所在街道编码")
    private String rigistStreetCode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String rigistAddressDetail;

    /**
     * 法人姓名
     */
    @ApiModelProperty("法人姓名")
    private String legalName;

    /**
     * 企业注册时间
     */
    @ApiModelProperty("企业注册时间")
    private Date registerTime;

    /**
     * 注册资本
     */
    @ApiModelProperty("注册资本")
    private BigDecimal registerFund;

    /**
     * 主营产品
     */
    @ApiModelProperty("主营产品")
    private String mainProducts;

    /**
     * 营业执照
     */
    @ApiModelProperty("营业执照")
    private MemberCertDTO businessCert;

    /**
     * 组织机构代码证
     */
    @ApiModelProperty("组织机构代码证")
    private MemberCertDTO organizationCert;

    /**
     * 税务登记证
     */
    @ApiModelProperty("税务登记证")
    private MemberCertDTO taxCert;

    /**
     * 操作者id
     */
    @ApiModelProperty("操作者id")
    private String operatorId;

    /**
     * 操作者
     */
    @ApiModelProperty("操作者")
    private Operator operator;

    @ApiModelProperty("是否中海壳牌股东关联方")
    private Boolean shareholderRelationParty;
}
