package com.cnoocshell.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.MessageDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanApprovePageDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.dao.mapper.SalesPlanApproveMapper;
import com.cnoocshell.order.dao.mapper.SalesPlanMapper;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.SalesPlan;
import com.cnoocshell.order.dao.vo.SalesPlanApproveRecord;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.service.SalesPlanApproveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class SalesPlanApproveServiceImpl implements SalesPlanApproveService {

    @Autowired
    private SalesPlanMapper salesPlanMapper;

    @Autowired
    private SalesPlanApproveMapper salesPlanApproveMapper;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private IMessageSendService messageSendService;

    @Autowired
    private IEmailSendService emailSendService;

    @Autowired
    private IAccountService accountService;
    private final BizRedisService bizRedisService;

    /**
     *order:approval:salePlan:销售计划单号
     */
    private static final String SALE_PLAN_APPROVAL_LOCK = "order:approval:salePlan:{}";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int approveSalesPlan(SalesPlanInfoDTO requestDTO) {
        Example example = new Example(SalesPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(DuplicateString.ID, requestDTO.getIds());
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        List<SalesPlan> salesPlans = salesPlanMapper.selectByCondition(example);
        if(CollUtil.isEmpty(salesPlans))
            throw new BizException(OrderErrorCode.CUSTOM,"销售计划不存在");
        //判断销售计划状态是否合法 前置状态需为待审批
        if (!BooleanUtil.isTrue(salesPlans
                .stream()
                .allMatch(v -> CharSequenceUtil.equals(v.getStatus(), DuplicateString.TO_APPROVE))))
            throw new BizException(OrderErrorCode.CUSTOM, "当前销售计划状态已发生变更，请刷新页面");
        try {
            //对当前审批单据进行加锁
            this.lock(salesPlans,requestDTO.getCreateUser());

            return salePlanApproval(requestDTO, salesPlans);
        } catch (BizException e){
            log.error("批量销售计划数据 业务报错 error:",e);
            throw e;
        }catch (Exception e){
            log.error("批量销售计划数据 异常 error:",e);
            throw new BizException(OrderErrorCode.CUSTOM,"销售计划审批异常，请联系管理员");
        }finally {
            //审批操作结束 释放当前操作人的锁
            this.unlock(salesPlans,requestDTO.getCreateUser());
        }
    }

    private int salePlanApproval(SalesPlanInfoDTO requestDTO, List<SalesPlan> salesPlans) {
        List<SalesPlanApproveRecord> approveRecordList = new ArrayList<>();
        salesPlans.forEach(salesPlan -> {
            salesPlan.setStatus(requestDTO.getStatus());
            salesPlanMapper.updateByPrimaryKeySelective(salesPlan);
            SalesPlanApproveRecord approveRecord = new SalesPlanApproveRecord();
            approveRecord.setId(uuidGenerator.gain());
            approveRecord.setPlanNo(salesPlan.getPlanNo());
            approveRecord.setApproveResult(requestDTO.getStatus());
            if (StringUtils.isNotBlank(requestDTO.getRejectedReason())) {
                approveRecord.setRejectedReason(requestDTO.getRejectedReason());
            }
            approveRecord.setCreateUser(requestDTO.getCreateUser());
            approveRecord.setCreateUserName(requestDTO.getCreateUserName());
            approveRecord.setCreateTime(new Date());
            approveRecord.setUpdateUser(requestDTO.getCreateUser());
            approveRecord.setUpdateTime(new Date());
            approveRecord.setDelFlg(false);
            approveRecordList.add(approveRecord);
            AccountDTO createUser = accountService.findById(salesPlan.getSubmitUser());
            if (requestDTO.getStatus().equals(DuplicateString.APPROVED)) {
                MessageDTO messageDTO = new MessageDTO();
                List<String> account = new ArrayList<>();
                account.add(createUser.getAccountId());
                messageDTO.setReceiveAccountIds(account);
                messageDTO.setTitle("销售计划审批已通过");
                messageDTO.setContent("尊敬的"+ createUser.getRealName() +"，您提交的销售计划"+ salesPlan.getPlanNo() +"申请已通过审批，望知悉，谢谢。");
                messageSendService.sendMessage(messageDTO);

                EmailDTO emailDTO = new EmailDTO();
                List<String> toEmailList = new ArrayList<>();
                toEmailList.add(createUser.getEmail());
                emailDTO.setTos(toEmailList);
                emailDTO.setToCcs(new ArrayList<>());
                emailDTO.setEmailTemplateCode(EmailTemplateEnum.SALES_PLAN_APPROVE.getCode());
                //邮件模板参数
                Map<String, Object> templateParam = new HashMap<>();
                templateParam.put("createUserName", createUser.getRealName());
                templateParam.put("salesPlanNo", salesPlan.getPlanNo());
                templateParam.put("salesPlanName", salesPlan.getPlanName());
                emailDTO.setTemplateParam(templateParam);
                emailSendService.sendEmail(emailDTO);
            }

            if (requestDTO.getStatus().equals(DuplicateString.REJECTED)) {
                MessageDTO messageDTO = new MessageDTO();
                List<String> account = new ArrayList<>();
                account.add(createUser.getAccountId());
                messageDTO.setReceiveAccountIds(account);
                messageDTO.setTitle("销售计划审批已驳回");
                messageDTO.setContent("尊敬的" + createUser.getRealName() + "，您有一条销售计划"+ salesPlan.getPlanNo() +"申请已被驳回，请及时查阅，谢谢。");
                messageSendService.sendMessage(messageDTO);
                EmailDTO emailDTO = new EmailDTO();
                List<String> toEmailList = new ArrayList<>();
                toEmailList.add(createUser.getEmail());
                emailDTO.setTos(toEmailList);
                emailDTO.setToCcs(new ArrayList<>());
                emailDTO.setEmailTemplateCode(EmailTemplateEnum.SALES_PLAN_REJECT.getCode());
                //邮件模板参数
                AccountDTO approveUser = accountService.findById(requestDTO.getCreateUser());
                Map<String, Object> templateParam = new HashMap<>();
                templateParam.put("createUserName", createUser.getRealName());
                templateParam.put("approveName", approveUser.getRealName());
                templateParam.put("salesPlanNo", salesPlan.getPlanNo());
                templateParam.put("salesPlanName", salesPlan.getPlanName());
                templateParam.put("rejectReason", requestDTO.getRejectedReason());
                emailDTO.setTemplateParam(templateParam);
                emailSendService.sendEmail(emailDTO);
            }

        });
        return salesPlanApproveMapper.insertList(approveRecordList);
    }

    @Override
    public List<SalesPlanApprovePageDTO> approveSalesPlanList(String id) {
        Example example = new Example(SalesPlanApproveRecord.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DuplicateString.PLAN_NO, id);
        criteria.andEqualTo(DuplicateString.DEL_FLG, false);
        example.orderBy("createTime").desc();
        List<SalesPlanApproveRecord> salesPlanApproveRecords = salesPlanApproveMapper.selectByCondition(example);
        List<SalesPlanApprovePageDTO> resDTOList = new ArrayList<>();
        salesPlanApproveRecords.forEach(record -> {
            SalesPlanApprovePageDTO resDTO = new SalesPlanApprovePageDTO();
            BeanUtils.copyProperties(record,resDTO);
            resDTO.setApproveDate(record.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            resDTO.setApproveUser(record.getCreateUserName());
            resDTOList.add(resDTO);
        });
        return resDTOList;
    }

    /**
     * 审批操作对销售计划单号加锁
     * 同一个单子只能由一个人审批
     * 锁有效时长3分钟
     */
    private void lock(List<SalesPlan> list, String operatorId) {
        if (CollUtil.isEmpty(list))
            return;
        for (SalesPlan v : list) {
            String key = CharSequenceUtil.format(SALE_PLAN_APPROVAL_LOCK, v.getPlanNo());
            Boolean lockRs = bizRedisService.getRedisTemplate().opsForValue().setIfAbsent(key, operatorId, 3, TimeUnit.MINUTES);
            if (!BooleanUtil.isTrue(lockRs))
                throw new BizException(OrderErrorCode.CUSTOM, CharSequenceUtil.format("销售计划{}正在进行审批", v.getPlanNo()));
        }
    }

    /**
     * 审批操作对销售计划单号解锁
     * 释放当前操作人的拿到的锁
     * 同一个单子只能由一个人审批
     * 锁有效时长3分钟
     */
    private void unlock(List<SalesPlan> list, String operatorId) {
        if (CollUtil.isEmpty(list))
            return;
        for (SalesPlan v : list) {
            String key = CharSequenceUtil.format(SALE_PLAN_APPROVAL_LOCK, v.getPlanNo());
            if (!BooleanUtil.isTrue(bizRedisService.existKey(key)))
                continue;
            String value = bizRedisService.get(key);
            if (CharSequenceUtil.equals(value, operatorId))
                bizRedisService.del(key);
        }
    }
}
