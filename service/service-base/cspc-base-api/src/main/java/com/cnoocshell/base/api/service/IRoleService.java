
package com.cnoocshell.base.api.service;

import com.cnoocshell.base.api.dto.DataPermissionAccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionGoodsCodeDTO;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.account.QueryAccountRoleDTO;
import com.cnoocshell.base.api.dto.account.RemoveAccountRoleDTO;
import com.cnoocshell.base.api.dto.account.SimpleAccountRoleDTO;
import com.cnoocshell.base.api.dto.role.*;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.common.result.ItemResult;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 角色相关service
 */
@Api(tags = {"IRoleService"}, description = "角色相关service")
@FeignClient(name = "cspc-service-base")
public interface IRoleService {

    @ApiOperation("根据账户id查询已分配的角色")
    @PostMapping(path = "/role/getRoleByAccountId", consumes = "application/json")
    List<RoleDTO> getRoleByAccountId(@RequestParam("arg0") String arg0);

    @ApiOperation("根据账户id批量查询可访问的web端")
    @PostMapping(path = "/role/getPlatformByAccountIds", consumes = "application/json")
    Map<String, Set<String>> getPlatformByAccountIds(@RequestBody Set<String> accountIds);

    @ApiOperation("给一个账户设置为个人买家角色")
    @PostMapping(value = "/role/setBuyerPersonal")
    void setBuyerPersonal(@RequestParam("memberId") String memberId,
                          @RequestParam("accountId") String accountId,
                          @RequestParam(value = "operatorId", required = false) String operatorId);

    @ApiOperation("给一个账户设置为企业买家角色")
    @PostMapping(path = "/role/setEnterpriseBuyer2Account", consumes = "application/json")
    void setEnterpriseBuyer2Account(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1, @RequestParam("arg2") String arg2);

    @ApiOperation("给一个账户设置为企业卖家角色")
    @PostMapping(path = "/role/setEnterpriseSeller2Account", consumes = "application/json")
    void setEnterpriseSeller2Account(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1, @RequestParam("arg2") String arg2);

    @PostMapping("/role/listBySimpleQuery")
    List<SimpleAccountRoleDTO> listBySimpleQuery(@RequestBody QueryAccountRoleDTO param);

    @ApiOperation("根据账户id查询已分配的角色")
    @PostMapping(path = "/role/getRoleByAccountId2", consumes = "application/json")
    AccountRoleDTO getRoleByAccountId2(@RequestBody AccountRoleDTO dto);

    @ApiOperation("变更主账号角色")
    @PostMapping(path = "/role/updateMainAccountRoleType", consumes = "application/json")
    void updateMainAccountRoleType(@RequestParam("oldMainAccountId") String oldMainAccountId,
                                   @RequestParam("changeAccountId") String changeAccountId,
                                   @RequestParam("operatorId") String operatorId,
                                   @RequestParam Boolean isSeller);



    @ApiOperation("分页查询角色列表")
    @RequestMapping(method = RequestMethod.POST, path = "/role/findRoleByCondiftion", consumes = "application/json")
    PageInfo<RolePageReponseDTO> findRoleByCondiftion(@RequestBody RolePageRequestDTO requestDTO);

    @ApiOperation("查询角色详情")
    @RequestMapping(method = RequestMethod.POST, path = "/role/findDetailByRoleCode", consumes = "application/json")
    RoleInfoDTO findDetailByRoleCode(@RequestParam String roleCode);

    @ApiOperation("修改角色状态（启用，停用）")
    @RequestMapping(method = RequestMethod.POST, path = "/role/updateStatus", consumes = "application/json")
    String updateStatus(@RequestParam("id") Long id, @RequestParam("status") int status);

    @ApiOperation("删除角色")
    @RequestMapping(method = RequestMethod.POST, path = "/role/deleteRole", consumes = "application/json")
    String deleteRole(@RequestParam("id") Long id);

    @ApiOperation("新增角色")
    @RequestMapping(method = RequestMethod.POST, path = "/role/addRole", consumes = "application/json")
    String addRole(@RequestBody @Validated RoleInfoDTO role);

    @ApiOperation("编辑角色")
    @RequestMapping(method = RequestMethod.POST, path = "/role/updateRole", consumes = "application/json")
    String updateRole(@RequestBody @Validated RoleInfoDTO role);

    @ApiOperation("更新账户角色关系")
    @PostMapping(path = "/role/updateAccountRole", consumes = "application/json")
    String updateAccountRole(@RequestBody AccountRoleDTO arg0);

    @ApiOperation("获取用户下的商品分类授权信息")
    @PostMapping(path = "/role/getDataPermissionList", consumes = "application/json")
    List<DataPermissionDTO> getDataPermissionList(@RequestBody AccountRoleDTO arg0);

    @ApiOperation("根据角色编码查询用户编码")
    @RequestMapping(method = RequestMethod.POST,path = "/role/getAccountIdByRoleCode", consumes = "application/json")
    List<String> getAccountIdByRoleCode(@RequestParam("roleCode") String roleCode);

    @ApiOperation("根据商品分类编码和角色信息查询用户")
    @RequestMapping(method = RequestMethod.POST,path = "/role/findAccountByGoodsCode", consumes = "application/json")
    List<DataPermissionAccountInfoDTO> findAccountByGoodsCode(@RequestBody DataPermissionGoodsCodeDTO accountInfoDTO);

    @ApiOperation("查询拥有对应角色的账户ID")
    @PostMapping(path = "/role/queryAccountIds", consumes = "application/json")
    List<String> queryAccountIds(@RequestBody QueryAccountDTO param);

    @ApiOperation("获取用户下的商品分类授权信息")
    @PostMapping(path = "/role/configureProductScope")
    ItemResult<List<DataPermissionDTO>> configureProductScope(@RequestBody AccountRoleDTO accountRoleDTO);

    @ApiOperation("根据会员ID和商品编码查询数据")
    @PostMapping(path = "/role/queryDataPermission")
    ItemResult<List<DataPermissionDTO>> queryDataPermission(@RequestBody QueryDataPermissionDTO param);

    @ApiOperation("根据goodsCode查询人员")
    @GetMapping(value = "/role/queryAccountIdsByGoodsCode")
    List<String> queryAccountIdsByGoodsCode(@RequestParam("goodsCode") String goodsCode);

    @ApiOperation("移除账户角色")
    @PostMapping("/role/removeAccountRole")
    void removeAccountRole(@RequestBody RemoveAccountRoleDTO param);
}