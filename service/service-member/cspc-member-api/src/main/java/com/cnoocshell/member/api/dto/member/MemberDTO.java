package com.cnoocshell.member.api.dto.member;

import com.cnoocshell.member.api.dto.KeyValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MemberDTO implements Serializable {
    /**
     * 正确状态值
     */
    @ApiModelProperty("正确状态值")
    public static final Integer FLG_TRUE = 1;
    /**
     * 错误状态值
     */
    @ApiModelProperty("错误状态值")
    public static final Integer FLG_FALSE = 0;

    /**
     * 企业类型，用法 => String.startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)
     */
    @ApiModelProperty("企业类型")
    public static final String ENTERPRISE_TYPE_PRE = "2";

    /**
     * 个人类型，用法 => String.startsWith(MemberDTO.PERSON_TYPE_PRE)
     */
    @ApiModelProperty("个人类型")
    public static final String PERSON_TYPE_PRE = "1";

    /**
     * 经营信息资质，用法 => String.startsWith(MemberDTO.BUSINESS_CERT_TYPE_PRE)
     */
    @ApiModelProperty("经营信息资质")
    public static final String BUSINESS_CERT_TYPE_PRE = "1";

    /**
     * 会员状态-正常，用法 => String.startsWith(MemberDTO.MEMBER_STATUS_NORMAL_PRE)
     */
    @ApiModelProperty("会员状态-正常")
    public static final String MEMBER_STATUS_NORMAL_PRE = "1";

    /**
     * 会员状态-异常，用法 => String.startsWith(MemberDTO.MEMBER_STATUS_ABNORMAL_PRE)
     */
    @ApiModelProperty("会员状态-异常")
    public static final String MEMBER_STATUS_ABNORMAL_PRE = "2";

    /**
     * redis-key
     */
    @ApiModelProperty("redis-key")
    public static final String DETAIL_PRE = "member:detail:";

    /**
     * redis-key
     */
    @ApiModelProperty("redis-key")
    public static final String DTO_PRE = "member:dto:";

    private static final long serialVersionUID = -6902125316210991832L;

    /**
     * 会员ID
     */
    @ApiModelProperty("会员ID")
    private String memberId;

    /**
     * 最近的请求ID
     */
    @ApiModelProperty("最近的请求ID")
    private String lastTimeRequestId;

    /**
     * 主账户id
     */
    @ApiModelProperty("主账户id")
    private String mainAccountId;

    /**
     * 会员名
     */
    @ApiModelProperty("会员名")
    private String memberName;

    /**
     * 会员简称
     */
    @ApiModelProperty("会员简称")
    private String memberShortName;

    /**
     * 会员代码
     */
    @ApiModelProperty("会员代码")
    private String memberCode;

    /**
     * 会员类型
     */
    @ApiModelProperty("会员类型")
    private String memberType;

    /**
     * 是否卖家
     */
    @ApiModelProperty("是否卖家")
    private Integer sellerFlg;


    /**
     * 账户冻结标识
     */
    @ApiModelProperty("会员状态")
    private String status;

    /**
     * 经营信息拒绝时 查看使用
     */
    @ApiModelProperty("请求ID，经营信息拒绝时 查看使用")
    private String businessRequestId;

    /**
     * 是否实名认证
     */
    @ApiModelProperty("是否实名认证")
    private Integer certRealName;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 所在国家编码
     */
    @ApiModelProperty("所在国家编码")
    private String countryCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty("所在省编码")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty("所在城市编码")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty("所在地区编码")
    private String areaCode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String addressDetail;

    /**
     * 注册详细地址
     */
    @ApiModelProperty("注册详细地址")
    private String rigistAddressDetail;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipCode;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 传真
     */
    @ApiModelProperty("传真")
    private String fax;

    /**
     * 网址
     */
    @ApiModelProperty("网址")
    private String website;

    /**
     * 企业信用号
     */
    @ApiModelProperty("企业信用号")
    private String creditCode;

    /**
     * 营业执照
     */
    @ApiModelProperty("营业执照")
    private MemberCertDTO businessCert;
    /**
     * 组织机构代码证
     */
    @ApiModelProperty("组织机构代码证")
    private MemberCertDTO organizationCert;
    /**
     * 税务登记证
     */
    @ApiModelProperty("税务登记证")
    private MemberCertDTO taxCert;

    /**
     * 营业范围
     */
    @ApiModelProperty("营业范围")
    private String mainProducts;

    /**
     * 商标URL
     */
    @ApiModelProperty("商标URL")
    private String trademarkUrl;

    @ApiModelProperty("省份(销售或购买范围)")
    private List<KeyValueDTO> provinceList;


    @ApiModelProperty("商品类别(销售或购买的)")
    private List<KeyValueDTO> goodsCategoryList;

    @ApiModelProperty("要购买或者销售的商品对应的商家的总公司id")
    private List<KeyValueDTO> tenantList;

    /**
     * 创建人
     */
    @ApiModelProperty("会员申请人")
    private String createUserName;

    /**
     * 会员申请人ID
     */
    @ApiModelProperty("会员申请人ID (accountID)")
    private String createUserId;

    /**
     * 一般资质列表
     */
    @ApiModelProperty("一般资质列表")
    private List<MemberCertDTO> memberCertDTOList;

    /**
     * 会员最近一次审批状态
     */
    @ApiModelProperty("会员最近一次审批状态")
    private String lastTimeRequestStatus;

    /**
     * 会员最近一次审批类型
     */
    @ApiModelProperty("会员最近一次审批类型")
    private String lastTimeRequestType;

    /**
     * 会员最近一次审批被拒原因
     */
    @ApiModelProperty("会员最近一次审批被拒原因")
    private String lastTimeRequestRejectReason;

    @ApiModelProperty("会员最近一次变更申请时间")
    private Date lastTimeRequestTime;


    @ApiModelProperty("主账号名称")
    private String mainAccountName;

    @ApiModelProperty("是否中海壳牌股东关联方")
    private Boolean shareholderRelationParty;

    @ApiModelProperty("意向变更状态")
    private String intentionChangeStatus;
    @ApiModelProperty("购买商品意向")
    private List<MemberPurchaseGoodsIntentionDTO> intentionList;

    @ApiModelProperty("企业保证金缴纳状态")
    private String depositStatus;

    @ApiModelProperty("最近一次经营信息变更请求ID")
    private String lastBusinessRequestId;
    @ApiModelProperty("最近一次经营信息状态")
    private String lastBusinessStatus;
    @ApiModelProperty("最近一次经营信息变更请求时间")
    private Date lastBusinessRequestTime;
    @ApiModelProperty("最近一次经营信息变更请求拒绝原因")
    private String lastBusinessRejectReason;

    @ApiModelProperty("最近一次一般资质变更请求ID")
    private String lastNormalCertRequestId;
    @ApiModelProperty("最近一次一般资质状态")
    private String lastNormalCertStatus;
    @ApiModelProperty("最近一次一般资质变更请求时间")
    private Date lastNormalCertRequestTime;
    @ApiModelProperty("最近一次一般资质变更请求拒绝原因")
    private String lastNormalCertRejectReason;

    @ApiModelProperty("身份证信息")
    private MemberCertDTO idCardInformation;
}
