package com.cnoocshell.order.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.order.api.dto.EnquiryBuyerDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListQueryDTO;
import com.cnoocshell.order.api.dto.EnquiryBuyerListViewDTO;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.dao.vo.EnquiryBuyer;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface EnquiryBuyerMapper extends IBaseMapper<EnquiryBuyer> {

    List<EnquiryBuyer> selectNotiryBuyer(Date startTime, Date endTime);

    List<BiddingBuyerDTO> selectBuyerList(@Param("idList") List<String> idList, @Param("enquiryStatus") String enquiryStatus);

    List<BiddingCustomerListDTO> queryBuyerUserInfo(@Param("dto") BiddingCustomerInfoDTO dto, @Param("status") String status);

    List<EnquiryBuyerDTO> selectMemberEnquiryDataByMemberCodes(List<String> memberCodes, String startDate, String endDate);

    List<EnquiryBuyerDTO> selectByEnquiryMemberCode(List<String> enquiryNos, String memberCode);

    List<EnquiryBuyerDTO> selectByEnquiryNoMemberCode(String enquiryNo, String memberCode);


    List<BiddingBuyerDTO> queryCheckBuyerUserInfo(@Param("buyerList") List<String> buyerList, @Param("enquiryTODOStatus") String enquiryTODOStatus, @Param("ids") List<String> ids);

    List<EnquiryBuyer> selectTODOList(String memberCode);

    List<EnquiryBuyerListViewDTO> buyerQueryEnquiryList(@Param("page") Page<EnquiryBuyerListViewDTO> page, @Param("dto") EnquiryBuyerListQueryDTO dto);

    List<EnquiryBuyer> queryEnquiryBuyer(@Param("enquiryId") String enquiryId, @Param("participationStatus") String participationStatus);

}

