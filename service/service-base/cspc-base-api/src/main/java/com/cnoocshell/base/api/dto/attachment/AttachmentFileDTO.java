package com.cnoocshell.base.api.dto.attachment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentFileDTO {

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件大小")
    private int fileSize;

    @ApiModelProperty("文件内容")
    private byte[] file;
}
