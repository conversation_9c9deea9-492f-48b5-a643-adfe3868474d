package com.cnoocshell.integration.service;

import com.cnoocshell.common.dto.WechatSendDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name ="cspc-service-interface")
public interface IWechatService {


    @PostMapping(path = "/message/wechat/send", consumes = "application/json")
    Boolean sendSubscribeMessage(@RequestBody WechatSendDTO request);

    @GetMapping(path = "/message/wechat/getOpenid", consumes = "application/json")
    Boolean getOpenId(@RequestParam("accountId")String accountId,@RequestParam("code") String code);

    @GetMapping(path = "/message/wechat/getTemplateById", consumes = "application/json")
    Map<String, Object> getTemplateById(String templateId);

    @PostMapping(path = "/message/wechat/refreshWechatTemplatesCache", consumes = "application/json")
    void refreshWechatTemplatesCache();

}
