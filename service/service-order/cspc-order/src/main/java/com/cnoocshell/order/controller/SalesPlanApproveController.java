package com.cnoocshell.order.controller;

import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanApprovePageDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageResponseDTO;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.service.SalesPlanApproveService;
import com.cnoocshell.order.service.SalesPlanService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Tag(name = "SalesPlanApproveController", description = "销售计划审批管理")
@RestController
@RequestMapping("/salesPlanApprove")
@RequiredArgsConstructor
public class SalesPlanApproveController {

    @Autowired
    private SalesPlanApproveService salesPlanApproveService;

    @Autowired
    private SalesPlanService salesPlanService;

    @ApiOperation("分页查询销售计划审批列表")
    @PostMapping("/findSalePlanApproveByCondition")
    PageInfo<SalesPlanPageResponseDTO> findSalePlanApproveByCondition(@RequestBody SalesPlanPageRequestDTO requestDTO){
        List<String> status = new ArrayList<>();
        status.add(DuplicateString.TO_APPROVE);
        requestDTO.setStatus(status);
        return salesPlanService.findSalePlanByCondition(requestDTO);
    }

    @ApiOperation("审批")
    @PostMapping("/approveSalesPlan")
    String approveSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO){
        return "审批" + salesPlanApproveService.approveSalesPlan(requestDTO) + "条数据";
    }

    @ApiOperation("审批数量")
    @PostMapping("/countApprove")
    Long countApprove(@RequestBody SalesPlanPageRequestDTO requestDTO){
        List<String> status = new ArrayList<>();
        status.add(DuplicateString.TO_APPROVE);
        requestDTO.setStatus(status);
        PageInfo<SalesPlanPageResponseDTO> salePlanByCondition = salesPlanService.findSalePlanByCondition(requestDTO);
        return salePlanByCondition.getTotal();
    }

    @ApiOperation("审批记录")
    @GetMapping("/approveSalesPlanList")
    List<SalesPlanApprovePageDTO> approveSalesPlanList(String id){
        return salesPlanApproveService.approveSalesPlanList(id);
    }
}
