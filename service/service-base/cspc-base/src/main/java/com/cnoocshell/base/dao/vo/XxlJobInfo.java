package com.cnoocshell.base.dao.vo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "xxl_job_info", schema = "xxl_job")
public class XxlJobInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "job_group")
    private Integer jobGroup;

    @Column(name = "job_desc")
    private String jobDesc;

    @Column(name = "add_time")
    private Date addTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "author")
    private String author;

    @Column(name = "alarm_email")
    private String alarmEmail;

    @Column(name = "schedule_type")
    private String scheduleType;

    @Column(name = "schedule_conf")
    private String scheduleConf;

    @Column(name = "misfire_strategy")
    private String misfireStrategy;

    @Column(name = "executor_route_strategy")
    private String executorRouteStrategy;

    @Column(name = "executor_handler")
    private String executorHandler;

    @Column(name = "executor_param")
    private String executorParam;

    @Column(name = "executor_block_strategy")
    private String executorBlockStrategy;

    @Column(name = "executor_timeout")
    private Integer executorTimeout;

    @Column(name = "executor_fail_retry_count")
    private Integer executorFailRetryCount;

    @Column(name = "glue_type")
    private String glueType;

    @Lob
    @Column(name = "glue_source")
    private String glueSource;

    @Column(name = "glue_remark")
    private String glueRemark;

    @Column(name = "glue_updatetime")
    private Date glueUpdatetime;

    @Column(name = "child_jobid")
    private String childJobid;

    @Column(name = "trigger_status")
    private Integer triggerStatus;

    @Column(name = "trigger_last_time")
    private Long triggerLastTime;

    @Column(name = "trigger_next_time")
    private Long triggerNextTime;

}