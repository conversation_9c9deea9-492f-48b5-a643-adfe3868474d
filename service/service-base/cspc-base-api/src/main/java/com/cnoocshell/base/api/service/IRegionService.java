
package com.cnoocshell.base.api.service;

import com.cnoocshell.base.api.dto.region.RegionDTO;
import com.cnoocshell.base.api.dto.region.RegionSampleDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Api(tags = {"IRegionService"}, description = "行政区域查询服务")
@FeignClient(name = "cspc-service-base")
public interface IRegionService {
    @ApiOperation("根据当前层级adcode查找下一层级的数据")
    @RequestMapping(method = RequestMethod.POST, path = "/region/findByParentAdCode", consumes = "application/json")
    List<RegionSampleDTO> findByParentAdCode(@RequestParam("parentAdcode") String parentAdcode);

    @ApiOperation("根据code批量查询名称")
    @RequestMapping(method = RequestMethod.POST, path = "/region/findNameByAdCodeList", consumes = "application/json")
    Map<String,String> findNameByAdCodeList(@RequestBody Set<String> adcodeList);

    @ApiOperation("查询全量区域数据")
    @RequestMapping(method = RequestMethod.GET, path = "/region/findAll", consumes = "application/json")
    List<RegionDTO> findAll();
}