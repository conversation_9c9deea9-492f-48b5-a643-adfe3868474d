package com.cnoocshell.order.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.salesPlan.*;
import com.cnoocshell.order.service.SalesPlanService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "SalesPlanController", description = "销售计划管理")
@RestController
@RequestMapping("/salesPlan")
@RequiredArgsConstructor
public class SalesPlanController {

    @Autowired
    private SalesPlanService salesPlanService;

    @ApiOperation("分页查询销售计划列表")
    @PostMapping("/findSalePlanByCondition")
    PageInfo<SalesPlanPageResponseDTO> findSalePlanByCondition(@RequestBody SalesPlanPageRequestDTO requestDTO){
        return salesPlanService.findSalePlanByCondition(requestDTO);
    }

    @ApiOperation("新增销售计划")
    @PostMapping("/addSalesPlan")
    String addSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO){
        return salesPlanService.addSalesPlan(requestDTO);
    }

    @ApiOperation("更新销售计划")
    @PostMapping("/updateSalesPlan")
    String updateSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO){
        return "更新" + salesPlanService.updateSalesPlan(requestDTO) + "条数据";
    }

    @ApiOperation("撤回销售计划")
    @PostMapping("/revokeSalesPlan")
    String revokeSalesPlan(@RequestParam("id") String id){
        return "撤回" + salesPlanService.revokeSalesPlan(id) + "条数据";
    }

    @ApiOperation("查询销售计划详情")
    @PostMapping("/findSalePlanById")
    SalesDetailDTO findSalePlanById(@RequestParam("id") String id, @RequestParam("accountId") String accountId, @RequestParam("memberId") String memberId){
        return salesPlanService.findSalePlanById(id,accountId,memberId);
    }

    @ApiOperation("删除草稿")
    @PostMapping("/removeDraft")
    public ItemResult<Void> removeDraft(@RequestBody RemoveSalesPlanDTO param){
        salesPlanService.removeDraft(param);
        return ItemResult.success();
    }
}
