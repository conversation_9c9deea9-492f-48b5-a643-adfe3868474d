package com.cnoocshell.base.api.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AccountReportResultDTO {
    @ApiModelProperty(value = "会员ID")
    private String memberId;

    @ApiModelProperty(value = "账号ID")
    private String accountId;

    @ApiModelProperty(value = "产品二级分类ID", notes = "ID")
    private String categoryIdLevelTwo;

    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;

    @ApiModelProperty(value = "产品名称")
    private String goodsName;

    @ApiModelProperty("角色编码")
    private List<String> roleCodes;
    @ApiModelProperty("角色名称")
    private List<String> roleNames;
}
