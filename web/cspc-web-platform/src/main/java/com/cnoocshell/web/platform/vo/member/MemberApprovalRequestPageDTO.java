package com.cnoocshell.web.platform.vo.member;

import com.cnoocshell.common.dto.BasePageDTO;
import com.cnoocshell.member.api.dto.member.MemberApprovalRequestQueryDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("会员变更分页请求")
public class MemberApprovalRequestPageDTO extends BasePageDTO {
    private MemberApprovalRequestQueryDTO memberApprovalRequestQueryDTO;
}
