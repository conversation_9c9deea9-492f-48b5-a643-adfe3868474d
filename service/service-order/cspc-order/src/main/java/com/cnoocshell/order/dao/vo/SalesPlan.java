package com.cnoocshell.order.dao.vo;

import cn.hutool.core.date.DatePattern;
import com.cnoocshell.common.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Table(name = "sales_plan")
@Data
public class SalesPlan extends BaseEntity {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "plan_no")
    private String planNo;

    @Column(name = "plan_name")
    private String planName;

    @Column(name = "is_need_inquiry")
    private Integer isNeedInquiry;

    @Column(name = "factory_code")
    private String factoryCode;

    @Column(name = "category_code")
    private String categoryCode;

    @Column(name = "goods_code")
    private String goodsCode;

    @Column(name = "goods_name")
    private String goodsName;

    @Column(name = "delivery_mode")
    private String deliveryMode;

    @Column(name = "delivery_cost_standard")
    private String deliveryCostStandard;

    @Column(name = "self_pickup_guide")
    private String selfPickupGuide;

    @Column(name = "self_pickup_carrier")
    private String selfPickupCarrier;

    @Column(name = "delivery_effect_start_date")
    private Date deliveryEffectStartDate;

    @Column(name = "delivery_effect_end_date")
    private Date deliveryEffectEndDate;

    @Column(name = "storage_quantity")
    private String storageQuantity;

    @Column(name = "delivery_production_quantity")
    private String deliveryProductionQuantity;

    @Column(name = "ltc_quantity")
    private String ltcQuantity;

    @Column(name = "spot_carryover_quantity")
    private String spotCarryoverQuantity;

    @Column(name = "others_quantity")
    private String othersQuantity;

    @Column(name = "target_storage_quantity")
    private String targetStorageQuantity;

    @Column(name = "system_sellable_quantity")
    private String systemSellableQuantity;

    @Column(name = "apply_sellable_quantity")
    private String applySellableQuantity;

    @Column(name = "expect_bidding_count")
    private Integer expectBiddingCount;

    @Column(name = "remark")
    private String remark;

    @Column(name = "status")
    private String status;

    @Column(name = "remain_sellable_quantity")
    private String remainSellAbleQuantity;

    @Column(name = "is_created_inquiry")
    private Integer isCreatedInquiry;

    @Column(name = "create_user_name")
    private String createUserName ;

    @Column(name = "submit_user")
    private String submitUser;

    @Column(name = "submit_user_name")
    private String submitUserName;

    @Column(name = "update_user_name")
    private String updateUserName;


    @Column(name = "submit_time")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;
}
