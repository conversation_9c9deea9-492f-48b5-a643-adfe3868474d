package com.cnoocshell.common.utils;

import com.github.pagehelper.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 */
public class ListUtils
{
    private ListUtils()
    {}

    /**
     * 对数据进行分页
     * @param list
     * @param size
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> toListPage(List<T> list, int size)
    {
        int maxPage = (int) Math.ceil(list.size() / (float) size);

        List<List<T>> dataList = new ArrayList<>();
        for(int page = 0; page < maxPage; page++)
        {
            List<T> data = list.stream().skip((long)(page * size)).limit(size).collect(Collectors.toList());
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 获取指定页码的数据
     * @param list
     * @param page
     * @param size
     * @param <T>
     * @return
     */
    public static <T> List<T> toListPage(List<T> list, int page, int size)
    {
        return list.stream().skip((long)((page - 1) * size)).limit(size).collect(Collectors.toList());
    }

    /**
     * 字符串分割为List
     * @param str
     * @return
     */
    public static List<String> toList(String str)
    {
        return toList(str, ",");
    }

    /**
     * 字符串分割为List
     * @param str
     * @param separate
     * @return
     */
    public static List<String> toList(String str, String separate)
    {
        if(StringUtil.isEmpty(str))
        {
            return new ArrayList<>();
        }

        String[] arr = str.split(separate);
        return newArrayList(arr);
    }

    /**
     * 初始化一个List
     * @param strArr
     * @param <E>
     * @return
     */
    public static <E> List<E> newArrayList(E... strArr)
    {
        List<E> list = new ArrayList<>();
        for(E str : strArr)
        {
            list.add(str);
        }

        return list;
    }
}
