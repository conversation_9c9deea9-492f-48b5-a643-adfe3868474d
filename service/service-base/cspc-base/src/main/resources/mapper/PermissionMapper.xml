<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.PermissionMapper">

    <select id="queryRolePermissions" resultType="com.cnoocshell.base.dao.vo.Permission">
        select *
        from sys_permission
        where del_flg = 0
        and permission_code in
        <foreach collection="permissionCodes" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        and app_type=#{appType}
        order by order_number is null,order_number asc
    </select>
</mapper>