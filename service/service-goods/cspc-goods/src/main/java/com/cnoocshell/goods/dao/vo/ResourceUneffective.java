package com.cnoocshell.goods.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "go_resource_uneffective")
public class ResourceUneffective implements Serializable {
    /**
     * 待生效资源ID
     */
    @Id
    @Column(name = "effective_id")
    private String effectiveId;

    /**
     * 资源ID
     */
    @Column(name = "resource_id")
    private String resourceId;

    /**
     * SKU属性聚合
     */
    @Column(name = "resource_attributes")
    private String resourceAttributes;

    /**
     * 资源区域集合
     */
    @Column(name = "resource_regions")
    private String resourceRegions;

    /**
     * 商品资源聚合ID
     */
    @Column(name = "goods_resource_id")
    private String goodsResourceId;

    /**
     * 品类商品ID
     */
    @Column(name = "spu_id")
    private String spuId;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 商品分类
     */
    @Column(name = "goods_type")
    private String goodsType;

    /**
     * 绑定的台班费规则ID
     */
    @Column(name = "machine_rule_id")
    private String machineRuleId;

    /**
     * 空载费规则id
     */
    @Column(name = "empty_load_rule_id")
    private String emptyLoadRuleId;

    /**
     * 商品分类编码
     */
    @Column(name = "category_code")
    private String categoryCode;

    /**
     * 商品描述
     */
    @Column(name = "goods_describe")
    private String goodsDescribe;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 资源名称
     */
    @Column(name = "resource_name")
    private String resourceName;

    /**
     * 资源code
     */
    @Column(name = "resource_code")
    private String resourceCode;

    /**
     * 生效时间
     */
    @Column(name = "effect_time")
    private Date effectTime;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    @Column(name = "currency_symbol")
    private String currencySymbol;

    /**
     * 币种名称
     */
    @Column(name = "currency_name")
    private String currencyName;

    /**
     * 价格描述
     */
    @Column(name = "price_describe")
    private String priceDescribe;

    /**
     * 计价方式，1 出厂价 2到位价
     */
    @Column(name = "price_way")
    private String priceWay;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 是否可议价
     */
    @Column(name = "if_protocol_price")
    private Boolean ifProtocolPrice;

    /**
     * 计量单位
     */
    @Column(name = "price_unit")
    private String priceUnit;

    /**
     * 销售单位
     */
    @Column(name = "sale_unit")
    private String saleUnit;

    /**
     * 仓库ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 仓库名称
     */
    @Column(name = "store_name")
    private String storeName;

    /**
     * 仓库类型, 100卖家基地库 200平台中心仓
     */
    @Column(name = "store_type")
    private String storeType;

    /**
     * 仓库详细地址
     */
    @Column(name = "store_address")
    private String storeAddress;

    /**
     * 版本号
     */
    @Column(name = "resource_version")
    private Integer resourceVersion;

    /**
     * 资源状态, 100已挂牌  200已撤牌  300撤牌处理中  400未上架  500待审批
     */
    private String status;

    /**
     * 修改原因
     */
    @Column(name = "approval_message")
    private String approvalMessage;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家姓名
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 卖家简称
     */
    @Column(name = "seller_nick_name")
    private String sellerNickName;

    /**
     * 销售员ID
     */
    @Column(name = "sales_id")
    private String salesId;

    /**
     * 销售员姓名
     */
    @Column(name = "sales_name")
    private String salesName;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    private String orgName;

    /**
     * 总销售数量
     */
    @Column(name = "sale_num")
    private BigDecimal saleNum;

    /**
     * 可售数量
     */
    @Column(name = "cansale_num")
    private BigDecimal cansaleNum;

    /**
     * 锁定数量
     */
    @Column(name = "lock_num")
    private BigDecimal lockNum;

    /**
     * 起售数量
     */
    @Column(name = "volume_num")
    private BigDecimal volumeNum;

    /**
     * 容差类型， 100绝对容差 200相对容差
     */
    @Column(name = "tolerance_type")
    private String toleranceType;

    /**
     * 容差
     */
    private BigDecimal tolerance;

    /**
     * 单笔最大购买量
     */
    @Column(name = "ordermax_num")
    private BigDecimal ordermaxNum;

    /**
     * 单笔最小购买量
     */
    @Column(name = "ordermin_num")
    private BigDecimal orderminNum;

    /**
     * 单笔最小变动量
     */
    @Column(name = "orderminchange_num")
    private BigDecimal orderminchangeNum;

    /**
     * 单日最大购买量
     */
    @Column(name = "daymax_num")
    private BigDecimal daymaxNum;

    /**
     * 区域定价层级
     */
    @Column(name = "area_level")
    private Integer areaLevel;

    /**
     * 是否允许自提
     */
    @Column(name = "if_take_self")
    private Boolean ifTakeSelf;

    /**
     * 是否平台配送
     */
    @Column(name = "if_platform_delivery")
    private Boolean ifPlatformDelivery;

    /**
     * 是否卖家配送
     */
    @Column(name = "if_seller_delivery")
    private Boolean ifSellerDelivery;

    /**
     * 自提优惠单价
     */
    @Column(name = "take_self_discounts")
    private BigDecimal takeSelfDiscounts;

    /**
     * 免物流费重量
     */
    @Column(name = "logistics_weight")
    private BigDecimal logisticsWeight;

    /**
     * 免物流费计算类型
     */
    @Column(name = "logistics_type")
    private String logisticsType;

    /**
     * 物流费计算单位
     */
    @Column(name = "logistics_unit")
    private String logisticsUnit;

    /**
     * 物流费单价
     */
    @Column(name = "logistics_price")
    private BigDecimal logisticsPrice;

    /**
     * 搬运费类型
     */
    @Column(name = "cartage_rule_id")
    private String cartageRuleId;

    /**
     * 支付方式
     */
    @Column(name = "pay_way")
    private String payWay;

    /**
     * 是否定时上架
     */
    private Boolean ifup;

    /**
     * 定时上架时间
     */
    @Column(name = "fix_uptime")
    private Date fixUptime;

    /**
     * 是否定时下架
     */
    private Boolean ifdown;

    /**
     * 定时下架时间
     */
    @Column(name = "fix_downtime")
    private Date fixDowntime;

    /**
     * 上架时间
     */
    @Column(name = "up_time")
    private Date upTime;

    /**
     * 下架时间
     */
    @Column(name = "down_time")
    private Date downTime;

    /**
     * 交易开始时间
     */
    @Column(name = "trade_starttime")
    private Date tradeStarttime;

    /**
     * 交易结束时间
     */
    @Column(name = "trade_endtime")
    private Date tradeEndtime;

    /**
     * 是否流量管控
     */
    @Column(name = "if_flowcontrol")
    private Boolean ifFlowcontrol;

    /**
     * 是否允许分批发货
     */
    @Column(name = "allow_partial")
    private Boolean allowPartial;

    /**
     * 销售区域
     */
    @Column(name = "sale_area")
    private String saleArea;

    /**
     * 一级销售区域编码
     */
    @Column(name = "sale_area_code")
    private String saleAreaCode;

    /**
     * 二级销售区域编码
     */
    @Column(name = "sale_area_code2")
    private String saleAreaCode2;

    /**
     * 三级销售区域编码
     */
    @Column(name = "sale_area_code3")
    private String saleAreaCode3;

    /**
     * 四级销售区域编码
     */
    @Column(name = "sale_area_code4")
    private String saleAreaCode4;

    /**
     * 五级销售区域编码
     */
    @Column(name = "sale_area_code5")
    private String saleAreaCode5;

    /**
     * 一级销售区域名称
     */
    @Column(name = "sale_area_name")
    private String saleAreaName;

    /**
     * 二级销售区域名称
     */
    @Column(name = "sale_area_name2")
    private String saleAreaName2;

    /**
     * 三级销售区域名称
     */
    @Column(name = "sale_area_name3")
    private String saleAreaName3;

    /**
     * 四级销售区域名称
     */
    @Column(name = "sale_area_name4")
    private String saleAreaName4;

    /**
     * 五级销售区域名称
     */
    @Column(name = "sale_area_name5")
    private String saleAreaName5;

    /**
     * 支付有效期类型
     */
    @Column(name = "paydate_type")
    private String paydateType;

    /**
     * 支付有效期
     */
    @Column(name = "paydate_limit")
    private Long paydateLimit;

    /**
     * 提货有效期类型
     */
    @Column(name = "takedate_type")
    private String takedateType;

    /**
     * 提货有效期
     */
    @Column(name = "takedate_limit")
    private Long takedateLimit;

    /**
     * 出厂价
     */
    @Column(name = "factory_price")
    private BigDecimal factoryPrice;

    /**
     * 到位价
     */
    @Column(name = "arrive_price")
    private BigDecimal arrivePrice;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    private static final long serialVersionUID = 1L;

    /**
     * 获取待生效资源ID
     *
     * @return effective_id - 待生效资源ID
     */
    public String getEffectiveId() {
        return effectiveId;
    }

    /**
     * 设置待生效资源ID
     *
     * @param effectiveId 待生效资源ID
     */
    public void setEffectiveId(String effectiveId) {
        this.effectiveId = effectiveId == null ? null : effectiveId.trim();
    }

    public BigDecimal getFactoryPrice() {
        return factoryPrice;
    }

    public void setFactoryPrice(BigDecimal factoryPrice) {
        this.factoryPrice = factoryPrice;
    }

    public BigDecimal getArrivePrice() {
        return arrivePrice;
    }

    public void setArrivePrice(BigDecimal arrivePrice) {
        this.arrivePrice = arrivePrice;
    }

    /**
     * 获取资源ID
     *
     * @return resource_id - 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源ID
     *
     * @param resourceId 资源ID
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * 获取SKU属性聚合
     *
     * @return resource_attributes - SKU属性聚合
     */
    public String getResourceAttributes() {
        return resourceAttributes;
    }

    /**
     * 设置SKU属性聚合
     *
     * @param resourceAttributes SKU属性聚合
     */
    public void setResourceAttributes(String resourceAttributes) {
        this.resourceAttributes = resourceAttributes == null ? null : resourceAttributes.trim();
    }

    /**
     * 获取资源区域集合
     *
     * @return resource_regions - 资源区域集合
     */
    public String getResourceRegions() {
        return resourceRegions;
    }

    /**
     * 设置资源区域集合
     *
     * @param resourceRegions 资源区域集合
     */
    public void setResourceRegions(String resourceRegions) {
        this.resourceRegions = resourceRegions == null ? null : resourceRegions.trim();
    }

    /**
     * 获取商品资源聚合ID
     *
     * @return goods_resource_id - 商品资源聚合ID
     */
    public String getGoodsResourceId() {
        return goodsResourceId;
    }

    /**
     * 设置商品资源聚合ID
     *
     * @param goodsResourceId 商品资源聚合ID
     */
    public void setGoodsResourceId(String goodsResourceId) {
        this.goodsResourceId = goodsResourceId == null ? null : goodsResourceId.trim();
    }

    /**
     * 获取品类商品ID
     *
     * @return spu_id - 品类商品ID
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     * 设置品类商品ID
     *
     * @param spuId 品类商品ID
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId == null ? null : spuId.trim();
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取商品分类
     *
     * @return goods_type - 商品分类
     */
    public String getGoodsType() {
        return goodsType;
    }

    /**
     * 设置商品分类
     *
     * @param goodsType 商品分类
     */
    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType == null ? null : goodsType.trim();
    }

    /**
     * 获取绑定的台班费规则ID
     *
     * @return machine_rule_id - 绑定的台班费规则ID
     */
    public String getMachineRuleId() {
        return machineRuleId;
    }

    /**
     * 设置绑定的台班费规则ID
     *
     * @param machineRuleId 绑定的台班费规则ID
     */
    public void setMachineRuleId(String machineRuleId) {
        this.machineRuleId = machineRuleId == null ? null : machineRuleId.trim();
    }

    /**
     * 获取空载费规则id
     *
     * @return empty_load_rule_id - 空载费规则id
     */
    public String getEmptyLoadRuleId() {
        return emptyLoadRuleId;
    }

    /**
     * 设置空载费规则id
     *
     * @param emptyLoadRuleId 空载费规则id
     */
    public void setEmptyLoadRuleId(String emptyLoadRuleId) {
        this.emptyLoadRuleId = emptyLoadRuleId == null ? null : emptyLoadRuleId.trim();
    }

    /**
     * 获取商品分类编码
     *
     * @return category_code - 商品分类编码
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 设置商品分类编码
     *
     * @param categoryCode 商品分类编码
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    /**
     * 获取商品描述
     *
     * @return goods_describe - 商品描述
     */
    public String getGoodsDescribe() {
        return goodsDescribe;
    }

    /**
     * 设置商品描述
     *
     * @param goodsDescribe 商品描述
     */
    public void setGoodsDescribe(String goodsDescribe) {
        this.goodsDescribe = goodsDescribe == null ? null : goodsDescribe.trim();
    }

    /**
     * 获取商品品牌
     *
     * @return brand - 商品品牌
     */
    public String getBrand() {
        return brand;
    }

    /**
     * 设置商品品牌
     *
     * @param brand 商品品牌
     */
    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    /**
     * 获取资源名称
     *
     * @return resource_name - 资源名称
     */
    public String getResourceName() {
        return resourceName;
    }

    /**
     * 设置资源名称
     *
     * @param resourceName 资源名称
     */
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    /**
     * 获取资源code
     *
     * @return resource_code - 资源code
     */
    public String getResourceCode() {
        return resourceCode;
    }

    /**
     * 设置资源code
     *
     * @param resourceCode 资源code
     */
    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode == null ? null : resourceCode.trim();
    }

    /**
     * 获取生效时间
     *
     * @return effect_time - 生效时间
     */
    public Date getEffectTime() {
        return effectTime;
    }

    /**
     * 设置生效时间
     *
     * @param effectTime 生效时间
     */
    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    /**
     * 获取币种
     *
     * @return currency - 币种
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 设置币种
     *
     * @param currency 币种
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * 获取币种符号
     *
     * @return currency_symbol - 币种符号
     */
    public String getCurrencySymbol() {
        return currencySymbol;
    }

    /**
     * 设置币种符号
     *
     * @param currencySymbol 币种符号
     */
    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol == null ? null : currencySymbol.trim();
    }

    /**
     * 获取币种名称
     *
     * @return currency_name - 币种名称
     */
    public String getCurrencyName() {
        return currencyName;
    }

    /**
     * 设置币种名称
     *
     * @param currencyName 币种名称
     */
    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName == null ? null : currencyName.trim();
    }

    /**
     * 获取价格描述
     *
     * @return price_describe - 价格描述
     */
    public String getPriceDescribe() {
        return priceDescribe;
    }

    /**
     * 设置价格描述
     *
     * @param priceDescribe 价格描述
     */
    public void setPriceDescribe(String priceDescribe) {
        this.priceDescribe = priceDescribe == null ? null : priceDescribe.trim();
    }

    /**
     * 获取计价方式，1 出厂价 2到位价
     *
     * @return price_way - 计价方式，1 出厂价 2到位价
     */
    public String getPriceWay() {
        return priceWay;
    }

    /**
     * 设置计价方式，1 出厂价 2到位价
     *
     * @param priceWay 计价方式，1 出厂价 2到位价
     */
    public void setPriceWay(String priceWay) {
        this.priceWay = priceWay == null ? null : priceWay.trim();
    }

    /**
     * 获取价格
     *
     * @return price - 价格
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置价格
     *
     * @param price 价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取是否可议价
     *
     * @return if_protocol_price - 是否可议价
     */
    public Boolean getIfProtocolPrice() {
        return ifProtocolPrice;
    }

    /**
     * 设置是否可议价
     *
     * @param ifProtocolPrice 是否可议价
     */
    public void setIfProtocolPrice(Boolean ifProtocolPrice) {
        this.ifProtocolPrice = ifProtocolPrice;
    }

    /**
     * 获取计量单位
     *
     * @return price_unit - 计量单位
     */
    public String getPriceUnit() {
        return priceUnit;
    }

    /**
     * 设置计量单位
     *
     * @param priceUnit 计量单位
     */
    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit == null ? null : priceUnit.trim();
    }

    /**
     * 获取销售单位
     *
     * @return sale_unit - 销售单位
     */
    public String getSaleUnit() {
        return saleUnit;
    }

    /**
     * 设置销售单位
     *
     * @param saleUnit 销售单位
     */
    public void setSaleUnit(String saleUnit) {
        this.saleUnit = saleUnit == null ? null : saleUnit.trim();
    }

    /**
     * 获取仓库ID
     *
     * @return store_id - 仓库ID
     */
    public String getStoreId() {
        return storeId;
    }

    /**
     * 设置仓库ID
     *
     * @param storeId 仓库ID
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId == null ? null : storeId.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return store_name - 仓库名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 设置仓库名称
     *
     * @param storeName 仓库名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName == null ? null : storeName.trim();
    }

    /**
     * 获取仓库类型, 100卖家基地库 200平台中心仓
     *
     * @return store_type - 仓库类型, 100卖家基地库 200平台中心仓
     */
    public String getStoreType() {
        return storeType;
    }

    /**
     * 设置仓库类型, 100卖家基地库 200平台中心仓
     *
     * @param storeType 仓库类型, 100卖家基地库 200平台中心仓
     */
    public void setStoreType(String storeType) {
        this.storeType = storeType == null ? null : storeType.trim();
    }

    /**
     * 获取仓库详细地址
     *
     * @return store_address - 仓库详细地址
     */
    public String getStoreAddress() {
        return storeAddress;
    }

    /**
     * 设置仓库详细地址
     *
     * @param storeAddress 仓库详细地址
     */
    public void setStoreAddress(String storeAddress) {
        this.storeAddress = storeAddress == null ? null : storeAddress.trim();
    }

    /**
     * 获取版本号
     *
     * @return resource_version - 版本号
     */
    public Integer getResourceVersion() {
        return resourceVersion;
    }

    /**
     * 设置版本号
     *
     * @param resourceVersion 版本号
     */
    public void setResourceVersion(Integer resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    /**
     * 获取资源状态, 100已挂牌  200已撤牌  300撤牌处理中  400未上架  500待审批
     *
     * @return status - 资源状态, 100已挂牌  200已撤牌  300撤牌处理中  400未上架  500待审批
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置资源状态, 100已挂牌  200已撤牌  300撤牌处理中  400未上架  500待审批
     *
     * @param status 资源状态, 100已挂牌  200已撤牌  300撤牌处理中  400未上架  500待审批
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取修改原因
     *
     * @return approval_message - 修改原因
     */
    public String getApprovalMessage() {
        return approvalMessage;
    }

    /**
     * 设置修改原因
     *
     * @param approvalMessage 修改原因
     */
    public void setApprovalMessage(String approvalMessage) {
        this.approvalMessage = approvalMessage == null ? null : approvalMessage.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家姓名
     *
     * @return seller_name - 卖家姓名
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家姓名
     *
     * @param sellerName 卖家姓名
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取卖家简称
     *
     * @return seller_nick_name - 卖家简称
     */
    public String getSellerNickName() {
        return sellerNickName;
    }

    /**
     * 设置卖家简称
     *
     * @param sellerNickName 卖家简称
     */
    public void setSellerNickName(String sellerNickName) {
        this.sellerNickName = sellerNickName == null ? null : sellerNickName.trim();
    }

    /**
     * 获取销售员ID
     *
     * @return sales_id - 销售员ID
     */
    public String getSalesId() {
        return salesId;
    }

    /**
     * 设置销售员ID
     *
     * @param salesId 销售员ID
     */
    public void setSalesId(String salesId) {
        this.salesId = salesId == null ? null : salesId.trim();
    }

    /**
     * 获取销售员姓名
     *
     * @return sales_name - 销售员姓名
     */
    public String getSalesName() {
        return salesName;
    }

    /**
     * 设置销售员姓名
     *
     * @param salesName 销售员姓名
     */
    public void setSalesName(String salesName) {
        this.salesName = salesName == null ? null : salesName.trim();
    }

    /**
     * 获取联系电话
     *
     * @return contact_phone - 联系电话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置联系电话
     *
     * @param contactPhone 联系电话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    /**
     * 获取组织机构ID
     *
     * @return org_id - 组织机构ID
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * 设置组织机构ID
     *
     * @param orgId 组织机构ID
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    /**
     * 获取组织机构名称
     *
     * @return org_name - 组织机构名称
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * 设置组织机构名称
     *
     * @param orgName 组织机构名称
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    /**
     * 获取总销售数量
     *
     * @return sale_num - 总销售数量
     */
    public BigDecimal getSaleNum() {
        return saleNum;
    }

    /**
     * 设置总销售数量
     *
     * @param saleNum 总销售数量
     */
    public void setSaleNum(BigDecimal saleNum) {
        this.saleNum = saleNum;
    }

    /**
     * 获取可售数量
     *
     * @return cansale_num - 可售数量
     */
    public BigDecimal getCansaleNum() {
        return cansaleNum;
    }

    /**
     * 设置可售数量
     *
     * @param cansaleNum 可售数量
     */
    public void setCansaleNum(BigDecimal cansaleNum) {
        this.cansaleNum = cansaleNum;
    }

    /**
     * 获取锁定数量
     *
     * @return lock_num - 锁定数量
     */
    public BigDecimal getLockNum() {
        return lockNum;
    }

    /**
     * 设置锁定数量
     *
     * @param lockNum 锁定数量
     */
    public void setLockNum(BigDecimal lockNum) {
        this.lockNum = lockNum;
    }

    /**
     * 获取起售数量
     *
     * @return volume_num - 起售数量
     */
    public BigDecimal getVolumeNum() {
        return volumeNum;
    }

    /**
     * 设置起售数量
     *
     * @param volumeNum 起售数量
     */
    public void setVolumeNum(BigDecimal volumeNum) {
        this.volumeNum = volumeNum;
    }

    /**
     * 获取容差类型， 100绝对容差 200相对容差
     *
     * @return tolerance_type - 容差类型， 100绝对容差 200相对容差
     */
    public String getToleranceType() {
        return toleranceType;
    }

    /**
     * 设置容差类型， 100绝对容差 200相对容差
     *
     * @param toleranceType 容差类型， 100绝对容差 200相对容差
     */
    public void setToleranceType(String toleranceType) {
        this.toleranceType = toleranceType == null ? null : toleranceType.trim();
    }

    /**
     * 获取容差
     *
     * @return tolerance - 容差
     */
    public BigDecimal getTolerance() {
        return tolerance;
    }

    /**
     * 设置容差
     *
     * @param tolerance 容差
     */
    public void setTolerance(BigDecimal tolerance) {
        this.tolerance = tolerance;
    }

    /**
     * 获取单笔最大购买量
     *
     * @return ordermax_num - 单笔最大购买量
     */
    public BigDecimal getOrdermaxNum() {
        return ordermaxNum;
    }

    /**
     * 设置单笔最大购买量
     *
     * @param ordermaxNum 单笔最大购买量
     */
    public void setOrdermaxNum(BigDecimal ordermaxNum) {
        this.ordermaxNum = ordermaxNum;
    }

    /**
     * 获取单笔最小购买量
     *
     * @return ordermin_num - 单笔最小购买量
     */
    public BigDecimal getOrderminNum() {
        return orderminNum;
    }

    /**
     * 设置单笔最小购买量
     *
     * @param orderminNum 单笔最小购买量
     */
    public void setOrderminNum(BigDecimal orderminNum) {
        this.orderminNum = orderminNum;
    }

    /**
     * 获取单笔最小变动量
     *
     * @return orderminchange_num - 单笔最小变动量
     */
    public BigDecimal getOrderminchangeNum() {
        return orderminchangeNum;
    }

    /**
     * 设置单笔最小变动量
     *
     * @param orderminchangeNum 单笔最小变动量
     */
    public void setOrderminchangeNum(BigDecimal orderminchangeNum) {
        this.orderminchangeNum = orderminchangeNum;
    }

    /**
     * 获取单日最大购买量
     *
     * @return daymax_num - 单日最大购买量
     */
    public BigDecimal getDaymaxNum() {
        return daymaxNum;
    }

    /**
     * 设置单日最大购买量
     *
     * @param daymaxNum 单日最大购买量
     */
    public void setDaymaxNum(BigDecimal daymaxNum) {
        this.daymaxNum = daymaxNum;
    }

    /**
     * 获取区域定价层级
     *
     * @return area_level - 区域定价层级
     */
    public Integer getAreaLevel() {
        return areaLevel;
    }

    /**
     * 设置区域定价层级
     *
     * @param areaLevel 区域定价层级
     */
    public void setAreaLevel(Integer areaLevel) {
        this.areaLevel = areaLevel;
    }

    /**
     * 获取是否允许自提
     *
     * @return if_take_self - 是否允许自提
     */
    public Boolean getIfTakeSelf() {
        return ifTakeSelf;
    }

    /**
     * 设置是否允许自提
     *
     * @param ifTakeSelf 是否允许自提
     */
    public void setIfTakeSelf(Boolean ifTakeSelf) {
        this.ifTakeSelf = ifTakeSelf;
    }

    /**
     * 获取是否平台配送
     *
     * @return if_platform_delivery - 是否平台配送
     */
    public Boolean getIfPlatformDelivery() {
        return ifPlatformDelivery;
    }

    /**
     * 设置是否平台配送
     *
     * @param ifPlatformDelivery 是否平台配送
     */
    public void setIfPlatformDelivery(Boolean ifPlatformDelivery) {
        this.ifPlatformDelivery = ifPlatformDelivery;
    }

    /**
     * 获取是否卖家配送
     *
     * @return if_seller_delivery - 是否卖家配送
     */
    public Boolean getIfSellerDelivery() {
        return ifSellerDelivery;
    }

    /**
     * 设置是否卖家配送
     *
     * @param ifSellerDelivery 是否卖家配送
     */
    public void setIfSellerDelivery(Boolean ifSellerDelivery) {
        this.ifSellerDelivery = ifSellerDelivery;
    }

    /**
     * 获取自提优惠单价
     *
     * @return take_self_discounts - 自提优惠单价
     */
    public BigDecimal getTakeSelfDiscounts() {
        return takeSelfDiscounts;
    }

    /**
     * 设置自提优惠单价
     *
     * @param takeSelfDiscounts 自提优惠单价
     */
    public void setTakeSelfDiscounts(BigDecimal takeSelfDiscounts) {
        this.takeSelfDiscounts = takeSelfDiscounts;
    }

    /**
     * 获取免物流费重量
     *
     * @return logistics_weight - 免物流费重量
     */
    public BigDecimal getLogisticsWeight() {
        return logisticsWeight;
    }

    /**
     * 设置免物流费重量
     *
     * @param logisticsWeight 免物流费重量
     */
    public void setLogisticsWeight(BigDecimal logisticsWeight) {
        this.logisticsWeight = logisticsWeight;
    }

    /**
     * 获取免物流费计算类型
     *
     * @return logistics_type - 免物流费计算类型
     */
    public String getLogisticsType() {
        return logisticsType;
    }

    /**
     * 设置免物流费计算类型
     *
     * @param logisticsType 免物流费计算类型
     */
    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType == null ? null : logisticsType.trim();
    }

    /**
     * 获取物流费计算单位
     *
     * @return logistics_unit - 物流费计算单位
     */
    public String getLogisticsUnit() {
        return logisticsUnit;
    }

    /**
     * 设置物流费计算单位
     *
     * @param logisticsUnit 物流费计算单位
     */
    public void setLogisticsUnit(String logisticsUnit) {
        this.logisticsUnit = logisticsUnit == null ? null : logisticsUnit.trim();
    }

    /**
     * 获取物流费单价
     *
     * @return logistics_price - 物流费单价
     */
    public BigDecimal getLogisticsPrice() {
        return logisticsPrice;
    }

    /**
     * 设置物流费单价
     *
     * @param logisticsPrice 物流费单价
     */
    public void setLogisticsPrice(BigDecimal logisticsPrice) {
        this.logisticsPrice = logisticsPrice;
    }

    /**
     * 获取搬运费类型
     *
     * @return cartage_rule_id - 搬运费类型
     */
    public String getCartageRuleId() {
        return cartageRuleId;
    }

    /**
     * 设置搬运费类型
     *
     * @param cartageRuleId 搬运费类型
     */
    public void setCartageRuleId(String cartageRuleId) {
        this.cartageRuleId = cartageRuleId == null ? null : cartageRuleId.trim();
    }

    /**
     * 获取支付方式
     *
     * @return pay_way - 支付方式
     */
    public String getPayWay() {
        return payWay;
    }

    /**
     * 设置支付方式
     *
     * @param payWay 支付方式
     */
    public void setPayWay(String payWay) {
        this.payWay = payWay == null ? null : payWay.trim();
    }

    /**
     * 获取是否定时上架
     *
     * @return ifup - 是否定时上架
     */
    public Boolean getIfup() {
        return ifup;
    }

    /**
     * 设置是否定时上架
     *
     * @param ifup 是否定时上架
     */
    public void setIfup(Boolean ifup) {
        this.ifup = ifup;
    }

    /**
     * 获取定时上架时间
     *
     * @return fix_uptime - 定时上架时间
     */
    public Date getFixUptime() {
        return fixUptime;
    }

    /**
     * 设置定时上架时间
     *
     * @param fixUptime 定时上架时间
     */
    public void setFixUptime(Date fixUptime) {
        this.fixUptime = fixUptime;
    }

    /**
     * 获取是否定时下架
     *
     * @return ifdown - 是否定时下架
     */
    public Boolean getIfdown() {
        return ifdown;
    }

    /**
     * 设置是否定时下架
     *
     * @param ifdown 是否定时下架
     */
    public void setIfdown(Boolean ifdown) {
        this.ifdown = ifdown;
    }

    /**
     * 获取定时下架时间
     *
     * @return fix_downtime - 定时下架时间
     */
    public Date getFixDowntime() {
        return fixDowntime;
    }

    /**
     * 设置定时下架时间
     *
     * @param fixDowntime 定时下架时间
     */
    public void setFixDowntime(Date fixDowntime) {
        this.fixDowntime = fixDowntime;
    }

    /**
     * 获取上架时间
     *
     * @return up_time - 上架时间
     */
    public Date getUpTime() {
        return upTime;
    }

    /**
     * 设置上架时间
     *
     * @param upTime 上架时间
     */
    public void setUpTime(Date upTime) {
        this.upTime = upTime;
    }

    /**
     * 获取下架时间
     *
     * @return down_time - 下架时间
     */
    public Date getDownTime() {
        return downTime;
    }

    /**
     * 设置下架时间
     *
     * @param downTime 下架时间
     */
    public void setDownTime(Date downTime) {
        this.downTime = downTime;
    }

    /**
     * 获取交易开始时间
     *
     * @return trade_starttime - 交易开始时间
     */
    public Date getTradeStarttime() {
        return tradeStarttime;
    }

    /**
     * 设置交易开始时间
     *
     * @param tradeStarttime 交易开始时间
     */
    public void setTradeStarttime(Date tradeStarttime) {
        this.tradeStarttime = tradeStarttime;
    }

    /**
     * 获取交易结束时间
     *
     * @return trade_endtime - 交易结束时间
     */
    public Date getTradeEndtime() {
        return tradeEndtime;
    }

    /**
     * 设置交易结束时间
     *
     * @param tradeEndtime 交易结束时间
     */
    public void setTradeEndtime(Date tradeEndtime) {
        this.tradeEndtime = tradeEndtime;
    }

    /**
     * 获取是否流量管控
     *
     * @return if_flowcontrol - 是否流量管控
     */
    public Boolean getIfFlowcontrol() {
        return ifFlowcontrol;
    }

    /**
     * 设置是否流量管控
     *
     * @param ifFlowcontrol 是否流量管控
     */
    public void setIfFlowcontrol(Boolean ifFlowcontrol) {
        this.ifFlowcontrol = ifFlowcontrol;
    }

    /**
     * 获取是否允许分批发货
     *
     * @return allow_partial - 是否允许分批发货
     */
    public Boolean getAllowPartial() {
        return allowPartial;
    }

    /**
     * 设置是否允许分批发货
     *
     * @param allowPartial 是否允许分批发货
     */
    public void setAllowPartial(Boolean allowPartial) {
        this.allowPartial = allowPartial;
    }

    /**
     * 获取销售区域
     *
     * @return sale_area - 销售区域
     */
    public String getSaleArea() {
        return saleArea;
    }

    /**
     * 设置销售区域
     *
     * @param saleArea 销售区域
     */
    public void setSaleArea(String saleArea) {
        this.saleArea = saleArea == null ? null : saleArea.trim();
    }

    /**
     * 获取一级销售区域编码
     *
     * @return sale_area_code - 一级销售区域编码
     */
    public String getSaleAreaCode() {
        return saleAreaCode;
    }

    /**
     * 设置一级销售区域编码
     *
     * @param saleAreaCode 一级销售区域编码
     */
    public void setSaleAreaCode(String saleAreaCode) {
        this.saleAreaCode = saleAreaCode == null ? null : saleAreaCode.trim();
    }

    /**
     * 获取二级销售区域编码
     *
     * @return sale_area_code2 - 二级销售区域编码
     */
    public String getSaleAreaCode2() {
        return saleAreaCode2;
    }

    /**
     * 设置二级销售区域编码
     *
     * @param saleAreaCode2 二级销售区域编码
     */
    public void setSaleAreaCode2(String saleAreaCode2) {
        this.saleAreaCode2 = saleAreaCode2 == null ? null : saleAreaCode2.trim();
    }

    /**
     * 获取三级销售区域编码
     *
     * @return sale_area_code3 - 三级销售区域编码
     */
    public String getSaleAreaCode3() {
        return saleAreaCode3;
    }

    /**
     * 设置三级销售区域编码
     *
     * @param saleAreaCode3 三级销售区域编码
     */
    public void setSaleAreaCode3(String saleAreaCode3) {
        this.saleAreaCode3 = saleAreaCode3 == null ? null : saleAreaCode3.trim();
    }

    /**
     * 获取四级销售区域编码
     *
     * @return sale_area_code4 - 四级销售区域编码
     */
    public String getSaleAreaCode4() {
        return saleAreaCode4;
    }

    /**
     * 设置四级销售区域编码
     *
     * @param saleAreaCode4 四级销售区域编码
     */
    public void setSaleAreaCode4(String saleAreaCode4) {
        this.saleAreaCode4 = saleAreaCode4 == null ? null : saleAreaCode4.trim();
    }

    /**
     * 获取五级销售区域编码
     *
     * @return sale_area_code5 - 五级销售区域编码
     */
    public String getSaleAreaCode5() {
        return saleAreaCode5;
    }

    /**
     * 设置五级销售区域编码
     *
     * @param saleAreaCode5 五级销售区域编码
     */
    public void setSaleAreaCode5(String saleAreaCode5) {
        this.saleAreaCode5 = saleAreaCode5 == null ? null : saleAreaCode5.trim();
    }

    /**
     * 获取一级销售区域名称
     *
     * @return sale_area_name - 一级销售区域名称
     */
    public String getSaleAreaName() {
        return saleAreaName;
    }

    /**
     * 设置一级销售区域名称
     *
     * @param saleAreaName 一级销售区域名称
     */
    public void setSaleAreaName(String saleAreaName) {
        this.saleAreaName = saleAreaName == null ? null : saleAreaName.trim();
    }

    /**
     * 获取二级销售区域名称
     *
     * @return sale_area_name2 - 二级销售区域名称
     */
    public String getSaleAreaName2() {
        return saleAreaName2;
    }

    /**
     * 设置二级销售区域名称
     *
     * @param saleAreaName2 二级销售区域名称
     */
    public void setSaleAreaName2(String saleAreaName2) {
        this.saleAreaName2 = saleAreaName2 == null ? null : saleAreaName2.trim();
    }

    /**
     * 获取三级销售区域名称
     *
     * @return sale_area_name3 - 三级销售区域名称
     */
    public String getSaleAreaName3() {
        return saleAreaName3;
    }

    /**
     * 设置三级销售区域名称
     *
     * @param saleAreaName3 三级销售区域名称
     */
    public void setSaleAreaName3(String saleAreaName3) {
        this.saleAreaName3 = saleAreaName3 == null ? null : saleAreaName3.trim();
    }

    /**
     * 获取四级销售区域名称
     *
     * @return sale_area_name4 - 四级销售区域名称
     */
    public String getSaleAreaName4() {
        return saleAreaName4;
    }

    /**
     * 设置四级销售区域名称
     *
     * @param saleAreaName4 四级销售区域名称
     */
    public void setSaleAreaName4(String saleAreaName4) {
        this.saleAreaName4 = saleAreaName4 == null ? null : saleAreaName4.trim();
    }

    /**
     * 获取五级销售区域名称
     *
     * @return sale_area_name5 - 五级销售区域名称
     */
    public String getSaleAreaName5() {
        return saleAreaName5;
    }

    /**
     * 设置五级销售区域名称
     *
     * @param saleAreaName5 五级销售区域名称
     */
    public void setSaleAreaName5(String saleAreaName5) {
        this.saleAreaName5 = saleAreaName5 == null ? null : saleAreaName5.trim();
    }

    /**
     * 获取支付有效期类型
     *
     * @return paydate_type - 支付有效期类型
     */
    public String getPaydateType() {
        return paydateType;
    }

    /**
     * 设置支付有效期类型
     *
     * @param paydateType 支付有效期类型
     */
    public void setPaydateType(String paydateType) {
        this.paydateType = paydateType == null ? null : paydateType.trim();
    }

    /**
     * 获取支付有效期
     *
     * @return paydate_limit - 支付有效期
     */
    public Long getPaydateLimit() {
        return paydateLimit;
    }

    /**
     * 设置支付有效期
     *
     * @param paydateLimit 支付有效期
     */
    public void setPaydateLimit(Long paydateLimit) {
        this.paydateLimit = paydateLimit;
    }

    /**
     * 获取提货有效期类型
     *
     * @return takedate_type - 提货有效期类型
     */
    public String getTakedateType() {
        return takedateType;
    }

    /**
     * 设置提货有效期类型
     *
     * @param takedateType 提货有效期类型
     */
    public void setTakedateType(String takedateType) {
        this.takedateType = takedateType == null ? null : takedateType.trim();
    }

    /**
     * 获取提货有效期
     *
     * @return takedate_limit - 提货有效期
     */
    public Long getTakedateLimit() {
        return takedateLimit;
    }

    /**
     * 设置提货有效期
     *
     * @param takedateLimit 提货有效期
     */
    public void setTakedateLimit(Long takedateLimit) {
        this.takedateLimit = takedateLimit;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", effectiveId=").append(effectiveId);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", resourceAttributes=").append(resourceAttributes);
        sb.append(", resourceRegions=").append(resourceRegions);
        sb.append(", goodsResourceId=").append(goodsResourceId);
        sb.append(", spuId=").append(spuId);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsType=").append(goodsType);
        sb.append(", machineRuleId=").append(machineRuleId);
        sb.append(", emptyLoadRuleId=").append(emptyLoadRuleId);
        sb.append(", categoryCode=").append(categoryCode);
        sb.append(", goodsDescribe=").append(goodsDescribe);
        sb.append(", brand=").append(brand);
        sb.append(", resourceName=").append(resourceName);
        sb.append(", resourceCode=").append(resourceCode);
        sb.append(", effectTime=").append(effectTime);
        sb.append(", currency=").append(currency);
        sb.append(", currencySymbol=").append(currencySymbol);
        sb.append(", currencyName=").append(currencyName);
        sb.append(", priceDescribe=").append(priceDescribe);
        sb.append(", priceWay=").append(priceWay);
        sb.append(", price=").append(price);
        sb.append(", ifProtocolPrice=").append(ifProtocolPrice);
        sb.append(", priceUnit=").append(priceUnit);
        sb.append(", saleUnit=").append(saleUnit);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeName=").append(storeName);
        sb.append(", storeType=").append(storeType);
        sb.append(", storeAddress=").append(storeAddress);
        sb.append(", resourceVersion=").append(resourceVersion);
        sb.append(", status=").append(status);
        sb.append(", approvalMessage=").append(approvalMessage);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", sellerNickName=").append(sellerNickName);
        sb.append(", salesId=").append(salesId);
        sb.append(", salesName=").append(salesName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", saleNum=").append(saleNum);
        sb.append(", cansaleNum=").append(cansaleNum);
        sb.append(", lockNum=").append(lockNum);
        sb.append(", volumeNum=").append(volumeNum);
        sb.append(", toleranceType=").append(toleranceType);
        sb.append(", tolerance=").append(tolerance);
        sb.append(", ordermaxNum=").append(ordermaxNum);
        sb.append(", orderminNum=").append(orderminNum);
        sb.append(", orderminchangeNum=").append(orderminchangeNum);
        sb.append(", daymaxNum=").append(daymaxNum);
        sb.append(", areaLevel=").append(areaLevel);
        sb.append(", ifTakeSelf=").append(ifTakeSelf);
        sb.append(", ifPlatformDelivery=").append(ifPlatformDelivery);
        sb.append(", ifSellerDelivery=").append(ifSellerDelivery);
        sb.append(", takeSelfDiscounts=").append(takeSelfDiscounts);
        sb.append(", logisticsWeight=").append(logisticsWeight);
        sb.append(", logisticsType=").append(logisticsType);
        sb.append(", logisticsUnit=").append(logisticsUnit);
        sb.append(", logisticsPrice=").append(logisticsPrice);
        sb.append(", cartageRuleId=").append(cartageRuleId);
        sb.append(", payWay=").append(payWay);
        sb.append(", ifup=").append(ifup);
        sb.append(", fixUptime=").append(fixUptime);
        sb.append(", ifdown=").append(ifdown);
        sb.append(", fixDowntime=").append(fixDowntime);
        sb.append(", upTime=").append(upTime);
        sb.append(", downTime=").append(downTime);
        sb.append(", tradeStarttime=").append(tradeStarttime);
        sb.append(", tradeEndtime=").append(tradeEndtime);
        sb.append(", ifFlowcontrol=").append(ifFlowcontrol);
        sb.append(", allowPartial=").append(allowPartial);
        sb.append(", saleArea=").append(saleArea);
        sb.append(", saleAreaCode=").append(saleAreaCode);
        sb.append(", saleAreaCode2=").append(saleAreaCode2);
        sb.append(", saleAreaCode3=").append(saleAreaCode3);
        sb.append(", saleAreaCode4=").append(saleAreaCode4);
        sb.append(", saleAreaCode5=").append(saleAreaCode5);
        sb.append(", saleAreaName=").append(saleAreaName);
        sb.append(", saleAreaName2=").append(saleAreaName2);
        sb.append(", saleAreaName3=").append(saleAreaName3);
        sb.append(", saleAreaName4=").append(saleAreaName4);
        sb.append(", saleAreaName5=").append(saleAreaName5);
        sb.append(", paydateType=").append(paydateType);
        sb.append(", paydateLimit=").append(paydateLimit);
        sb.append(", takedateType=").append(takedateType);
        sb.append(", takedateLimit=").append(takedateLimit);
        sb.append(", factoryPrice=").append(factoryPrice);
        sb.append(", arrivePrice=").append(arrivePrice);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}