<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>service</artifactId>
		<groupId>com.cnoocshell</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>service-interface</artifactId>
	<packaging>pom</packaging>

	<name>service-interface</name>
	<description>Open Microservice</description>

	<modules>
		<module>cspc-interface</module>
		<module>cspc-interface-api</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.cnoocshell</groupId>
				<artifactId>generator</artifactId>
				<version>${project.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

		</dependencies>
	</dependencyManagement>
</project>
