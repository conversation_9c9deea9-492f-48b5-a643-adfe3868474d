
package com.cnoocshell.member.api.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Author:   jacoyang
 * Date:     05/03/2019
 */
@Data
public class PageDTO implements Serializable {

    /**
     * 页面id
     */
    @ApiModelProperty("页面id")
    private String pageId;

    /**
     * 页面代码
     */
    @ApiModelProperty("页面代码")
    private String pageCode;

    /**
     * 页面名称
     */
    @ApiModelProperty("页面名称")
    private String pageName;

    /**
     * 页面url
     */
    @ApiModelProperty("页面url")
    private String pageUrl;

    /**
     * 页面所在app 取值集JZ0001
     */
    @ApiModelProperty("页面所在app(取值集JZ0001)")
    private String pageApp;

    /**
     *
     */
    @ApiModelProperty("一级模块")
    private String muduleL1;

    /**
     *
     */
    @ApiModelProperty("二级模块")
    private String muduleL2;

    /**
     * 页面文件名称
     */
    @ApiModelProperty("页面文件名称")
    private String pageFileName;

    /**
     * VUE/NODE
     */
    @ApiModelProperty("VUE/NODE")
    private String implWay;

    /**
     * 需要/不需要/登录前后不一样
     */
    @ApiModelProperty("需要/不需要/登录前后不一样")
    private String needLogin;

    /**
     * 主页面/弹出页面/跳转画面/子页面
     */
    @ApiModelProperty("主页面/弹出页面/跳转画面/子页面")
    private String urlType;

    /**
     * url说明
     */
    @ApiModelProperty("url说明")
    private String pageRemark;

    /**
     * 删除标识(true表示已删除)
     */
    @ApiModelProperty("删除标识(true表示已删除)")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @ApiModelProperty("更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 数据版本
     */
    @ApiModelProperty("数据版本")
    private Long version;

    @ApiModelProperty("默认开始页数")
    private Integer pageNum = 1;
    @ApiModelProperty("一页显示数据量")
    private Integer pageSize = 10;

    /**
     * 父页面id
     */
    @ApiModelProperty("父页面id")
    private String parentPageId;

    /**
     * 父页面code
     */
    @ApiModelProperty("父页面code")
    private String parentCode;

    /**
     * 调用关系(弹出/跳转)
     */
    @ApiModelProperty("调用关系")
    private String callRelationShip;
    /**
     * 调用说明(父页面id存在时有用)
     */
    @ApiModelProperty("调用说明")
    private String callThat;

    /**
     * 是否有子页面(方便前端显示)
     */
    @ApiModelProperty("是否有子页面(方便前端显示)")
    private Boolean haveChild = false;

    /**
     * 子孙页面
     */
    @ApiModelProperty("子孙页面")
    private List<PageDTO> childs;

}
