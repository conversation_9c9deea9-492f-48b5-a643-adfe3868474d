package com.cnoocshell.information.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@Data
public class MessageSendDTO {

    @ApiModelProperty("消息接收人账户id集合")
    private List<String> receiveAccountIds;

    @ApiModelProperty("收信人会员id集合(如果不为空，则表示这些会员下的所有人（此时receiveAccountIds无效）)")
    private List<String> receiveMemberIds;

    @ApiModelProperty("消息标题")
    @NotBlank
    private String title;

    @ApiModelProperty("消息附件url")
    private String attachmentUrl;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("消息内容URL(与消息内容二者不能都为空)")
    private String contentUrl;

    @ApiModelProperty("操作者")
    private String operator;

    @ApiModelProperty("消息id")
    private String messageHistoryMqId;
}
