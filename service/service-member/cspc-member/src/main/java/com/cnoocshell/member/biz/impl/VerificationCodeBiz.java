package com.cnoocshell.member.biz.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.SecureRandomStringUtil;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.dto.account.checkCode.*;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.biz.IAccountBiz;
import com.cnoocshell.member.biz.IVerificationCodeBiz;
import com.cnoocshell.member.dao.vo.Account;
//import com.cnoocshell.member.service.impl.SMSMessageProducerImpl;
//import com.cnoocshell.open.api.dto.email.EmailCodeSendDTO;
//import com.cnoocshell.open.api.service.IEmailSendService;
import com.google.code.kaptcha.Producer;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * @DESCRIPTION:验证码服务实现，基于Redis
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VerificationCodeBiz implements IVerificationCodeBiz {

    private static final String SMS_VERIFICATION_FAILURE_MESSAGE = "验证码错误/已过期";
    private final BizRedisService bizRedisService;

    private final IAccountBiz accountBiz;

    private final ISmsSendService iSmsSendService;


    //todo 短信发送
//    private final SMSMessageProducerImpl smsMessageProducerImpl;

    //todo 邮件发送
    private final IEmailSendService emailSendService;
    //图形验证码服务
    @Qualifier("kaptcha")
    private final Producer producer;

    @Value("${account.login.captcha.disable:false}")
    private boolean disableCaptcha = false;

    @Value("${account.login.smscode.disable:false}")
    private boolean disableSMSCode = false;
    @Value("${sms.signature.custom.enable:true}")
    private boolean useCustomSign = true;
    /**
     * 验证码过期时间 默认180秒（3分钟）
     */
    @Value("${account.login.redis.expirTime:180}")
    private long expirTime = 180;


    /**
     *邮箱验证码过期时间 默认180秒（3分钟）
     */
    @Value("${account.email.verifyCode.expirationTime:180}")
    private long emailVerifyCodeExpirationTime;

    @Override
    public CaptchaDTO getCaptcha(GetCaptchaDTO dto) {
        try {
            String code = "8888";
            if (!disableCaptcha) {
                code = producer.createText();
            } else {
                log.info("图形验证码功能已禁用");
            }
            BufferedImage bi = producer.createImage(code);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);

            String key = MemberRedisKeys.CAPTCHA + dto.getSessionId() + "_" + dto.getUse();
            //验证码10分钟有效
            bizRedisService.setex(key, expirTime, code);
            log.info("=============>获取到的图形验证码：key: {},code: {}", key, code);

            CaptchaDTO result = new CaptchaDTO();
            result.setCaptcha(baos.toByteArray());
            result.setCaptchaCode(code);
            return result;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MemberBizException(MemberCode.CAPTCHA_SEND_ERROR, "图形验证码获取失败");
        }
    }

    @Override
    public void checkCaptcha(CheckCaptchaDTO dto) {
        if (disableCaptcha) {
            log.info("图形验证码功能已禁用");
        }
        String key = MemberRedisKeys.CAPTCHA + dto.getSessionId() + "_" + dto.getUse();
        if (bizRedisService.hasKey(key)) {
            String captcha = bizRedisService.get(key, String.class);
            if (StringUtils.equals(captcha, dto.getCaptcha())) {
                if (dto.getDeleteCaptcha() != null && dto.getDeleteCaptcha().booleanValue()) {
                    bizRedisService.del(key);
                    log.info("删除RedisKey:{}", key);
                }
                log.info("图形验证码验证通过：{} {} {}", captcha, dto.getCaptcha(), key);
            } else {
                log.info("图形验证码不正确：{} {} {}", captcha, dto.getCaptcha(), key);
                throw new MemberBizException(MemberCode.CAPTCHA_CHECK_ERROR, "图形验证码不正确");
            }
        } else {
            log.info("图形验证码不存在 key：{}", key);
            throw new MemberBizException(MemberCode.CAPTCHA_CHECK_ERROR, "图形验证码不存在");
        }
    }

    @Override
    public String getSMSCode(GetSMSCodeDTO dto) {
        if (disableSMSCode) {
            log.info("短信验证码功能已禁用");
            return "disableSMSCode";
        }
        //等于空或者真都要校验，只有等于false则不校验
        if (checkBoolean(dto.getCheckCaptcha())) {
            //校验图形验证码
            checkCaptcha(dto.getCheckCaptchaDTO());
        }
        String mobile = dto.getMobile();
        //校验手机号发送频率 1分钟1个手机只能发送1条
        String key1 = MemberRedisKeys.SMSCODE + mobile;
        if (bizRedisService.hasKey(key1)) {
            long time = bizRedisService.get(key1, Long.class);
            if (System.currentTimeMillis() - time < 60000) {
                log.info("每个手机号对应用途60秒内只能发送1次验证码短信，当前请求发送短信的手机:{},use:{}", mobile, dto.getUse());
                throw new MemberBizException(MemberCode.SMSCODE_SEND_FREQUENCY_LIMIT, "");
            }
        }
        //如果需要校验手机号
        if (checkBoolean(dto.getCheckMobile())) {
            //如果手机号不存在，则不发送
        }
        String smsCode = SecureRandomStringUtil.random(6, false, true);
        String key2 = MemberRedisKeys.SMSCODE + dto.getUse() + "_" + dto.getSessionId();
        //获取签名
        String sign = dto.getSign();
        if (useCustomSign && StringUtils.isBlank(sign) && StringUtils.isNotBlank(dto.getSignAccountId())) {
            Account account = accountBiz.findById(dto.getSignAccountId());
            if (account != null && StringUtils.isNotBlank(account.getRegisterSign())) {
                sign = account.getRegisterSign();
            }
        }
        bizRedisService.setex(key1, 60, System.currentTimeMillis());
        //验证码10分钟有效
        bizRedisService.setex(key2, expirTime, smsCode);
        sendSmsCode(dto, smsCode, dto.getMobile());
        log.info("设置短信验证码到Redis，redisKey：{},value:{},expire:{}second", key2, smsCode, expirTime);
        return smsCode;
    }

    private boolean checkBoolean(Boolean booleanObj) {
        return booleanObj != null && booleanObj;
    }

    @Override
    public void checkSMSCode(CheckSMSCodeDTO dto) {
        if (disableSMSCode) {
            log.info("短信验证码功能未启用");
            return;
        }
        String key = MemberRedisKeys.SMSCODE + dto.getUse() + "_" + dto.getSessionId();
        if (bizRedisService.hasKey(key)) {
            String smsCode = bizRedisService.get(key, String.class);
            if (StringUtils.equals(smsCode, dto.getSmsCode())) {
                if (dto.getDeleteSmsCode() != null && dto.getDeleteSmsCode().booleanValue()) {
                    bizRedisService.del(key);
                    log.info("删除RedisKey：{}", key);
                }
                log.info("短信验证码验证通过,redisKey：{} redisCode:{} checkCode:{}", key, smsCode, dto.getSmsCode());
            } else {
                log.info("短信验证码不正确,redisKey：{} redisCode:{} checkCode:{}", key, smsCode, dto.getSmsCode());
                throw new MemberBizException(MemberCode.SMSCODE_CHECK_ERROR, SMS_VERIFICATION_FAILURE_MESSAGE);
            }
        } else {
            log.info("短信验证码不存在 redisKey：{}", key);
            throw new MemberBizException(MemberCode.SMSCODE_CHECK_ERROR, SMS_VERIFICATION_FAILURE_MESSAGE);
        }
    }

    @Override
    public boolean checkEmailCode(CheckEmailCodeDTO dto) {
        String key = MemberRedisKeys.EMAILCODE + dto.getUse() + "_" + dto.getSessionId();
        if (bizRedisService.hasKey(key)) {
            String emailCode = bizRedisService.get(key, String.class);
            if (StringUtils.equals(emailCode, dto.getEmailCode())) {
                if (dto.getDeleteEmailCode() != null && dto.getDeleteEmailCode().booleanValue()) {
                    bizRedisService.del(key);
                    log.info("删除RedisKey：{}", key);
                }
                log.info("电子邮件验证码验证通过,redisKey：{} redisCode:{} checkCode:{}", key, emailCode, dto.getEmailCode());
                return true;
            } else {
                log.info("电子邮件验证码不正确,redisKey：{} redisCode:{} checkCode:{}", key, emailCode, dto.getEmailCode());
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, SMS_VERIFICATION_FAILURE_MESSAGE);
            }
        } else {
            log.info("电子邮件验证码不存在 redisKey：{}", key);
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, SMS_VERIFICATION_FAILURE_MESSAGE);
        }
    }

    @Override
    public String getEmailCode(GetEmailCodeDTO dto) {
        String email = dto.getEmail();
        //校验手机号发送频率 1分钟1个手机只能发送1条
        String key1 = MemberRedisKeys.EMAILCODE + email;
        if (bizRedisService.hasKey(key1)) {
            long time = bizRedisService.get(key1, Long.class);
            if (System.currentTimeMillis() - time < 60000) {
                log.info("每个邮件地址60秒内只能发送1次验证码，当前请求发送验证码的邮件:{},use:{}", email, dto.getUse());
                throw new MemberBizException(MemberCode.SMSCODE_SEND_FREQUENCY_LIMIT, "");
            }
        }
        Account account = StringUtils.isBlank(dto.getAccountId()) ? null : accountBiz.findById(dto.getAccountId());
        if(Objects.isNull(account))
            account = accountBiz.findByEmail(dto.getEmail());

        if (dto.getCheckEmail() != null && BooleanUtil.isTrue(dto.getCheckEmail())) {
            //验证邮箱
            if (account != null && !StringUtils.equals(dto.getAccountId(), account.getAccountId())) {
                log.info("邮箱已被其它账号占用,otherAccountId:{},email: {},use: {}", account.getAccountId(), email, dto.getUse());
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "邮箱已被占用");
            }
        }
        String emailCode = SecureRandomStringUtil.random(6, false, true);
        String key2 = MemberRedisKeys.EMAILCODE + dto.getUse() + "_" + dto.getSessionId();
        bizRedisService.setex(key1, 60, System.currentTimeMillis());
        //验证码10分钟有效
        bizRedisService.setex(key2, emailVerifyCodeExpirationTime, emailCode);
        EmailDTO emailDTO = new EmailDTO();
        emailDTO.setTos(Arrays.asList(email));
        emailDTO.setEmailTemplateCode(dto.getUse());
        emailDTO.setTemplateParam(this.getEmailTemplateParam(account,emailCode));
        emailSendService.sendEmail(emailDTO);

        log.info("设置电子邮件验证码到Redis，redisKey：{},value:{},expire:{}second", key2, emailCode, emailVerifyCodeExpirationTime);
        return emailCode;
    }

    @Override
    public void cleanRedisKeys(String operator) {
        log.info("清理RedisKey,前缀匹配:{},operator:{}", MemberRedisKeys.VERIFICATION_CODE, operator);
        Set<String> keys = bizRedisService.getRedisTemplate().keys(MemberRedisKeys.VERIFICATION_CODE + "*");
        if (keys != null && !keys.isEmpty()) {
            keys.forEach(key -> bizRedisService.del(key));
        }
    }

    private Map<String,Object> getEmailTemplateParam(Account account,String emailCode){
        Map<String,Object> emailTemplate = new HashMap<>();
        emailTemplate.put("realName", account.getRealName());
        emailTemplate.put("verifyCode",emailCode);
        return emailTemplate;
    }

    public void sendSmsCode(GetSMSCodeDTO dto, String smsCode, String mobile) {
        if (StringUtils.isEmpty(dto.getUse())) {
            return;
        }
        SmsDTO sms = new SmsDTO();
        if (dto.USE_LOGIN_PC.equals(dto.getUse())) {
            sms = new SmsDTO(SmsTemplateEnum.USER_PHONE_VERIFICATION_LOGIN_PROCESS_CODE.getCode(), Lists.newArrayList(mobile), Arrays.asList(smsCode));
        }
        if (dto.USE_MODIFY_MOBILE.equals(dto.getUse())) {
            sms = new SmsDTO(SmsTemplateEnum.USER_PHONE_MAINTENANCE_PROCESS_CODE.getCode(), Lists.newArrayList(mobile), Arrays.asList(smsCode));
        }
        iSmsSendService.sendSms(sms);
    }

}
