package com.cnoocshell.base.biz;

import com.cnoocshell.base.api.dto.account.RemoveAccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.dao.vo.AccountRole;
import com.cnoocshell.common.service.IBaseBiz;

import java.util.Collection;
import java.util.List;

public interface IAccountRoleBiz extends IBaseBiz<AccountRole> {
    List<AccountRole> findByAccountId(String accountId);

    void addAccountRoleIfNotExists(List<BaseRoleTypeEnum> roleList, String accountId, String operatorId);

    /**
     * 账户角色迁移，迁移指定的角色(角色对调)
     *
     * @param sourceAccountId
     * @param targetAccountId
     * @param operatorId
     */
    void move(String sourceAccountId, String targetAccountId, String operatorId, Boolean isSeller);

    void deleteByAccountId(String accountId, String operatorId);

    void refreshAccount(String accountId);

    void refreshAccount(Collection<String> accountSet);

    void updateRoleByAccountId(List<Integer> roleIdList, String accountId,String operatorId,boolean delOldRole);

    void removeAccountRole(RemoveAccountRoleDTO param);
}