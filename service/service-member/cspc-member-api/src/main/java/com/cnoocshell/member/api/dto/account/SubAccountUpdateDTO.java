package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.member.api.enums.RegexPatterns;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @DESCRIPTION:
 */
@Data
public class SubAccountUpdateDTO implements Serializable {
    /**
     * 用户名格式
     */
    @ApiModelProperty("用户名格式")
    public static final String USERNAME_PATTERN = "^$|^[a-zA-Z]\\w{5,19}$";
    /**
     * 电话号码格式
     */
    @ApiModelProperty("电话号码格式")
    public static final String MOBILE_PHONE_NUMBER_PATTERN = "^0?(1\\d{2})\\d{8}$";

    private static final long serialVersionUID = -8181753104736999430L;

    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称(6到20个字母、数字或下划线组成，且必须以字母开头)")
//    @NotBlank
//    @Pattern(regexp = USERNAME_PATTERN, message = "6到20个字母、数字或下划线组成，且必须以字母开头")
    private String accountName;

    /**
     * 账户id
     */
    @ApiModelProperty("账户id")
    @NotBlank(message = "账户ID不能为空")
    private String accountId;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    @NotBlank(message = "会员ID不能为空")
    private String memberId;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    @Size(max = 10,message = "工号限制输入10位字符")
    private String employeeId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private Date birthDay;

    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private Integer sex;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("邮箱")
//    @Pattern(regexp = RegexPatterns.EMAIL_REGEX,message = "邮箱错误")
    private String email;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
//    @Pattern(regexp = MOBILE_PHONE_NUMBER_PATTERN, message = "手机号错误")
    private String mobile;

    /**
     * 是否业务员
     */
    @ApiModelProperty("是否业务员")
    private Boolean salesman;

    /**
     * 销售区域id列表
     */
    @ApiModelProperty("销售区域id列表")
    private List<String> saleRegionInfoIdList;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<Integer> roleIdList;

    /**
     * 操作人AccountId
     */
    @ApiModelProperty("操作人AccountId")
    private String operatorId;

    /**
     * 操作人客户端ip
     */
    @ApiModelProperty("操作人客户端ip")
    private String operatorIP;
}
