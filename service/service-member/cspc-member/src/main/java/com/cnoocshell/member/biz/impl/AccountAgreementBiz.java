package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.biz.IAccountAgreementBiz;
import com.cnoocshell.member.dao.mapper.AccountAgreementMapper;
import com.cnoocshell.member.dao.vo.AccountAgreement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAgreementBiz extends BaseBiz<AccountAgreement> implements IAccountAgreementBiz {
    private final AccountAgreementMapper accountAgreementMapper;

}
