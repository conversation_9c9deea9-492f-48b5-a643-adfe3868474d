package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BiddingMemberDTO {

    @ApiModelProperty("客户名称")
    private String memberName;

    @ApiModelProperty("客户编码")
    private String memberCode;

    @ApiModelProperty("客户crm编码")
    private String crmCode;

    @ApiModelProperty("次要意向商品")
    private String mainGoods;

    @ApiModelProperty("主要意向商品")
    private String minorGoods;

    @ApiModelProperty(value = "主要意向商品编码",notes = "多个用逗号隔开")
    private String mainGoodsCodes;

    @ApiModelProperty(value = "次要意向商品编码",notes = "多个用逗号隔开")
    private String minorGoodsCodes;
}
