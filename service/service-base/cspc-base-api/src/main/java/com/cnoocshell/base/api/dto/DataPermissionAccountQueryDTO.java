package com.cnoocshell.base.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DataPermissionAccountQueryDTO implements Serializable {

    private static final long serialVersionUID = -8630941021246587746L;

    @ApiModelProperty("商品分类编码")
    private String goodsCode;

    @ApiModelProperty("用户角色")
    private String roleCode;

    @ApiModelProperty("账号id")
    private String accountId;
}
