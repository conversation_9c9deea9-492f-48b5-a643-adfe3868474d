package com.cnoocshell.base.service.permission.authenticate;

import com.cnoocshell.base.api.dto.AccountPermissionDTO;
import com.cnoocshell.base.api.dto.authRes.VUEPageCrumbsDTO;

import java.util.List;
import java.util.Map;

public interface IRoleCashService {

    List<AccountPermissionDTO> getRolePermissionList(List<String> roleIds,String projectType);

    Map<String,List<VUEPageCrumbsDTO>> getPlatformCrumbs(String projectType);
}
