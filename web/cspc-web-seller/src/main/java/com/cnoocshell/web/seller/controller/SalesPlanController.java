package com.cnoocshell.web.seller.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.order.api.dto.salesPlan.*;
import com.cnoocshell.order.api.service.ISalesPlanService;
import com.cnoocshell.web.common.utils.LoginUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/salesPlan")
public class SalesPlanController {

    private final ISalesPlanService salesPlanService;

    @ApiOperation("分页查询销售计划列表")
    @PostMapping("/findSalePlanByCondition")
    ItemResult<PageInfo<SalesPlanPageResponseDTO>> findSalePlanByCondition(@ApiIgnore LoginInfo loginInfo, @RequestBody SalesPlanPageRequestDTO requestDTO){
        requestDTO.setAccountId(loginInfo.getAccountId());
        requestDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(salesPlanService.findSalePlanByCondition(requestDTO));
    }

    @ApiOperation("新增销售计划")
    @PostMapping("/addSalesPlan")
    ItemResult<String> addSalesPlan(@ApiIgnore LoginInfo loginInfo,@RequestBody SalesPlanInfoDTO requestDTO){
        requestDTO.setCreateUser(loginInfo.getAccountId());
        requestDTO.setCreateUserName(loginInfo.getRealName());
        return new ItemResult<>(salesPlanService.addSalesPlan(requestDTO));
    }

    @ApiOperation("更新销售计划")
    @PostMapping("/updateSalesPlan")
    ItemResult<String> updateSalesPlan(@ApiIgnore LoginInfo loginInfo,@RequestBody SalesPlanInfoDTO requestDTO){
        requestDTO.setUpdateUser(loginInfo.getAccountId());
        requestDTO.setUpdateUserName(loginInfo.getRealName());
        return new ItemResult<>(salesPlanService.updateSalesPlan(requestDTO));
    }

    @ApiOperation("撤回销售计划")
    @PostMapping("/revokeSalesPlan")
    ItemResult<String> revokeSalesPlan(@RequestParam("id") String id){
        return new ItemResult<>(salesPlanService.revokeSalesPlan(id));
    }


    @ApiOperation("查询销售计划详情")
    @PostMapping("/findSalePlanById")
    ItemResult<SalesDetailDTO> findSalePlanById(@ApiIgnore LoginInfo loginInfo,@RequestParam("id") String id){
        return new ItemResult<>(salesPlanService.findSalePlanById(id,loginInfo.getAccountId(),loginInfo.getMemberId()));
    }

    @ApiOperation("删除草稿")
    @PostMapping("/removeDraft")
    public ItemResult<Void> removeDraft(@ApiIgnore LoginInfo loginInfo,@Validated @RequestBody RemoveSalesPlanDTO param){
        LoginUtil.checkLogin(loginInfo);
        param.setOperatorBy(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        param.setOperationTime(new Date());
        return salesPlanService.removeDraft(param);
    }

}
