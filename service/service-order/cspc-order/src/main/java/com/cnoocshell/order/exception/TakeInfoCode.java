package com.cnoocshell.order.exception;

import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.CodeMeta;

public class TakeInfoCode extends BasicCode {

	public static final CodeMeta DATA_NOT_FOUND = new CodeMeta("330301", "DATA_NOT_FOUND", "未查询到记录{}",
			"DATA_NOT_FOUND error, see: {}");

	public static final CodeMeta VALUE_NOT_EMPTY = new CodeMeta("330302", "VALUE_NOT_EMPTY", "{}不能为空",
			"{} can not be empty");

	public static final CodeMeta DATA_DUPLICATE = new CodeMeta("330303", "DATA_DUPLICATE", "存在重复的{}", "Duplicate {}");

	public static final CodeMeta VALUE_ERROR = new CodeMeta("330304", "VALUE_ERROR", "{}错误", "{} error");

	public static final CodeMeta TAKE_TIME_LIMIT = new CodeMeta("330355", "TAKE_TIME_LIMIT", "提货有效期已到");

	public static final CodeMeta LOGISTIC_ERROR_CREATE = new CodeMeta("330365", "LOGISTIC_ERROR_CREATE", "物流处理发货单失败 {} ") ;

	public static final CodeMeta LOGISTIC_ERROR_CLOSE = new CodeMeta("330366", "LOGISTIC_ERROR_CLOSE", "物流关闭发货单失败 {} ");

	public static final CodeMeta LOGISTIC_ERROR_SENDINONE = new CodeMeta("330377", "LOGISTIC_ERROR_SENDINONE", "{}");

}
