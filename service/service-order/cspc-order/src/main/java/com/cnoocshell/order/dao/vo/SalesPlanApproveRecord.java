package com.cnoocshell.order.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "sales_plan_approve_record")
@Data
public class SalesPlanApproveRecord  extends BaseEntity {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "plan_no")
    private String planNo;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "approve_result")
    private String approveResult;

    @Column(name = "rejected_reason")
    private String rejectedReason;
}
