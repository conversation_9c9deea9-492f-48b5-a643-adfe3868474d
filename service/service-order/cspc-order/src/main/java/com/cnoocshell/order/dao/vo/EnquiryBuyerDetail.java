package com.cnoocshell.order.dao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 买家询价详情
 */
@Table(name = "enquiry_buyer_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBuyerDetail {

    @Id
    @Column(name = "id", nullable = false, length = 32)
    private String id; // 主键ID

    @Column(name = "enquiry_no", length = 32)
    private String enquiryNo; // 询价场次编号

    @Column(name = "member_code", length = 32)
    private String memberCode; // 买家编码

    @Column(name = "member_name", length = 32)
    private String memberName; // 买家名称

    @Column(name = "goods_code", length = 32)
    private String goodsCode; // 商品编码

    @Column(name = "goods_name", length = 32)
    private String goodsName; // 商品名称

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price; // 报价

    @Column(name = "quantity", precision = 10, scale = 2)
    private BigDecimal quantity; // 报量

    @Column(name = "delivery_mode", length = 32)
    private String deliveryMode; // 运输方式 1自提，2配送

    @Column(name = "pack", length = 32)
    private String pack; // 包装方式

    @Column(name = "del_flg")
    private Boolean delFlg; // 删除标志

    @Column(name = "create_user", length = 32)
    private String createUser; // 创建人ID

    @Column(name = "create_user_name", length = 32)
    private String createUserName; // 创建人名称

    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime; // 创建时间

    @Column(name = "update_user", length = 32)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateUser; // 修改人

    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime; // 修改时间

}