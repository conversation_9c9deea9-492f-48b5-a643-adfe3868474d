package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsDTO {

    /**
     * 模板code必须在枚举com.cnoocshell.integration.enums.SmsTemplateEnum中存在
     */
    @ApiModelProperty("邮件模板Code")
    private String templateCode;

    /**
     * 最大长度不能超过100个
     */
    @ApiModelProperty("收信人手机号")
    private List<String> mobiles;

    /**
     * 因为联通模板中的动态参数是按下标顺序取的,
     * 所以这里的动态参数需要依次传入(务必保证顺序正确), 比如 姓名是王二 地址是四川省 则传入 ["王二","四川"]
     *
     */
    @ApiModelProperty("短信模板参数")
    private List<String> templateParams;
}
