package com.cnoocshell.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.biz.IEnquiryBiz;
import com.cnoocshell.order.dao.mapper.EnquiryMapper;
import com.cnoocshell.order.exception.DuplicateString;
import com.cnoocshell.order.dao.vo.Enquiry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;


/**
 * @Author: wangshunmin
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnquiryBiz extends BaseBiz<Enquiry> implements IEnquiryBiz {
    private final EnquiryMapper enquiryMapper;

    @Override
    public Enquiry getByNo(String no) {
        if(CharSequenceUtil.isBlank(no))
            return null;

        Condition condition = new Condition(Enquiry.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo("enquiryNo",no);
        return CollUtil.getFirst(this.findByCondition(condition));
    }

    @Override
    public Boolean existByPlanAndStatus(String planNo, List<String> enquiryStatus) {
        Condition condition = new Condition(Enquiry.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(ColumnConstant.SALES_PLAN_NO,planNo)
                .andIn( DuplicateString.STATUS,enquiryStatus);
        return enquiryMapper.selectCountByCondition(condition) > 0;
    }
}
