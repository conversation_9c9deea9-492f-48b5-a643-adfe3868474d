package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberIntentionChangeDTO {
    @ApiModelProperty("会员ID")
    private String memberId;
    @ApiModelProperty("会员编码")
    private String memberCode;
    @ApiModelProperty("变更操作人ID")
    private String operatorId;
    @ApiModelProperty("操作时间")
    private Date operationTime;
    @ApiModelProperty("最新意向数据")
    private List<MemberPurchaseGoodsIntentionDTO> newestIntentions;

}
