package com.cnoocshell.base.api.dto.authRes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 20/03/2019 15:07
 * @DESCRIPTION:
 */
@Data
public class GetMenuTreeDTO {

    @NotBlank
    @ApiModelProperty("平台类型")
    private String platform;

    @NotNull
    @ApiModelProperty("角色id集合")
    private List<String> roleIdList;
}
