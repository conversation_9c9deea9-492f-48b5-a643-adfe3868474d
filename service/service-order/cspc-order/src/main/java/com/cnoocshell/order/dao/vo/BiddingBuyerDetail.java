package com.cnoocshell.order.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "bidding_buyer_detail")
public class BiddingBuyerDetail extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "bidding_no")
    private String biddingNo;

    @Column(name = "member_code")
    private String memberCode;

    @Column(name = "member_name")
    private String memberName;

    @Column(name = "current_round")
    private Integer currentRound;

    @Column(name = "current_standard_price")
    private BigDecimal currentStandardPrice;

    @Column(name = "want_quantity")
    private BigDecimal wantQuantity;

    @Column(name = "delivery_mode")
    private String deliveryMode;

    @Column(name = "pack")
    private String pack;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "unit")
    private String unit;

    @Column(name = "bidding_start_time")
    private Date biddingStartTime;

    @Column(name = "bidding_end_time")
    private Date biddingEndTime;

    @Column(name = "create_user_name")
    private String createUserName;

}