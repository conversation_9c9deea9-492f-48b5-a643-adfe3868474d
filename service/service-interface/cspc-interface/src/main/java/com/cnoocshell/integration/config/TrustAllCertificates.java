package com.cnoocshell.integration.config;

import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

@Slf4j
public class TrustAllCertificates implements X509TrustManager {
    @Override
    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
        // Do nothing
        log.debug("checkClientTrusted");
    }

    @Override
    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
        // Do nothing
        log.debug("checkServerTrusted");
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return new X509Certificate[0];
    }
}