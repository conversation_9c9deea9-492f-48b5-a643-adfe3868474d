package com.cnoocshell.base.dao.mapper;

import com.cnoocshell.common.dto.InterfacePermissionDTO;
import com.cnoocshell.base.dao.vo.SysInterfacePermission;
import com.cnoocshell.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysInterfacePermissionMapper extends IBaseMapper<SysInterfacePermission> {
    List<InterfacePermissionDTO> listByPublic(@Param("isPublic")Boolean isPublic);
}
