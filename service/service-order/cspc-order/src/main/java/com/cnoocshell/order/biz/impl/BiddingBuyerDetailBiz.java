package com.cnoocshell.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.biz.IBiddingBuyerDetailBiz;
import com.cnoocshell.order.dao.mapper.BiddingBuyerDetailMapper;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.vo.Bidding;
import com.cnoocshell.order.dao.vo.BiddingBuyerDeal;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;
import com.cnoocshell.order.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingBuyerDetailBiz extends BaseBiz<BiddingBuyerDetail> implements IBiddingBuyerDetailBiz {
    @Resource
    private BiddingMapper biddingMapper;
    @Resource
    private BiddingBuyerDetailMapper biddingBuyerDetailMapper;


    @Override
    public List<BiddingBuyerDetail> listByNo(String biddingNo) {
        Bidding bidding = biddingMapper.getByNo(biddingNo);
        if(Objects.isNull(bidding))
            return Collections.emptyList();
        return this.listByNo(biddingNo,bidding.getCurrentRound());
    }

    @Override
    public boolean existsBiddingBuyerDetail(String biddingNo) {
        Bidding bidding = biddingMapper.getByNo(biddingNo);
        if(Objects.isNull(bidding))
            return false;

        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.CURRENT_ROUND,bidding.getCurrentRound())
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);

        return biddingBuyerDetailMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public List<BiddingBuyerDetail> listByIds(Collection<String> ids) {
        if (CollUtil.isEmpty(ids))
            return Collections.emptyList();

        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.ID, ids);
        condition.orderBy(ColumnConstant.CREATE_TIME).desc();
        return this.findByCondition(condition);
    }

    @Override
    public List<BiddingBuyerDetail> listByNo(String biddingNo, Integer round) {
        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.CURRENT_ROUND,round)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);
        condition.orderBy(ColumnConstant.CREATE_TIME).desc();
        List<BiddingBuyerDetail> list = this.findByCondition(condition);
        if(CollUtil.isEmpty(list))
            return Collections.emptyList();

        return list;
    }

    @Override
    public List<BiddingBuyerDetail> listByDeals(List<BiddingBuyerDeal> deals) {
        if(!CollUtil.isEmpty(deals)){
            List<String> detailsIds = deals.stream().map(BiddingBuyerDeal::getBiddingBuyerDetailId).collect(Collectors.toList());
            return this.listByIds(detailsIds);
        }

        return Collections.emptyList();
    }

    @Override
    public List<BiddingBuyerDetail> listByBuyer(String biddingNo, String buyerMemberCode) {
        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.MEMBER_CODE,buyerMemberCode)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);
        condition.orderBy(ColumnConstant.CREATE_TIME).desc();

        return this.findByCondition(condition);
    }

    @Override
    public List<BiddingBuyerDetail> listAllByNo(String biddingNo) {
        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo);
        condition.orderBy(ColumnConstant.CREATE_TIME).desc();
        List<BiddingBuyerDetail> list = this.findByCondition(condition);
        if(CollUtil.isEmpty(list))
            return Collections.emptyList();

        return list;
    }

    @Override
    public Boolean existByMemberCode(String memberCode, String biddingNo, Integer round) {
        Condition condition = new Condition(BiddingBuyerDetail.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(ColumnConstant.BIDDING_NO, biddingNo)
                .andEqualTo(ColumnConstant.MEMBER_CODE,memberCode)
                .andEqualTo(ColumnConstant.CURRENT_ROUND,round);

        return biddingBuyerDetailMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public BiddingBuyerDetail getById(String id) {
        return biddingBuyerDetailMapper.getById(id);
    }

    @Override
    public BiddingBuyerDetail getLastBuyerDetail(String biddingNo, String memberCode) {
        return biddingBuyerDetailMapper.getLastBuyerDetail(biddingNo,memberCode);
    }
}
