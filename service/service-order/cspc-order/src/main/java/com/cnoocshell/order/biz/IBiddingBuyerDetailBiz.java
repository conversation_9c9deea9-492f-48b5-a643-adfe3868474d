package com.cnoocshell.order.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.order.dao.vo.BiddingBuyerDeal;
import com.cnoocshell.order.dao.vo.BiddingBuyerDetail;

import java.util.Collection;
import java.util.List;

public interface IBiddingBuyerDetailBiz extends IBaseBiz<BiddingBuyerDetail> {

    /**
     * 获取当前竞价轮次  买家竞价明细
     */
    List<BiddingBuyerDetail> listByNo(String biddingNo);


    /**
     * 当前竞价场次 +当前轮次是否存在买家报量
     */
    boolean existsBiddingBuyerDetail(String biddingNo);

    List<BiddingBuyerDetail> listByIds(Collection<String> ids);


    /**
     * 获取指定轮次参与竞价的买家竞价明细
     */
    List<BiddingBuyerDetail> listByNo(String biddingNo, Integer round);

    List<BiddingBuyerDetail> listByDeals(List<BiddingBuyerDeal> deals);

    List<BiddingBuyerDetail> listByBuyer(String biddingNo, String buyerMemberCode);

    List<BiddingBuyerDetail> listAllByNo(String biddingNo);


    Boolean existByMemberCode(String memberCode, String biddingNo, Integer round);

    BiddingBuyerDetail getById(String id);

    BiddingBuyerDetail getLastBuyerDetail( String biddingNo, String memberCode);
}
