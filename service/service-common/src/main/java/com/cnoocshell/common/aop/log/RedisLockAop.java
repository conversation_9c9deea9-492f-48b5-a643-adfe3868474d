package com.cnoocshell.common.aop.log;

import com.cnoocshell.common.annotation.RedisLock;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.lock.RedisLockService;
import org.apache.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 注解给方法加redis锁，
 */
@Slf4j
@Component
@Aspect
public class RedisLockAop {

    @Lazy
    @Autowired
    private RedisLockService redisLockService;
    /**
     * 切入点
     */
    @Pointcut("@annotation(com.cnoocshell.common.annotation.RedisLock)")
    public void entryPoint() {
        //do nothing
    }

    @Around(value = "entryPoint() && @annotation(rl)")
    public Object assertAround(ProceedingJoinPoint pjp,RedisLock rl) throws RuntimeException {
        String lock = null;
        String key = null;
        try {
            try {
                key = getKey(pjp, rl);
                if (key != null) {
                    lock = redisLockService.lockFast(key);
                    log.info("add redis lock,lock:{},key:{}", lock, key);
                } else {
                    log.info("add redis lock fail ,key is null");
                }
            }catch (Exception e){
                log.error(e.getMessage(),e);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "系统繁忙,请稍后再试");
            }

            Object result = null;
            try {
                result = pjp.proceed();
            } catch (Throwable t) {
                if( t instanceof BizException ){
                    throw (BizException)t;
                }
                if( t instanceof RuntimeException ){
                    throw (RuntimeException)t;
                }
                throw new RuntimeException(t);
            }
            return result;

        }finally {
            try {
                if (StringUtils.isNotBlank(lock)) {
                    redisLockService.unlock(key, lock);
                }
            }catch(Exception e){
                log.error(e.getMessage(),e);
            }
        }
    }

    private String getKey(ProceedingJoinPoint pjp,RedisLock rl) {
        Object[] args = pjp.getArgs();
        if(checkArgs(args)){
            return null;
        }
        if(checkRule(rl)){
            return null;
        }

        String className = pjp.getTarget().getClass().getName();
        String method = pjp.getSignature().getName();
        int argsLength = args.length;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(className).append(".").append(method).append(".");

        if ( rl.index() >= 0 && rl.index() <argsLength ){
            if (extracted(rl, stringBuilder, args)) return null;
        }
        appendIndexs(rl, argsLength, args, stringBuilder);
        return stringBuilder.toString();
    }

    private static boolean extracted(RedisLock rl, StringBuilder stringBuilder, Object[] args) {
        if (rl.fieldName() == null || rl.fieldName().length == 0){
            stringBuilder.append(args[rl.index()]);
        }else{
            for (String s : rl.fieldName()) {
                try {
                    Object obj = PropertyUtils.getProperty(args[rl.index()],s);
                    if( obj != null) {
                        stringBuilder.append(obj).append(".");
                    }
                } catch (Exception e) {
                    return true;
                }
            }
        }
        return false;
    }

    private static boolean checkArgs(Object[] args) {
        return args == null || args.length == 0;
    }

    private static boolean checkRule(RedisLock rl) {
        return rl.index() == -1 && (rl.fieldName() == null || rl.fieldName().length == 0) && (rl.indexs() == null || rl.indexs().length == 0);
    }

    private static void appendIndexs(RedisLock rl, int argsLength, Object[] args, StringBuilder stringBuilder) {
        if( rl.indexs() != null && rl.indexs().length>0 ){
            for (int index : rl.indexs()) {
                if( index >0 && index< argsLength){
                    Object obj = args[index];
                    if( obj != null ) {
                        stringBuilder.append(obj).append(".");
                    }
                }
            }
        }
    }

}
