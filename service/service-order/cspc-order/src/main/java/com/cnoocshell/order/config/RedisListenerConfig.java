package com.cnoocshell.order.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * Author: lehu
 * Description:
 * Date: Create in 上午11:58 20/11/9
 */
@Configuration
public class RedisListenerConfig {

   @Bean("expiredRedisMessageListenerContainer")
   public RedisMessageListenerContainer container(@Qualifier("bizRedisConnectionFactory") RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }
}
