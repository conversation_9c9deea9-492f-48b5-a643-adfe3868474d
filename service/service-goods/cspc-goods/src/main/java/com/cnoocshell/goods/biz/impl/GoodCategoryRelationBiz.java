package com.cnoocshell.goods.biz.impl;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;
import com.cnoocshell.goods.api.enums.GoodsTypeEnum;
import com.cnoocshell.goods.api.enums.ResourceStatusEnum;
import com.cnoocshell.goods.biz.IGoodCategoryRelationBiz;
import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.dao.mapper.GoodCategoryRelationMapper;
import com.cnoocshell.goods.dao.vo.GoodCategoryRelation;
import com.cnoocshell.goods.dao.vo.Goods;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class GoodCategoryRelationBiz extends BaseBiz<GoodCategoryRelation> implements IGoodCategoryRelationBiz {
    @Resource
    private GoodCategoryRelationMapper goodCategoryRelationMapper;


    private final IGoodsBiz goodsBiz;


    private final ResourceBiz resourceBiz;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    @Transactional
    public boolean save(List<GoodCategoryRelationDto> relations) {
        List<GoodCategoryRelation> from = GoodCategoryRelation.from(relations);

        Map<String, List<GoodCategoryRelation>> map = from.stream().collect(Collectors.groupingBy(GoodCategoryRelation::getGoodId));

        map.forEach(
                (goodId, rs) -> {
                    Goods goods = goodsBiz.get(goodId);
                    Example example = new Example(GoodCategoryRelation.class);
                    example.createCriteria().andEqualTo("goodId", goodId);
                    List<GoodCategoryRelation> origin = goodCategoryRelationMapper.selectByExample(example);
                    Set<String> originCategories = origin.stream().map(GoodCategoryRelation::getCategoryId).collect(Collectors.toSet());
                    Set<String> newCategories = rs.stream().map(GoodCategoryRelation::getCategoryId).collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(newCategories) && CollectionUtils.isEmpty(originCategories)) {
                        return;
                    }
                    if (originCategories.equals(newCategories)) {
                        return;
                    }
                    goodCategoryRelationMapper.deleteByExample(example);
                    if (!goods.getDelFlg()) {
                        goodCategoryRelationMapper.insertList(rs);

                        Integer goodsType = goods.getGoodsType();
                        List<String> goodIds;
                        goodIds = getGoodIds(goodId, goodsType, goods);

                        if (CollectionUtils.isEmpty(goodIds)){
                            logger.info("================>没有创建单一商品,放弃同步分类");
                            return;
                        }
                        // 查找符合条件的挂牌资源
                        Condition condition = new Condition(com.cnoocshell.goods.dao.vo.Resource.class);
                        condition.createCriteria().andIn("goodsId", goodIds)
                                .andEqualTo("status", ResourceStatusEnum.RES_STATUS100.code())
                                .andEqualTo("delFlg", Boolean.FALSE);
                        List<com.cnoocshell.goods.dao.vo.Resource> resources = resourceBiz.findByCondition(condition);
                        asyncEsCreateOrUpdateResource(resources, goods);

                    }
                });
        return true;
    }

    @Override
    public List<GoodsCategoryRelationSimpleDTO> queryRelationByCategoryCodes(List<String> categoryCodes) {
        if(CollUtil.isEmpty(categoryCodes))
            return Collections.emptyList();

        return goodCategoryRelationMapper.queryRelationByCategoryCodes(categoryCodes);
    }

    private void asyncEsCreateOrUpdateResource(List<com.cnoocshell.goods.dao.vo.Resource> resources, Goods goods) {
        if (!CollectionUtils.isEmpty(resources)) {
            logger.info("================>开始同步分类");
            String splitStr = "ABAB";
            //代码优化，尽量减少循环
        } else {
            logger.info("================>没有挂牌资源需要同步");
        }
    }


    private List<String> getGoodIds(String goodId, Integer goodsType, Goods goods) {
        List<String> goodIds;
        if (GoodsTypeEnum.BASE.getCode() == goodsType) {
            // spu修改分类
            Goods good = new Goods();
            good.setSpuId(goods.getGoodsId());
            good.setDelFlg(false);
            List<Goods> goodList = goodsBiz.find(good);
            goodIds = goodList.stream().map(Goods::getGoodsId).collect(Collectors.toList());
        } else {
            // sku修改分类
            goodIds = Collections.singletonList(goodId);
        }
        return goodIds;
    }
}
