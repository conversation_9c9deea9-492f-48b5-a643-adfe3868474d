spring:
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacosdev}
      password: ${NACOS_PASSWORD:eDCVpHxBDpLQm/nw}
      config:
        profile: ${spring.application.active:local}
        namespace: ${NACOS_NAMESPACE:cnoocshell-local}
        server-addr: ${NACOS_SERVER_ADDR:27.40.98.108:8848} # Nacos 服务器地址
        file-extension: yaml          # 配置文件扩展名
        file—prefix: ${spring.application.name:local}
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}  # Nacos 服务器地址
