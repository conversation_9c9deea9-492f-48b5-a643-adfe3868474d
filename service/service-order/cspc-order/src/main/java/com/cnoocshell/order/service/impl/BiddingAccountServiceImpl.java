package com.cnoocshell.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.QueryDataPermissionDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.api.dto.account.AccountNameDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.QueryAccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.member.MemberSimpleDTO;
import com.cnoocshell.member.api.dto.member.QueryMemberAccountDTO;
import com.cnoocshell.member.api.enums.DepositStatusEnum;
import com.cnoocshell.member.api.enums.SubscribeWechatBusinessTypeEnum;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.order.api.dto.AccountSubscribeWechatKeyDTO;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;
import com.cnoocshell.order.service.IBiddingAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingAccountServiceImpl implements IBiddingAccountService {
    private final IMemberService iMemberService;
    private final IRoleService iRoleService;
    private final IAccountService iAccountService;


    /**
     *根据产品查询买家采购员信息
     */
    @Override
    public Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> queryAccountDataPermission(List<String> memberCodes,
                                                                                                 List<String> goodsCodes,
                                                                                                 String... depositStatus) {
        if (CollUtil.isEmpty(memberCodes) || CollUtil.isEmpty(goodsCodes))
            return null;
        List<MemberSimpleDTO> members = iMemberService.listSimpleMemberByCodes(memberCodes);
        if(Objects.nonNull(depositStatus)){
            List<String> depositStatusList = Arrays.asList(depositStatus).stream()
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct().collect(Collectors.toList());
            if(CollUtil.isNotEmpty(depositStatusList)) {
                members = members.stream()
                        .filter(v -> CollUtil.contains(depositStatusList,v.getDepositStatus()))
                        .collect(Collectors.toList());
            }
        }
        if (CollUtil.isEmpty(members))
            return null;
        Map<String, MemberSimpleDTO> memberMap = CommonUtils.getMap(members, MemberSimpleDTO::getMemberId);

        List<String> memberIds = members.stream().map(v -> v.getMemberId()).collect(Collectors.toList());
        QueryDataPermissionDTO param = new QueryDataPermissionDTO(memberIds, goodsCodes,null,Arrays.asList(BaseRoleTypeEnum.BUYER_PURCHASER.getRoleCode()));
        ItemResult<List<DataPermissionDTO>> result = iRoleService.queryDataPermission(param);
        if (Objects.isNull(result) || CollUtil.isEmpty(result.getData()))
            return null;
        List<DataPermissionDTO> dataPermissions = result.getData();
        //K:memberCode_goodsCode
        Map<QueryAccountDataPermissionDTO, List<DataPermissionDTO>> memberAccountPermissionGroup = dataPermissions.stream()
                .collect(Collectors.groupingBy(v -> new QueryAccountDataPermissionDTO(getMemberCode(memberMap, v.getMemberId()), v.getGoodsCode())));

        List<String> accountIds = dataPermissions.stream().map(v -> v.getAccountId()).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(accountIds))
            return null;
        List<AccountSimpleDTO> accounts = iAccountService.listSimpleByIds(accountIds);
        if (CollUtil.isEmpty(accounts))
            return null;
        Map<String, AccountSimpleDTO> accountMap = CommonUtils.getMap(accounts, AccountSimpleDTO::getAccountId);

        Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> rs = new HashMap<>();
        memberAccountPermissionGroup.forEach((k, v) -> {
            List<String> permissionAccountIds = v.stream().map(v1 -> v1.getAccountId()).distinct().collect(Collectors.toList());
            List<AccountSimpleDTO> permissionAccounts = CommonUtils.getListByKey(accountMap, permissionAccountIds);
            if (CollUtil.isNotEmpty(permissionAccounts))
                rs.put(k, permissionAccounts);
        });
        return rs;
    }

    @Override
    public Map<String, AccountSimpleDTO> getAccount(List<String> accountIds) {
        if(CollUtil.isEmpty(accountIds))
            return null;
        return CommonUtils.getMap(iAccountService.listSimpleByIds(accountIds),AccountSimpleDTO::getAccountId);
    }

    @Override
    public Map<String, AccountSimpleDTO> getAccount(String... accountId) {
        if(Objects.isNull(accountId))
            return null;
        return this.getAccount(Arrays.stream(accountId).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList()));
    }

    @Override
    public List<DataPermissionDTO> queryDataPermission(String accountId, String memberId, String goodsCode) {
        if(CharSequenceUtil.isBlank(accountId) || CharSequenceUtil.isBlank(goodsCode))
            return null;
        QueryDataPermissionDTO param = new QueryDataPermissionDTO(Arrays.asList(memberId),Arrays.asList(goodsCode),Arrays.asList(accountId));
        ItemResult<List<DataPermissionDTO>> result = iRoleService.queryDataPermission(param);
        return Objects.nonNull(result) ? result.getData() : null;
    }

    @Override
    public Map<String,List<AccountNameDTO>> findApprover(String roleCode, List<String> goodsCodes) {
        if(CharSequenceUtil.isBlank(roleCode) || CollUtil.isEmpty(goodsCodes))
            return null;
        goodsCodes = goodsCodes.stream().filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(goodsCodes))
            return null;
        List<AccountNameDTO> accountsByRole = iAccountService.findAccountNameByRoleCode(roleCode);
        if(CollUtil.isEmpty(accountsByRole))
            return null;
        Map<String,AccountNameDTO> accountMap = CommonUtils.getMap(accountsByRole,AccountNameDTO::getAccountId);

        List<String> accountIds = accountsByRole.stream().map(AccountNameDTO::getAccountId).collect(Collectors.toList());
        QueryDataPermissionDTO param = new QueryDataPermissionDTO();
        param.setGoodsCodes(goodsCodes);
        param.setAccountIds(accountIds);
        ItemResult<List<DataPermissionDTO>> result = iRoleService.queryDataPermission(param);
        if(Objects.isNull(result) || CollUtil.isEmpty(result.getData()))
            return null;

        Map<String,List<AccountNameDTO>> rs = new HashMap<>();
        result.getData().stream()
                .collect(Collectors.groupingBy(DataPermissionDTO::getGoodsCode))
                .forEach((k,v)->{
            List<String> accountIdsByCodes = v.stream()
                    .map(v1->v1.getAccountId())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            List<AccountNameDTO> accountNames = CommonUtils.getListByKey(accountMap, accountIdsByCodes);
            if(CollUtil.isNotEmpty(accountNames))
                rs.put(k,accountNames);
        });

        return rs;
    }

    @Override
    public Map<AccountSubscribeWechatKeyDTO, List<AccountSubscribeWechatDTO>> queryAccountSubscribeWechat(List<String> accountIds,
                                                                                                          List<String> businessNos,
                                                                                                          SubscribeWechatBusinessTypeEnum type) {
        if(CollUtil.isEmpty(accountIds) || CollUtil.isEmpty(businessNos) || Objects.isNull(type))
            return null;
        List<AccountSubscribeWechatDTO> list = iAccountService.querySubscribeByCondition(new QueryAccountSubscribeWechatDTO(accountIds,businessNos,type.getType()));
        if(CollUtil.isEmpty(list))
            return null;

        return list.stream()
                .collect(Collectors.groupingBy(v->
                        new AccountSubscribeWechatKeyDTO(v.getAccountId(),v.getBusinessNo(),v.getBusinessType())));
    }

    @Override
    public List<AccountSimpleDTO> queryMemberAccountByRoleCodes(List<String> memberCodes, List<String> roleCodes,
                                                                DepositStatusEnum depositStatus) {
        if (CollUtil.isEmpty(memberCodes))
            return Collections.emptyList();

        QueryMemberAccountDTO query = new QueryMemberAccountDTO();
        query.setMemberCodes(memberCodes);
        query.setRoleCodes(roleCodes);
        if (Objects.nonNull(depositStatus))
            query.setDepositStatus(depositStatus.getCode());
        return iMemberService.listAccountsByMemberCodes(query);
    }

    @Override
    public List<AccountNameDTO> queryAccountByRole(String roleCode) {
        if (CharSequenceUtil.isBlank(roleCode))
            return null;
        return iAccountService.findAccountNameByRoleCode(roleCode);
    }

    private static String getMemberCode(Map<String, MemberSimpleDTO> memberMap, String memberId) {
        MemberSimpleDTO member = CommonUtils.getByKey(memberMap, memberId);
        if (Objects.nonNull(member))
            return member.getMemberCode();
        return null;
    }
}
