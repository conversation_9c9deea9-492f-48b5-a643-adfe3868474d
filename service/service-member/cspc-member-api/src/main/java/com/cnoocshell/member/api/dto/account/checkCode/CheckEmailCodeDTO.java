package com.cnoocshell.member.api.dto.account.checkCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * @DESCRIPTION:
 */
@Data
public class CheckEmailCodeDTO {

    /**
     * 短信验证码用途
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的用途,见GetEmailCodeDTO")
    private String use;

    /**
     * 电子邮箱
     */
    @NotBlank
    @ApiModelProperty("要收验证码的电子邮件")
    private String email;

    /**
     * 会话id
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码的前端sessionId")
    private String sessionId="sessionId";

    /**
     * 短信验证码
     */
    @NotBlank
    @ApiModelProperty("要验证的短信验证码")
    private String emailCode;

    /**
     * 是否清除短信验证码
     */
    @ApiModelProperty("是否需要清除该短信验证码")
    private Boolean deleteEmailCode = Boolean.FALSE;

    /**
     * 校验短信验证码
     */
    public CheckEmailCodeDTO(){}

    /**
     * 校验短信验证码
     */
    public CheckEmailCodeDTO(String use, String email, String emailCode, String sessionId){
        this(use,email,emailCode,sessionId,false);
    }

    /**
     * 校验短信验证码
     */
    public CheckEmailCodeDTO(String use, String email, String emailCode, String sessionId, Boolean deleteEmailCode){
        this.use = use;
        this.email = email;
        this.sessionId = sessionId;
        this.emailCode = emailCode;
        this.deleteEmailCode = deleteEmailCode != null && deleteEmailCode;
    }

}
