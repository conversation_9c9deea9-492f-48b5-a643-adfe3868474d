package com.cnoocshell.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cnoocshell.base.api.dto.DataPermissionAccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionGoodsCodeDTO;
import com.cnoocshell.base.api.dto.ValueSetDTO;
import com.cnoocshell.base.api.dto.ValueSetTreeDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.*;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.IMessageSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.CategoryAndParentCategoryDTO;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.integration.dto.sap.BiddingDealContractDTO;
import com.cnoocshell.integration.dto.sap.SapContractDTO;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.integration.service.ISapService;
import com.cnoocshell.member.api.dto.account.AccountAgreementDTO;
import com.cnoocshell.member.api.dto.account.AccountNameDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.enums.AgreementBusinessTypeEnum;
import com.cnoocshell.member.api.enums.AgreementTypeEnum;
import com.cnoocshell.member.api.service.IAccountRelationService;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.mq.core.MQMessage;
import com.cnoocshell.mq.core.service.IMQProducer;
import com.cnoocshell.order.api.constant.ColumnConstant;
import com.cnoocshell.order.api.constant.OrderNumberConstant;
import com.cnoocshell.order.api.constant.StringConstant;
import com.cnoocshell.order.api.dto.AccountUserDTO;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.cnoocshell.order.api.dto.QueryAccountDataPermissionDTO;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingAdjustDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingBuyerDetailDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.BiddingReverseDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.RemoveBiddingDTO;
import com.cnoocshell.order.api.enums.BiddingMessageTemplateEnum;
import com.cnoocshell.order.api.enums.BiddingStatusEnum;
import com.cnoocshell.order.api.enums.ParticipationStatusEnum;
import com.cnoocshell.order.api.enums.ReportDealStatusEnum;
import com.cnoocshell.order.biz.*;
import com.cnoocshell.order.dao.mapper.BiddingMapper;
import com.cnoocshell.order.dao.vo.*;
import com.cnoocshell.order.exception.OrderErrorCode;
import com.cnoocshell.order.dao.vo.Enquiry;
import com.cnoocshell.order.service.IBiddingAccountService;
import com.cnoocshell.order.service.IBiddingService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingServiceImpl implements IBiddingService {
    private static final String TEMPLATE = "{}_{}";
    private final IBiddingBiz biddingBiz;
    private final IBiddingBuyerDetailBiz biddingBuyerDetailBiz;
    private final IGoodsService iGoodsService;
    private final IBiddingAdjustBiz iBiddingAdjustBiz;
    private final IMemberService iMemberService;
    private final IValueSetService iValueSetService;
    private final IBiddingBuyerDealBiz iBiddingBuyerDealBiz;
    private final ISmsSendService iSmsSendService;
    private final IEmailSendService iEmailSendService;
    private final IAccountRelationService iAccountRelationService;
    private final IMessageSendService iMessageSendService;
    private final ISalesPlanBiz iSalesPlanBiz;
    private final IEnquiryBiz iEnquiryBiz;
    private final IBiddingApproveRecordBiz iBiddingApproveRecordBiz;
    private final IBiddingBuyerDetailBiz iBiddingBuyerDetailBiz;
    private final IAccountService iAccountService;
    private final IRoleService iRoleService;
    private final IGoodsCategoryService iGoodsCategoryService;
    private final IBiddingAccountService iBiddingAccountService;
    private final IBiddingBuyerBiz iBiddingBuyerBiz;
    private final IMQProducer mqProducer;
    private final ISapService iSapService;
    private final BiddingMapper biddingMapper;

    @Resource(name = "threadPoolExecutor")
    private Executor executor;

    private static final String BIDDING_NOT_EXIST = "竞价场次不存在！";


    @SneakyThrows
    @Override
    @AddLog(operatorFieldName = ColumnConstant.CREATE_USER)
    @Transactional(rollbackFor = Exception.class)
    public Boolean joinBiddingByBuyer(BiddingBuyerDetailDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM, "竞价数据不存在");
        if (!BiddingStatusEnum.BIDDING.getStatus().equals(bidding.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "不在竞价中，不可进行报量");
        //判断当前时间是 在最新竞价结束时间之后
        if(new Date().after(bidding.getLastBiddingEndTime()))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价已结束，不可进行报量");
        //判断有无该产品意向数据
        QueryIntentionInfoDTO queryIntention = new QueryIntentionInfoDTO();
        queryIntention.setMemberCodes(Arrays.asList(param.getMemberCode()));
        queryIntention.setGoodsCodes(Arrays.asList(bidding.getGoodsCode()));
        List<MemberPurchaseGoodsIntentionDTO> intentions = iMemberService.queryMemberBySaleInfo(queryIntention);
        if(CollUtil.isEmpty(intentions)){
            throw new BizException(OrderErrorCode.CUSTOM,"您无此产品的操作权限，如需参与此竞价，请前往企业信息管理处添加此意向产品。");
        }
        //判断当前用户是否拥有关联产品权限
        if (CollUtil.isEmpty(iBiddingAccountService.queryDataPermission(param.getCreateUser(), param.getMemberId(), bidding.getGoodsCode())))
            throw new BizException(OrderErrorCode.CUSTOM, "您暂无权限参与此产品相关的竞价，请与您的企业管理员确认相关权限");

        //不是首轮 上轮未参与竞价客户不能参与当前轮次竞价
        if(bidding.getCurrentRound() > OrderNumberConstant.ONE){
            if(!BooleanUtil.isTrue(biddingBuyerDetailBiz.existByMemberCode(param.getMemberCode(),
                    bidding.getBiddingNo(),bidding.getCurrentRound()-OrderNumberConstant.ONE)))
                throw new BizException(OrderErrorCode.CUSTOM,"您上一场未参与，无法参与当前场次的竞价");
        }

        BiddingBuyerDetail biddingBuyerDetail = BeanUtil.toBean(param, BiddingBuyerDetail.class);
        biddingBuyerDetail.setBiddingNo(bidding.getBiddingNo());
        biddingBuyerDetail.setCurrentRound(bidding.getCurrentRound());
        biddingBuyerDetail.setCurrentStandardPrice(bidding.getLastStandardPrice());
        ItemResult<List<GoodsSimpleDTO>> goodsResult = iGoodsService.findGoodsSimpleByCodes(Arrays.asList(bidding.getGoodsCode()));
        Map<String, GoodsSimpleDTO> goodsMap = CommonUtils.getMap(goodsResult, GoodsSimpleDTO::getGoodsCode);
        GoodsSimpleDTO goods = CommonUtils.getByKey(goodsMap, bidding.getGoodsCode());
        if (Objects.nonNull(goods)) {
            biddingBuyerDetail.setUnit(goods.getUnit());
        }
        //设置总价
        biddingBuyerDetail.setTotalAmount(NumberUtil.mul(bidding.getLastStandardPrice(), param.getWantQuantity()));
        biddingBuyerDetail.setBiddingEndTime(bidding.getLastBiddingEndTime());
        biddingBuyerDetail.setBiddingStartTime(bidding.getLastBiddingStartTime());
        biddingBuyerDetail.handleUser(param.getCreateUser(), true);

        biddingBuyerDetailBiz.save(biddingBuyerDetail);

        //更新参与客户参与状态
        iBiddingBuyerBiz.updateStatus(ParticipationStatusEnum.DONE, param.getMemberCode(), param.getBiddingNo(), param.getCreateUser());

        //保存客户报量协议记录
        MQMessage<List<AccountAgreementDTO>> mq = new MQMessage<>();
        mq.setExchange(MqTopicConstant.SAVE_AGREEMENT_TOPIC);
        AccountAgreementDTO biddingBuyerAgreement = new AccountAgreementDTO();
        biddingBuyerAgreement.setAccountId(param.getCreateUser());
        biddingBuyerAgreement.setBusinessNo(biddingBuyerDetail.getId());
        biddingBuyerAgreement.setBusinessType(AgreementBusinessTypeEnum.BIDDING_BUYER_DETAIL_ID.getType());
        biddingBuyerAgreement.setAgreementType(AgreementTypeEnum.TRANSACTION_AGREEMENT.getType());
        mq.setData(Arrays.asList(biddingBuyerAgreement));

        mqProducer.send(mq);

        return true;
    }

    @Override
    public ExportExcelDTO exportAdjustRecord(String biddingNo) {
        List<BiddingAdjustRecord> list = iBiddingAdjustBiz.listByNo(biddingNo);

        List<ExportBiddingAdjustDTO> result = BeanUtil.copyToList(list, ExportBiddingAdjustDTO.class);
        if (Objects.isNull(result))
            result = new ArrayList<>();
        int i = 1;
        for (ExportBiddingAdjustDTO v : result) {
            v.setSerialNum(String.valueOf(i));
            i++;
        }

        String fileName = CharSequenceUtil.format("线上销售平台_调价记录_{}.xlsx", biddingNo);
        return new ExportExcelDTO(fileName, Arrays.asList(new ExportSheetDTO("调价记录", ExportBiddingAdjustDTO.class, result)));
    }

    @Override
    public ExportExcelDTO exportBiddingBuyerDetail(String biddingNo) {
        Bidding bidding = biddingBiz.getByNo(biddingNo);
        if(Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不存在");
        String fileName = CharSequenceUtil.format("线上销售平台_竞价结果列表_{}.xlsx", biddingNo);
        String sheet1Name = "客户最新竞价结果列表";
        String sheet2Name = "客户全部竞价结果列表";
        ExportSheetDTO sheet1 = new ExportSheetDTO(sheet1Name, ExportBiddingBuyerDetailDTO.class, Collections.emptyList());
        ExportSheetDTO sheet2 = new ExportSheetDTO(sheet2Name, ExportBiddingBuyerDetailDTO.class, Collections.emptyList());

        List<BiddingBuyerDetail> list = biddingBuyerDetailBiz.listAllByNo(biddingNo);
        if (CollUtil.isEmpty(list))
            return new ExportExcelDTO(fileName, Arrays.asList(sheet1, sheet2));

        List<String> memberCodes = list.stream()
                .map(v -> v.getMemberCode())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        Map<String, MemberSimpleDTO> memberMap = null;
        if (CollUtil.isNotEmpty(memberCodes)) {
            memberMap = CommonUtils.getMap(iMemberService.listSimpleMemberByCodes(memberCodes), MemberSimpleDTO::getMemberCode);
        }
        //包装方式运输方式字典
        List<ValueSetDTO> dictList = iValueSetService.getValueTreeByCodes(Arrays.asList(StringConstant.VS_DELIVERY_METHOD, StringConstant.VS_PACKAGE_METHOD));
        Map<String, ValueSetDTO> dictMap = CommonUtils.getMap(dictList, ValueSetDTO::getReferenceCode);
        //运输方式
        ValueSetDTO deliveryMethodSet = CommonUtils.getByKey(dictMap, StringConstant.VS_DELIVERY_METHOD);
        Map<String, ValueSetTreeDTO> ysfsMap = Objects.nonNull(deliveryMethodSet) ? CommonUtils.getMap(deliveryMethodSet.getOptions(), ValueSetTreeDTO::getOptionKey) : null;
        //包装方式
        ValueSetDTO packMethodSet = CommonUtils.getByKey(dictMap, StringConstant.VS_PACKAGE_METHOD);
        Map<String, ValueSetTreeDTO> bzfsMap = Objects.nonNull(packMethodSet) ? CommonUtils.getMap(packMethodSet.getOptions(), ValueSetTreeDTO::getOptionKey) : null;

        Map<String, MemberSimpleDTO> finalMemberMap = memberMap;
        List<ExportBiddingBuyerDetailDTO> allResult = getAllResult(list, finalMemberMap, ysfsMap, bzfsMap);
        if (CollUtil.isNotEmpty(allResult)) {
            int i = 1;
            for (ExportBiddingBuyerDetailDTO v : allResult) {
                v.setSerialNum(String.valueOf(i));
                i++;
            }
        }
        sheet2.setDataList(allResult);

        List<ExportBiddingBuyerDetailDTO> sheet1Result = allResult.stream()
                .filter(v->Objects.equals(v.getCurrentRound(),bidding.getCurrentRound()))
                .collect(Collectors.groupingBy(ExportBiddingBuyerDetailDTO::getMemberCode))
                .values()
                .stream().map(CollUtil::getFirst)
                .sorted(Comparator.comparing(ExportBiddingBuyerDetailDTO::getCreateTime).reversed())
                .collect(Collectors.toList());
        if (Objects.isNull(sheet1Result))
            sheet1Result = new ArrayList<>();
        if (CollUtil.isNotEmpty(sheet1Result)) {
            int i = 1;
            for (ExportBiddingBuyerDetailDTO v : sheet1Result) {
                v.setSerialNum(String.valueOf(i));
                i++;
            }
        }

        sheet1.setDataList(sheet1Result);

        return new ExportExcelDTO(fileName, Arrays.asList(sheet1, sheet2));
    }

    public List<ExportBiddingBuyerDetailDTO> getAllResult(List<BiddingBuyerDetail> list, Map<String, MemberSimpleDTO> finalMemberMap, Map<String, ValueSetTreeDTO> ysfsMap, Map<String, ValueSetTreeDTO> bzfsMap) {
        return list.stream().map(v -> {

            ExportBiddingBuyerDetailDTO target = BeanUtil.toBean(v, ExportBiddingBuyerDetailDTO.class);
            target.setCurrentRoundName(CharSequenceUtil.format("第{}轮", Convert.numberToChinese(v.getCurrentRound(), false)));
            MemberSimpleDTO memberSimple = CommonUtils.getByKey(finalMemberMap, v.getMemberCode());
            if (Objects.nonNull(memberSimple)) {
                target.setCrmCode(memberSimple.getCrmCode());
            }
            ValueSetTreeDTO ysfsOption = CommonUtils.getByKey(ysfsMap, v.getDeliveryMode());
            if (Objects.nonNull(ysfsOption)) {
                target.setDeliveryModeName(ysfsOption.getOptionValue());
            }
            ValueSetTreeDTO bzfsOption = CommonUtils.getByKey(bzfsMap, v.getPack());
            if (Objects.nonNull(bzfsOption)) {
                target.setPackName(bzfsOption.getOptionValue());
            }
            return target;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Boolean> biddingAdjust(BiddingAdjustDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            return fail(BIDDING_NOT_EXIST);
        if (!CollUtil.contains(Arrays.asList(BiddingStatusEnum.REJECTED_RESULT.getStatus(), BiddingStatusEnum.TO_CONFIRM_RESULT.getStatus()), bidding.getStatus())) {
            return fail("当前竞价场次状态不支持调整价格");
        }
        Date now = new Date();

        if (now.after(param.getBiddingStartTime())) {
            return fail("下轮竞价开始时间不能早于当前时间");
        }
        if (param.getBiddingStartTime().after(param.getBiddingEndTime())) {
            return fail("下轮竞价结束时间不能早于开始时间");
        }
        //查询当前轮次参与竞价的买家明细
        List<BiddingBuyerDetail> lastBuyerDetail = biddingBuyerDetailBiz.listByNo(bidding.getBiddingNo(), bidding.getCurrentRound());

        BiddingAdjustRecord adjust = new BiddingAdjustRecord();
        adjust.setBiddingNo(bidding.getBiddingNo());
        adjust.setOldCurrentRound(bidding.getCurrentRound());
        adjust.setOldStandardPrice(bidding.getLastStandardPrice());
        adjust.setNewStandardPrice(param.getNewStandardPrice());
        adjust.setBiddingStartTime(param.getBiddingStartTime());
        adjust.setBiddingEndTime(param.getBiddingEndTime());
        adjust.handleUser(param.getCreateUser(), true);
        adjust.setCreateUserName(param.getCreateUserName());

        iBiddingAdjustBiz.save(adjust);

        bidding.setCurrentRound(bidding.getCurrentRound() + 1);
        bidding.handleUser(param.getCreateUser(), false);
        bidding.setStatus(BiddingStatusEnum.ADJUSTED_PRICE.getStatus());
        bidding.setLastStandardPrice(param.getNewStandardPrice());
        bidding.setLastBiddingStartTime(param.getBiddingStartTime());
        bidding.setLastBiddingEndTime(param.getBiddingEndTime());
        //CR 2025.4.28 提货时间处理
        bidding.setDeliveryEffectStartDate(
                biddingBiz.getNewDeliveryEffectStartDate(bidding.getLastBiddingStartTime(),bidding));

        biddingBiz.updateSelective(bidding);

        //重置买家成交结果
        iBiddingBuyerDealBiz.resetBuyerDealByBiddingList(Arrays.asList(bidding));
        //重置买家参与状态
        iBiddingBuyerBiz.resetBuyerStatus(bidding.getBiddingNo());

        //创建定时任务
        biddingBiz.createJob(bidding);
        //发送站内信与短信 提示上一轮参与竞价的客户
        this.notifyLastBuyer(lastBuyerDetail, bidding);

        return ItemResult.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Boolean> biddingNotDeal(BiddingDealConfirmDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.REJECTED_RESULT.getStatus(), BiddingStatusEnum.TO_CONFIRM_RESULT.getStatus());
        if (!CollUtil.contains(allowStatus, bidding.getStatus()))
            return fail("当前状态不支持不成交竞价操作");
        bidding.setStatus(BiddingStatusEnum.CANCELLED.getStatus());
        bidding.setNotDealReason(param.getNotDealReason());
        bidding.setDealOperator(param.getAccountId());
        bidding.setDealTime(new Date());

        bidding.handleUser(param.getAccountId(), false);
        biddingBiz.updateSelective(bidding);

        List<BiddingBuyerDetail> buyerDetails = biddingBuyerDetailBiz.listByNo(bidding.getBiddingNo(),bidding.getCurrentRound());
        //重置买家成交结果
        iBiddingBuyerDealBiz.resetBuyerDealByBiddingList(Arrays.asList(bidding));

        //异步通知 相关人员
        executor.execute(() -> {
            biddingBiz.notifyByNotDeal(bidding, buyerDetails);
        });

        return ItemResult.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Boolean> biddingDeal(BiddingDealConfirmDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.REJECTED_RESULT.getStatus(), BiddingStatusEnum.TO_CONFIRM_RESULT.getStatus());
        if (!CollUtil.contains(allowStatus, bidding.getStatus()))
            return fail("当前状态不支持成交竞价操作");
        if (CollUtil.isEmpty(param.getBiddingBuyerDetailIds()))
            return fail("成交结果不能为空");

        List<String> distinctIds = param.getBiddingBuyerDetailIds().stream().distinct().collect(Collectors.toList());
        List<BiddingBuyerDetail> details = biddingBuyerDetailBiz.listByIds(distinctIds);
        if (!CommonUtils.equalsSize(distinctIds, details))
            return fail("买家竞价记录不存在");

        //计算总量
        BigDecimal buyerTotal = NumberUtil.add(details.stream()
                .map(v -> v.getWantQuantity())
                .collect(Collectors.toList())
                .toArray(BigDecimal[]::new));
        BiddingStatusEnum status = BiddingStatusEnum.COMPLETED;
        //是否超量
        Boolean isOver = Boolean.FALSE;

        if (Objects.nonNull(bidding.getCurrApplySellableQuantity())
                && buyerTotal.compareTo(bidding.getCurrApplySellableQuantity()) > 0) {
            //提交成交结果待审批
            status = BiddingStatusEnum.TO_APPROVE_RESULT;
            isOver = Boolean.TRUE;
        }
        //保存成交结果
        iBiddingBuyerDealBiz.saveDealResult(details, bidding, param.getAccountId(), param.getRealName());
        //更新竞价状态
        biddingBiz.updateBiddingStatusByIds(status, param.getAccountId(), Arrays.asList(bidding), true);
        //更新超量成交备注 若有
        if(CharSequenceUtil.isNotBlank(param.getOverDealRemark()))
            biddingBiz.updateOverDealRemark(param.getOverDealRemark(),bidding.getId());

        if (isOver) {
            //超量 通知一级审批人CMMS
            executor.execute(() -> {
                this.notifyWaitApprovalByDeal(bidding, details, param.getRealName());
            });
        }

        return ItemResult.success(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Boolean> withdrawDealResult(WithdrawDealResultDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            return fail(BIDDING_NOT_EXIST);
        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.APPROVING_RESULT.getStatus(), BiddingStatusEnum.TO_APPROVE_RESULT.getStatus());
        if (!CollUtil.contains(allowStatus, bidding.getStatus()))
            return fail("竞价场次状态不支持撤回");
        bidding.handleUser(param.getAccountId(), false);
        bidding.setStatus(BiddingStatusEnum.TO_CONFIRM_RESULT.getStatus());

        biddingBiz.updateSelective(bidding);

        //重置买家成交结果
        iBiddingBuyerDealBiz.resetBuyerDealByBiddingList(Arrays.asList(bidding));

        return ItemResult.success(true);
    }

    @Override
    public ItemResult<BiddingDetailDTO> biddingDetail(QueryBiddingDetailDTO param) {
        if (Boolean.TRUE.equals(checkDataPermissionsByDetail(param))) {
            return fail("无权限");
        }
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            return fail(BIDDING_NOT_EXIST);

        if (BooleanUtil.isTrue(param.getIsSeller())) {
            List<String> salePersonalMemberCodes = new ArrayList<>();
            if (BooleanUtil.isTrue(param.getIsOnlySalePersonal())) {
                //销售人员可以看到的客户+商品数据
                QueryIntentionInfoDTO query = new QueryIntentionInfoDTO();
                query.setSaleUserIds(Arrays.asList(param.getAccountId()));
                query.setGoodsCodes(Arrays.asList(bidding.getGoodsCode()));
                List<MemberPurchaseGoodsIntentionDTO> salePersonal = iMemberService.queryMemberBySaleInfo(query);
                if (CollUtil.isNotEmpty(salePersonal)) {
                    salePersonalMemberCodes = salePersonal.stream()
                            .map(v -> v.getMemberCode())
                            .distinct().collect(Collectors.toList());
                }
            }

            return this.detailBySeller(bidding, param.getIsOnlySalePersonal(), salePersonalMemberCodes);
        }

        return this.detailByBuyer(bidding, param);
    }

    @Override
    public ItemResult<Boolean> createSapContractBySeller(String dealNo) {
        BiddingBuyerDeal deal = iBiddingBuyerDealBiz.getByDealNo(dealNo);
        if(Objects.isNull(deal))
            throw new BizException(OrderErrorCode.CUSTOM,"成交数据不存在");
        if(CharSequenceUtil.isNotBlank(deal.getContractNo()))
            throw new BizException(OrderErrorCode.CUSTOM,"已存在合同");

        Bidding bidding = biddingBiz.getByNo(deal.getBiddingNo());
        if(Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不存在");
        if(!BiddingStatusEnum.COMPLETED.getStatus().equals(bidding.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不是已完成状态");

        List<SapContractDTO> params = biddingBiz.createSapContract(Arrays.asList(bidding.getId()),Arrays.asList(dealNo),false);
        if(CollUtil.isEmpty(params))
            throw new BizException(OrderErrorCode.CUSTOM,"创建成交合同失败");

        BiddingDealContractDTO result = iSapService.createSapContract(CollUtil.getFirst(params));
        if(Objects.isNull(result))
            throw new BizException(OrderErrorCode.CUSTOM,"创建SAP成交合同失败");

        iBiddingBuyerDealBiz.updateBiddingDealContractInfo(result);

        if(!BooleanUtil.isTrue(result.getCreateContractStatus()))
            return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(),"创建SAP合同失败："+ ObjectUtil.defaultIfNull(result.getCreateContractFailReason(),""));

        return ItemResult.success(true);
    }

    @Override
    public Boolean biddingStrategyWithdrawDraft(String biddingNo,String operatorId) {
        Bidding bidding = biddingBiz.getByNo(biddingNo);
        if(Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM,"竞价场次不存在");
        //竞价策略待提交 竞价策略已驳回 逆向流程新增竞价策略已撤回
        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.TO_SUBMIT_STRATEGY.getStatus(),
                BiddingStatusEnum.REJECTED_STRATEGY.getStatus(),BiddingStatusEnum.WITHDRAWN.getStatus());
        if(!CollUtil.contains(allowStatus,bidding.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM,"当前状态不允许撤回竞价策略");

        Bidding modifyBidding = new Bidding();
        modifyBidding.setStatus(BiddingStatusEnum.DRAFT.getStatus());
        if(CharSequenceUtil.isNotBlank(operatorId))
            modifyBidding.handleUser(operatorId,false);
        Condition modifyCondition = new Condition(Bidding.class);
        modifyCondition.createCriteria()
                .andEqualTo(ColumnConstant.ID,bidding.getId());

        return biddingMapper.updateByConditionSelective(modifyBidding,modifyCondition) > 0;
    }

    @Override
    public void withDrawStrategy(BiddingReverseDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM, "竞价场次不存在");
        if (!CharSequenceUtil.equals(bidding.getStatus(), BiddingStatusEnum.APPROVED_STRATEGY.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不允许撤回竞价策略");

        bidding.setStatus(BiddingStatusEnum.WITHDRAW_TO_APPROVAL.getStatus());
        bidding.setWithdrawUser(param.getOperatorBy());
        bidding.setWithdrawUserName(param.getOperatorName());
        bidding.setWithdrawReason(param.getReason());
        bidding.setWithdrawTime(ObjectUtil.defaultIfNull(param.getOperationTime(), new Date()));

        biddingBiz.updateSelective(bidding);
        //停止定时任务
        biddingBiz.stopBiddingJob(param.getBiddingNo());
        //通知撤回审批一级审批人
        executor.execute(() -> this.notifyWithdrawToCmms(param, bidding));
    }

    @Override
    public void cancelBidding(BiddingReverseDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM, "竞价场次不存在");
        if (!CharSequenceUtil.equals(bidding.getStatus(), BiddingStatusEnum.APPROVED_STRATEGY.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不允许取消竞价场次");

        bidding.setStatus(BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus());
        bidding.setCancelUser(param.getOperatorBy());
        bidding.setCancelUserName(param.getOperatorName());
        bidding.setCancelReason(param.getReason());
        bidding.setCancelTime(ObjectUtil.defaultIfNull(param.getOperationTime(), new Date()));

        biddingBiz.updateSelective(bidding);
        //停止定时任务
        biddingBiz.stopBiddingJob(param.getBiddingNo());
        //通知取消审批一级审批人

        executor.execute(() -> notifyCancelToCmms(param, bidding));
    }

    @Override
    public void removeDraft(RemoveBiddingDTO param) {
        Bidding bidding = biddingBiz.getByNo(param.getBiddingNo());
        if (Objects.isNull(bidding))
            throw new BizException(OrderErrorCode.CUSTOM, "竞价场次不存在");
        if (!CharSequenceUtil.equals(bidding.getStatus(), BiddingStatusEnum.DRAFT.getStatus()))
            throw new BizException(OrderErrorCode.CUSTOM, "当前状态不允许删除");
        biddingMapper.removeBiddingById(bidding.getId());
    }

    @Override
    public PageInfo<BiddingReportResultDTO> biddingReport(BiddingReportQueryDTO param) {
        PageInfo<BiddingReportResultDTO> pageResult = biddingBiz.biddingReport(param);
        if (CollUtil.isEmpty(pageResult.getList()))
            return pageResult;

        List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), BiddingReportResultDTO::getGoodsCode);
        //K:goodsCode
        Map<String, GoodsSimpleDTO> goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByCodes(goodsCodes), GoodsSimpleDTO::getGoodsCode);

        //查询会员及意向商品信息
        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), BiddingReportResultDTO::getMemberCode);
        QueryMemberIntentionInfoDTO queryMemberParam = new QueryMemberIntentionInfoDTO(memberCodes, goodsCodes);
        List<MemberIntentionInfoDTO> memberList = iMemberService.queryMemberIntentionInfo(queryMemberParam);
        //K:memberCode
        Map<String, MemberIntentionInfoDTO> memberMap = CommonUtils.getMap(memberList, MemberIntentionInfoDTO::getMemberCode);
        //K:memberCode_goodsCode
        Map<String, MemberIntentionInfoDTO> memberIntentionMap = CommonUtils.getMap(memberList,
                v -> CharSequenceUtil.format(TEMPLATE, v.getMemberCode(), v.getGoodsCode()));

        //查询销售渠道字典
        ValueSetTreeDTO salesChannelDict = iValueSetService.getValueSetByReferenceCode(StringConstant.VS_SALES_CHANNEL);
        Map<String, ValueSetTreeDTO> salesChannelMap = null;
        if (Objects.nonNull(salesChannelDict)) {
            salesChannelMap = CommonUtils.getMap(salesChannelDict.getTreeList(), ValueSetTreeDTO::getOptionKey);
        }

        //查询商品分类及父级分类信息
        List<String> categoryCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), BiddingReportResultDTO::getCategoryCodeLevelTwo);
        //K：categoryCodeLevelTwo
        Map<String, CategoryAndParentCategoryDTO> categoryMap = CommonUtils.getMap(iGoodsCategoryService.queryCategoryAndParentCategory(categoryCodes), CategoryAndParentCategoryDTO::getCategoryCodeLevelTwo);

        for (BiddingReportResultDTO v : pageResult.getList()) {
            String memberCode = v.getMemberCode();
            String salesChannelKey = CharSequenceUtil.format(TEMPLATE, memberCode, v.getGoodsCode());
            //物料编码
            GoodsSimpleDTO goods = CommonUtils.getByKey(goodsMap, v.getGoodsCode());
            if (Objects.nonNull(goods))
                v.setSapMaterialCode(goods.getSapMaterialCode());
            //CRM编码
            MemberIntentionInfoDTO memberIntentionInfoDTO = CommonUtils.getByKey(memberMap, memberCode);
            if (Objects.nonNull(memberIntentionInfoDTO))
                v.setCrmCode(memberIntentionInfoDTO.getCrmCode());
            //销售渠道
            MemberIntentionInfoDTO intention = CommonUtils.getByKey(memberIntentionMap, salesChannelKey);
            if (Objects.nonNull(intention)) {
                ValueSetTreeDTO saleChannelOption = CommonUtils.getByKey(salesChannelMap, intention.getSaleChannel());
                if (Objects.nonNull(saleChannelOption)) {
                    v.setSaleChannel(saleChannelOption.getOptionKey());
                    v.setSaleChannelName(saleChannelOption.getOptionValue());
                }
            }
            //分类信息
            CategoryAndParentCategoryDTO category = CommonUtils.getByKey(categoryMap, v.getCategoryCodeLevelTwo());
            if (Objects.nonNull(category)) {
                v.setCategoryNameLevelOne(category.getCategoryNameLevelOne());
                v.setCategoryNameLevelTwo(category.getCategoryNameLevelTwo());
                v.setCategoryCodeLevelOne(category.getCategoryCodeLevelOne());
            }
            //状态
            v.setDealStatusName(ReportDealStatusEnum.getNameByStatus(v.getDealStatus()));
            v.setStatusName(BiddingStatusEnum.getNameByStatus(v.getStatus()));
        }

        return pageResult;
    }

    @Override
    public PageInfo<MemberActivityReportResultDTO> memberActivityReport(MemberActivityReportQueryDTO param) {
        PageInfo<MemberActivityReportResultDTO> pageResult = biddingBiz.memberActivityReport(param);
        if (CollUtil.isEmpty(pageResult.getList()))
            return pageResult;
        List<String> memberCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(),MemberActivityReportResultDTO::getMemberCode);
        List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(),MemberActivityReportResultDTO::getGoodsCode);

        //查询销售渠道字典
        ValueSetTreeDTO salesChannelDict = iValueSetService.getValueSetByReferenceCode(StringConstant.VS_SALES_CHANNEL);
        Map<String, ValueSetTreeDTO> salesChannelMap = null;
        if (Objects.nonNull(salesChannelDict)) {
            salesChannelMap = CommonUtils.getMap(salesChannelDict.getTreeList(), ValueSetTreeDTO::getOptionKey);
        }

        //会员信息 及销售渠道
        List<MemberIntentionInfoDTO> memberList = iMemberService.queryMemberIntentionInfo(new QueryMemberIntentionInfoDTO(memberCodes,goodsCodes));
        //K:memberCode
        Map<String, MemberIntentionInfoDTO> memberMap = CommonUtils.getMap(memberList, MemberIntentionInfoDTO::getMemberCode);
        //K:memberCode_goodsCode
        Map<String, MemberIntentionInfoDTO> memberIntentionMap = CommonUtils.getMap(memberList,
                v -> CharSequenceUtil.format(TEMPLATE, v.getMemberCode(), v.getGoodsCode()));
        //商品信息
        Map<String,GoodsAndCategoryInfoResultDTO> goodsMap = CommonUtils.getMap(
                iGoodsService.queryGoodsAndCategoryInfoByGoodsCodes(goodsCodes),GoodsAndCategoryInfoResultDTO::getGoodsCode);

        for (MemberActivityReportResultDTO v : pageResult.getList()) {
            MemberIntentionInfoDTO member = CommonUtils.getByKey(memberMap,v.getMemberCode());
            if(Objects.nonNull(member)){
                v.setMemberName(member.getMemberName());
                v.setCrmCode(member.getCrmCode());
            }
            MemberIntentionInfoDTO intention = CommonUtils.getByKey(memberIntentionMap, CharSequenceUtil.format(TEMPLATE, v.getMemberCode(), v.getGoodsCode()));
            if(Objects.nonNull(intention)){
                v.setSaleChannel(intention.getSaleChannel());
                ValueSetTreeDTO saleChannelOption = CommonUtils.getByKey(salesChannelMap, intention.getSaleChannel());
                if (Objects.nonNull(saleChannelOption))
                    v.setSaleChannelName(saleChannelOption.getOptionValue());
            }
            GoodsAndCategoryInfoResultDTO goods = CommonUtils.getByKey(goodsMap, v.getGoodsCode());
            if(Objects.nonNull(goods)){
                v.setSapMaterialCode(goods.getSapMaterialCode());
                v.setGoodsName(goods.getGoodsName());
                v.setCategoryNameLevelOne(goods.getCategoryNameLevelOne());
                v.setCategoryNameLevelTwo(goods.getCategoryNameLevelTwo());
                v.setCategoryCodeLevelOne(goods.getCategoryCodeLevelOne());
                v.setCategoryCodeLevelTwo(goods.getCategoryCodeLevelTwo());
            }
        }
        return pageResult;
    }

    private Boolean checkDataPermissionsByDetail(QueryBiddingDetailDTO param) {
        int dataFrom = param.getDataFrom();
        if (dataFrom == 1) {
            BiddingSalesListDTO biddingSalesListDTO = new BiddingSalesListDTO();
            biddingSalesListDTO.setAccountId(param.getAccountId());
            biddingSalesListDTO.setDetailBiddingNo(param.getBiddingNo());
            biddingSalesListDTO.setRoleList(param.getRoleList());
            PageInfo<BiddingSalesListViewDTO> biddingSalesListViewDTOPageInfo = biddingBiz.salesBiddingList(biddingSalesListDTO, 1);
            List<BiddingSalesListViewDTO> list = biddingSalesListViewDTOPageInfo.getList();
            return CollUtil.isEmpty(list);
        }
        if (dataFrom == 2) {
            BiddingSalesListDTO biddingSalesListDTO = new BiddingSalesListDTO();
            biddingSalesListDTO.setAccountId(param.getAccountId());
            biddingSalesListDTO.setDetailBiddingNo(param.getBiddingNo());
            biddingSalesListDTO.setRoleList(param.getRoleList());
            PageInfo<BiddingSalesListViewDTO> biddingSalesListViewDTOPageInfo = biddingBiz.queryApproveList(biddingSalesListDTO);
            List<BiddingSalesListViewDTO> list = biddingSalesListViewDTOPageInfo.getList();
            return CollUtil.isEmpty(list);
        }
        if (dataFrom == 3) {
            return false;
        }
        return true;

    }

    private ItemResult<BiddingDetailDTO> detailBySeller(Bidding bidding, Boolean isSalePersonal, List<String> salePersonalMemberCodes) {

        //调价记录 创建时间降序
        List<BiddingAdjustRecord> adjustRecords = iBiddingAdjustBiz.listByNo(bidding.getBiddingNo());
        //竞价结果 卖家  竞价结果的数据=成交结果的数据
        List<BiddingBuyerDeal> dealList = iBiddingBuyerDealBiz.listByBiddingNos(Arrays.asList(bidding.getBiddingNo()));
        Map<String, BiddingBuyerDeal> dealMap = CommonUtils.getMap(dealList, BiddingBuyerDeal::getBiddingBuyerDetailId);
        List<BiddingBuyerDetail> biddingBuyerDetailBySeller = setBiddingBuyerDetailBySeller(bidding, dealList);

        //当前轮次买家报量总量
        BigDecimal buyerTotal = null;
        //销售 客户信息
        List<BiddingBuyerDetailDTO> biddingDetails = BeanUtil.copyToList(biddingBuyerDetailBySeller, BiddingBuyerDetailDTO.class);
        if (CollUtil.isNotEmpty(biddingDetails)) {

            buyerTotal = NumberUtil.add(biddingDetails.stream()
                    .map(v -> v.getWantQuantity())
                    .collect(Collectors.toList())
                    .toArray(BigDecimal[]::new));

            //销售人员可以看到的竞价数据
            biddingDetails = setBiddingDetails(isSalePersonal, biddingDetails, salePersonalMemberCodes);
            List<String> memberCodes = biddingDetails.stream()
                    .map(v -> v.getMemberCode())
                    .distinct()
                    .collect(Collectors.toList());
            List<BiddingMemberSaleUserDTO> saleInfo = this.getMemberSaleUsers(memberCodes, bidding.getGoodsCode());
            //K:memberCode_goodsCode
            Map<String, BiddingMemberSaleUserDTO> saleMap = setSaleMap(saleInfo);
            Map<String, MemberSimpleDTO> memberMap = CommonUtils.getMap(iMemberService.listSimpleMemberByCodes(memberCodes), MemberSimpleDTO::getMemberCode);
            setBiddingBuyerDetailData(biddingDetails, bidding, memberMap, saleMap, dealMap);
        }

        AccountSimpleDTO dealAccount = null;
        if (CharSequenceUtil.isNotBlank(bidding.getDealOperator())) {
            dealAccount = CollUtil.getFirst(iAccountService.listSimpleByIds(Arrays.asList(bidding.getDealOperator())));
        }
        //处理成交结果
        BiddingBuyerDealDTO biddingBuyerDeal = new BiddingBuyerDealDTO();
        biddingBuyerDeal.setLastStandardPrice(bidding.getLastStandardPrice());
        biddingBuyerDeal.setDealOperator(bidding.getDealOperator());
        //成交提交时间
        biddingBuyerDeal.setSubmitDealTime(bidding.getSubmitDealTime());
        if (Objects.nonNull(dealAccount)) {
            biddingBuyerDeal.setDealOperatorName(dealAccount.getRealName());
        }

        List<String> allowStatus = Arrays.asList(BiddingStatusEnum.COMPLETED.getStatus(),
                BiddingStatusEnum.TO_APPROVE_RESULT.getStatus(), BiddingStatusEnum.APPROVING_RESULT.getStatus());
        if (CollUtil.contains(allowStatus, bidding.getStatus())) {
            biddingBuyerDeal.setDealTime(bidding.getDealTime());
            biddingBuyerDeal.setBuyerDealDetail(biddingDetails);
        } else {
            biddingBuyerDeal.setNotDealReason(bidding.getNotDealReason());
        }

        //查询销售计划 询价

        Enquiry enquiry = iEnquiryBiz.getByNo(bidding.getEnquiryNo());
        SalesPlan salesPlan = iSalesPlanBiz.getByNo(bidding.getSalesPlanNo());

        BiddingDetailDTO result = new BiddingDetailDTO();
        BiddingBasicDTO basic = BeanUtil.toBean(bidding, BiddingBasicDTO.class);
        //处理创建人与更新人信息
        handleUserName(basic, iBiddingAccountService.getAccount(basic.getCreateUser(), basic.getUpdateUser()));

        basic.setEnquiryNo(bidding.getEnquiryNo());
        basic.setSalePlanNo(bidding.getSalesPlanNo());
        //设置参与竞价客户数量
        basic.setBiddingBuyerCount(iBiddingBuyerBiz.countBiddingBuyer(bidding.getBiddingNo()));
        //计算是否超量
        if (Objects.nonNull(buyerTotal) && Objects.nonNull(bidding.getCurrApplySellableQuantity())
                && buyerTotal.compareTo(bidding.getCurrApplySellableQuantity()) > 0)
            basic.setOverQuantity(Boolean.TRUE);

        if (Objects.nonNull(enquiry)) {
            basic.setEnquiryId(enquiry.getId());
            basic.setEnquiryNo(enquiry.getEnquiryNo());
        }
        if (Objects.nonNull(salesPlan)) {
            basic.setSalePlanId(salesPlan.getId());
            basic.setSalePlanNo(salesPlan.getPlanNo());
        }
        BiddingStrategyDTO strategy = BeanUtil.toBean(bidding, BiddingStrategyDTO.class);

        //取消信息使用状态进行控制
        List<String> allowShowCancelInfoStatusList = Arrays.asList(BiddingStatusEnum.BIDDING_CANCELLED.getStatus(),
                BiddingStatusEnum.CANCEL_TO_APPROVAL.getStatus(),BiddingStatusEnum.CANCEL_APPROVING.getStatus());
        if(!CollUtil.contains(allowShowCancelInfoStatusList,bidding.getStatus()))
            basic.initNull();

        result.setBiddingBasic(basic);
        result.setBiddingStrategy(strategy);
        result.setBiddingAdjustList(BeanUtil.copyToList(adjustRecords, BiddingAdjustDTO.class));
        result.setBiddingBuyerDetails(biddingDetails);
        result.setBiddingBuyerDeal(biddingBuyerDeal);
        result.setApproveList(BeanUtil.copyToList(iBiddingApproveRecordBiz.listByBiddingNo(bidding.getBiddingNo()), BiddingApproveRecordDTO.class));
        //竞价分析图数据
        result.setBiddingAnalysis(biddingBiz.biddingAnalysis(bidding));

        return ItemResult.success(result);
    }

    private void setBiddingBuyerDetailData(List<BiddingBuyerDetailDTO> biddingDetails, Bidding bidding, Map<String, MemberSimpleDTO> memberMap, Map<String, BiddingMemberSaleUserDTO> saleMap, Map<String, BiddingBuyerDeal> dealMap) {
        for (BiddingBuyerDetailDTO v : biddingDetails) {
            String key = CharSequenceUtil.format(TEMPLATE, v.getMemberCode(), bidding.getGoodsCode());
            MemberSimpleDTO member = CommonUtils.getByKey(memberMap, v.getMemberCode());
            if (Objects.nonNull(member))
                v.setCrmCode(member.getCrmCode());

            BiddingMemberSaleUserDTO saleUser = CommonUtils.getByKey(saleMap, key);
            if (Objects.nonNull(saleUser)) {
                v.setSaleUser(saleUser.getSaleUser());
                v.setCustomerServiceUser(saleUser.getCustomerServiceUser());
            }

            BiddingBuyerDeal deal = CommonUtils.getByKey(dealMap, v.getId());
            if (Objects.nonNull(deal)) {
                v.setSapContractNo(deal.getContractNo());
                v.setDealNo(deal.getDealNo());
                v.setCreateContractStatus(deal.getCreateContractStatus());
                v.setCreateContractFailReason(deal.getCreateContractFailReason());
            }
        }
    }

    public Map<String, BiddingMemberSaleUserDTO> setSaleMap(List<BiddingMemberSaleUserDTO> saleInfo) {
        Map<String, BiddingMemberSaleUserDTO> saleMap = null;
        if (CollUtil.isNotEmpty(saleInfo))
            saleMap = saleInfo.stream()
                    .collect(Collectors.toMap(v -> CharSequenceUtil.format(TEMPLATE,
                                    v.getMemberCode(), v.getGoodsCode()),
                            Function.identity(),
                            (v1, v2) -> v1));
        return saleMap;
    }

    public List<BiddingBuyerDetailDTO> setBiddingDetails(Boolean isSalePersonal, List<BiddingBuyerDetailDTO> biddingDetails, List<String> salePersonalMemberCodes) {
        //销售人员可以看到的竞价数据
        if (BooleanUtil.isTrue(isSalePersonal))
            return biddingDetails.stream().filter(v -> CollUtil.contains(salePersonalMemberCodes, v.getMemberCode())).collect(Collectors.toList());
        return biddingDetails;
    }

    private List<BiddingBuyerDetail> setBiddingBuyerDetailBySeller(Bidding bidding, List<BiddingBuyerDeal> dealList) {
        //竞价结果 卖家  竞价结果的数据=成交结果的数据
        List<BiddingBuyerDetail> biddingBuyerDetailBySeller = null;
        if (CollUtil.isNotEmpty(dealList)) {
            biddingBuyerDetailBySeller = iBiddingBuyerDetailBiz.listByDeals(dealList);
        } else {
            List<BiddingBuyerDetail> buyerDetailsByRound = iBiddingBuyerDetailBiz.listByNo(bidding.getBiddingNo(), bidding.getCurrentRound());
            if (CollUtil.isNotEmpty(buyerDetailsByRound))
                biddingBuyerDetailBySeller = buyerDetailsByRound.stream()
                        .collect(Collectors.groupingBy(BiddingBuyerDetail::getMemberCode))
                        .values()
                        .stream().map(v -> CollUtil.getFirst(v))
                        .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(biddingBuyerDetailBySeller)) {
            biddingBuyerDetailBySeller = biddingBuyerDetailBySeller.stream()
                    .sorted(Comparator.comparing(BiddingBuyerDetail::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }
        return biddingBuyerDetailBySeller;
    }

    private ItemResult<BiddingDetailDTO> detailByBuyer(Bidding bidding, QueryBiddingDetailDTO param) {
        BiddingDetailDTO result = new BiddingDetailDTO();

        BiddingBasicDTO basic = BeanUtil.toBean(bidding, BiddingBasicDTO.class);
        basic.setLastBiddingStartTime(null);
        basic.setLastBiddingEndTime(null);
        basic.setOverDealRemark(null);
        basic.setEnquiryNo(bidding.getEnquiryNo());
        basic.setSalePlanNo(bidding.getSalesPlanNo());
        basic.initNull();

        //参与状态
        BiddingBuyer buyer = iBiddingBuyerBiz.getBuyer(bidding.getBiddingNo(), param.getBuyerMemberCode());
        if (Objects.isNull(buyer))
            return fail("未查询到当前用户在当前竞价的可参与客户中");
        basic.setJoinStatus(ParticipationStatusEnum.DONE.getCode().equals(buyer.getParticipationStatus()));

        //客户人数
        List<BiddingBuyerDetail> biddingBuyerDetails = biddingBuyerDetailBiz.listByNo(bidding.getBiddingNo());
        if (CollUtil.isNotEmpty(biddingBuyerDetails)) {
            basic.setBiddingBuyerCount(biddingBuyerDetails.size());
        }
        //询价
        Enquiry enquiry = iEnquiryBiz.getByNo(bidding.getEnquiryNo());
        //销售计划
        SalesPlan salesPlan = iSalesPlanBiz.getByNo(bidding.getSalesPlanNo());
        if (Objects.nonNull(enquiry)) {
            basic.setEnquiryId(enquiry.getId());
            basic.setEnquiryNo(enquiry.getEnquiryNo());
        }

        BiddingStrategyDTO biddingStrategy = BeanUtil.toBean(bidding, BiddingStrategyDTO.class);
        GoodsCategorySimpleDTO category = CollUtil.getFirst(iGoodsCategoryService.getSimpleList(Set.of(bidding.getCategoryCode())));
        if (Objects.nonNull(category)) {
            biddingStrategy.setCategoryName(category.getCategoryName());
        }
        biddingStrategy.setCurrentWeekExpectPrice(null);
        biddingStrategy.setCostPrice(null);
        biddingStrategy.setRemainSellableQuantity(null);
        biddingStrategy.setCurrApplySellableQuantity(null);
        biddingStrategy.setMinSellableQuantity(null);
        biddingStrategy.setMarketSituation(null);
        biddingStrategy.setDescription(null);
        biddingStrategy.setSupportDocument(null);
        //初始化数据为null
        biddingStrategy.initNull();
        if (Objects.nonNull(salesPlan)) {
            basic.setSalePlanId(salesPlan.getId());
            basic.setSalePlanId(salesPlan.getPlanNo());

            biddingStrategy.setDeliveryMode(salesPlan.getDeliveryMode());
            biddingStrategy.setDeliveryCostStandard(salesPlan.getDeliveryCostStandard());
            biddingStrategy.setSelfPickupGuide(salesPlan.getSelfPickupGuide());
            biddingStrategy.setSelfPickupCarrier(salesPlan.getSelfPickupCarrier());

        }

        List<BiddingBuyerDetail> buyerDetails = iBiddingBuyerDetailBiz.listByBuyer(bidding.getBiddingNo(), param.getBuyerMemberCode());

        //非首轮情况 处理最新最新价格 与是否参与上轮竞价数据
        if(bidding.getCurrentRound() > OrderNumberConstant.ONE) {
            //是否参与了上一轮竞价
            basic.setJoinLastRoundBidding(biddingBuyerDetailBiz.existByMemberCode(param.getBuyerMemberCode(),
                    bidding.getBiddingNo(), bidding.getCurrentRound() - OrderNumberConstant.ONE));

            //若某一轮未参与竞价 后续看到的最新基准价格都为最开始没有参与轮次的价格
            biddingStrategy.setLastStandardPrice(this.getNewestStandardPriceByBuyer(bidding,param.getBuyerMemberCode()));
        }

        result.setBiddingStrategy(biddingStrategy);
        result.setBiddingBuyerDetails(BeanUtil.copyToList(buyerDetails, BiddingBuyerDetailDTO.class));

        BiddingBuyerDealDTO biddingResult = new BiddingBuyerDealDTO();
        biddingResult.setBiddingNo(bidding.getBiddingNo());
        basic.setDealStatus(false);
        if (BiddingStatusEnum.COMPLETED.getStatus().equals(bidding.getStatus())) {
            biddingResult.setDealTime(bidding.getDealTime());
            // sap合同编号
            BiddingBuyerDeal deal = CollUtil.getFirst(iBiddingBuyerDealBiz.listByBiddingNo(bidding.getBiddingNo(), param.getBuyerMemberCode()));
            if (Objects.nonNull(deal)) {
                biddingResult.setSapContractNo(deal.getContractNo());
                biddingResult.setBuyerDealDetail(BeanUtil.copyToList(
                        biddingBuyerDetailBiz.listByDeals(Arrays.asList(deal)),
                        BiddingBuyerDetailDTO.class));
            }

            //判断买家 参与、成交状态
            basic.setDealStatus(CollUtil.isNotEmpty(biddingResult.getBuyerDealDetail()));
            biddingResult.setDealStatus(basic.getDealStatus());

            List<BiddingMemberSaleUserDTO> saleInfo = this.getMemberSaleUsers(Arrays.asList(param.getBuyerMemberCode()), bidding.getGoodsCode());
            BiddingMemberSaleUserDTO saleUser = CollUtil.getFirst(saleInfo);
            if (Objects.nonNull(saleUser)) {
                biddingResult.setSaleUser(saleUser.getSaleUser());
                biddingResult.setCustomerServiceUser(saleUser.getCustomerServiceUser());
            }
        }

        result.setBiddingBasic(basic);
        result.setBiddingBuyerDeal(biddingResult);

        return ItemResult.success(result);
    }

    private List<BiddingMemberSaleUserDTO> getMemberSaleUsers(List<String> memberCodes, String goodsCode) {
        if (CollUtil.isEmpty(memberCodes))
            return Collections.emptyList();

        QueryIntentionInfoDTO query = new QueryIntentionInfoDTO();
        query.setGoodsCodes(Arrays.asList(goodsCode));
        query.setMemberCodes(memberCodes);

        List<MemberPurchaseGoodsIntentionDTO> intentions = iMemberService.queryMemberBySaleInfo(query);
        if (CollUtil.isEmpty(intentions))
            return Collections.emptyList();

        List<String> saleUserIds = intentions.stream()
                .map(v -> v.getSaleUserId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(saleUserIds))
            return Collections.emptyList();

        QueryAccountRelationDTO queryRelation = new QueryAccountRelationDTO();
        queryRelation.setAccountIds(saleUserIds);
        queryRelation.setLinkAccountRoles(Arrays.asList(BaseRoleTypeEnum.SELLER_CUSTOMER_SERVICE.getRoleCode()));
        List<AccountRelationInfoDTO> relations = iAccountRelationService.queryAccountRelation(queryRelation);

        Map<String, AccountRelationInfoDTO> relationMap = CommonUtils.getMap(relations, AccountRelationInfoDTO::getAccountId);
        List<String> customerServiceIds = null;
        if (CollUtil.isNotEmpty(relations)) {
            customerServiceIds = relations.stream()
                    .map(v -> v.getLinkAccountId())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<String> allUserIds = CollUtil.addAll(saleUserIds, customerServiceIds).stream().collect(Collectors.toList());
        Map<String, AccountSimpleDTO> accountMap = CommonUtils.getMap(iAccountService.listSimpleByIds(allUserIds), AccountSimpleDTO::getAccountId);

        return intentions.stream().map(v -> {
            BiddingMemberSaleUserDTO saleInfo = new BiddingMemberSaleUserDTO(v.getMemberCode(), v.getGoodsCode(), null, null);
            AccountSimpleDTO saleUser = CommonUtils.getByKey(accountMap, v.getSaleUserId());
            if (Objects.nonNull(saleUser))
                saleInfo.setSaleUser(new AccountUserDTO(saleUser.getAccountId(), saleUser.getRealName(), saleUser.getMobile(), saleUser.getEmployeeId()));
            AccountRelationInfoDTO relation = CommonUtils.getByKey(relationMap, v.getSaleUserId());
            if (Objects.nonNull(relation) && CharSequenceUtil.isNotBlank(relation.getLinkAccountId())) {
                AccountSimpleDTO customerServiceUser = CommonUtils.getByKey(accountMap, relation.getLinkAccountId());
                if (Objects.nonNull(customerServiceUser))
                    saleInfo.setCustomerServiceUser(new AccountUserDTO(customerServiceUser.getAccountId(), customerServiceUser.getRealName(), customerServiceUser.getMobile(), customerServiceUser.getEmployeeId()));
            }
            return saleInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 通知一级审批人 CMMS
     */
    private void notifyWaitApprovalByDeal(Bidding bidding, List<BiddingBuyerDetail> details, String operatorName) {
        if (CollUtil.isEmpty(details) || Objects.isNull(bidding))
            return;
        List<AccountNameDTO> cmmsAccounts = iAccountService.findAccountNameByRoleCode(BaseRoleTypeEnum.SELLER_CMMS.getRoleCode());
        if (CollUtil.isEmpty(cmmsAccounts))
            return;

        DataPermissionGoodsCodeDTO query = new DataPermissionGoodsCodeDTO();
        query.setGoodsCodeList(Arrays.asList(bidding.getGoodsCode()));
        query.setRoleCode(BaseRoleTypeEnum.SELLER_BUSINESS_MANAGER.getRoleCode());
        List<DataPermissionAccountInfoDTO> ccAccounts = iRoleService.findAccountByGoodsCode(query);
        if (CollUtil.isEmpty(ccAccounts))
            return;

        MessageDTO message = new MessageDTO();
        message.setTitle(BiddingMessageTemplateEnum.NOTIFY_WAIT_APPROVAL_BY_CMMS.getTitle());
        Map<String, Object> param = new HashMap<>();
        param.put("biddingNo", bidding.getBiddingNo());
        param.put("biddingName", bidding.getBiddingName());
        for (AccountNameDTO cmmsAccount : cmmsAccounts) {

            //发送站内信通知一级审批人
            message.setReceiveAccountIds(Arrays.asList(cmmsAccount.getAccountId()));
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_WAIT_APPROVAL_BY_CMMS.getMsg(cmmsAccount.getRealName(),bidding.getBiddingNo()));
            iMessageSendService.sendMessage(message);

            EmailDTO email = new EmailDTO();
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_WAIT_APPROVAL_BY_DEAL_ATE.getCode());
            if (CollUtil.isNotEmpty(ccAccounts) && CollUtil.isNotEmpty(CollUtil.getFirst(ccAccounts).getAccountList())) {
                List<String> ccEmails = CollUtil.getFirst(ccAccounts).getAccountList().stream()
                        .map(v -> v.getEmail())
                        .filter(CharSequenceUtil::isNotBlank)
                        .distinct().collect(Collectors.toList());

                email.setToCcs(ccEmails);
            }
            if (CharSequenceUtil.isNotBlank(cmmsAccount.getEmail())) {
                email.setTos(Arrays.asList(cmmsAccount.getEmail()));
                param.put("realName", cmmsAccount.getRealName());
                param.put("submitUserName", operatorName);
                email.setTemplateParam(param);
                //发送邮件通知cmms cc给商务经理
                iEmailSendService.sendEmail(email);
            }
        }
    }


    private static ItemResult fail(String message) {
        return ItemResult.fail(OrderErrorCode.CUSTOM.getCode(), message);
    }

    private void notifyLastBuyer(List<BiddingBuyerDetail> lastBuyerDetail, Bidding bidding) {
        if (CollUtil.isNotEmpty(lastBuyerDetail)) {
            String startTime = DateUtil.formatDateTime(bidding.getLastBiddingStartTime());
            List<String> memberCodes = lastBuyerDetail.stream().map(v -> v.getMemberCode()).distinct().collect(Collectors.toList());
            executor.execute(() -> {
                QueryMemberAccountDTO query = new QueryMemberAccountDTO(memberCodes, Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode()));
                List<AccountSimpleDTO> accountList = iMemberService.listAccountsByMemberCodes(query);
                //发送短信，站内信通知上一轮参与竞价的客户
                this.notifySmsLastBuyerByAdjust(bidding, accountList, startTime);
                this.notifyMessageLastBuyerByAdjust(bidding, accountList, startTime);
                //发送短信站内信通知上一轮客户产品人员
                Map<QueryAccountDataPermissionDTO, List<AccountSimpleDTO>> permissionAccountMap = iBiddingAccountService.queryAccountDataPermission(memberCodes, Arrays.asList(bidding.getGoodsCode()));
                //通知客户产品相关人员
                List<QueryAccountDataPermissionDTO> keys = memberCodes.stream()
                        .distinct()
                        .map(v -> new QueryAccountDataPermissionDTO(v, bidding.getGoodsCode()))
                        .collect(Collectors.toList());
                keys.forEach(v -> {
                    List<AccountSimpleDTO> accounts = CommonUtils.getByKey(permissionAccountMap, v);
                    if (CollUtil.isNotEmpty(accounts)) {
                        //短信通知
                        this.notifySmsLastBuyerByAdjust(bidding, accounts, startTime);
                        //站内信通知
                        this.notifyMessageLastBuyerByAdjust(bidding, accounts, startTime);
                    }
                });
            });
        }
    }

    private void notifySmsLastBuyerByAdjust(Bidding bidding, List<AccountSimpleDTO> accountList, String startTime) {
        List<String> mobiles = accountList.stream()
                .map(v -> v.getMobile())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());

        SmsDTO sms = new SmsDTO();
        sms.setMobiles(mobiles);
        sms.setTemplateCode(SmsTemplateEnum.NEXT_BIDDING_NOTICE_CODE.getCode());
        sms.setTemplateParams(Arrays.asList(bidding.getBiddingNo(), bidding.getBiddingNo(),bidding.getGoodsName(), startTime, bidding.getBiddingNo(), bidding.getBiddingName()));
        iSmsSendService.sendSms(sms);
    }

    private void notifyMessageLastBuyerByAdjust(Bidding bidding, List<AccountSimpleDTO> accountList, String startTime) {
        //发送站内信通知上一轮参与竞价的客户
        for (AccountSimpleDTO v : accountList) {
            MessageDTO message = new MessageDTO();
            message.setReceiveAccountIds(Arrays.asList(v.getAccountId()));
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_LAST_BIDDING_START.getTitle());
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_LAST_BIDDING_START.getMsg(v.getMemberName(),
                    bidding.getBiddingNo(),
                    bidding.getBiddingNo(),
                    bidding.getGoodsName(),
                    startTime));
            iMessageSendService.sendMessage(message);
        }
    }

    private static void handleUserName(BaseResultDTO base, Map<String, AccountSimpleDTO> accountMap) {
        if (Objects.isNull(base))
            return;
        AccountSimpleDTO createUser = CommonUtils.getByKey(accountMap, base.getCreateUser());
        if (Objects.nonNull(createUser))
            base.setCreateUserName(createUser.getRealName());
        AccountSimpleDTO updateUser = CommonUtils.getByKey(accountMap, base.getUpdateUser());
        if (Objects.nonNull(updateUser))
            base.setUpdateUserName(updateUser.getRealName());
    }

    /**
     * 获取买家最新的基准价格。
     * 如果买家从未参与过竞价，则返回初始的基准价格。
     * 如果买家最新参与的轮次加1小于当前竞价轮次，则返回该轮次调价后的旧基准价格。
     * 否则，返回竞价最新基准价格。
     *
     * @param bidding    竞价信息
     * @param memberCode 会员代码
     * @return 最新的基准价格
     */
    private BigDecimal getNewestStandardPriceByBuyer(Bidding bidding, String memberCode) {
        BiddingBuyerDetail newestBuyerDetail = biddingBuyerDetailBiz.getLastBuyerDetail(bidding.getBiddingNo(), memberCode);
        // 全部都未参与 返回最初价格
        if (Objects.isNull(newestBuyerDetail)) {
            return bidding.getStandardPrice();
        }
        Integer nextRound = newestBuyerDetail.getCurrentRound() + OrderNumberConstant.ONE;
        // 最新参与竞价轮次+1 小于 当前竞价轮次 取nextRound 进行调价的轮次的价格
        if (nextRound < bidding.getCurrentRound()) {
            BiddingAdjustRecord adjustByNextRound = iBiddingAdjustBiz.getByRound(bidding.getBiddingNo(), nextRound);
            if (Objects.nonNull(adjustByNextRound)) {
                return adjustByNextRound.getOldStandardPrice();
            }
        }

        return bidding.getLastStandardPrice();
    }

    private void notifyWithdrawToCmms(BiddingReverseDTO param, Bidding bidding) {
        //市场开发部经理
        List<AccountNameDTO> cmmsList = iBiddingAccountService.queryAccountByRole(BaseRoleTypeEnum.SELLER_CMMS.getRoleCode());
        //商务经理
        Map<String, List<AccountNameDTO>> businessList = iBiddingAccountService.findApprover(
                BaseRoleTypeEnum.SELLER_BUSINESS_MANAGER.getRoleCode(), Arrays.asList(bidding.getGoodsCode()));

        if (CollUtil.isEmpty(cmmsList))
            return;

        List<String> businessEmails = CommonUtils.getListValueByDistinctAndFilterBank(
                CommonUtils.getByKey(businessList, bidding.getGoodsCode()), AccountNameDTO::getEmail);

        for (AccountNameDTO cmms : cmmsList) {
            //站内信通知CMMS
            MessageDTO message = new MessageDTO();
            message.setReceiveAccountIds(Arrays.asList(cmms.getAccountId()));
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CMMS.getTitle());
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CMMS.getMsg(cmms.getRealName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(message);

            //邮件通知CMMS CC商务经理
            EmailDTO email = new EmailDTO();
            email.setTos(Arrays.asList(cmms.getEmail()));
            email.setToCcs(businessEmails);
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_WITHDRAW_STRATEGY_TO_CMMS.getCode());
            Map<String, Object> emailParam = new HashMap<>();
            emailParam.put("realName", cmms.getRealName());
            emailParam.put("biddingNo", bidding.getBiddingNo());
            emailParam.put("biddingName", bidding.getBiddingName());
            emailParam.put("withdrawUsername", param.getOperatorName());
            emailParam.put("withdrawReason", param.getReason());
            email.setTemplateParam(emailParam);
            iEmailSendService.sendEmail(email);
        }
    }

    private void notifyCancelToCmms(BiddingReverseDTO param, Bidding bidding) {
        //市场开发部经理
        List<AccountNameDTO> cmmsList = iBiddingAccountService.queryAccountByRole(BaseRoleTypeEnum.SELLER_CMMS.getRoleCode());
        //商务经理
        Map<String, List<AccountNameDTO>> businessList = iBiddingAccountService.findApprover(
                BaseRoleTypeEnum.SELLER_BUSINESS_MANAGER.getRoleCode(), Arrays.asList(bidding.getGoodsCode()));

        if (CollUtil.isEmpty(cmmsList))
            return;

        List<String> businessEmails = CommonUtils.getListValueByDistinctAndFilterBank(
                CommonUtils.getByKey(businessList, bidding.getGoodsCode()), AccountNameDTO::getEmail);

        for (AccountNameDTO cmms : cmmsList) {
            //站内信通知CMMS
            MessageDTO message = new MessageDTO();
            message.setReceiveAccountIds(Arrays.asList(cmms.getAccountId()));
            message.setTitle(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CMMS.getTitle());
            message.setContent(BiddingMessageTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CMMS.getMsg(cmms.getRealName(), bidding.getBiddingNo()));
            iMessageSendService.sendMessage(message);

            //邮件通知CMMS CC商务经理
            EmailDTO email = new EmailDTO();
            email.setTos(Arrays.asList(cmms.getEmail()));
            email.setToCcs(businessEmails);
            email.setEmailTemplateCode(EmailTemplateEnum.NOTIFY_CANCEL_BIDDING_TO_CMMS.getCode());
            Map<String, Object> emailParam = new HashMap<>();
            emailParam.put("realName", cmms.getRealName());
            emailParam.put("biddingNo", bidding.getBiddingNo());
            emailParam.put("biddingName", bidding.getBiddingName());
            emailParam.put("cancelUsername", param.getOperatorName());
            emailParam.put("cancelReason", param.getReason());
            email.setTemplateParam(emailParam);
            iEmailSendService.sendEmail(email);
        }
    }
}
