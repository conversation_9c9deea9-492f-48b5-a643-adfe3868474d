package com.cnoocshell.common.aop.log;

import com.cnoocshell.common.annotation.SetThreadName;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import org.apache.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Aspect
public class SetThreadNameAop {

    @Lazy
    @Autowired
    private UUIDGenerator uuidGenerator;
    /**
     * 切入点
     */
    @Pointcut("@annotation(com.cnoocshell.common.annotation.SetThreadName)")
    public void entryPoint() {
        //do nothing
    }

    @Around(value = "entryPoint() && @annotation(stn)")
    public Object assertAround(ProceedingJoinPoint pjp, SetThreadName stn) throws RuntimeException {
        String name = null;
        try {
            try {
                //获取线程原来的名称
                name = Thread.currentThread().getName();
                String newName = getThreadName(pjp,stn);
                if( StringUtils.isNotBlank(newName)){
                    Thread.currentThread().setName(newName);
                }
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }

            Object result = null;
            try {
                result = pjp.proceed();
            } catch (Throwable t) {
                if( t instanceof BizException){
                    throw (BizException)t;
                }
                if( t instanceof RuntimeException ){
                    throw (RuntimeException)t;
                }
                throw new RuntimeException(t);
            }
            return result;

        }finally {
            try {
                if (StringUtils.isNotBlank(name)) {
                    //还原线程原来的名称
                    Thread.currentThread().setName(name);
                }
            }catch(Exception e){
                log.error(e.getMessage(),e);
            }
        }
    }

    private String getThreadName(ProceedingJoinPoint pjp,SetThreadName rl) {
        if( rl != null && rl.uuid() ){
            return uuidGenerator.gain();
        }
        StringBuilder stringBuilder = getStringBuilder(pjp, rl);
        if (stringBuilder == null) return null;
        return stringBuilder.toString();
    }

    @Nullable
    private static StringBuilder getStringBuilder(ProceedingJoinPoint pjp, SetThreadName rl) {
        Object[] args = pjp.getArgs();
        if (checkArgsAndRl(rl, args)) return null;

        String className = pjp.getTarget().getClass().getName();
        String method = pjp.getSignature().getName();
        int argsLength = args.length;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(className).append(".").append(method).append(".");

        if ( rl.index() >= 0 && rl.index() <argsLength ){
            if (rl.fieldName() == null || rl.fieldName().length == 0){
                stringBuilder.append(args[rl.index()]);
            }else{
                if (forAppendObj(rl, args, stringBuilder)) return null;
            }
        }
        appendIndex(rl, argsLength, args, stringBuilder);
        return stringBuilder;
    }

    private static boolean forAppendObj(SetThreadName rl, Object[] args, StringBuilder stringBuilder) {
        for (String s : rl.fieldName()) {
            try {
                Object obj = PropertyUtils.getProperty(args[rl.index()],s);
                if( obj != null) {
                    stringBuilder.append(obj).append(".");
                }
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }

    private static void appendIndex(SetThreadName rl, int argsLength, Object[] args, StringBuilder stringBuilder) {
        if( rl.indexs() != null && rl.indexs().length>0 ){
            for (int index : rl.indexs()) {
                if( index >0 && index< argsLength){
                    Object obj = args[index];
                    if( obj != null ) {
                        stringBuilder.append(obj).append(".");
                    }
                }
            }
        }
    }

    private static boolean checkArgsAndRl(SetThreadName rl, Object[] args) {
        if( args == null || args.length == 0 ){
            return true;
        }
        if( rl.index() == -1 && (rl.fieldName() == null || rl.fieldName().length == 0) && (rl.indexs() == null || rl.indexs().length == 0) ){
            return true;
        }
        return false;
    }
}
