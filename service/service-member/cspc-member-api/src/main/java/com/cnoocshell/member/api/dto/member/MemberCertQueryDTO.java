package com.cnoocshell.member.api.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <EMAIL>
 * @description
 * @date 21/08/2018 14:28
 */
@Data
public class MemberCertQueryDTO implements Serializable {

    private static final long serialVersionUID = -9221845924097977221L;
    /**
     * 附件ID
     */
    @NotBlank
    @ApiModelProperty("附件ID")
    private String attachmentId;

    /**
     * 是否默认资质
     */
    @ApiModelProperty("是否默认资质")
    private Integer isDefault;

    /**
     * 资质名称
     */
    @NotBlank
    @ApiModelProperty("资质名称")
    private String certName;

    /**
     * 资质类型
     */
    @ApiModelProperty("资质类型")
    private String certType;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idNumber;

    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty("紧急联系人姓名")
    private String contactName;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty("紧急联系人电话")
    private String contactPhone;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Date effectiveTime;

    /**
     * 资质ID
     */
    @ApiModelProperty("资质ID")
    private String certId;

}
